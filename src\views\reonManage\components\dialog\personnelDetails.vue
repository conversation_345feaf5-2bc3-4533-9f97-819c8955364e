<!-- 人员详情 -->
<template>
    <div>
        <el-dialog :title="props.title" v-model="dialogShow" width="65%" append-to-body draggable @close="close">
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" inline label-width="auto">
                <el-form-item label="姓名/身份证/唯一号:">
                    <el-input class="width220" v-model="obj.dialogForm.city" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" plain icon="Search" @click="handleQuery">查询</el-button>
                    <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
                    <el-button type="primary"
                        v-if="props.menuName != 'wagePaymentDetails' && props.menuName != 'payrollManagement'" plain
                        @click="handleExport">同步银行卡</el-button>
                    <el-button type="danger"
                        v-if="props.menuName != 'wagePaymentDetails' && props.menuName != 'payrollManagement'" plain
                        icon="Delete" @click="handleExport">删除未发放工资</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="props.tableData" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="序号" align="center" fixed prop="id" width="60" />
                <el-table-column label="员工编号" align="center" fixed prop="employeeNo" min-width="100" />
                <el-table-column label="姓名" align="center" fixed prop="name" width="100" />
                <el-table-column label="电话" align="center" fixed prop="phone" width="100" />
                <el-table-column label="证件类型" align="center" fixed prop="idType" width="100" />
                <el-table-column label="证件号码" align="center" fixed prop="idNo" min-width="150" />
                <el-table-column label="工资所属月" align="center" fixed prop="salaryMonth" width="100" />
                <el-table-column v-if="props.menuName == 'payrollManagement'" label="计算是否有差异" align="center" fixed
                    prop="isDifference" width="130" />
                <el-table-column v-if="props.menuName != 'payrollManagement'" label="支付创建时间" align="center" fixed
                    prop="createTime" width="120" />
                <el-table-column label="工资支付日期" align="center" fixed prop="payDate" width="120" />
                <el-table-column label="开户行" align="center" prop="bankName" min-width="120" />
                <el-table-column label="银行卡号" align="center" prop="bankCardNo" min-width="150" />
                <el-table-column label="工资发放地" align="center" prop="payLocation" min-width="120" />
                <el-table-column label="扣缴义务人名称" align="center" prop="payeeName" min-width="150" />
                <el-table-column label="发薪地是否变更" align="center" prop="isPayLocationChange" width="120" />
                <el-table-column label="发薪地与参保地不一致" align="center" prop="isPayLocationChange" width="160" />
                <el-table-column label="发薪是否新增" align="center" prop="isPayeeChange" width="120" />
                <el-table-column label="银行卡是否变更" align="center" prop="isBankCardChange" width="120" />
                <el-table-column label="薪资发放状态" align="center" prop="payStatus" width="120" />
                <el-table-column label="基本工资(系统项)" align="center" prop="basicSalary" width="140" />
                <el-table-column label="工资服务费" align="center" prop="serviceFee" width="100" />
                <el-table-column label="五险一金" align="center" prop="insuranceFee" width="100" />
                <el-table-column label="应税工资(含个税基数)" align="center" prop="taxableSalary" width="160" />
                <el-table-column label="个人所得稅" align="center" prop="personalIncomeTax" width="100" />
                <el-table-column label="实发合计" align="center" prop="totalSalary" width="100" />
                <el-table-column label="累计工资" align="center" prop="cumulativeSalary" width="100" />
                <el-table-column label="累计扣除" align="center" prop="cumulativeDeduction" width="100" />
                <el-table-column label="累计扣税(不包含当月)" align="center" prop="cumulativeTax" width="160" />
                <el-table-column label="累计五险一金" align="center" prop="insuranceFee" width="120" />
                <el-table-column label="累计住房租金支出扣除" align="center" prop="housingRentDeduction" width="160" />
                <el-table-column label="累计住房贷款利息支出扣除" align="center" prop="housingLoanDeduction" width="200" />
                <el-table-column label="累计赡养老人支出扣除" align="center" prop="oldSupportDeduction" width="160" />
                <el-table-column label="累计子女教育支出" align="center" prop="childEducationDeduction" width="150" />
                <el-table-column label="累计继续教育支出" align="center" prop="continueEducationDeduction" width="150" />
                <el-table-column label="累计基本减除费用" align="center" prop="basicDeduction" width="150" />
                <el-table-column label="累计专项附加扣除" align="center" prop="specialAdditionalDeduction" width="150" />
                <el-table-column label="本次扣减的住房租金支出" align="center" prop="housingRentDeduction" width="170" />
                <el-table-column label="本次扣减的住房贷" align="center" prop="housingLoanDeduction" width="140" />
                <el-table-column label="本次扣减的赡养" align="center" prop="oldSupportDeduction" width="120" />
                <el-table-column label="本次扣减子女教" align="center" prop="childEducationDeduction" width="120" />
                <el-table-column label="本次扣减的继续教" align="center" prop="continueEducationDeduction" width="140" />
                <el-table-column label="本次使用专项附" align="center" prop="specialAdditionalDeduction" width="120" />
                <el-table-column label="本次税后扣减" align="center" prop="afterTaxDeduction" width="120" />
                <el-table-column label="本次税后扣增" align="center" prop="afterTaxIncrease" width="120" />
                <el-table-column label="收入合计" align="center" prop="totalIncome" width="100" />
                <el-table-column label="扣款合计" align="center" prop="totalDeduction" width="100" />
                <el-table-column label="其它扣除" align="center" prop="otherDeduction" width="100" />
                <el-table-column label="累计其它扣除" align="center" prop="cumulativeOtherDeduction" width="120" />
                <el-table-column label="养老个人" align="center" prop="oldSupportDeduction" width="100" />
                <el-table-column label="医疗个人" align="center" prop="medicalPersonal" width="100" />
                <el-table-column label="失业个人" align="center" prop="unemploymentPersonal" width="100" />
                <el-table-column label="大病个人" align="center" prop="largeDiseasePersonal" width="100" />
                <el-table-column label="公积金个人" align="center" prop="housingFundPersonal" width="100" />
                <el-table-column label="其他个人" align="center" prop="otherPersonal" width="100" />
                <el-table-column label="应发工资" align="center" prop="totalSalary" width="100" />
                <el-table-column label="本次扣减的3岁以下婴幼儿照护" align="center" prop="childcareDeduction" width="220" />
                <el-table-column label="累计3岁以下婴幼儿照护" align="center" prop="cumulativeChildcareDeduction" width="180" />
                <el-table-column label="本次扣减的其它扣除" align="center" prop="otherDeduction" width="150" />
                <el-table-column label="累计其它扣除(专项)" align="center" prop="cumulativeOtherDeduction" width="170" />
                <el-table-column label="残障金" align="center" prop="disabilityAllowance" width="100" />
                <el-table-column label="经济补偿金" align="center" prop="economicCompensation" width="100" />
                <el-table-column label="经济补偿金超额计税部分" align="center" prop="economicCompensationOverTax" width="170" />
                <el-table-column label="累计经济补偿金超额计税部分" align="center" prop="cumulativeEconomicCompensationOverTax"
                    width="200" />
                <el-table-column label="公积金超额计税部分" align="center" prop="housingFundOverTax" width="150" />
                <el-table-column label="累计公积金超额计税部分" align="center" prop="cumulativeHousingFundOverTax" width="180" />
                <el-table-column label="本次个人养老金" align="center" prop="personalPension" width="120" />
                <el-table-column label="累计个人养老金" align="center" prop="cumulativePersonalPension" width="120" />
                <el-table-column label="经济补偿金应税金" align="center" prop="economicCompensationTaxable" width="150" />
                <el-table-column label="经济补偿金个税" align="center" prop="economicCompensationTax" width="120" />
                <el-table-column label="经济补偿金实发金额" align="center" prop="economicCompensationActual" width="150" />
                <el-table-column label="累计经济补偿金扣税(不包含当月)" align="center" prop="" width="220" />
                <el-table-column label="个税调" align="center" prop="personalIncomeTaxAdjustment" width="100" />
                <el-table-column label="跨行手续费" align="center" prop="crossBankFee" width="100" />
                <el-table-column label="税率" align="center" prop="taxRate" width="80" />
                <el-table-column label="速算扣除数" align="center" prop="quickDeduction" width="100" />
                <el-table-column label="经济补偿金税率" align="center" prop="economicCompensationTaxRate" width="150" />
                <el-table-column label="经济补偿金速算扣除数" align="center" prop="economicCompensationQuickDeduction"
                    width="180" />
                <el-table-column label="工会费" align="center" prop="unionFee" width="100" />
                <el-table-column label="个人长护险" align="center" prop="personalLongCareInsurance" width="100" />
                <el-table-column label="大额医疗补助" align="center" prop="largeMedicalAssistance" width="120" />
                <el-table-column label="补充医疗保险" align="center" prop="supplementMedicalInsurance" width="120" />
                <el-table-column label="门诊医疗(社保)" align="center" prop="outpatientMedicalInsurance" width="120" />
                <el-table-column label="大病保险(税后扣除)" align="center" prop="largeMedicalInsurance" width="150" />
                <el-table-column label="补充医疗保险(税后扣除)" align="center" prop="supplementMedicalInsurance" width="180" />
                <el-table-column label="个人长护险(税后扣除)" align="center" prop="personalLongCareInsurance" width="170" />
                <el-table-column label="跨年个税调" align="center" prop="crossYearTaxAdjustment" width="100" />
                <el-table-column label="银行发薪备注(员工)" align="center" prop="bankPayRemark" min-width="150" />
                <el-table-column label="客服是否确认" align="center" prop="customerConfirm" width="120" />
                <el-table-column label="客服确认时间" align="center" prop="customerConfirmTime" width="150" />
                <el-table-column label="供应商服务费" align="center" prop="supplierServiceFee" width="120" />
                <el-table-column label="供应商残障金" align="center" prop="supplierDisabilityAllowance" width="120" />
                <el-table-column label="供应商跨行手续费" align="center" prop="supplierCrossBankFee" width="150" />
                <el-table-column label="供应商工会费" align="center" prop="supplierUnionFee" width="120" />
                <el-table-column label="供应商税金合计" align="center" prop="supplierTaxTotal" width="120" />
            </el-table>
        </el-dialog>
    </div>
</template>
<script setup>
const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    tableData: {
        type: Array,
        default: () => []
    },
    menuName: {
        type: String,
        default: ''
    }
})
// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});
const emit = defineEmits(['update:dialogShow', 'close'])
const close = () => {
    dialogShow.value = false;
    emit('close')
}

const obj = ref({
    dialogForm: {
        city: ''
    },
})

/** 查询 */
const handleQuery = () => {
    console.log('handleQuery')
}

/** 导出 */
const handleExport = () => {
    console.log('handleExport')
}



</script>
