<!-- 供应商银行基础数据 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="公司:" prop="typeCode">
                <el-select class="width220" filterable v-model="obj.queryParams.province" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="开户行:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.province" placeholder="请输入" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" type="index" width="80" align="center" />
            <el-table-column label="供应商名称" align="center" prop="supplierName" />
            <el-table-column label="银行" align="center" prop="bankName" />
            <el-table-column label="开户行" align="center" prop="bankBranch" />
            <el-table-column label="开户地" align="center" prop="bankLocation" />
            <el-table-column label="银行账户" align="center" prop="bankAccount" />
            <el-table-column label="账户全称" align="center" prop="accountName" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="25%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item label="公司" prop="supplierName">
                    <el-select style="width: 100%" v-model="obj.dialogForm.supplierName" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账户全称" prop="accountName">
                    <el-input style="width: 100%" v-model="obj.dialogForm.accountName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="银行" prop="bankName">
                    <el-select style="width: 100%" v-model="obj.dialogForm.bankName" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="开户银行" prop="bankBranch">
                    <el-input style="width: 100%" v-model="obj.dialogForm.bankBranch" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开户地" prop="bankLocation">
                    <el-input style="width: 100%" v-model="obj.dialogForm.bankLocation" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="银行账号" prop="bankAccount">
                    <el-input style="width: 100%" v-model="obj.dialogForm.bankAccount" placeholder="请输入" />
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Supplier_bankBasicData">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    rules: {
        city: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        type: [{ required: true, message: '请选择人员类型', trigger: 'blur' }],
    },
    total: 0,//总条数
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
    tableData: [
        {
            id: 1,
            supplierName: '北京科技有限公司',
            bankName: '中国工商银行',
            bankBranch: '北京海淀支行',
            bankLocation: '北京市海淀区中关村',
            bankAccount: '6222021234567890123',
            accountName: '北京科技有限公司'
        },
        {
            id: 2,
            supplierName: '上海信息技术有限公司',
            bankName: '中国建设银行',
            bankBranch: '上海浦东支行',
            bankLocation: '上海市浦东新区张江高科技园区',
            bankAccount: '6227002345678901234',
            accountName: '上海信息技术有限公司'
        },
        {
            id: 3,
            supplierName: '广州数字科技有限公司',
            bankName: '中国农业银行',
            bankBranch: '广州天河支行',
            bankLocation: '广州市天河区珠江新城',
            bankAccount: '6228483456789012345',
            accountName: '广州数字科技有限公司'
        }
    ]//列表
});

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据加载
    setTimeout(() => {
        // 实际项目中应该调用API
        // listScale(obj.queryParams).then(response => {
        //     obj.tableData = response.rows;
        //     obj.total = response.total;
        //     obj.loading = false;
        // });
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || obj.ids
    getUsers(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改";
    });
}
/** 提交按钮 */
function submitForm() {
    console.log(obj.dialogForm);
    return
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row.id || obj.ids
    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}




getList();
</script>
<style lang="scss" scoped></style>