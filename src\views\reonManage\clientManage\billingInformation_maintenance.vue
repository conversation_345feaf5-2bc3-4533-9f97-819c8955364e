<!-- 开票信息维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="typeCode" required>
                <el-select class="width220" filterable v-model="obj.queryParams.province" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="开票抬头:" prop="typeCode" required>
                <el-select class="width220" filterable v-model="obj.queryParams.province" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table style="width: 100%" v-loading="obj.loading" border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户名称" align="center" prop="clientName" />
            <el-table-column label="银行账号" align="center" prop="bankAccount" />
            <el-table-column label="开票抬头" align="center" prop="invoiceTitle" />
            <el-table-column label="纳税人识别号" align="center" prop="taxpayerIdentificationNumber" />
            <el-table-column label="开户名" align="center" prop="accountName" />
            <el-table-column label="开户银行" align="center" prop="accountBank" />
            <el-table-column label="项目客服" align="center" prop="projectCustomer" />
            <el-table-column label="公司性质" align="center" prop="companyNature" />
            <el-table-column label="开票方式" align="center" prop="invoiceMethod" />
            <el-table-column label="邮箱" align="center" prop="email" />
            <el-table-column label="推送模式" align="center" prop="pushMode" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="操作" align="center" width="120">
                <template #default="scope">
                    <el-button text size="small" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button text size="small" @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="30%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item label="客户" prop="clientName" required>
                    <el-input style="width: 100%;" readonly v-model="obj.dialogForm.clientName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="公司性质" prop="companyNature" required>
                    <el-select style="width: 100%;" filterable v-model="obj.dialogForm.companyNature" placeholder="请选择"
                        clearable>
                        <el-option label="小规模纳税人" value="1" />
                        <el-option label="一般纳税人" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="发票方式" prop="invoiceMethod">
                    <el-select style="width: 100%;" filterable v-model="obj.dialogForm.invoiceMethod" placeholder="请选择"
                        clearable>
                        <el-option label="全电发票" value="1" />
                        <el-option label="电子发票" value="2" />
                        <el-option label="纸质发票" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="开票抬头" prop="invoiceTitle" required>
                    <el-select style="width: 100%;" filterable v-model="obj.dialogForm.invoiceTitle" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="纳税人识别号" prop="taxpayerIdentificationNumber" required>
                    <el-input style="width: 100%;" v-model="obj.dialogForm.taxpayerIdentificationNumber"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开户名" prop="accountName">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.accountName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开户银行" prop="accountBank">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.accountBank" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="银行账号" prop="bankAccount">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.bankAccount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开票邮箱" prop="email">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.email" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="推送模式" prop="pushMode">
                    <el-select style="width: 100%;" filterable v-model="obj.dialogForm.pushMode" placeholder="请选择"
                        clearable>
                        <el-option label="推送当前邮箱和提票人邮箱" value="1" />
                        <el-option label="仅推送提票人邮箱" value="2" />
                        <el-option label="不推送" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="开票电话" prop="phone">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.phone" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开票地址" prop="address">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.address" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="备注:" prop="remark">
                    <el-input style="width: 100%;" type="textarea" :rows="3" v-model="obj.dialogForm.remark"
                        placeholder="请输入" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="BillingInformation_maintenance">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    rules: {

    },
    total: 0,//总条数

    tableData: [],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.tableData = [{
            id: 1,
            clientName: '客户1',
            bankAccount: '**********',
            invoiceTitle: '开票抬头1',
            taxpayerIdentificationNumber: '纳税人识别号1',
            accountName: '开户名1',
            accountBank: '开户银行1',
        }]
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}
/** 查看按钮操作 */
function handleDetail(row) {
    console.log(row);
    obj.dialogShow = true;
    obj.title = "详情";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || obj.ids
    getUsers(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改";
    });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}


/** 提交按钮 */
function submitForm() {
    console.log(obj.dialogForm);
    return
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

getList();
</script>
<style lang="scss" scoped></style>