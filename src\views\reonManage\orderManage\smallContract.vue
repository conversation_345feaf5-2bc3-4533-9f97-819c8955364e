<!-- 小合同管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="100px">
            <el-form-item label="合同编号:" prop="contractCode">
                <el-input v-model="obj.queryParams.contractCode" placeholder="请输入合同编号" clearable />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户编号:" prop="customerCode">
                <el-input v-model="obj.queryParams.customerCode" placeholder="请输入客户编号" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="View" @click="handleView">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Sort" @click="handleSetPriority">设置优先</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleExport">数据导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Setting" @click="handleWhiteList">白名单设置</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleView">
            <el-table-column type="selection" width="55" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="人数" align="center" prop="personCount" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同起始日期" align="center" prop="startDate" />
            <el-table-column label="合同终止日期" align="center" prop="endDate" />
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="修改人" align="center" prop="updateBy" />
            <el-table-column label="修改时间" align="center" prop="updateTime" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button text type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增 -->
        <el-dialog v-model="obj.dialogShow" title="上传导入数据" width="60%" append-to-body>
            <el-tabs v-model="obj.activeTab" type="border-card">
                <el-tab-pane label="基础信息">
                    <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline
                        label-width="auto">
                        <el-row>
                            <el-form-item label="合同编号:" prop="typeCode">
                                HT-20250108005932
                            </el-form-item>
                            <el-form-item label="合同名称:" prop="typeCode">
                                上海鼎捷数智软件有限公司全代理+代发工资大户+代理大户
                            </el-form-item>
                            <el-form-item label="合同类型:" prop="typeCode">
                                全代理
                            </el-form-item>
                            <el-form-item label="人数:" prop="typeCode">
                                1
                            </el-form-item>
                        </el-row>
                        <el-row>
                            <el-form-item label="客户编号:" prop="typeCode">
                                HT-20250108005932
                            </el-form-item>
                            <el-form-item label="客户名称:" prop="typeCode">
                                上海鼎捷数智软件有限公司全代理+代发工资大户+代理大户
                            </el-form-item>
                            <el-form-item label="合同起始日期:" prop="typeCode">
                                2025-01-08
                            </el-form-item>
                            <el-form-item label="合同终止日期:" prop="typeCode">
                                2025-01-08
                            </el-form-item>
                        </el-row>
                        <el-divider />
                        <el-row :gutter="10" class="mb8">
                            <el-col :span="1.5">
                                <el-button type="primary" plain icon="Plus" @click="handleImport">新增</el-button>
                            </el-col>
                            <el-col :span="1.5">
                                <el-button type="danger" plain icon="Delete" @click="handleImport">删除</el-button>
                            </el-col>
                            <el-col :span="1.5">
                                <el-button type="success" plain @click="handleImport">复制</el-button>
                            </el-col>
                        </el-row>
                        <el-table :data="obj.dialogForm.tableData" border @selection-change="handleSelectionChange">
                            <el-table-column type="selection" width="55" />
                            <el-table-column label="序号" type="index" align="center" width="60" />
                            <el-table-column label="小合同名称" align="center" width="120">
                                <template #default="scope">
                                    <el-input style="width: 100%;" v-model="scope.row.name" placeholder="请输入"
                                        clearable />
                                </template>
                            </el-table-column>
                            <el-table-column label="签单方" align="center" prop="id" />
                            <el-table-column label="派单方" align="center" prop="id" />
                            <el-table-column label="接单方" align="center" width="140">
                                <template #default="scope">
                                    <el-input style="width: 100%;" v-model="scope.row.name" placeholder="请输入"
                                        clearable />
                                </template>
                            </el-table-column>
                            <el-table-column label="城市" align="center" prop="id" />
                            <el-table-column label="客户账单模板" align="center" width="140">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="账单方" align="center" prop="id" />
                            <el-table-column label="收款方" align="center" prop="id" />
                            <el-table-column label="派单类型" align="center" width="140">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="派单方客服" align="center" width="100" prop="id" />
                            <el-table-column label="接单方客服" align="center" width="100" prop="id" />
                            <el-table-column label="小合同状态" align="center" width="140">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="特殊说明" align="center" prop="id" />
                            <el-table-column label="是否需要劳动合同" align="center" width="140">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="劳动合同版本" align="center" width="120">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="是否存档" align="center" width="120">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="是否外呼" align="center" width="120">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="派单人" align="center" prop="id" />
                            <el-table-column label="是否单立户" align="center" width="140">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="选中单立户" align="center" width="100" prop="id" />
                            <el-table-column label="是否优先" align="center" width="120">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="是否单工伤" align="center" width="120">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择"
                                        clearable>
                                        <el-option label="选项1" value="1" />
                                        <el-option label="选项2" value="2" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" align="center" width="160">
                                <template #default="scope">
                                    <el-button type="primary" text icon="Plus"
                                        @click="handleImport(scope.row)">新增</el-button>
                                    <el-button type="danger" text icon="Delete"
                                        @click="handleImport(scope.row)">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane :disabled="!obj.isEdit && !obj.isDetail" label="流程信息">
                    <ProcessInformation :tableData="obj.dialogForm.tableData" />
                </el-tab-pane>
            </el-tabs>
            <template #footer>
                <el-button @click="obj.dialogShow = false">取消</el-button>
                <el-button type="primary" @click="obj.dialogShow = false">保存</el-button>
            </template>
        </el-dialog>
        <!-- 白名单 -->
        <el-dialog v-model="obj.dialogShow2" title="白名单设置" width="60%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <el-form-item label="小合同名称" prop="smallContractName">
                    <el-input v-model="obj.dialogForm.smallContractName" placeholder="请输入小合同名称" clearable />
                </el-form-item>
                <el-form-item label="小合同编号" prop="smallContractCode">
                    <el-input v-model="obj.dialogForm.smallContractCode" placeholder="请输入小合同编号" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                    <el-button type="primary" icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" icon="Plus" plain @click="handleImport">新增</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="success" icon="Edit" plain @click="handleImport">修改</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="primary" icon="Download" plain @click="handleExport">导出</el-button>
                </el-col>
            </el-row>
            <el-table :data="obj.dialogForm.tableData" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="小合同编号" align="center" prop="smallContractCode" />
                <el-table-column label="小合同名称" align="center" prop="smallContractName" />
                <el-table-column label="开始时间" align="center" prop="startTime" />
                <el-table-column label="备注" align="center" prop="remark" />
                <el-table-column label="文件" align="center" prop="file" />
                <el-table-column label="创建人" align="center" prop="createBy" />
                <el-table-column label="创建时间" align="center" prop="createTime" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";
import ProcessInformation from '@/views/reonManage/components/processInformation.vue';
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    // 可以根据需要添加验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractCode: null,
        contractName: null,
        customerName: null,
        customerCode: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {
        tableData: []
    },//弹出框表单
    dialogShow: false,//弹出框
    dialogShow2: false,//弹出框
    ids: [],//选中的id
    title: "",//标题
    activeTab: '0',//当前选中的tab
    isEdit: false,//是否编辑
    isDetail: false,//是否详情
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                contractCode: 'HT20230001',
                contractName: '小合同1',
                contractType: '全代理',
                personCount: 10,
                customerCode: 'KH20230001',
                customerName: '上海鼎捷数智软件有限公司',
                startDate: '2023-01-01',
                endDate: '2023-12-31',
                createBy: '创建人1',
                createTime: '2023-01-01 10:00:00',
                updateBy: '修改人1',
                updateTime: '2023-01-01 11:00:00'
            },
            {
                id: 2,
                contractCode: 'HT20230002',
                contractName: '小合同2',
                contractType: '代发工资',
                personCount: 5,
                customerCode: 'KH20230002',
                customerName: '北京智能科技有限公司',
                startDate: '2023-02-01',
                endDate: '2023-12-31',
                createBy: '创建人2',
                createTime: '2023-02-01 10:00:00',
                updateBy: '修改人2',
                updateTime: '2023-02-01 11:00:00'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

// 表单重置
function reset() {
    obj.dialogForm = {
        tableData: []
    };
    obj.isEdit = false;
    obj.isDetail = false;
    obj.activeTab = '0';
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogForm.tableData.push({
        id: 1,
        name: ''
    });
    obj.dialogShow = true;
    obj.title = '新增合同';
}

/** 修改按钮操作 */
function handleEdit() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条数据进行修改');
        return;
    }
    reset();
    obj.isEdit = true;
    obj.dialogShow = true;
    obj.title = '修改合同';

    // 模拟数据，实际开发时可以从表格中获取
    const row = obj.tableData.find(item => item.id === obj.ids[0]);
    if (row) {
        obj.dialogForm = { ...row, tableData: [{ id: 1, name: '' }] };
    }
}

/** 删除按钮操作 */
function handleDelete() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要删除的数据');
        return;
    }
    proxy.$modal.confirm('确定要删除选中的数据吗？').then(function () {
        // 这里可以调用API进行删除操作
        proxy.$modal.msgSuccess('删除成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消删除');
    });
}

/** 查看按钮操作 */
function handleView(row) {
    reset();
    obj.isDetail = true;
    obj.dialogShow = true;
    obj.title = '查看合同';

    // 模拟数据，实际开发时可以从表格中获取
    if (row) {
        obj.dialogForm = { ...row, tableData: [{ id: 1, name: '' }] };
    } else if (obj.ids.length === 1) {
        const selectedRow = obj.tableData.find(item => item.id === obj.ids[0]);
        if (selectedRow) {
            obj.dialogForm = { ...selectedRow, tableData: [{ id: 1, name: '' }] };
        }
    } else {
        proxy.$modal.msgError('请选择一条数据进行查看');
        obj.dialogShow = false;
        return;
    }
}

/** 设置优先按钮操作 */
function handleSetPriority() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要设置优先的数据');
        return;
    }
    proxy.$modal.confirm('确定要设置选中的数据为优先吗？').then(function () {
        // 这里可以调用API进行设置优先操作
        proxy.$modal.msgSuccess('设置成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

/** 数据导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 白名单设置按钮操作 */
function handleWhiteList() {
    obj.dialogShow2 = true;
    obj.dialogForm = {
        smallContractName: '',
        smallContractCode: '',
        tableData: [
            {
                id: 1,
                smallContractCode: 'XHT20230001',
                smallContractName: '小合同白名单1',
                startTime: '2023-01-01',
                remark: '备注1',
                file: '文件1.pdf',
                createBy: '创建人1',
                createTime: '2023-01-01 10:00:00'
            },
            {
                id: 2,
                smallContractCode: 'XHT20230002',
                smallContractName: '小合同白名单2',
                startTime: '2023-02-01',
                remark: '备注2',
                file: '文件2.pdf',
                createBy: '创建人2',
                createTime: '2023-02-01 10:00:00'
            }
        ]
    };
}

/** 导入按钮操作 */
function handleImport() {
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>