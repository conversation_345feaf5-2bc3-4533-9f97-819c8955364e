<!-- 批量实做调整 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入编号:" prop="importNo">
                <el-input class="width220" v-model="obj.queryParams.importNo" placeholder="请输入导入编号" />
            </el-form-item>
            <el-form-item label="姓名:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="请输入姓名" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleDownload">下载模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 表格 -->
        <ExportTable :tableData="obj.tableData" :loading="obj.loading" :total="obj.total"
            :page="obj.queryParams.pageNum" :limit="obj.queryParams.pageSize" @row-dblclick="handleRowDblClick"
            @handlePagination="handlePagination" />

        <!-- 导入组件 -->
        <UploadFile v-model:dialogShow="obj.importDialogShow" title="导入页面" :dialogForm="obj.dialogForm"
            :type="'batchActualAdjustment'" :rules="importRules" />
        <!-- 历史信息查看 -->
        <HistoricalInformation :dialogShow="obj.dialogShow2" :title="obj.title" :tableData="obj.tableData"
            @close="handleHistoryClose" />
    </div>
</template>


<script setup name="BatchImplementAdjustments">
import { listScale } from "@/api/reonApi/scale";
import HistoricalInformation from '../components/dialog/historicalInformation.vue';
import ExportTable from '@/views/reonManage/components/table/exportTable.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 导入表单验证规则
const importRules = {
    sales: [
        { required: true, message: '请输入备注', trigger: 'blur' }
    ],
    file: [
        { required: true, message: '请选择要上传的文件', trigger: 'change' }
    ]
};

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        importNo: 'IMP20230001',
        creator: '张三',
        createTime: '2023-01-15 10:30:00',
        successCount: 120,
        failCount: 0,
        importFile: '批量调整_202301.xlsx',
        processStatus: '1',
        name: '李四'
    },
    {
        id: 2,
        importNo: 'IMP20230002',
        creator: '李四',
        createTime: '2023-02-15 09:15:00',
        successCount: 85,
        failCount: 3,
        importFile: '批量调整_202302.xlsx',
        processStatus: '1',
        name: '王五'
    },
    {
        id: 3,
        importNo: 'IMP20230003',
        creator: '王五',
        createTime: '2023-03-15 11:45:00',
        successCount: 0,
        failCount: 0,
        importFile: '批量调整_202303.xlsx',
        processStatus: '0',
        name: '赵六'
    }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importNo: null, // 导入编号
        name: null, // 姓名
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        remark: '', // 备注
        sales: '', // 备注（组件内部使用）
        file: null // 文件
    }, // 导入表单
    importDialogShow: false, // 导入对话框
    dialogShow2: false, // 历史信息查看对话框
    ids: [], // 选中的id
    title: "", // 标题
});


/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 显示导入对话框
function handleImport() {
    obj.importDialogShow = true;
    resetForm();
}

// 关闭导入对话框
function handleImportClose() {
    obj.importDialogShow = false;
    resetForm();
    getList(); // 刷新列表
}

// 下载模版
function handleDownload() {
    // 实际项目中应该调用API进行下载模板
    proxy.$modal.msgSuccess('模板下载成功');

    // 模拟下载文件
    const fileName = '批量实做调整模板.xlsx';
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '/template/batch-actual-adjustment-template.xlsx';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 重置表单
function resetForm() {
    obj.dialogForm = {
        remark: '',
        sales: '', // 备注（组件内部使用）
        file: null // 文件对象（组件内部使用）
    };
}

// 双击行
function handleRowDblClick(row) {
    obj.dialogShow2 = true;
    obj.title = '历史信息查看';
}

// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}

// 关闭历史信息查看对话框
function handleHistoryClose() {
    obj.dialogShow2 = false;
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.width420 {
    width: 420px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>