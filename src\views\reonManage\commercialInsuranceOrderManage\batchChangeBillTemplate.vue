<!-- 批量变更帐单模板 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateFrom" type="date"
                    placeholder="请选择开始日期" value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateTo" type="date"
                    placeholder="请选择结束日期" value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="导入人:" prop="importer">
                <el-select class="width220" v-model="obj.queryParams.importer" placeholder="请选择导入人" clearable>
                    <el-option v-for="item in importerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入编号:" prop="importId">
                <el-select class="width220" v-model="obj.queryParams.importId" placeholder="请选择导入编号" clearable>
                    <el-option v-for="item in importIdOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="warning" plain icon='Upload' @click="handleExport">导入</el-button>
                <el-button type="primary" plain icon="Download" @click="handleDownloadTemplate">下载模版</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <ExportTable2 :tableData="obj.tableData" :loading="obj.loading" :total="obj.total"
            :page="obj.queryParams.pageNum" :limit="obj.queryParams.pageSize" menuName="batchChangeBillingTemplate"
            @row-dblclick="handleRowDblClick" @handlePagination="handlePagination" />
        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" type="batchChangeBillingTemplate" title="上传导入数据"
            :dialogForm="obj.dialogForm" @close="handleClose" />
        <!-- 历史信息查看 -->
        <HistoricalInformation :dialogShow="obj.dialogShow2" :title="obj.title" :tableData="obj.tableData"
            @close="handleHistoryClose" />
    </div>
</template>

<script setup name="BatchChangeBilling_template">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 导入人选项
const importerOptions = [
    { value: '1', label: '管理员' },
    { value: '2', label: '张三' },
    { value: '3', label: '李四' },
    { value: '4', label: '王五' },
    { value: '5', label: '赵六' }
];

// 导入编号选项
const importIdOptions = [
    { value: 'IMP20230501001', label: 'IMP20230501001' },
    { value: 'IMP20230502001', label: 'IMP20230502001' },
    { value: 'IMP20230503001', label: 'IMP20230503001' },
    { value: 'IMP20230504001', label: 'IMP20230504001' },
    { value: 'IMP20230505001', label: 'IMP20230505001' }
];

// 响应式数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateFrom: '', // 导入日期从
        importDateTo: '', // 导入日期到
        importer: '', // 导入人
        importId: '' // 导入编号
    }, // 查询表单
    total: 0, // 总条数

    tableData: [
        {
            id: 1,
            importId: 'IMP20230501001',
            importer: '管理员',
            importTime: '2023-05-01 09:30:00',
            remark: '批量变更账单模板测试数据1',
            successCount: 95,
            failCount: 5,
            importFile: '变更账单模板数据1.xlsx',
            processStatus: '3',
            creator: '管理员',
            createTime: '2023-05-01 09:30:00',
            modifier: '管理员',
            modifyTime: '2023-05-01 10:30:00'
        },
        {
            id: 2,
            importId: 'IMP20230502001',
            importer: '张三',
            importTime: '2023-05-02 10:15:00',
            remark: '批量变更账单模板测试数据2',
            successCount: 80,
            failCount: 0,
            importFile: '变更账单模板数据2.xlsx',
            processStatus: '3',
            creator: '张三',
            createTime: '2023-05-02 10:15:00',
            modifier: '张三',
            modifyTime: '2023-05-02 11:20:00'
        }
    ], // 列表
    dialogShow: false, // 导入弹窗
    dialogForm: {
        remark: '',
        fileList: []
    }, // 导入表单
    ids: [], // 选中的id
    title: "", // 标题
})



/** 列表 */
function getList() {
    obj.loading = true;
    // api.getList().then(res => {
    //     obj.tableData = res.data.list;
    //     obj.total = res.data.total;
    //     obj.loading = false;
    // })
    // 模拟数据已经定义在 obj.tableData 中
    setTimeout(() => {
        obj.loading = false;
        obj.total = obj.tableData.length;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

// 双击行
function handleRowDblClick(row) {
    obj.dialogShow2 = true;
    obj.title = '历史信息查看';
}

// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}
// 数据导入
function handleExport() {
    obj.dialogShow = true;
}

// 关闭导入
function handleClose() {
    obj.dialogShow = false;
}

// 下载模版
function handleDownloadTemplate() {
    // 显示加载中提示
    const loading = proxy.$message({
        message: '正在准备下载，请稍候...',
        type: 'info',
        duration: 0
    });

    // 模拟下载过程
    setTimeout(() => {
        // 关闭加载中提示
        loading.close();

        // 显示成功提示
        proxy.$modal.msgSuccess('模板下载成功');
    }, 1000);
}
/** 历史信息关闭 */
function handleHistoryClose() {
    obj.dialogShow2 = false;
}

// 初始化
getList();
</script>
<style lang="scss" scoped></style>