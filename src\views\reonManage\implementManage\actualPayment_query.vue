<!-- 实做支付查询页 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable>
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="审批状态:" prop="approvalStatus">
                <el-select class="width220" v-model="obj.queryParams.approvalStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in approvalStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请支付时间起:" prop="applyTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeStart" type="datetime"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="申请支付时间止:" prop="applyTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeEnd" type="datetime"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="支付所属年月:" prop="paymentMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentMonth" type="month" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="审批通过时间>=:" prop="approvalTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.approvalTimeStart" type="datetime"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="审批通过时间<=:" prop="approvalTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.approvalTimeEnd" type="datetime"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="支付方式:" prop="paymentMethod">
                <el-select class="width220" v-model="obj.queryParams.paymentMethod" placeholder="请选择" clearable>
                    <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="addPaymentBtn">新增支付申请</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="editPaymentBtn">编辑支付申请</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain @click="stopPaymentBtn">终止</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="addWhiteListBtn">添加支付白名单</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="审批状态" align="center" prop="approvalStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.approvalStatus === '3' ? 'success' : (scope.row.approvalStatus === '4' ? 'danger' : (scope.row.approvalStatus === '2' ? 'warning' : 'info'))">
                        {{ scope.row.approvalStatus === '3' ? '已审批' : (scope.row.approvalStatus === '4' ? '已驳回' :
                            (scope.row.approvalStatus === '2' ? '审批中' : '待审批')) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="支付类型" align="center" prop="paymentType">
                <template #default="scope">
                    <el-tag type="info">
                        {{ scope.row.paymentType === '1' ? '社保' : (scope.row.paymentType === '2' ? '公积金' : '服务费') }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="支付方式" align="center" prop="paymentMethod">
                <template #default="scope">
                    <el-tag type="info">
                        {{ scope.row.paymentMethod === '1' ? '银行转账' : (scope.row.paymentMethod === '2' ? '现金支付' : '支票')
                        }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="收款方" align="center" prop="payee" />
            <el-table-column label="收款银行" align="center" prop="bank" />
            <el-table-column label="银行账号/支票号" align="center" prop="accountNo" />
            <el-table-column label="开户银行" align="center" prop="bankBranch" />
            <el-table-column label="应付金额" align="center" prop="payableAmount" />
            <el-table-column label="申请金额" align="center" prop="requestAmount" />
            <el-table-column label="支付所属年月" align="center" prop="paymentMonth" />
            <el-table-column label="最晚支付时间" align="center" prop="latestPaymentDate" />
            <el-table-column label="申请人" align="center" prop="applicant" />
            <el-table-column label="申请时间" align="center" prop="applyTime" />
            <el-table-column label="支付地" align="center" prop="paymentPlace" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="审批通过时间" align="center" prop="approvalTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 查看个人订单 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="60%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" inline label-width="auto">
                <el-form-item label="福利办理方" prop="welfareHandler">
                    <el-select class="width220" v-model="obj.dialogForm.welfareHandler" placeholder="请选择" clearable>
                        <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="福利包名称" prop="welfarePackageName">
                    <el-select class="width220" v-model="obj.dialogForm.welfarePackageName" placeholder="请选择" clearable>
                        <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="报表年月" prop="reportMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.reportMonth" type="month" placeholder="请选择"
                        clearable />
                </el-form-item>
                <el-form-item label="支付详细类型" prop="paymentDetailType">
                    <el-select class="width220" v-model="obj.dialogForm.paymentDetailType" placeholder="请选择" clearable>
                        <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="addPaymentBtn">选择支付费用</el-button>
                    <el-button type="primary" @click="addPaymentBtn">重新选中金额</el-button>
                </el-form-item>
                <el-form-item label="支付方式" prop="paymentMethod">
                    <el-select class="width220" v-model="obj.dialogForm.paymentMethod" placeholder="请选择" clearable>
                        <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="详细支付方式" prop="detailPaymentMethod">
                    <el-input class="width220" v-model="obj.dialogForm.detailPaymentMethod" placeholder="请输入"
                        clearable />
                </el-form-item>
                <el-form-item label="收款方" prop="payee">
                    <el-input class="width220" v-model="obj.dialogForm.payee" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="收款银行" prop="bank">
                    <el-input class="width220" v-model="obj.dialogForm.bank" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="收款银行开户行" prop="bankBranch">
                    <el-input class="width220" v-model="obj.dialogForm.bankBranch" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="银行账号/支票号" prop="accountNo">
                    <el-input class="width220" v-model="obj.dialogForm.accountNo" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="单据数量" prop="documentCount">
                    <el-input class="width220" v-model="obj.dialogForm.documentCount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="申请支付总额" prop="requestAmount">
                    <el-input class="width220" v-model="obj.dialogForm.requestAmount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="应付总额" prop="payableAmount">
                    <el-input class="width220" v-model="obj.dialogForm.payableAmount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="最晚支付日期" prop="latestPaymentDate">
                    <el-date-picker class="width220" v-model="obj.dialogForm.latestPaymentDate" type="date"
                        placeholder="请选择" clearable />
                </el-form-item>
                <el-form-item label="支付所属年月" prop="paymentMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.paymentMonth" type="month"
                        placeholder="请选择" clearable />
                </el-form-item>
                <el-form-item label="支付地" prop="paymentPlace">
                    <el-input class="width220" v-model="obj.dialogForm.paymentPlace" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="付款类型" prop="paymentType">
                    <el-select class="width220" v-model="obj.dialogForm.paymentType" placeholder="请选择" clearable>
                        <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="差异金额" prop="differenceAmount">
                    <el-input class="width220" v-model="obj.dialogForm.differenceAmount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="历史差异" prop="historicalDifference">
                    <el-input class="width220" v-model="obj.dialogForm.historicalDifference" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="付款用途" prop="paymentPurpose">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.paymentPurpose" :rows="3"
                        placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.remark" :rows="3"
                        placeholder="请输入" clearable />
                </el-form-item>
                <border-box title="文件上传">
                    <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
                </border-box>
            </el-form>
            <template #footer>
                <el-button @click="cancelWhiteList">取消</el-button>
                <el-button type="primary" @click="saveWhiteList">保存</el-button>
            </template>
        </el-dialog>

        <!-- 添加白名单 -->
        <el-dialog v-model="obj.dialogShow3" :title="obj.title" width="50%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" inline label-width="auto">
                <el-form-item label="福利办理方" prop="welfareHandler">
                    <el-select class="width220" v-model="obj.whiteListForm.welfareHandler" placeholder="请选择" clearable>
                        <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="福利包名称" prop="welfarePackageName">
                    <el-select class="width220" v-model="obj.whiteListForm.welfarePackageName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户" prop="customer">
                    <el-input class="width220" readonly v-model="obj.whiteListForm.customer" placeholder="请输入"
                        clearable />
                </el-form-item>
                <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
            </el-form>
            <el-table :data="obj.whiteListForm.formTable" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="客户Id" prop="customerId" align="center" />
                <el-table-column label="客户名称" prop="customerName" align="center" />
                <el-table-column label="福利办理方" prop="welfareHandler" align="center" />
                <el-table-column label="类型" prop="type" align="center" />
                <el-table-column label="创建人" prop="creator" align="center" />
                <el-table-column label="创建时间" prop="createTime" align="center" />
            </el-table>
            <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
            <template #footer>
                <el-button @click="cancelForm">取消</el-button>
                <el-button type="primary" @click="saveForm">保存</el-button>
            </template>
        </el-dialog>

        <!-- 详情 -->
        <PaymentApplication v-model:dialogShow="obj.detailShow" title="查看支付管理" :form="obj.detailForm"
            menuName="actualPayment_query" :disabled="true" />
    </div>
</template>

<script setup name="ActualPayment_query">
import { listScale } from "@/api/reonApi/scale";
import PaymentApplication from '@/views/reonManage/components/dialog/paymentApplication.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 审批状态选项
const approvalStatusOptions = [
    { value: '1', label: '待审批' },
    { value: '2', label: '审批中' },
    { value: '3', label: '已审批' },
    { value: '4', label: '已驳回' }
];

// 支付方式选项
const paymentMethodOptions = [
    { value: '1', label: '银行转账' },
    { value: '2', label: '现金支付' },
    { value: '3', label: '支票' }
];

// 支付类型选项
const paymentTypeOptions = [
    { value: '1', label: '社保' },
    { value: '2', label: '公积金' },
    { value: '3', label: '服务费' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        approvalStatus: '3',
        welfarePackageName: '标准福利包',
        paymentType: '1',
        paymentMethod: '1',
        payee: '公司A',
        bank: '中国银行',
        accountNo: '****************',
        bankBranch: '北京分行',
        payableAmount: 100000.00,
        requestAmount: 100000.00,
        paymentMonth: '2023-05',
        latestPaymentDate: '2023-05-15',
        applicant: '张三',
        applyTime: '2023-05-01 10:30:00',
        paymentPlace: '北京',
        welfareHandler: '公司A',
        approvalTime: '2023-05-03 15:20:00'
    },
    {
        id: 2,
        approvalStatus: '2',
        welfarePackageName: '高级福利包',
        paymentType: '2',
        paymentMethod: '2',
        payee: '公司B',
        bank: '工商银行',
        accountNo: '****************',
        bankBranch: '上海分行',
        payableAmount: 150000.00,
        requestAmount: 150000.00,
        paymentMonth: '2023-06',
        latestPaymentDate: '2023-06-15',
        applicant: '李四',
        applyTime: '2023-06-01 09:15:00',
        paymentPlace: '上海',
        welfareHandler: '公司B',
        approvalTime: ''
    },
    {
        id: 3,
        approvalStatus: '1',
        welfarePackageName: '基础福利包',
        paymentType: '3',
        paymentMethod: '3',
        payee: '公司C',
        bank: '建设银行',
        accountNo: '****************',
        bankBranch: '广州分行',
        payableAmount: 80000.00,
        requestAmount: 80000.00,
        paymentMonth: '2023-07',
        latestPaymentDate: '2023-07-15',
        applicant: '王五',
        applyTime: '2023-07-01 11:45:00',
        paymentPlace: '广州',
        welfareHandler: '公司C',
        approvalTime: ''
    }
];

// 模拟白名单数据
const mockWhiteListData = [
    {
        id: 1,
        customerId: 'CUS001',
        customerName: '客户A',
        welfareHandler: '公司A',
        type: '社保',
        creator: '张三',
        createTime: '2023-05-01 10:30:00'
    },
    {
        id: 2,
        customerId: 'CUS002',
        customerName: '客户B',
        welfareHandler: '公司B',
        type: '公积金',
        creator: '李四',
        createTime: '2023-06-01 09:15:00'
    }
];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        welfarePackageName: null, // 福利包名称
        approvalStatus: null, // 审批状态
        applyTimeStart: null, // 申请支付时间起
        applyTimeEnd: null, // 申请支付时间止
        paymentMonth: null, // 支付所属年月
        approvalTimeStart: null, // 审批通过时间>=
        approvalTimeEnd: null, // 审批通过时间<=
        paymentMethod: null, // 支付方式
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        welfareHandler: '', // 福利办理方
        welfarePackageName: '', // 福利包名称
        reportMonth: '', // 报表年月
        paymentDetailType: '', // 支付详细类型
        paymentMethod: '', // 支付方式
        detailPaymentMethod: '', // 详细支付方式
        payee: '', // 收款方
        bank: '', // 收款银行
        bankBranch: '', // 收款银行开户行
        accountNo: '', // 银行账号/支票号
        documentCount: 0, // 单据数量
        requestAmount: 0, // 申请支付总额
        payableAmount: 0, // 应付总额
        latestPaymentDate: '', // 最晚支付日期
        paymentMonth: '', // 支付所属年月
        paymentPlace: '', // 支付地
        paymentType: '', // 付款类型
        differenceAmount: 0, // 差异金额
        historicalDifference: 0, // 历史差异
        paymentPurpose: '', // 付款用途
        remark: '', // 备注
        files: [] // 上传文件
    }, // 表单
    dialogShow: false, // 显示对话框
    dialogShow3: false, // 显示白名单对话框
    title: '', // 对话框标题
    ids: [], // 选中的id
    whiteListForm: {
        welfareHandler: '', // 福利办理方
        welfarePackageName: '', // 福利包名称
        customer: '', // 客户
        files: [], // 上传文件
        formTable: mockWhiteListData // 白名单表格数据
    } // 白名单表单
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function resetForm() {
    obj.dialogForm = {
        welfareHandler: '', // 福利办理方
        welfarePackageName: '', // 福利包名称
        reportMonth: '', // 报表年月
        paymentDetailType: '', // 支付详细类型
        paymentMethod: '', // 支付方式
        detailPaymentMethod: '', // 详细支付方式
        payee: '', // 收款方
        bank: '', // 收款银行
        bankBranch: '', // 收款银行开户行
        accountNo: '', // 银行账号/支票号
        documentCount: 0, // 单据数量
        requestAmount: 0, // 申请支付总额
        payableAmount: 0, // 应付总额
        latestPaymentDate: '', // 最晚支付日期
        paymentMonth: '', // 支付所属年月
        paymentPlace: '', // 支付地
        paymentType: '', // 付款类型
        differenceAmount: 0, // 差异金额
        historicalDifference: 0, // 历史差异
        paymentPurpose: '', // 付款用途
        remark: '', // 备注
        files: [] // 上传文件
    };
    proxy.resetForm("formRef");
}

// 重置白名单表单
function resetWhiteListForm() {
    obj.whiteListForm = {
        welfareHandler: '', // 福利办理方
        welfarePackageName: '', // 福利包名称
        customer: '', // 客户
        files: [], // 上传文件
        formTable: mockWhiteListData // 白名单表格数据
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

// 新增支付申请
function addPaymentBtn() {
    obj.dialogShow = true;
    obj.title = '新增支付申请';
    resetForm();
}

// 编辑支付申请
function editPaymentBtn() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行编辑');
        return;
    }

    // 获取选中的记录
    const selectedRow = obj.tableData.find(item => item.id === obj.ids[0]);
    if (!selectedRow) {
        proxy.$modal.msgError('未找到选中的记录');
        return;
    }

    // 如果已审批或已驳回，不允许编辑
    if (selectedRow.approvalStatus === '3' || selectedRow.approvalStatus === '4') {
        proxy.$modal.msgError('已审批或已驳回的记录不能编辑');
        return;
    }

    obj.dialogShow = true;
    obj.title = '编辑支付申请';

    // 填充表单数据
    obj.dialogForm = {
        welfareHandler: selectedRow.welfareHandler,
        welfarePackageName: selectedRow.welfarePackageName,
        reportMonth: selectedRow.paymentMonth, // 使用支付所属年月作为报表年月
        paymentDetailType: selectedRow.paymentType,
        paymentMethod: selectedRow.paymentMethod,
        detailPaymentMethod: '',
        payee: selectedRow.payee,
        bank: selectedRow.bank,
        bankBranch: selectedRow.bankBranch,
        accountNo: selectedRow.accountNo,
        documentCount: 1,
        requestAmount: selectedRow.requestAmount,
        payableAmount: selectedRow.payableAmount,
        latestPaymentDate: selectedRow.latestPaymentDate,
        paymentMonth: selectedRow.paymentMonth,
        paymentPlace: selectedRow.paymentPlace,
        paymentType: selectedRow.paymentType,
        differenceAmount: 0,
        historicalDifference: 0,
        paymentPurpose: '',
        remark: '',
        files: []
    };
}

// 终止
function stopPaymentBtn() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要终止的记录');
        return;
    }

    proxy.$modal.confirm('确定要终止选中的记录吗？').then(() => {
        // 实际项目中应该调用API进行终止操作
        proxy.$modal.msgSuccess('终止成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

// 添加白名单
function addWhiteListBtn() {
    obj.dialogShow3 = true;
    obj.title = '实做申请白名单';
    resetWhiteListForm();
}

// 详情
function handleDetail(row) {
    obj.detailShow = true;
    obj.detailForm = row;
}

// 取消表单
function cancelForm() {
    obj.dialogShow = false;
    resetForm();
}

// 保存表单
function saveForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            // 实际项目中应该调用API进行提交
            proxy.$modal.msgSuccess('保存成功');
            obj.dialogShow = false;
            resetForm();
            getList(); // 刷新列表
        }
    });
}

// 取消白名单
function cancelWhiteList() {
    obj.dialogShow3 = false;
    resetWhiteListForm();
}

// 保存白名单
function saveWhiteList() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            // 实际项目中应该调用API进行提交
            proxy.$modal.msgSuccess('保存成功');
            obj.dialogShow3 = false;
            resetWhiteListForm();
            getList(); // 刷新列表
        }
    });
}

// 初始化列表
getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>