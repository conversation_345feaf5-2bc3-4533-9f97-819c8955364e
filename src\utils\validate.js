/**
 * 判断给定的URL是否为HTTP或HTTPS协议
 * @param {string} url - 需要检查的URL字符串
 * @returns {Boolean} - 如果URL是HTTP或HTTPS协议，则返回true，否则返回false
 */
export function isHttp(url) {
  // 检查URL中是否包含'http://'或'https://'
  return url.indexOf("http://") !== -1 || url.indexOf("https://") !== -1;
}

/**
 * 判断给定的路径是否为外部链接
 * @param {string} path - 需要检查的路径字符串
 * @returns {Boolean} - 如果路径是外部链接，则返回true，否则返回false
 */
export function isExternal(path) {
  // 使用正则表达式检查路径是否以'http://'、'https://'、'mailto:'或'tel:'开头
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * 验证用户名是否有效
 * @param {string} str - 需要验证的用户名
 * @returns {boolean} - 如果用户名有效则返回true，否则返回false
 */
export function validUsername(str) {
  // 定义一个包含有效用户名的数组
  const valid_map = ["admin", "editor"];
  // 去除用户名前后的空格，并检查是否在有效用户名数组中
  return valid_map.indexOf(str.trim()) >= 0;
}

/**
 * 验证URL的有效性
 * @param {string} url - 需要验证的URL字符串
 * @returns {boolean} - 如果URL有效则返回true，否则返回false
 */
export function validURL(url) {
  // 定义一个正则表达式，用于匹配有效的URL格式
  const reg =
    /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
  // 使用正则表达式测试传入的URL字符串，返回测试结果
  return reg.test(url);
}

/**
 * 验证字符串是否只包含小写字母
 * @param {string} str - 需要验证的字符串
 * @returns {boolean} - 如果字符串只包含小写字母则返回true，否则返回false
 */
export function validLowerCase(str) {
  // 定义一个正则表达式，用于匹配只包含小写字母的字符串
  const reg = /^[a-z]+$/;
  // 使用正则表达式测试传入的字符串，返回测试结果
  return reg.test(str);
}

/**
 * 验证字符串是否只包含大写字母
 * @param {string} str - 需要验证的字符串
 * @returns {boolean} - 如果字符串只包含大写字母则返回true，否则返回false
 */
export function validUpperCase(str) {
  // 定义一个正则表达式，用于匹配只包含大写字母的字符串
  const reg = /^[A-Z]+$/;
  // 使用正则表达式测试传入的字符串，返回测试结果
  return reg.test(str);
}

/**
 * 验证字符串是否只包含字母（大写或小写）
 * @param {string} str - 需要验证的字符串
 * @returns {boolean} - 如果字符串只包含字母则返回true，否则返回false
 */
export function validAlphabets(str) {
  // 定义一个正则表达式，用于匹配只包含字母的字符串
  const reg = /^[A-Za-z]+$/;
  // 使用正则表达式测试传入的字符串，返回测试结果
  return reg.test(str);
}

/**
 * 验证电子邮件地址的有效性
 * @param {string} email - 需要验证的电子邮件地址
 * @returns {boolean} - 如果电子邮件地址有效则返回true，否则返回false
 */
export function validEmail(email) {
  // 定义一个正则表达式，用于匹配有效的电子邮件地址格式
  const reg =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  // 使用正则表达式测试传入的电子邮件地址字符串，返回测试结果
  return reg.test(email);
}

/**
 * 验证一个变量是否为字符串类型
 * @param {*} str - 需要验证的变量
 * @returns {boolean} - 如果变量是字符串类型则返回true，否则返回false
 */
export function isString(str) {
  // 检查变量的类型是否为字符串或者是否是String对象的实例
  if (typeof str === "string" || str instanceof String) {
    // 如果是字符串类型，则返回true
    return true;
  }
  // 如果不是字符串类型，则返回false
  return false;
}

/**
 * 检查给定的参数是否为数组
 *
 * @param {*} arg - 需要检查的参数
 * @returns {Boolean} - 如果参数是数组则返回true，否则返回false
 */
export function isArray(arg) {
  // 检查当前环境是否支持 Array.isArray 方法
  if (typeof Array.isArray === "undefined") {
    // 如果不支持，则使用 Object.prototype.toString 方法来检查参数是否为数组
    return Object.prototype.toString.call(arg) === "[object Array]";
  }
  // 如果支持 Array.isArray 方法，则直接使用该方法来检查参数是否为数组
  return Array.isArray(arg);
}
