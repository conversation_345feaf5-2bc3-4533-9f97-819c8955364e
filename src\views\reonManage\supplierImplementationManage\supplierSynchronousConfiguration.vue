<!-- 供应商同步配置 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-input class="width220" v-model="obj.queryParams.supplierName" placeholder="请输入供应商名称" clearable />
            </el-form-item>
            <el-form-item label="参保城市:" prop="insuredCity">
                <el-select class="width220" v-model="obj.queryParams.insuredCity" placeholder="请选择参保城市" clearable>
                    <el-option v-for="item in provinces" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="产品类型:" prop="productType">
                <el-select class="width220" v-model="obj.queryParams.productType" placeholder="请选择产品类型" clearable>
                    <el-option v-for="item in product_type" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mt20 mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon='Plus' @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon='Edit' @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon='Delete' @click="handleDelete">删除</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table ref="refTable" v-loading="obj.loading" border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商" align="center" prop="supplierName" />
            <el-table-column label="参保城市" align="center" prop="insuredCity" />
            <el-table-column label="产品类型" align="center" prop="productType" />
            <el-table-column label="配置类型" align="center" prop="configType" />
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增/修改 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="45%">
            <el-form :model="obj.dialogForm" ref="dialogRef" inline label-width="auto">
                <el-form-item label="供应商" prop="supplierName">
                    <el-select class="width220" v-model="obj.dialogForm.supplierName" placeholder="请选择供应商">
                        <el-option v-for="item in supplierOptions" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="参保城市" prop="insuredCity">
                    <el-select class="width220" v-model="obj.dialogForm.insuredCity" placeholder="请选择参保城市">
                        <el-option v-for="item in provinces" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="产品类型" prop="productType">
                    <el-select class="width220" v-model="obj.dialogForm.productType" placeholder="请选择产品类型">
                        <el-option v-for="item in product_type" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="配置类型" prop="configType">
                    <el-select class="width220" v-model="obj.dialogForm.configType" placeholder="请选择配置类型">
                        <el-option v-for="item in configTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSubmit">确定</el-button>
                <el-button @click="closeDialog">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup name="SupplierSynchronousConfiguration">

import { useAreaStore } from '@/store/modules/area'

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { product_type } = proxy.useDict('product_type')

const provinces = areaStore.provinces // 获取省份数据

// 配置类型选项
const configTypeOptions = [
    { value: '1', label: '同步基数' },
    { value: '2', label: '同步比例' },
    { value: '3', label: '不需同步' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        insuredCity: null,
        productType: null,
    },//查询表单
    total: 10,//总条数
    tableData: [],//列表
    dialogForm: {
        supplierName: null,
        insuredCity: null,
        productType: null,
        configType: null
    },//配置表单
    dialogShow: false,//配置弹窗
    ids: [],//选中的id
    title: "",//标题
    isEdit: false //是否编辑模式
})


/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                supplierName: '供应商A',
                insuredCity: '北京',
                productType: '社保',
                configType: '实时同步',
                creator: '张三',
                createTime: '2023-05-15 10:30:00'
            },
            {
                id: 2,
                supplierName: '供应商B',
                insuredCity: '上海',
                productType: '公积金',
                configType: '定时同步',
                creator: '李四',
                createTime: '2023-05-16 14:20:00'
            },
            {
                id: 3,
                supplierName: '供应商C',
                insuredCity: '广州',
                productType: '医保',
                configType: '手动同步',
                creator: '王五',
                createTime: '2023-06-10 09:15:00'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 新增
function handleAdd() {
    obj.isEdit = false;
    obj.dialogForm = {
        supplierName: null,
        insuredCity: null,
        productType: null,
        configType: null
    };
    obj.dialogShow = true;
    obj.title = "新增同步配置";
}

// 修改
function handleUpdate() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要修改的数据");
        return;
    }

    obj.isEdit = true;
    // 获取选中的数据
    const selectedData = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedData) {
        // 将选中数据转换为表单数据
        obj.dialogForm = {
            supplierName: selectedData.supplierName,
            insuredCity: selectedData.insuredCity,
            productType: selectedData.productType,
            configType: selectedData.configType
        };
        obj.dialogShow = true;
        obj.title = "修改同步配置";
    }
}

// 关闭弹窗
function closeDialog() {
    obj.dialogShow = false;
    obj.dialogForm = {
        supplierName: null,
        insuredCity: null,
        productType: null,
        configType: null
    };
}

// 提交表单
function handleSubmit() {
    if (!obj.dialogForm.supplierName) {
        proxy.$modal.msgError("请输入供应商名称");
        return;
    }
    if (!obj.dialogForm.insuredCity) {
        proxy.$modal.msgError("请选择参保城市");
        return;
    }
    if (!obj.dialogForm.productType) {
        proxy.$modal.msgError("请选择产品类型");
        return;
    }
    if (!obj.dialogForm.configType) {
        proxy.$modal.msgError("请选择配置类型");
        return;
    }

    proxy.$modal.msgSuccess(obj.isEdit ? "修改成功" : "新增成功");
    closeDialog();
    getList();
}

// 删除
function handleDelete() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要删除的数据");
        return;
    }

    proxy.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        proxy.$modal.msgSuccess("删除成功!");
        getList();
    }).catch(() => { });
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

getList();
</script>
<style lang="scss" scoped></style>