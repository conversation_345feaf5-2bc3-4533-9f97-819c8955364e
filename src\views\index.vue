<template>
  <div class="app-container home">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-content">
        <el-icon class="header-icon">
          <Platform />
        </el-icon>
        <h1 class="header-title">REON客户管理系统</h1>
      </div>
    </div>

    <!-- 登录信息区域 -->
    <el-row :gutter="30" class="main-content">
      <!-- 登录信息基本信息 -->
      <el-col :span="24">
        <el-card class="system-info-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon class="card-icon">
                  <Monitor />
                </el-icon>
                <span class="card-title">登录信息</span>
              </div>
              <div class="header-right">
                <span class='corred fw600' style="font-size: 24px;">待办事项总数量:</span>
                <span style="color: #007BFF;font-size: 24px;">10</span>
              </div>


            </div>
          </template>

          <div class="system-info-content">
            <div class="info-table">
              <div class="info-row">
                <div class="info-label">当前账号</div>
                <div class="info-value">admin</div>
              </div>
              <div class="info-row">
                <div class="info-label">用户名</div>
                <div class="info-value">admin</div>
              </div>
              <div class="info-row">
                <div class="info-label">最近登录时间</div>
                <div class="info-value">2025-04-17 13:18:47</div>
              </div>
              <div class="info-row">
                <div class="info-label">操作系统</div>
                <div class="info-value">other</div>
              </div>
              <div class="info-row">
                <div class="info-label">浏览器名称</div>
                <div class="info-value">Chrome/137.0.0.0</div>
              </div>
              <div class="info-row">
                <div class="info-label">浏览器信息</div>
                <div class="info-value">5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</div>
              </div>
              <div class="info-row">
                <div class="info-label">屏幕分辨率</div>
                <div class="info-value">2560*1440</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格区域 -->
    <el-row :gutter="30" class="bottom-content">
      <!-- 政策文件 -->
      <el-col :xs="24" :lg="12">
        <el-card class="data-table-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon class="card-icon">
                  <Document />
                </el-icon>
                <span class="card-title">政策文件</span>
              </div>
              <div class="header-right">
                <el-button type="primary" @click="handleShowAll">
                  显示全部
                </el-button>
                <el-button type="info" @click="handleTextLibrary">
                  文本库
                </el-button>
              </div>
            </div>
          </template>

          <div class="table-content">
            <el-table :data="fileTableData" size="small">
              <el-table-column prop="name" label="文件名称" align="center" width="150">
                <template #default="scope">
                  <el-link type="primary">{{ scope.row.name }}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="path" label="文件路径" align="center" min-width="140" show-overflow-tooltip />
              <el-table-column prop="time" label="上传时间" align="center" width="160" />
              <el-table-column prop="operation" label="操作" align="center" width="100">
                <template #default>
                  <el-button type="primary" size="small" text>下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>

      <!-- 临时政策 -->
      <el-col :xs="24" :lg="12">
        <el-card class="recent-access-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon class="card-icon">
                  <Clock />
                </el-icon>
                <span class="card-title">临时政策</span>
              </div>
            </div>
          </template>

          <div class="table-content">
            <el-table :data="recentTableData" size="small">
              <el-table-column prop="module" label="模块名称" align="center" width="120" />
              <el-table-column prop="company" label="公司名称" align="center" min-width="200" show-overflow-tooltip />
              <el-table-column prop="type" label="类型" align="center" width="140">
                <template #default="scope">
                  <el-link type="primary">{{ scope.row.type }}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="time" label="访问时间" align="center" width="120" />
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 文本库 -->
    <el-dialog v-model="obj.dialogShow" title="查看全部文件" width="65%" append-to-body draggable>
      <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
        <el-form-item label="文件类型:" prop="companyName">
          <el-select class="width180" v-model="obj.queryParams.type" placeholder="请选择">
            <el-option label="制度类" value="1" />
            <el-option label="政策类" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!obj.isAll" label="备注:" prop="remark">
          <el-input class="width180" v-model="obj.queryParams.remark" placeholder="请输入备注" clearable
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="obj.tableData" border>
        <el-table-column prop="type" label="文件类型" align="center" width="120" />
        <el-table-column prop="name" label="文件名称" align="center" width="150">
          <template #default="scope">
            <el-link type="primary">{{ scope.row.name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center" min-width="200" show-overflow-tooltip />
        <el-table-column prop="time" label="上传时间" align="center" width="160" />
        <el-table-column prop="operation" label="操作" align="center" width="100">
          <template #default>
            <el-button type="primary" text @click="handleDownload">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
        v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </el-dialog>
  </div>
</template>
<script setup name="Index">
const { proxy } = getCurrentInstance();
// 政策文件数据
const fileTableData = ref([
  {
    name: '合同文档',
    path: '/upload/contract/2025/04/17/',
    time: '2025-02-14 14:45:35',
  },
  {
    name: '报告文件',
    path: '/upload/report/2025/04/17/',
    time: '2025-02-14 14:25:31',
  },
  {
    name: '数据备份',
    path: '/upload/backup/2025/04/17/',
    time: '2025-02-12 14:45:35',
  },
  {
    name: '系统日志',
    path: '/upload/logs/2025/04/17/',
    time: '2025-01-24 09:18:17',
  },
  {
    name: '配置文件',
    path: '/upload/config/2025/04/17/',
    time: '2025-01-17 17:34:24',
  },
  {
    name: '用户数据',
    path: '/upload/users/2025/04/17/',
    time: '2024-12-30 14:49:22',
  },
  {
    name: '临时文件',
    path: '/upload/temp/2025/04/17/',
    time: '2024-12-30 14:48:03',
  },
  {
    name: '系统备份',
    path: '/upload/system/2025/04/17/',
    time: '2024-12-30 14:48:03',
  }
])

// 临时政策数据
const recentTableData = ref([
  {
    module: '正常',
    company: '天津山东山西省有限责任公司',
    type: '销售出库单',
    time: '2025-03-13'
  },
  {
    module: '出库',
    company: '天津山东山西省有限责任公司',
    type: '销售出库单',
    time: '2025-03-13'
  },
  {
    module: '天津',
    company: '天津山东山西省有限责任公司',
    type: '公司信息录入',
    time: '2025-03-13'
  },
  {
    module: '天津',
    company: '天津山东山西省有限责任公司',
    type: '公司信息录入',
    time: '2025-03-13'
  },
  {
    module: '上海',
    company: '上海定山信息技术有限公司',
    type: '销售出库单',
    time: '2025-03-13'
  },
  {
    module: '上海',
    company: '上海定山信息技术有限公司',
    type: '销售出库单',
    time: '2025-03-13'
  }
])

const obj = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    type: null,
    name: null,
  },
  total: 3,
  tableData: [
    {
      type: '合同文档',
      name: '合同文档',
      remark: '/upload/contract/2025/04/17/',
      time: '2025-02-14 14:45:35',
    },
    {
      type: '报告文件',
      name: '报告文件',
      remark: '/upload/report/2025/04/17/',
      time: '2025-02-14 14:25:31',
    },
    {
      type: '数据备份',
      name: '数据备份',
      remark: '/upload/backup/2025/04/17/',
      time: '2025-02-12 14:45:35',
    },
  ],
  dialogShow: false,
  isAll: false,
})

// 处理显示全部按钮点击
function handleShowAll() {
  obj.dialogShow = true
  obj.isAll = true
}

// 处理文本库按钮点击
function handleTextLibrary() {
  obj.dialogShow = true
  obj.isAll = false
}
/** 搜索按钮操作 */
function handleQuery() {
  obj.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
// 下载
function handleDownload() {
  proxy.$modal.msgSuccess('文件下载成功');
}

function getList() {

}
</script>

<style scoped lang="scss">
.pagination-container {
  position: relative !important;
}

.home {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 100px);
  padding: 20px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

// 页面标题样式
.page-header {
  text-align: center;
  margin-bottom: 40px;

  .header-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: shimmer 4s ease-in-out infinite;
    }

    .header-icon {
      font-size: 60px;
      margin-bottom: 20px;
      animation: bounce 2s ease-in-out infinite;
    }

    .header-title {
      font-size: 42px;
      font-weight: 700;
      margin: 0 0 15px 0;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

  }
}

// 内容区域
.main-content,
.bottom-content {
  margin-bottom: 30px;
}

// 卡片通用样式
.system-info-card,
.data-table-card,
.recent-access-card {
  border-radius: 15px;
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-card__header) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-bottom: none;
    padding: 20px 25px;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: white;
      width: 100%;

      .header-left {
        display: flex;
        align-items: center;

        .card-icon {
          font-size: 24px;
          margin-right: 12px;
        }

        .card-title {
          font-size: 18px;
          font-weight: 600;
        }
      }

      .header-right {
        .header-btn {
          border-color: rgba(255, 255, 255, 0.6);
          color: white;
          font-size: 12px;
          padding: 4px 12px;
          height: 28px;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: white;
            color: white;
            transform: translateY(-1px);
          }

          &.el-button--primary {
            &:hover {
              background: rgba(64, 158, 255, 0.3);
            }
          }

          &.el-button--info {
            &:hover {
              background: rgba(144, 147, 153, 0.3);
            }
          }
        }
      }
    }
  }

  :deep(.el-card__body) {
    padding: 30px;
    background: white;
  }
}

// 登录信息卡片
.system-info-card {
  .system-info-content {
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 20px;

      .info-item {
        display: flex;
        align-items: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: #e3f2fd;
          transform: translateY(-2px);
        }

        .info-label {
          font-weight: 600;
          color: #2c3e50;
          min-width: 120px;
          margin-right: 15px;
          flex-shrink: 0;
        }

        .info-value {
          color: #7f8c8d;
          word-break: break-all;
          line-height: 1.5;
          font-size: 14px;
        }
      }
    }
  }
}



// 数据表格卡片
.data-table-card,
.recent-access-card {
  :deep(.el-card__header) {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

    .card-header {
      color: #2c3e50;
    }
  }

  .table-content {
    :deep(.el-table) {
      .el-table__header {
        th {
          background: #f8f9fa;
          color: #2c3e50;
          font-weight: 600;
        }
      }

      .el-table__row {
        &:hover {
          background: #f0f9ff;
        }
      }

      .el-link {
        font-weight: 500;
      }

      .el-button {
        &.is-text {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }
  }
}



// 动画效果
@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

@keyframes shimmer {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .home {
    padding: 15px;
  }

  .page-header {
    margin-bottom: 30px;

    .header-content {
      padding: 40px 20px;

      .header-title {
        font-size: 32px;
      }

      .header-icon {
        font-size: 48px;
      }

      .tech-stack {
        .tech-separator {
          display: none;
        }
      }
    }
  }

  .main-content,
  .bottom-content {
    .el-col {
      margin-bottom: 20px;
    }
  }

  .intro-content {
    .action-buttons {
      .action-btn {
        width: 100%;
        margin-bottom: 10px;
      }
    }
  }

  .contact-content {
    .contact-item {
      flex-direction: column;
      align-items: flex-start;

      .contact-icon {
        margin-bottom: 5px;
      }

      .qq-groups {
        margin-top: 5px;
      }
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    .header-content {
      padding: 30px 15px;

      .header-title {
        font-size: 28px;
      }

      .header-subtitle {
        font-size: 16px;
      }
    }
  }

  .tech-content {
    .el-row {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
