<!-- 历史工资账单管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="客户帐套:" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户帐套" clearable>
                    <el-option label="帐套A" value="帐套A" />
                    <el-option label="帐套B" value="帐套B" />
                    <el-option label="帐套C" value="帐套C" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择账单年月"
                    clearable />
            </el-form-item>
            <el-form-item label="是否锁定:" prop="isLocked">
                <el-select class="width220" v-model="obj.queryParams.isLocked" placeholder="请选择是否锁定" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="约定账单生成日:" prop="agreedBillGenerationDay">
                <el-input class="width220" v-model="obj.queryParams.agreedBillGenerationDay" placeholder="请输入生成日"
                    clearable />
            </el-form-item>
            <el-form-item label="约定账单锁定日:" prop="agreedBillLockDay">
                <el-input class="width220" v-model="obj.queryParams.agreedBillLockDay" placeholder="请输入锁定日" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" icon="Link" @click="handleGenerateBill">生成账单</el-button>
                        <el-button type="primary" icon="Printer" @click="handlePrintBill">账单打印</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" icon="Lock" :disabled="obj.single" @click="handleLock">锁定</el-button>
                <el-button type="warning" icon="Unlock" :disabled="obj.single" @click="handleUnlock">解锁</el-button>
                <el-button type="primary" icon="View" :disabled="obj.single"
                    @click="handleViewBillLog">查看账单日志</el-button>
            </el-col>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="提交分公司" align="center" prop="submitBranch" />
            <el-table-column label="提交人" align="center" prop="submitter" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户帐套" align="center" prop="customerAccount" />
            <el-table-column label="大合同编号" align="center" prop="contractCode" />
            <el-table-column label="大合同名称" align="center" prop="contractName" />
            <el-table-column label="账单年月" align="center" prop="billMonth" />
            <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
            <el-table-column label="产品类型" align="center" prop="productType" />
            <el-table-column label="产品分类" align="center" prop="productCategory" />
            <el-table-column label="应收金额" align="center" prop="receivableAmount" />
            <el-table-column label="金额" align="center" prop="amount" />
            <el-table-column label="金额（不含税）" align="center" prop="amountExcludingTax" width="120" />
            <el-table-column label="增值税率(%)" align="center" prop="vatRate" />
            <el-table-column label="增值税" align="center" prop="vat" />
            <el-table-column label="总人次" align="center" prop="totalPersonTimes" />
            <el-table-column label="供应商人次" align="center" prop="supplierPersonTimes" />
            <el-table-column label="供应商成本" align="center" prop="supplierCost" />
            <el-table-column label="一次性支持人员" align="center" prop="oneTimeStaff" />
            <el-table-column label="供应商" align="center" prop="supplier" />
            <el-table-column label="审批状态" align="center" prop="approvalStatus" />
            <el-table-column label="审批备注" align="center" prop="approvalRemark" />
            <el-table-column label="原因备注" align="center" prop="reasonRemark" />
            <el-table-column label="客服审批人" align="center" prop="serviceApprover" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 查看账单日志 -->
        <el-dialog v-model="obj.dialogShow" title="查看账单日志" width="50%">
            <el-table :data="obj.dialogData" border>
                <el-table-column label="客户名称" align="center" prop="customerName" />
                <el-table-column label="客户帐套" align="center" prop="customerAccount" />
                <el-table-column label="账单年月" align="center" prop="billMonth" />
                <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
                <el-table-column label="应收金额" align="center" prop="receivableAmount" />
                <el-table-column label="生成人" align="center" prop="creator" />
                <el-table-column label="创建时间" align="center" prop="createTime" />
                <el-table-column label="生成状态" align="center" prop="generationStatus" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="HistoricalWageBillManagement">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    contractName: [
        { required: false, message: '合同名称不能为空', trigger: 'blur' }
    ],
    customerAccount: [
        { required: false, message: '客户帐套不能为空', trigger: 'change' }
    ],
    billMonth: [
        { required: false, message: '账单年月不能为空', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractName: null,
        customerAccount: null,
        billMonth: null,
        isLocked: null,
        agreedBillGenerationDay: null,
        agreedBillLockDay: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                submitBranch: '北京分公司',
                submitter: '张三',
                customerName: '客户名称1',
                customerCode: 'CUST001',
                customerAccount: '帐套A',
                contractCode: 'CONTRACT001',
                contractName: '大合同名称1',
                billMonth: '2023-01',
                financialReceivableMonth: '2023-02',
                productType: '类型A',
                productCategory: '分类A',
                receivableAmount: '10000',
                amount: '10000',
                amountExcludingTax: '9433.96',
                vatRate: '6',
                vat: '566.04',
                totalPersonTimes: '10',
                supplierPersonTimes: '5',
                supplierCost: '5000',
                oneTimeStaff: '5',
                supplier: '供应商A',
                approvalStatus: '已审批',
                approvalRemark: '审批通过',
                reasonRemark: '无',
                serviceApprover: '李四'
            },
            {
                id: 2,
                submitBranch: '上海分公司',
                submitter: '王五',
                customerName: '客户名称2',
                customerCode: 'CUST002',
                customerAccount: '帐套B',
                contractCode: 'CONTRACT002',
                contractName: '大合同名称2',
                billMonth: '2023-02',
                financialReceivableMonth: '2023-03',
                productType: '类型B',
                productCategory: '分类B',
                receivableAmount: '20000',
                amount: '20000',
                amountExcludingTax: '18867.92',
                vatRate: '6',
                vat: '1132.08',
                totalPersonTimes: '20',
                supplierPersonTimes: '10',
                supplierCost: '10000',
                oneTimeStaff: '10',
                supplier: '供应商B',
                approvalStatus: '已审批',
                approvalRemark: '审批通过',
                reasonRemark: '无',
                serviceApprover: '赵六'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 生成账单
function handleGenerateBill() {
    proxy.$modal.msgSuccess('生成账单成功');
}

// 账单打印
function handlePrintBill() {
    proxy.$modal.msgSuccess('账单打印成功');
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 锁定
function handleLock() {
    proxy.$modal.msgSuccess('锁定成功');
}

// 解锁
function handleUnlock() {
    proxy.$modal.msgSuccess('解锁成功');
}

// 查看账单日志
function handleViewBillLog() {
    obj.dialogShow = true;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>