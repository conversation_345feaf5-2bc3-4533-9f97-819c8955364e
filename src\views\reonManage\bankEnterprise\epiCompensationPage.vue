<!-- 银企直连补偿页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form @submit.prevent :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="支付：原业务参考号:" prop="referenceNo">
                <el-input class="width220" v-model="obj.queryParams.referenceNo" placeholder="请输入原业务参考号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="原业务参考号" align="center" prop="originalReferenceNo" />
            <el-table-column label="业务类型" align="center" prop="businessType" />
            <el-table-column label="业务模式" align="center" prop="businessMode" />
            <el-table-column label="交易金额" align="center" prop="amount" />
            <el-table-column label="交易时间" align="center" prop="transactionTime" width="180" />
            <el-table-column label="交易状态" align="center" prop="transactionStatus" />
            <el-table-column label="操作" align="center" width="120">
                <template #default="scope">
                    <el-button type="primary" link @click="handleCompensate(scope.row)">补偿</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="EpiCompensationPage">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        referenceNo: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: []//选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                originalReferenceNo: 'REF20230501001',
                businessType: '转账',
                businessMode: '单笔转账',
                amount: 5000.00,
                transactionTime: '2023-05-15 10:30:00',
                transactionStatus: '失败'
            },
            {
                id: 2,
                originalReferenceNo: 'REF20230502001',
                businessType: '工资发放',
                businessMode: '批量转账',
                amount: 10000.00,
                transactionTime: '2023-05-16 14:20:00',
                transactionStatus: '失败'
            },
            {
                id: 3,
                originalReferenceNo: 'REF20230503001',
                businessType: '税费缴纳',
                businessMode: '单笔转账',
                amount: 3000.00,
                transactionTime: '2023-05-17 09:15:00',
                transactionStatus: '失败'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 补偿操作
function handleCompensate(row) {
    proxy.$modal.confirm('确定要对参考号为 ' + row.originalReferenceNo + ' 的交易进行补偿吗？').then(function () {
        proxy.$modal.msgSuccess('补偿操作成功');
        getList();
    }).catch(() => { });
}
getList();
</script>
<style lang="scss" scoped></style>