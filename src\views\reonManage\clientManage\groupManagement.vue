<!-- 集团管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="集团名称:" prop="groupName">
                <el-select class="width220" filterable v-model="obj.queryParams.groupName" placeholder="请选择" clearable>
                    <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="associatedCustomer">关联客户</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="viewAssociatedCustomer">查看关联客户</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleCustomerScale">维护客户规模</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="viewAssociatedCustomer">
            <el-table-column type="selection" width="55" />
            <el-table-column label="集团名称" align="center" prop="groupName" />
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="客户规模" align="center" prop="customerScale" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="25%" append-to-body draggable>
            <el-form class="form-container" ref="formRef" :model="obj.dialogForm" :rules="obj.rules"
                label-width="100px">
                <el-form-item label="集团名称" prop="groupName" required>
                    <el-input style="width: 100%;" v-model="obj.dialogForm.groupName" placeholder="请输入集团名称" />
                </el-form-item>
                <el-form-item label="客户规模" prop="customerScale" required>
                    <el-select style="width: 100%;" v-model="obj.dialogForm.customerScale" placeholder="请选择" clearable>
                        <el-option v-for="item in scaleOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 关联客户 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="60%" append-to-body draggable>
            <el-form class="form-container" ref="associateFormRef" :model="obj.associateForm" inline label-width="auto">
                <el-form-item label="客户名称" prop="customerName">
                    <el-select class="width220" filterable v-model="obj.associateForm.customerName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户编号" prop="customerCode">
                    <el-input class="width220" v-model="obj.associateForm.customerCode" placeholder="请输入客户编号" />
                </el-form-item>
                <el-form-item label="城市" prop="city">
                    <el-select class="width220" filterable v-model="obj.associateForm.city" placeholder="请选择" clearable>
                        <el-option v-for="item in cityOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-row>
                    <el-col :span="24" class="text-right">
                        <el-button type="primary" icon="Search" @click="handleCustomerSearch">查询</el-button>
                        <el-button icon="Refresh" @click="resetAssociateForm">重置</el-button>
                    </el-col>
                </el-row>

                <el-divider content-position="left">关联合同</el-divider>
                <el-form-item label="合同编号" prop="customerName">
                    <el-input class="width220" v-model="obj.associateForm.customerCode" placeholder="请输入合同编号" />
                </el-form-item>
                <el-form-item label="合同名称" prop="customerCode">
                    <el-input class="width220" v-model="obj.associateForm.customerCode" placeholder="请输入合同名称" />
                </el-form-item>
                <el-row>
                    <el-form-item>
                        <el-button type="primary" @click="">关联</el-button>
                        <el-button type="primary" @click="">取消</el-button>
                    </el-form-item>
                </el-row>

                <el-table :data="obj.customerData" border @selection-change="handleCustomerSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="客户编号" align="center" prop="customerCode" />
                    <el-table-column label="客户名称" align="center" prop="customerName" />
                    <el-table-column label="所属行业" align="center" prop="industry" />
                    <el-table-column label="城市" align="center" prop="city" />
                    <el-table-column label="创建人" align="center" prop="creator" />
                </el-table>

                <div class="mt20 mb10 text-center">
                    <el-button type="primary" @click="handleAssociateCustomer"
                        :disabled="obj.customerIds.length === 0">关联选中客户</el-button>
                    <el-button @click="obj.dialogShow2 = false">取消</el-button>
                </div>
            </el-form>
        </el-dialog>
        <!-- 查看关联客户 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow3" width="60%" append-to-body draggable>
            <el-form class="form-container" ref="viewFormRef" :model="obj.viewForm" inline label-width="auto">
                <el-form-item label="客户名称" prop="customerName">
                    <el-select class="width220" filterable v-model="obj.viewForm.customerName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleViewSearch">查询</el-button>
                    <el-button icon="Refresh" @click="resetViewForm">重置</el-button>
                </el-form-item>
                <div class="mb10">
                    <el-button type="danger" icon="Delete" @click="handleRemoveAssociation"
                        :disabled="obj.viewCustomerIds.length === 0">删除</el-button>
                    <el-button type="primary" icon="Download" @click="handleExportCustomers">导出</el-button>
                </div>

                <el-table :data="obj.relatedCustomerData" border @selection-change="handleViewCustomerSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="客户编号" align="center" prop="customerCode" />
                    <el-table-column label="客户名称" align="center" prop="customerName" />
                    <el-table-column label="所属行业" align="center" prop="industry" />
                    <el-table-column label="城市" align="center" prop="city" />
                    <el-table-column label="创建人" align="center" prop="creator" />
                    <el-table-column label="联系人" align="center" prop="contactPerson" />
                    <el-table-column label="联系电话" align="center" prop="contactPhone" />
                    <el-table-column label="邮箱" align="center" prop="email" />
                    <el-table-column label="联系地址" align="center" prop="address" />
                </el-table>

                <pagination v-show="obj.relatedTotal > 0" :total="obj.relatedTotal" v-model:page="obj.viewForm.pageNum"
                    v-model:limit="obj.viewForm.pageSize" @pagination="getRelatedCustomers" />
            </el-form>
        </el-dialog>
        <!-- 维护客户规模 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow4" width="25%" append-to-body draggable>
            <el-form class="form-container" ref="scaleFormRef" :model="obj.scaleForm" label-width="auto">
                <el-form-item label="集团名称" prop="groupName">
                    <el-input style="width: 100%;" v-model="obj.scaleForm.groupName" placeholder="请输入集团名称" />
                </el-form-item>
                <el-form-item label="客户规模" prop="customerScale">
                    <el-select style="width: 100%;" v-model="obj.scaleForm.customerScale" placeholder="请选择" clearable>
                        <el-option v-for="item in scaleOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow4 = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="GroupManagement">


// 初始化常量和对象
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 引用表单元素
const formRef = ref(null)
const queryRef = ref(null)
const associateFormRef = ref(null)
const viewFormRef = ref(null)

// 选项数据
const groupOptions = ref([
    { value: '1', label: '集团A' },
    { value: '2', label: '集团B' },
    { value: '3', label: '集团C' }
])

const scaleOptions = ref([
    { value: '1', label: '大型集团(>500人)' },
    { value: '2', label: '中型集团(100-500人)' },
    { value: '3', label: '小型集团(<100人)' }
])

const customerOptions = ref([
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' },
    { value: '4', label: '客户D' },
    { value: '5', label: '客户E' }
])

const cityOptions = ref([
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
])

// 响应式数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null
    }, // 查询表单
    rules: {
        groupName: [{ required: true, message: '请输入集团名称', trigger: 'blur' }],
        customerScale: [{ required: true, message: '请选择客户规模', trigger: 'change' }]
    },
    total: 0, // 总条数
    relatedTotal: 0, // 关联客户总数
    tableData: [], // 集团列表
    customerData: [], // 客户列表
    relatedCustomerData: [], // 关联客户列表
    dialogForm: {}, // 集团表单
    associateForm: {}, // 关联客户表单
    viewForm: {}, // 查看关联客户表单
    scaleForm: {}, // 维护客户规模表单
    dialogShow: false, // 集团弹出框
    dialogShow2: false, // 关联客户弹出框
    dialogShow3: false, // 查看关联客户弹出框
    dialogShow4: false, // 维护客户规模弹出框
    ids: [], // 选中的集团id
    customerIds: [], // 选中的客户id
    viewCustomerIds: [], // 选中的关联客户id
    title: "", // 标题
    currentGroupId: null // 当前操作的集团ID
})

/**
 * 获取集团列表
 */
function getList() {
    obj.loading = true;

    // 模拟数据
    setTimeout(() => {
        // 生成模拟数据
        const mockData = [];
        for (let i = 0; i < 10; i++) {
            mockData.push({
                id: i + 1,
                groupName: `集团${String.fromCharCode(65 + i % 3)}`,
                creator: `管理员${i % 3 + 1}`,
                customerScale: i % 3 === 0 ? '大型集团(>500人)' : (i % 3 === 1 ? '中型集团(100-500人)' : '小型集团(<100人)'),
                createTime: `2023-${String(i % 12 + 1).padStart(2, '0')}-${String(i % 28 + 1).padStart(2, '0')} 10:00:00`
            });
        }

        // 应用查询条件过滤
        let filteredData = [...mockData];
        if (obj.queryParams.groupName) {
            filteredData = filteredData.filter(item => item.groupName.includes(obj.queryParams.groupName));
        }

        // 分页处理
        const start = (obj.queryParams.pageNum - 1) * obj.queryParams.pageSize;
        const end = start + obj.queryParams.pageSize;
        const pageData = filteredData.slice(start, end);

        obj.tableData = pageData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 500); // 模拟网络延迟
}

/**
 * 获取客户列表
 */
function getCustomerList() {
    // 模拟数据
    setTimeout(() => {
        // 生成模拟数据
        const mockData = [];
        for (let i = 0; i < 15; i++) {
            mockData.push({
                id: i + 1,
                customerCode: `CUS${String(1000 + i).padStart(4, '0')}`,
                customerName: `客户${String.fromCharCode(65 + i % 5)}`,
                industry: i % 4 === 0 ? '金融业' : (i % 4 === 1 ? '互联网' : (i % 4 === 2 ? '制造业' : '服务业')),
                city: i % 5 === 0 ? '北京' : (i % 5 === 1 ? '上海' : (i % 5 === 2 ? '广州' : (i % 5 === 3 ? '深圳' : '杭州'))),
                creator: `管理员${i % 3 + 1}`
            });
        }

        // 应用查询条件过滤
        let filteredData = [...mockData];
        const params = obj.associateForm;

        if (params.customerName) {
            filteredData = filteredData.filter(item => item.customerName === params.customerName);
        }

        if (params.customerCode) {
            filteredData = filteredData.filter(item => item.customerCode.includes(params.customerCode));
        }

        if (params.city) {
            filteredData = filteredData.filter(item => item.city === params.city);
        }

        obj.customerData = filteredData;
    }, 300);
}

/**
 * 获取关联客户列表
 */
function getRelatedCustomers() {
    // 模拟数据
    setTimeout(() => {
        // 生成模拟数据
        const mockData = [];
        for (let i = 0; i < 8; i++) {
            mockData.push({
                id: i + 1,
                customerCode: `CUS${String(1000 + i).padStart(4, '0')}`,
                customerName: `客户${String.fromCharCode(65 + i % 5)}`,
                industry: i % 4 === 0 ? '金融业' : (i % 4 === 1 ? '互联网' : (i % 4 === 2 ? '制造业' : '服务业')),
                city: i % 5 === 0 ? '北京' : (i % 5 === 1 ? '上海' : (i % 5 === 2 ? '广州' : (i % 5 === 3 ? '深圳' : '杭州'))),
                creator: `管理员${i % 3 + 1}`,
                contactPerson: `联系人${i + 1}`,
                contactPhone: `1381234${String(1000 + i).padStart(4, '0')}`,
                email: `customer${i + 1}@example.com`,
                address: `北京市朝阳区朝阳路${i + 1}号大厦${i + 100}室`
            });
        }

        // 应用查询条件过滤
        let filteredData = [...mockData];
        const params = obj.viewForm;

        if (params.customerName) {
            filteredData = filteredData.filter(item => item.customerName === params.customerName);
        }

        if (params.customerCode) {
            filteredData = filteredData.filter(item => item.customerCode.includes(params.customerCode));
        }

        // 分页处理
        const start = (params.pageNum || 1 - 1) * (params.pageSize || 10);
        const end = start + (params.pageSize || 10);
        const pageData = filteredData.slice(start, end);

        obj.relatedCustomerData = pageData;
        obj.relatedTotal = filteredData.length;
    }, 300);
}

/**
 * 表单重置
 */
function reset() {
    obj.dialogForm = {};
    if (formRef.value) {
        formRef.value.resetFields();
    }
}

/**
 * 关联客户表单重置
 */
function resetAssociateForm() {
    obj.associateForm = {};
    if (associateFormRef.value) {
        associateFormRef.value.resetFields();
    }
    getCustomerList();
}

/**
 * 查看关联客户表单重置
 */
function resetViewForm() {
    obj.viewForm = {
        pageNum: 1,
        pageSize: 10
    };
    if (viewFormRef.value) {
        viewFormRef.value.resetFields();
    }
    getRelatedCustomers();
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/**
 * 客户搜索按钮操作
 */
function handleCustomerSearch() {
    getCustomerList();
}

/**
 * 查看关联客户搜索按钮操作
 */
function handleViewSearch() {
    obj.viewForm.pageNum = 1;
    getRelatedCustomers();
}

/**
 * 重置按钮操作
 */
function resetQuery() {
    if (queryRef.value) {
        queryRef.value.resetFields();
    }
    handleQuery();
}

/**
 * 集团多选框选中数据
 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = selection.length === 0;
}

/**
 * 客户多选框选中数据
 */
function handleCustomerSelectionChange(selection) {
    obj.customerIds = selection.map(item => item.id);
}

/**
 * 关联客户多选框选中数据
 */
function handleViewCustomerSelectionChange(selection) {
    obj.viewCustomerIds = selection.map(item => item.id);
}

/**
 * 新增按钮操作
 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增集团";
}

/**
 * 编辑按钮操作
 */
function handleEdit(row) {
    reset();
    obj.dialogShow = true;
    obj.title = "编辑集团";
    obj.dialogForm = { ...row };
}

/**
 * 关联客户
 */
function associatedCustomer(row) {
    resetAssociateForm();
    obj.currentGroupId = row?.id;
    obj.dialogShow2 = true;
    obj.title = `关联客户 - ${row?.groupName || ''}`;
    getCustomerList();
}

/**
 * 查看关联客户
 */
function viewAssociatedCustomer() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError("请选择一个集团");
        return;
    }

    resetViewForm();
    const row = obj.tableData.find(item => item.id === obj.ids[0]);
    obj.currentGroupId = row?.id;
    obj.dialogShow3 = true;
    obj.title = `查看关联客户 - ${row?.groupName || ''}`;
    getRelatedCustomers();
}

/**
 * 维护客户规模
 */
function handleCustomerScale() {
    obj.dialogShow4 = true;
    obj.title = "维护客户规模";
}

/**
 * 关联选中客户
 */
function handleAssociateCustomer() {
    if (obj.customerIds.length === 0) {
        proxy.$modal.msgError("请选择要关联的客户");
        return;
    }

    proxy.$modal.msgSuccess(`成功关联 ${obj.customerIds.length} 个客户`);
    obj.dialogShow2 = false;
    getList();
}

/**
 * 移除关联
 */
function handleRemoveAssociation() {
    if (obj.viewCustomerIds.length === 0) {
        proxy.$modal.msgError("请选择要移除关联的客户");
        return;
    }

    proxy.$modal.confirm(`是否确认移除 ${obj.viewCustomerIds.length} 个客户的关联？`).then(() => {
        proxy.$modal.msgSuccess(`成功移除 ${obj.viewCustomerIds.length} 个客户的关联`);
        getRelatedCustomers();
    }).catch(() => { });
}

/**
 * 导出客户
 */
function handleExportCustomers() {
    proxy.$modal.msgSuccess("客户数据导出成功");
}

/**
 * 删除按钮操作
 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}


/**
 * 提交表单
 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 页面加载时获取数据
getList();
</script>
<style lang="scss" scoped>
.form-container {
    padding: 10px;
}

.dialog-footer {
    text-align: center;
    margin-top: 20px;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}
</style>