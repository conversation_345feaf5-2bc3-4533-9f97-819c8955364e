<template>
  <el-menu :default-active="activeMenu" mode="horizontal" @select="handleSelect" :ellipsis="false">
    <template v-for="(item, index) in topMenus">
      <el-menu-item :style="{ '--theme': theme }" :index="item.path" :key="index" v-if="index < visibleNumber">
        <svg-icon v-if="item.meta && item.meta.icon && item.meta.icon !== '#'" :icon-class="item.meta.icon" />
        {{ item.meta.title }}
      </el-menu-item>
    </template>

    <!-- 顶部菜单超出数量折叠 -->
    <el-sub-menu :style="{ '--theme': theme }" index="more" v-if="topMenus.length > visibleNumber">
      <template #title>更多菜单</template>
      <template v-for="(item, index) in topMenus">
        <el-menu-item :index="item.path" :key="index" v-if="index >= visibleNumber">
          <svg-icon v-if="item.meta && item.meta.icon && item.meta.icon !== '#'" :icon-class="item.meta.icon" />
          {{ item.meta.title }}
        </el-menu-item>
      </template>
    </el-sub-menu>
  </el-menu>
</template>

<script setup>
import { constantRoutes } from "@/router"
import { isHttp } from '@/utils/validate'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

// 顶部栏初始数
const visibleNumber = ref(null);
// 当前激活菜单的 index
const currentIndex = ref(null);
// 隐藏侧边栏路由
const hideList = ['/index', '/user/profile'];

const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()
const route = useRoute();
const router = useRouter();

// 主题颜色
const theme = computed(() => settingsStore.theme);
// 所有的路由信息
const routers = computed(() => permissionStore.topbarRouters);

// 顶部显示菜单
const topMenus = computed(() => {
  let topMenus = [];
  routers.value.map((menu) => {
    if (menu.hidden !== true) {
      // 兼容顶部栏一级菜单内部跳转
      if (menu.path === "/") {
        topMenus.push(menu.children[0]);
      } else {
        topMenus.push(menu);
      }
    }
  })
  return topMenus;
})

// 设置子路由
const childrenMenus = computed(() => {
  let childrenMenus = [];
  routers.value.map((router) => {
    for (let item in router.children) {
      if (router.children[item].parentPath === undefined) {
        if (router.path === "/") {
          router.children[item].path = "/" + router.children[item].path;
        } else {
          if (!isHttp(router.children[item].path)) {
            router.children[item].path = router.path + "/" + router.children[item].path;
          }
        }
        router.children[item].parentPath = router.path;
      }
      childrenMenus.push(router.children[item]);
    }
  })
  return constantRoutes.concat(childrenMenus);
})

// 默认激活的菜单
const activeMenu = computed(() => {
  const path = route.path;
  let activePath = path;
  if (path !== undefined && path.lastIndexOf("/") > 0 && hideList.indexOf(path) === -1) {
    const tmpPath = path.split("/").filter(item => item !== "");
    if (tmpPath.length == 2) {
      const tmpPath2 = path.substring(1, path.length);
      activePath = "/" + tmpPath2.substring(0, tmpPath2.indexOf("/"));
    } else if (tmpPath.length == 3) {
      const tmpPath2 = path.substring(1, path.length);
      activePath = "/" + tmpPath2.substring(0, tmpPath2.lastIndexOf("/"));
    }
    if (!route.meta.link) {
      appStore.toggleSideBarHide(false);
    }
  } else if (!route.children) {
    activePath = path;
    appStore.toggleSideBarHide(true);
  }
  activeRoutes(activePath);
  return activePath;
})

/**
 * 计算并设置可见数字数量
 * 
 * 该函数根据当前页面宽度计算出一个合适的数字数量，这个数字数量用于控制在用户界面上可见的数字按钮的数量
 * 它通过获取页面宽度的三分之一，然后除以一个固定值来确定数字按钮的数量，这个固定值代表每个数字按钮在界面上大致占用的宽度
 */
function setVisibleNumber() {
  // 计算页面宽度的三分之一，用以确定可见数字按钮的大致数量
  const width = document.body.getBoundingClientRect().width / 3;
  // 根据计算出的宽度，设置可见数字的数量，确保界面布局合理且响应式
  visibleNumber.value = parseInt(width / 85);
}

/**
 * 处理菜单选择事件
 * @param {string} key - 被选中菜单的路径
 * @param {Array} keyPath - 被选中菜单的路径数组
 */
function handleSelect(key, keyPath) {
  // 更新当前选中的菜单项
  currentIndex.value = key;

  // 查找与选中菜单路径对应的路由
  const route = routers.value.find(item => item.path === key);

  // 判断路径是否为http(s)://开头，如果是则在新窗口打开
  if (isHttp(key)) {
    window.open(key, "_blank");
  } else if (!route || !route.children) {
    // 如果没有找到对应的路由或路由没有子路由，则在内部打开路径
    const routeMenu = childrenMenus.value.find(item => item.path === key);
    if (routeMenu && routeMenu.query) {
      // 如果路径有查询参数，则解析并携带查询参数导航
      let query = JSON.parse(routeMenu.query);
      router.push({ path: key, query: query });
    } else {
      // 如果路径没有查询参数，则直接导航
      router.push({ path: key });
    }
    // 隐藏侧边栏
    appStore.toggleSideBarHide(true);
  } else {
    // 如果找到对应的路由且有子路由，则显示左侧联动菜单
    activeRoutes(key);
    // 显示侧边栏
    appStore.toggleSideBarHide(false);
  }
}

/**
 * 根据给定的键值激活对应的路由
 * 此函数用于根据父路径或特定条件筛选出需要激活的路由，并根据结果更新侧边栏的路由配置或隐藏侧边栏
 * @param {string} key - 用于匹配路由的键值，通常是父路径的标识
 * @returns {Array} - 匹配到的路由数组
 */
function activeRoutes(key) {
  // 初始化一个空数组，用于存储匹配到的路由
  let routes = [];

  // 检查是否存在子菜单且子菜单数量大于0
  if (childrenMenus.value && childrenMenus.value.length > 0) {
    // 遍历子菜单，寻找匹配的路由
    childrenMenus.value.map((item) => {
      // 如果当前菜单的父路径与给定的键值匹配，或者键值为"index"且菜单路径为空，则将其添加到路由数组中
      if (key == item.parentPath || (key == "index" && "" == item.path)) {
        routes.push(item);
      }
    });
  }
  // 根据匹配到的路由数量决定是否更新侧边栏的路由配置或隐藏侧边栏
  if (routes.length > 0) {
    // 如果有匹配的路由，设置侧边栏的路由配置
    permissionStore.setSidebarRouters(routes);
  } else {
    // 如果没有匹配的路由，隐藏侧边栏
    // appStore.toggleSideBarHide(true);
  }

  // 返回匹配到的路由数组
  return routes;
}

onMounted(() => {
  window.addEventListener('resize', setVisibleNumber)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', setVisibleNumber)
})

onMounted(() => {
  setVisibleNumber()
})
</script>

<style lang="scss">
.topmenu-container.el-menu--horizontal>.el-menu-item {
  float: left;
  height: 50px !important;
  line-height: 50px !important;
  color: #999093 !important;
  padding: 0 5px !important;
  margin: 0 10px !important;
}

.topmenu-container.el-menu--horizontal>.el-menu-item.is-active,
.el-menu--horizontal>.el-sub-menu.is-active .el-submenu__title {
  border-bottom: 2px solid #{'var(--theme)'} !important;
  color: #303133;
}

/* sub-menu item */
.topmenu-container.el-menu--horizontal>.el-sub-menu .el-sub-menu__title {
  float: left;
  height: 50px !important;
  line-height: 50px !important;
  color: #999093 !important;
  padding: 0 5px !important;
  margin: 0 10px !important;
}

/* 背景色隐藏 */
.topmenu-container.el-menu--horizontal>.el-menu-item:not(.is-disabled):focus,
.topmenu-container.el-menu--horizontal>.el-menu-item:not(.is-disabled):hover,
.topmenu-container.el-menu--horizontal>.el-submenu .el-submenu__title:hover {
  background-color: #ffffff !important;
}

/* 图标右间距 */
.topmenu-container .svg-icon {
  margin-right: 4px;
}

/* topmenu more arrow */
.topmenu-container .el-sub-menu .el-sub-menu__icon-arrow {
  position: static;
  vertical-align: middle;
  margin-left: 8px;
  margin-top: 0px;
}
</style>
