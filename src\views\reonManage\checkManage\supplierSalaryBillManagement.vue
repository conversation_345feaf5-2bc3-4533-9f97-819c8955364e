<!-- 供应商工资账单管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-input class="width220" v-model="obj.queryParams.supplierName" placeholder="请输入供应商名称" clearable />
            </el-form-item>
            <el-form-item label="账单年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择账单年月"
                    clearable />
            </el-form-item>
            <el-form-item label="供应商核验状态:" prop="supplierVerificationStatus">
                <el-select class="width220" v-model="obj.queryParams.supplierVerificationStatus" placeholder="请选择核验状态"
                    clearable>
                    <el-option label="未核验" value="0" />
                    <el-option label="已核验" value="1" />
                    <el-option label="已驳回" value="2" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" icon="Check" plain :disabled="obj.single"
                    @click="handleSupplierConfirm">供应商客服确认</el-button>
                <el-button type="warning" icon="Close" plain :disabled="obj.single"
                    @click="handleSupplierCancelConfirm">供应商客服取消确认</el-button>
                <el-button type="danger" icon="Delete" plain :disabled="obj.single" @click="handleReject">驳回</el-button>
            </el-col>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商名称" align="center" prop="supplierName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户帐套" align="center" prop="customerAccount" />
            <el-table-column label="账单年月" align="center" prop="billMonth" />
            <el-table-column label="账单人数" align="center" prop="billPersonCount" />
            <el-table-column label="应收金额" align="center" prop="receivableAmount" />
            <el-table-column label="生成状态" align="center" prop="generationStatus" />
            <el-table-column label="今日已生成次数" align="center" prop="todayGenerationCount" />
            <el-table-column label="供应商核验状态" align="center" prop="supplierVerificationStatus" />
            <el-table-column label="首次生成时间" align="center" prop="firstGenerationTime" />
            <el-table-column label="生成人" align="center" prop="creator" />
            <el-table-column label="供应商客服" align="center" prop="supplierServiceManager" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="SupplierSalaryBillManagement">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        billMonth: null,
        supplierVerificationStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                supplierName: '供应商A',
                customerName: '客户名称1',
                customerAccount: '帐套A',
                billMonth: '2023-01',
                billPersonCount: '10',
                receivableAmount: '10000',
                generationStatus: '已生成',
                todayGenerationCount: '1',
                supplierVerificationStatus: '已核验',
                firstGenerationTime: '2023-01-01 10:00:00',
                creator: '张三',
                supplierServiceManager: '李四'
            },
            {
                id: 2,
                supplierName: '供应商B',
                customerName: '客户名称2',
                customerAccount: '帐套B',
                billMonth: '2023-02',
                billPersonCount: '20',
                receivableAmount: '20000',
                generationStatus: '已生成',
                todayGenerationCount: '0',
                supplierVerificationStatus: '未核验',
                firstGenerationTime: '2023-02-01 10:00:00',
                creator: '王五',
                supplierServiceManager: '赵六'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 供应商客服确认
function handleSupplierConfirm() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要确认的数据');
        return;
    }
    proxy.$modal.confirm('是否确认供应商客服确认？').then(function () {
        // 这里可以调用API确认数据
        proxy.$modal.msgSuccess("供应商客服确认成功");
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 供应商客服取消确认
function handleSupplierCancelConfirm() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要取消确认的数据');
        return;
    }
    proxy.$modal.confirm('是否确认供应商客服取消确认？').then(function () {
        // 这里可以调用API取消确认数据
        proxy.$modal.msgSuccess("供应商客服取消确认成功");
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 驳回
function handleReject() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要驳回的数据');
        return;
    }
    proxy.$modal.prompt('请输入驳回原因').then(({ value }) => {
        if (!value) {
            proxy.$modal.msgWarning('驳回原因不能为空');
            return;
        }
        // 这里可以调用API驳回数据
        proxy.$modal.msgSuccess('驳回成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>