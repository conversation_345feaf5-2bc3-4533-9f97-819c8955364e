<!-- 实做白名单管理页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="类型:" prop="type">
                <el-select class="width220" v-model="obj.queryParams.type" placeholder="请选择" clearable>
                    <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-select class="width220" v-model="obj.queryParams.customer" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd_apply">添加实做申请白名单</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd_pay">添加实做支付白名单</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" align="center" />
            <el-table-column label="客户Id" align="center" prop="id" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="类型" align="center" prop="type" />
            <el-table-column label="文件" align="center" prop="file">
                <template #default="scope">
                    <el-link type="primary" @click="handleClick(scope.row)">
                        点击
                    </el-link>
                </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 添加实做申请白名单 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="30%" append-to-body>
            <el-form :model="obj.dialogForm" ref="applyDialogRef" :rules="applyRules" inline label-width="auto">
                <el-form-item label="客户:" prop="customer">
                    <el-select class="width220" v-model="obj.dialogForm.customer" placeholder="请选择" clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-row :gutter="10">
                    <el-col :span="16">
                        <FileUpload v-model="obj.dialogForm.file" />
                    </el-col>
                    <el-col :span="8">
                        <el-button type="primary" @click="submitApplyWhitelist">开始上传</el-button>
                        <el-button @click="cancelApplyWhitelist">取消</el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="obj.applyWhitelistData" border @selection-change="handleApplySelectionChange">
                <el-table-column type="selection" align="center" />
                <el-table-column label="客户Id" align="center" prop="id" />
                <el-table-column label="客户名称" align="center" prop="customerName" />
                <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
                <el-table-column label="类型" align="center" prop="type" />
                <el-table-column label="创建人" align="center" prop="creator" />
                <el-table-column label="创建时间" align="center" prop="createTime" />
            </el-table>
        </el-dialog>
        <!-- 添加实做支付白名单 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="45%" append-to-body>
            <el-form :model="obj.dialogForm" ref="payDialogRef" :rules="payRules" inline label-width="auto">
                <el-form-item label="福利办理方:" prop="welfareHandler">
                    <el-select class="width220" v-model="obj.dialogForm.welfareHandler" placeholder="请选择" clearable>
                        <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="福利包名称:" prop="welfarePackageName">
                    <el-select class="width220" v-model="obj.dialogForm.welfarePackageName" placeholder="请选择" clearable>
                        <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户:" prop="customer">
                    <el-select class="width220" v-model="obj.dialogForm.customer" placeholder="请选择" clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-row :gutter="10">
                    <el-col :span="16">
                        <FileUpload v-model="obj.dialogForm.file" />
                    </el-col>
                    <el-col :span="8">
                        <el-button type="primary" @click="submitPayWhitelist">开始上传</el-button>
                        <el-button @click="cancelPayWhitelist">取消</el-button>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="obj.payWhitelistData" border @selection-change="handlePaySelectionChange">
                <el-table-column type="selection" align="center" />
                <el-table-column label="客户Id" align="center" prop="id" />
                <el-table-column label="客户名称" align="center" prop="customerName" />
                <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
                <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
                <el-table-column label="类型" align="center" prop="type" />
                <el-table-column label="创建人" align="center" prop="creator" />
                <el-table-column label="创建时间" align="center" prop="createTime" />
            </el-table>
        </el-dialog>


        <!-- 查看 -->
        <el-dialog v-model="obj.dialogShow3" :title="obj.title" width="45%" append-to-body>
            <FileUpload v-model="obj.dialogForm.file" />
        </el-dialog>
    </div>
</template>


<script setup name="ImplementWhitelist">
import { listScale, getScale, delScale, addScale, updateScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 类型选项
const typeOptions = [
    { value: '1', label: '实做申请白名单' },
    { value: '2', label: '实做支付白名单' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        customerName: '客户A',
        welfareHandler: '公司A',
        type: '实做申请白名单',
        file: '申请白名单.xlsx',
        creator: '张三',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 2,
        customerName: '客户B',
        welfareHandler: '公司B',
        type: '实做支付白名单',
        file: '支付白名单.xlsx',
        creator: '李四',
        createTime: '2023-02-15 09:15:00'
    },
    {
        id: 3,
        customerName: '客户C',
        welfareHandler: '公司C',
        type: '实做申请白名单',
        file: '申请白名单_2.xlsx',
        creator: '王五',
        createTime: '2023-03-15 11:45:00'
    }
];

// 模拟申请白名单数据
const mockApplyWhitelistData = [
    {
        id: 1,
        customerName: '客户A',
        welfareHandler: '公司A',
        type: '实做申请白名单',
        creator: '张三',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 2,
        customerName: '客户B',
        welfareHandler: '公司B',
        type: '实做申请白名单',
        creator: '李四',
        createTime: '2023-02-15 09:15:00'
    }
];

// 模拟支付白名单数据
const mockPayWhitelistData = [
    {
        id: 1,
        customerName: '客户A',
        welfareHandler: '公司A',
        welfarePackageName: '标准福利包',
        type: '实做支付白名单',
        creator: '张三',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 2,
        customerName: '客户B',
        welfareHandler: '公司B',
        welfarePackageName: '高级福利包',
        type: '实做支付白名单',
        creator: '李四',
        createTime: '2023-02-15 09:15:00'
    }
];

// 表单验证规则
const applyRules = {
    customer: [
        { required: true, message: '请选择客户', trigger: 'change' }
    ],
    file: [
        { required: true, message: '请选择要上传的文件', trigger: 'change' }
    ]
};

const payRules = {
    welfareHandler: [
        { required: true, message: '请选择福利办理方', trigger: 'change' }
    ],
    welfarePackageName: [
        { required: true, message: '请选择福利包名称', trigger: 'change' }
    ],
    customer: [
        { required: true, message: '请选择客户', trigger: 'change' }
    ],
    file: [
        { required: true, message: '请选择要上传的文件', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        type: null, // 类型
        customer: null, // 客户
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        customer: null, // 客户
        welfareHandler: null, // 福利办理方
        welfarePackageName: null, // 福利包名称
        file: null // 文件
    }, // 导入表单
    dialogShow: false, // 实做申请白名单弹窗
    dialogShow2: false, // 实做支付白名单弹窗
    dialogShow3: false, // 查看弹窗
    applyWhitelistData: mockApplyWhitelistData, // 申请白名单数据
    payWhitelistData: mockPayWhitelistData, // 支付白名单数据
    ids: [], // 选中的id
    applyIds: [], // 选中的申请白名单id
    payIds: [], // 选中的支付白名单id
    title: "", // 标题

})


/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 选择行 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

// 添加实做申请白名单
function handleAdd_apply() {
    obj.dialogShow = true;
    obj.title = "添加白名单";
}

// 添加实做支付白名单
function handleAdd_pay() {
    obj.dialogShow2 = true;
    obj.title = "添加白名单";
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 修改
function handleUpdate() {
    obj.dialogShow = true;
    obj.title = "修改";
}

// 查看
function handleClick(row) {
    obj.dialogShow3 = true;
    obj.title = "查看";
    obj.dialogForm.file = row.file;
}

// 删除
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

// 处理申请白名单选择
function handleApplySelectionChange(selection) {
    obj.applyIds = selection.map(item => item.id);
}

// 处理支付白名单选择
function handlePaySelectionChange(selection) {
    obj.payIds = selection.map(item => item.id);
}

// 提交申请白名单
function submitApplyWhitelist() {
    proxy.$refs["applyDialogRef"].validate(valid => {
        if (valid) {
            if (!obj.dialogForm.file) {
                proxy.$modal.msgError('请选择要上传的文件');
                return;
            }

            // 实际项目中应该调用API进行文件上传
            proxy.$modal.msgSuccess('申请白名单上传成功');
            obj.dialogShow = false;
            resetForm();
            getList(); // 刷新列表
        }
    });
}

// 取消申请白名单
function cancelApplyWhitelist() {
    obj.dialogShow = false;
    resetForm();
}

// 提交支付白名单
function submitPayWhitelist() {
    proxy.$refs["payDialogRef"].validate(valid => {
        if (valid) {
            if (!obj.dialogForm.file) {
                proxy.$modal.msgError('请选择要上传的文件');
                return;
            }

            // 实际项目中应该调用API进行文件上传
            proxy.$modal.msgSuccess('支付白名单上传成功');
            obj.dialogShow2 = false;
            resetForm();
            getList(); // 刷新列表
        }
    });
}

// 取消支付白名单
function cancelPayWhitelist() {
    obj.dialogShow2 = false;
    resetForm();
}

// 重置表单
function resetForm() {
    obj.dialogForm = {
        customer: null, // 客户
        welfareHandler: null, // 福利办理方
        welfarePackageName: null, // 福利包名称
        file: null // 文件
    };
    if (proxy.$refs["applyDialogRef"]) {
        proxy.resetForm("applyDialogRef");
    }
    if (proxy.$refs["payDialogRef"]) {
        proxy.resetForm("payDialogRef");
    }
}

getList();
</script>
<style lang="scss" scoped>
.el-link.is-underline::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    bottom: 0;
    border-bottom: 1px solid #1890ff;
}
</style>