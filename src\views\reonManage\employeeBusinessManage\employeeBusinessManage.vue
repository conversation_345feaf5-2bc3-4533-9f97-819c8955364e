<!-- 员工业务管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="接单方:" prop="receivingParty">
                <el-select class="width220" v-model="obj.queryParams.receivingParty" placeholder="请选择" clearable>
                    <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择" clearable>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="业务大类:" prop="businessCategory">
                <el-select class="width220" v-model="obj.queryParams.businessCategory" placeholder="请选择" clearable>
                    <el-option v-for="item in businessCategoryOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="业务小类:" prop="businessSubcategory">
                <el-select class="width220" v-model="obj.queryParams.businessSubcategory" placeholder="请选择" clearable>
                    <el-option v-for="item in businessSubcategoryOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="分公司/供应商" align="center" prop="company">
                <template #default="scope">
                    {{companyOptions.find(item => item.value === scope.row.company)?.label || '未知分公司/供应商'}}
                </template>
            </el-table-column>
            <el-table-column label="城市" align="center" prop="city">
                <template #default="scope">
                    {{cityOptions.find(item => item.value === scope.row.city)?.label || '未知城市'}}
                </template>
            </el-table-column>
            <el-table-column label="业务大类" align="center" prop="businessCategory">
                <template #default="scope">
                    {{businessCategoryOptions.find(item => item.value === scope.row.businessCategory)?.label || '未知大类'
                    }}
                </template>
            </el-table-column>
            <el-table-column label="业务小类" align="center" prop="businessSubcategory">
                <template #default="scope">
                    {{businessSubcategoryOptions.find(item => item.value === scope.row.businessSubcategory)?.label ||
                        '未知小类'}}
                </template>
            </el-table-column>
            <el-table-column label="具体业务" align="center" prop="specificBusiness">
                <template #default="scope">
                    <el-link style="text-decoration: underline;color: #0000ff;" @click="lookBusiness">单击查看具体业务</el-link>
                </template>
            </el-table-column>
            <el-table-column label="业务时限" align="center" prop="businessTimeLimit" />
            <el-table-column label="办理时限" align="center" prop="handleTimeLimit" />
            <el-table-column label="是否需要支付" align="center" prop="needPayment">
                <template #default="scope">
                    {{needPaymentOptions.find(item => item.value === scope.row.needPayment)?.label || '未知'}}
                </template>
            </el-table-column>
            <el-table-column label="社保公积金中心支付对象" align="center" width="180" prop="paymentObject" />
            <el-table-column label="办理资料" align="center" prop="materials">
                <template #default="scope">
                    <el-link style="text-decoration: underline;color: #0000ff;"
                        @click="handleViewMaterials(scope.row)">点击查看</el-link>
                </template>
            </el-table-column>
            <el-table-column label="停用状态" align="center" prop="deactivatedStatus">
                <template #default="scope">
                    <el-tag :type="scope.row.deactivatedStatus === '1' ? 'danger' : 'success'">
                        {{deactivatedStatusOptions.find(item => item.value === scope.row.deactivatedStatus)?.label ||
                            '未知状态'}}
                    </el-tag>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增/编辑 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="60%" append-to-body draggable>
            <el-form :model="obj.dialogForm" ref="dialogRef" :rules="rules" inline label-width="auto">
                <el-form-item label="所属城市:" prop="city">
                    <el-select class="width220" v-model="obj.dialogForm.city" placeholder="请选择" :disabled="obj.isEdit">
                        <el-option v-for="item in cityOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公司名称:" prop="company">
                    <el-select class="width220" v-model="obj.dialogForm.company" placeholder="请选择"
                        :disabled="obj.isEdit">
                        <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="业务大类:" prop="businessCategory">
                    <el-select class="width220" v-model="obj.dialogForm.businessCategory" placeholder="请选择"
                        :disabled="obj.isEdit">
                        <el-option v-for="item in businessCategoryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="业务小类:" prop="businessSubcategory">
                    <el-select class="width220" v-model="obj.dialogForm.businessSubcategory" placeholder="请选择"
                        :disabled="obj.isEdit">
                        <el-option v-for="item in businessSubcategoryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="具体业务:" prop="specificBusiness">
                    <el-select class="width220" v-model="obj.dialogForm.specificBusiness" placeholder="请选择"
                        :disabled="obj.isEdit">
                        <el-option v-for="item in specificBusinessOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="业务时限:" prop="businessTimeLimit">
                    <el-input class="width220" v-model="obj.dialogForm.businessTimeLimit" placeholder="请输入业务时限" />
                </el-form-item>
                <el-form-item label="办理时限:" prop="handleTimeLimit">
                    <el-input class="width220" v-model="obj.dialogForm.handleTimeLimit" placeholder="请输入办理时限" />
                </el-form-item>
                <el-form-item label="社保公积金中心支付对象:" width="180" prop="paymentObject">
                    <el-input class="width220" v-model="obj.dialogForm.paymentObject" placeholder="请输入支付对象" />
                </el-form-item>
                <el-form-item label="是否需要支付:" prop="needPayment">
                    <el-select class="width220" v-model="obj.dialogForm.needPayment" placeholder="请选择">
                        <el-option v-for="item in needPaymentOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="银行账户:" prop="bankAccount">
                    <el-input class="width220" v-model="obj.dialogForm.bankAccount" placeholder="请输入银行账户" />
                </el-form-item>
                <el-form-item label="账户名:" prop="accountName">
                    <el-input class="width220" v-model="obj.dialogForm.accountName" placeholder="请输入账户名" />
                </el-form-item>
                <el-form-item label="开户行:" prop="bankName">
                    <el-input class="width220" v-model="obj.dialogForm.bankName" placeholder="请输入开户行" />
                </el-form-item>
                <el-form-item label="工伤办理所需资料:" prop="materials">
                    <el-input class="width220" v-model="obj.dialogForm.materials" placeholder="请输入工伤办理所需资料" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
        <!-- 具体业务 -->
        <el-dialog v-model="obj.dialogShow2" title="查看所有数据" width="60%" append-to-body draggable>
            <el-form :model="obj.dialogForm" ref="dialogRef2" inline label-width="auto">
                <el-form-item label="所属城市:" prop="city">
                    <el-select class="width220" v-model="obj.dialogForm.city" placeholder="请选择">
                        <el-option v-for="item in cityOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公司名称:" prop="company">
                    <el-select class="width220" v-model="obj.dialogForm.company" placeholder="请选择">
                        <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="业务大类:" prop="businessCategory">
                    <el-select class="width220" v-model="obj.dialogForm.businessCategory" placeholder="请选择">
                        <el-option v-for="item in businessCategoryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="业务小类:" prop="businessSubcategory">
                    <el-select class="width220" v-model="obj.dialogForm.businessSubcategory" placeholder="请选择">
                        <el-option v-for="item in businessSubcategoryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <el-row class="mb8" :gutter="10">
                <el-col :span="1.5">
                    <el-button type="primary" @click="updateBtn">修改</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="primary" @click="deactivatedBtn">停用</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="primary" @click="activatedBtn">启用</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="primary" @click="addOtherBusinessBtn">新增其他业务</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="primary" @click="deleteBusinessBtn">删除</el-button>
                </el-col>
            </el-row>
            <el-table :data="obj.tableData" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="具体业务" align="center" prop="specificBusiness">
                    <template #default="scope">
                        {{specificBusinessOptions.find(item => item.value === scope.row.specificBusiness)?.label ||
                            '未知业务'}}
                    </template>
                </el-table-column>
                <el-table-column label="业务时限" align="center" prop="businessTimeLimit" />
                <el-table-column label="办理时限" align="center" prop="handleTimeLimit" />
                <el-table-column label="是否需要支付" align="center" prop="needPayment">
                    <template #default="scope">
                        {{needPaymentOptions.find(item => item.value === scope.row.needPayment)?.label || '未知'}}
                    </template>
                </el-table-column>
                <el-table-column label="银行账户" align="center" prop="bankAccount" />
                <el-table-column label="账户名称" align="center" prop="accountName" />
                <el-table-column label="开户行" align="center" prop="bankName" />
                <el-table-column label="社保公积金中心支付对象" width="180" align="center" prop="paymentObject" />
                <el-table-column label="办理资料" align="center" prop="materials">
                    <template #default="scope">
                        <el-link style="text-decoration: underline;color: #0000ff;"
                            @click="handleViewMaterials(scope.row)">查看</el-link>
                    </template>
                </el-table-column>
                <el-table-column label="停用状态" align="center" prop="deactivatedStatus">
                    <template #default="scope">
                        <el-tag :type="scope.row.deactivatedStatus === '1' ? 'danger' : 'success'">
                            {{deactivatedStatusOptions.find(item => item.value === scope.row.deactivatedStatus)?.label
                                || '未知状态'}}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <!-- 办理资料 -->
        <el-dialog v-model="obj.dialogShow3" title="办理材料" width="40%" append-to-body draggable>
            <el-table :data="obj.materialsData" border>
                <el-table-column label="序号" align="center" prop="id" />
                <el-table-column label="所需资料" align="center" prop="name" />
            </el-table>
            <template #footer>
                <el-button @click="obj.dialogShow3 = false">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup>
import { listScale } from "@/api/reonApi/scale";


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' }
];

// 分公司/供应商选项
const companyOptions = [
    { value: '1', label: '分公司A' },
    { value: '2', label: '分公司B' },
    { value: '3', label: '分公司C' },
    { value: '4', label: '供应商A' },
    { value: '5', label: '供应商B' }
];

// 业务大类选项
const businessCategoryOptions = [
    { value: '1', label: '社保类' },
    { value: '2', label: '公积金类' },
    { value: '3', label: '人事类' }
];

// 业务小类选项
const businessSubcategoryOptions = [
    { value: '1', label: '养老保险' },
    { value: '2', label: '医疗保险' },
    { value: '3', label: '失业保险' },
    { value: '4', label: '公积金' },
    { value: '5', label: '入职办理' },
    { value: '6', label: '离职办理' }
];

// 具体业务选项
const specificBusinessOptions = [
    { value: '1', label: '养老保险变更' },
    { value: '2', label: '医疗保险变更' },
    { value: '3', label: '失业保险变更' },
    { value: '4', label: '公积金变更' },
    { value: '5', label: '入职办理' },
    { value: '6', label: '离职办理' }
];

// 是否需要支付选项
const needPaymentOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' }
];

// 停用状态选项
const deactivatedStatusOptions = [
    { value: '1', label: '停用' },
    { value: '0', label: '启用' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        company: '1',
        city: '1',
        businessCategory: '1',
        businessSubcategory: '1',
        specificBusiness: '1',
        businessTimeLimit: '30天',
        handleTimeLimit: '15天',
        needPayment: '1',
        paymentObject: '社保中心A',
        materials: '身份证复印件，申请表',
        deactivatedStatus: '0',
        bankAccount: '6222021234567890123',
        accountName: '张三',
        bankName: '中国建设银行'
    },
    {
        id: 2,
        company: '2',
        city: '2',
        businessCategory: '2',
        businessSubcategory: '4',
        specificBusiness: '4',
        businessTimeLimit: '45天',
        handleTimeLimit: '20天',
        needPayment: '1',
        paymentObject: '公积金中心B',
        materials: '身份证复印件，申请表，单位证明',
        deactivatedStatus: '0',
        bankAccount: '6222021234567890456',
        accountName: '李四',
        bankName: '中国工商银行'
    },
    {
        id: 3,
        company: '3',
        city: '3',
        businessCategory: '3',
        businessSubcategory: '5',
        specificBusiness: '5',
        businessTimeLimit: '7天',
        handleTimeLimit: '3天',
        needPayment: '0',
        paymentObject: '',
        materials: '身份证复印件，入职申请表',
        deactivatedStatus: '1',
        bankAccount: '',
        accountName: '',
        bankName: ''
    }
];

// 模拟办理资料数据
const mockMaterialsData = [
    { id: 1, name: '身份证复印件' },
    { id: 2, name: '申请表' },
    { id: 3, name: '单位证明' },
    { id: 4, name: '其他证明材料' }
];

// 表单验证规则
const rules = {
    company: [
        { required: true, message: '请选择分公司/供应商', trigger: 'change' }
    ],
    city: [
        { required: true, message: '请选择城市', trigger: 'change' }
    ],
    businessCategory: [
        { required: true, message: '请选择业务大类', trigger: 'change' }
    ],
    businessSubcategory: [
        { required: true, message: '请选择业务小类', trigger: 'change' }
    ],
    specificBusiness: [
        { required: true, message: '请选择具体业务', trigger: 'change' }
    ],
    businessTimeLimit: [
        { required: true, message: '请输入业务时限', trigger: 'blur' }
    ],
    handleTimeLimit: [
        { required: true, message: '请输入办理时限', trigger: 'blur' }
    ],
    needPayment: [
        { required: true, message: '请选择是否需要支付', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        company: null, // 分公司/供应商
        city: null, // 城市
        businessCategory: null, // 业务大类
        businessSubcategory: null, // 业务小类
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    materialsData: mockMaterialsData, // 办理资料数据
    dialogForm: {
        company: '', // 分公司/供应商
        city: '', // 城市
        businessCategory: '', // 业务大类
        businessSubcategory: '', // 业务小类
        specificBusiness: '', // 具体业务
        businessTimeLimit: '', // 业务时限
        handleTimeLimit: '', // 办理时限
        needPayment: '', // 是否需要支付
        paymentObject: '', // 社保公积金中心支付对象
        bankAccount: '', // 银行账户
        accountName: '', // 账户名
        bankName: '', // 开户行
        materials: '', // 办理资料
        deactivatedStatus: '0' // 停用状态，默认启用
    }, // 表单
    dialogShow: false, // 新增/编辑弹窗
    dialogShow2: false, // 具体业务弹窗
    dialogShow3: false, // 办理资料弹窗
    ids: [], // 选中的id
    title: "", // 标题
    isEdit: false, // 是否编辑
});

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.company) {
            filteredData = filteredData.filter(item => item.company === obj.queryParams.company);
        }

        if (obj.queryParams.city) {
            filteredData = filteredData.filter(item => item.city === obj.queryParams.city);
        }

        if (obj.queryParams.businessCategory) {
            filteredData = filteredData.filter(item => item.businessCategory === obj.queryParams.businessCategory);
        }

        if (obj.queryParams.businessSubcategory) {
            filteredData = filteredData.filter(item => item.businessSubcategory === obj.queryParams.businessSubcategory);
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 新增
function handleAdd() {
    obj.dialogShow = true;
    obj.title = "新增业务";
    obj.isEdit = false;
    resetForm();
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 修改具体业务
function updateBtn() {
    obj.dialogShow = true;
    obj.title = "修改业务";
    obj.isEdit = true;
}

// 停用具体业务
function deactivatedBtn() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要停用的记录');
        return;
    }

    proxy.$modal.confirm('确认要停用选中的记录吗？').then(() => {
        // 实际项目中应该调用API进行停用
        proxy.$modal.msgSuccess('停用成功');
    }).catch(() => { });
}

// 启用具体业务
function activatedBtn() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要启用的记录');
        return;
    }

    proxy.$modal.confirm('确认要启用选中的记录吗？').then(() => {
        // 实际项目中应该调用API进行启用
        proxy.$modal.msgSuccess('启用成功');
    }).catch(() => { });
}

// 新增其他业务
function addOtherBusinessBtn() {
    obj.dialogShow = true;
    obj.title = "新增业务";
    obj.isEdit = false;
    resetForm();
}

// 删除具体业务
function deleteBusinessBtn() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要删除的记录');
        return;
    }

    proxy.$modal.confirm('确认要删除选中的记录吗？').then(() => {
        // 实际项目中应该调用API进行删除
        proxy.$modal.msgSuccess('删除成功');
    }).catch(() => { });
}

// 查看办理资料
function handleViewMaterials(row) {
    obj.dialogShow3 = true;
    obj.title = '办理资料';

    // 实际项目中应该调用API获取办理资料数据
    // 这里使用模拟数据
    obj.materialsData = mockMaterialsData;
}

// 保存
function handleSave() {
    proxy.$refs["dialogRef"].validate(valid => {
        if (valid) {
            // 实际项目中应该调用API进行保存
            proxy.$modal.msgSuccess('保存成功');
            obj.dialogShow = false;
            resetForm();
            getList(); // 刷新列表
        }
    });
}

// 重置表单
function resetForm() {
    obj.dialogForm = {
        company: '', // 分公司/供应商
        city: '', // 城市
        businessCategory: '', // 业务大类
        businessSubcategory: '', // 业务小类
        specificBusiness: '', // 具体业务
        businessTimeLimit: '', // 业务时限
        handleTimeLimit: '', // 办理时限
        needPayment: '', // 是否需要支付
        paymentObject: '', // 社保公积金中心支付对象
        bankAccount: '', // 银行账户
        accountName: '', // 账户名
        bankName: '', // 开户行
        materials: '', // 办理资料
        deactivatedStatus: '0' // 停用状态，默认启用
    };
    if (proxy.$refs["dialogRef"]) {
        proxy.resetForm("dialogRef");
    }
}
function lookBusiness() {
    obj.dialogShow2 = true
}

getList();
</script>
<style lang="scss" scoped></style>