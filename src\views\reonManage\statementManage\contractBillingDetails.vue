<!-- 合同账单详情 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-row class="mb8">
                <el-form-item label="合同名称:" prop="contractName">
                    <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
                </el-form-item>
                <el-form-item label="客户名称:" prop="customerName">
                    <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
                </el-form-item>
                <el-form-item label="账单年月:" prop="billMonth">
                    <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month"
                        placeholder="请选择年月" clearable />
                </el-form-item>
                <el-form-item label="是否生成账单:" prop="isGenerated">
                    <el-select class="width220" v-model="obj.queryParams.isGenerated" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button icon="Search" @click="handleReset">重置</el-button>
                        <el-button type="primary" icon="Refresh" @click="handleSearch">查询</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row class="mb8">
            <el-col :span="24">
                <el-button type="primary" icon="Download" @click="handleExport">数据导出</el-button>
            </el-col>
        </el-row>
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" border style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column typee="index" label="序号" width="80" align="center" />
            <el-table-column prop="contractCode" label="合同编号" align="center" />
            <el-table-column prop="contractName" label="合同名称" align="center" />
            <el-table-column prop="customerCode" label="客户编号" align="center" />
            <el-table-column prop="customerName" label="客户名称" align="center" />
            <el-table-column prop="billMonth" label="账单月份" align="center" />
            <el-table-column prop="isGenerated" label="是否生成账单" align="center">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isGenerated" />
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="contractBillingDetails">

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    ids: [], // 选中的ID数组
    single: true, // 是否单选
    multiple: true, // 是否多选
    tableData: [], // 表格数据
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractName: null,
        customerName: null,
        billMonth: null,
        isGenerated: null
    } // 查询表单
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                contractCode: 'HT20230001',
                contractName: '合同1',
                customerCode: 'KH20230001',
                customerName: '客户A',
                billMonth: '2023-05',
                isGenerated: 'Y'
            },
            {
                id: 2,
                contractCode: 'HT20230002',
                contractName: '合同2',
                customerCode: 'KH20230002',
                customerName: '客户B',
                billMonth: '2023-05',
                isGenerated: 'N'
            },
            {
                id: 3,
                contractCode: '**********',
                contractName: '合同3',
                customerCode: '**********',
                customerName: '客户C',
                billMonth: '2023-06',
                isGenerated: 'Y'
            }
        ];
        obj.total = 3;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>