<!-- 实做办理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-select class="width220" v-model="obj.queryParams.customer" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable>
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="单立户名称:" prop="standAloneName">
                <el-select class="width220" v-model="obj.queryParams.standAloneName" placeholder="请选择" clearable>
                    <el-option v-for="item in standAloneOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入订单编号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="入离职状态:" prop="employmentStatus">
                <el-select class="width220" v-model="obj.queryParams.employmentStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in employmentStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单客服:" prop="customerService">
                <el-input class="width220" v-model="obj.queryParams.customerService" placeholder="请输入接单客服" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="福利起始月起:" prop="welfareStartMonthFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.welfareStartMonthFrom" type="month"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="福利起始月止:" prop="welfareStartMonthTo">
                <el-date-picker class="width220" v-model="obj.queryParams.welfareStartMonthTo" type="month"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="入职日期>=:" prop="entryDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.entryDateFrom" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="入职日期<=:" prop="entryDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.entryDateTo" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="办理日期>=:" prop="handleDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.handleDateFrom" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="办理日期<=:" prop="handleDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.handleDateTo" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="办停日期>=:" prop="stopDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.stopDateFrom" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="办停日期<=:" prop="stopDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.stopDateTo" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="签约方抬头:" prop="contractHeader">
                <el-select class="width220" v-model="obj.queryParams.contractHeader" placeholder="请选择" clearable>
                    <el-option v-for="item in contractHeaderOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="查询方式:" prop="queryMethod">
                <el-select class="width220" v-model="obj.queryParams.queryMethod" placeholder="请选择" clearable>
                    <el-option v-for="item in queryMethodOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Search" @click="personalOrderBtn">查询个人订单</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="personalBtn">个人办理</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="batchBtn">批量办理</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Back" @click="returnBtn">退回</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="exportDataBtn">导出数据</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column type="index" align="center" label="序号" width="55" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="手机号码" align="center" prop="phoneNumber" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="福利包编号" align="center" prop="welfarePackageNo" />
            <el-table-column label="订单编号" align="center" prop="orderNo" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户编号" align="center" prop="customerNo" />
            <el-table-column label="服务区域类型" align="center" prop="serviceAreaType" />
            <el-table-column label="小合同" align="center" prop="smallContract" />
            <el-table-column label="派单方" align="center" prop="dispatchingParty" />
            <el-table-column label="接单方" align="center" prop="receivingParty" />
            <el-table-column label="派单方客服" align="center" prop="dispatchingPartyCustomerService" />
            <el-table-column label="接单方客服" align="center" prop="receivingPartyCustomerService" />
            <el-table-column label="申请时间" align="center" prop="applyTime" />
            <el-table-column label="福利起始月" align="center" prop="welfareStartMonth" />
            <el-table-column label="账号" align="center" prop="account" />
            <el-table-column label="新增方式" align="center" prop="newMethod" />
            <el-table-column label="备注" align="center" prop="remark" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 查看个人订单 -->
        <order-contract-reserve-fund type="Practice" v-model:dialogShow="obj.dialogShow" title="查看个人订单"
            :form="obj.dialogForm" :tableData="obj.tableData" :tableData_no="obj.tableData_no" :isDetail="true" />

        <!-- 查看 -->
        <management-information v-model:dialogShow="obj.dialogShow2" title="查看" :form="obj.dialogForm"
            :tableData="obj.tableData" :tableData2="obj.tableData2" :isDetail="obj.isDetail" />
    </div>
</template>

<script setup name="PracticalHandling">
import { listScale } from "@/api/reonApi/scale";
import OrderContractReserveFund from '@/views/reonManage/components/dialog/orderContractReserveFund.vue'
import ManagementInformation from '@/views/reonManage/components/dialog/managementInformation.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 单立户选项
const standAloneOptions = [
    { value: '1', label: '单立户A' },
    { value: '2', label: '单立户B' },
    { value: '3', label: '单立户C' }
];

// 入离职状态选项
const employmentStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '停薪' }
];

// 签约方抬头选项
const contractHeaderOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 查询方式选项
const queryMethodOptions = [
    { value: '1', label: '按订单查询' },
    { value: '2', label: '按雇员查询' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        employeeName: '张三',
        idNumber: '110101199001011234',
        phoneNumber: '***********',
        orderNo: 'ORD20230001',
        welfarePackageName: '标准福利包',
        welfarePackageNo: 'WP001',
        welfareHandler: '公司A',
        accountType: '大户',
        customerName: '客户A',
        customerNo: 'C001',
        serviceAreaType: '一线城市',
        smallContract: '是',
        contractHeader: '公司A',
        currentMonthAmount: 2000,
        status: '在缴',
        applyTime: '2023-01-01 10:00:00',
        handleTime: '2023-01-02 14:30:00',
        handler: '李四',
        handleMonth: '2023-01',
        account: 'account001',
        stopHandler: '',
        stopTime: ''
    },
    {
        id: 2,
        employeeName: '李四',
        idNumber: '110101199001021234',
        phoneNumber: '***********',
        orderNo: 'ORD20230002',
        welfarePackageName: '高级福利包',
        welfarePackageNo: 'WP002',
        welfareHandler: '公司B',
        accountType: '单立户',
        customerName: '客户B',
        customerNo: 'C002',
        serviceAreaType: '二线城市',
        smallContract: '否',
        contractHeader: '公司B',
        currentMonthAmount: 3000,
        status: '在缴',
        applyTime: '2023-02-01 09:15:00',
        handleTime: '2023-02-02 11:20:00',
        handler: '王五',
        handleMonth: '2023-02',
        account: 'account002',
        stopHandler: '',
        stopTime: ''
    },
    {
        id: 3,
        employeeName: '王五',
        idNumber: '110101199001031234',
        phoneNumber: '***********',
        orderNo: 'ORD20230003',
        welfarePackageName: '基础福利包',
        welfarePackageNo: 'WP003',
        welfareHandler: '公司C',
        accountType: '大户',
        customerName: '客户C',
        customerNo: 'C003',
        serviceAreaType: '三线城市',
        smallContract: '是',
        contractHeader: '公司C',
        currentMonthAmount: 1500,
        status: '停缴',
        applyTime: '2023-03-01 14:20:00',
        handleTime: '2023-03-02 16:45:00',
        handler: '赵六',
        handleMonth: '2023-03',
        account: 'account003',
        stopHandler: '孙七',
        stopTime: '2023-04-01 10:30:00'
    }
];

// 模拟个人订单数据
const mockPersonalOrderData = [
    {
        id: 1,
        orderNo: 'ORD20230001',
        productName: '养老保险',
        status: '在缴',
        amount: 800,
        startDate: '2023-01-01',
        endDate: '2023-12-31'
    },
    {
        id: 2,
        orderNo: 'ORD20230001',
        productName: '医疗保险',
        status: '在缴',
        amount: 600,
        startDate: '2023-01-01',
        endDate: '2023-12-31'
    },
    {
        id: 3,
        orderNo: 'ORD20230001',
        productName: '失业保险',
        status: '在缴',
        amount: 300,
        startDate: '2023-01-01',
        endDate: '2023-12-31'
    },
    {
        id: 4,
        orderNo: 'ORD20230001',
        productName: '公积金',
        status: '在缴',
        amount: 300,
        startDate: '2023-01-01',
        endDate: '2023-12-31'
    }
];

// 模拟无需办理数据
const mockNoNeedHandleData = [
    {
        id: 1,
        orderNo: 'ORD20230004',
        productName: '生育保险',
        status: '无需办理',
        reason: '客户未要求'
    },
    {
        id: 2,
        orderNo: 'ORD20230004',
        productName: '工伤保险',
        status: '无需办理',
        reason: '客户未要求'
    }
];

// 模拟管理信息数据
const mockManagementData = [
    {
        id: 1,
        productName: '养老保险',
        base: 8000,
        personalRatio: '8%',
        personalAmount: 640,
        companyRatio: '16%',
        companyAmount: 1280,
        totalAmount: 1920
    },
    {
        id: 2,
        productName: '医疗保险',
        base: 8000,
        personalRatio: '2%',
        personalAmount: 160,
        companyRatio: '8%',
        companyAmount: 640,
        totalAmount: 800
    },
    {
        id: 3,
        productName: '失业保险',
        base: 8000,
        personalRatio: '0.5%',
        personalAmount: 40,
        companyRatio: '1.5%',
        companyAmount: 120,
        totalAmount: 160
    },
    {
        id: 4,
        productName: '公积金',
        base: 8000,
        personalRatio: '5%',
        personalAmount: 400,
        companyRatio: '5%',
        companyAmount: 400,
        totalAmount: 800
    }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        standAloneName: null, // 单立户名称
        orderNo: null, // 订单编号
        employeeName: null, // 雇员姓名
        idNumber: null, // 证件号码
        employmentStatus: null, // 入离职状态
        customerService: null, // 接单客服
        welfareStartMonthFrom: null, // 福利起始月起
        welfareStartMonthTo: null, // 福利起始月止
        entryDateFrom: null, // 入职日期>=
        entryDateTo: null, // 入职日期<=
        handleDateFrom: null, // 办理日期>=
        handleDateTo: null, // 办理日期<=
        stopDateFrom: null, // 办停日期>=
        stopDateTo: null, // 办停日期<=
        contractHeader: null, // 签约方抬头
        queryMethod: null, // 查询方式
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 显示对话框
    dialogShow2: false, // 显示对话框
    title: '', // 对话框标题
    ids: [], // 选中的id
    isDetail: false, // 是否查看
    tableData_no: mockNoNeedHandleData, // 无需办理数据
    tableData2: mockManagementData // 管理信息数据
})

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.welfareHandler) {
            filteredData = filteredData.filter(item => item.welfareHandler.includes(obj.queryParams.welfareHandler));
        }

        if (obj.queryParams.accountType) {
            filteredData = filteredData.filter(item => item.accountType.includes(obj.queryParams.accountType));
        }

        if (obj.queryParams.customer) {
            filteredData = filteredData.filter(item => item.customerName.includes(obj.queryParams.customer));
        }

        if (obj.queryParams.welfarePackageName) {
            filteredData = filteredData.filter(item => item.welfarePackageName.includes(obj.queryParams.welfarePackageName));
        }

        if (obj.queryParams.orderNo) {
            filteredData = filteredData.filter(item => item.orderNo.includes(obj.queryParams.orderNo));
        }

        if (obj.queryParams.employeeName) {
            filteredData = filteredData.filter(item => item.employeeName.includes(obj.queryParams.employeeName));
        }

        if (obj.queryParams.idNumber) {
            filteredData = filteredData.filter(item => item.idNumber.includes(obj.queryParams.idNumber));
        }


        if (obj.queryParams.handleDateFrom) {
            const startDate = new Date(obj.queryParams.handleDateFrom).getTime();
            filteredData = filteredData.filter(item => new Date(item.handleTime).getTime() >= startDate);
        }

        if (obj.queryParams.handleDateTo) {
            const endDate = new Date(obj.queryParams.handleDateTo).getTime();
            filteredData = filteredData.filter(item => new Date(item.handleTime).getTime() <= endDate);
        }

        if (obj.queryParams.stopDateFrom) {
            const startDate = new Date(obj.queryParams.stopDateFrom).getTime();
            filteredData = filteredData.filter(item => item.stopTime && new Date(item.stopTime).getTime() >= startDate);
        }

        if (obj.queryParams.stopDateTo) {
            const endDate = new Date(obj.queryParams.stopDateTo).getTime();
            filteredData = filteredData.filter(item => item.stopTime && new Date(item.stopTime).getTime() <= endDate);
        }

        if (obj.queryParams.contractHeader) {
            filteredData = filteredData.filter(item => item.contractHeader.includes(obj.queryParams.contractHeader));
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 查询个人订单
function personalOrderBtn() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行查询');
        return;
    }
    obj.dialogShow = true;
    obj.title = '查询个人订单';
    // 实际项目中应该调用API获取数据
    obj.tableData = mockPersonalOrderData;
}

/** 关闭弹窗 */
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {};
    obj.isDetail = false;
}

// 查看
function viewBtn() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行查看');
        return;
    }
    obj.dialogShow2 = true;
    obj.isDetail = true;
    obj.title = '查看详情';
    // 实际项目中应该调用API获取数据
    obj.tableData2 = mockManagementData;
}

function handleClose2() {
    obj.dialogShow2 = false;
    obj.dialogForm = {};
    obj.isDetail = false;
}


// 导出数据
function exportDataBtn() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped></style>