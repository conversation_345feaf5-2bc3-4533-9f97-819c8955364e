<!-- 政策文件上传 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="文件类型:" prop="fileType">
                <el-select class="width220" filterable v-model="obj.queryParams.fileType" placeholder="请选择文件类型"
                    clearable>
                    <el-option v-for="item in fileTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入人:" prop="uploadBy">
                <el-select class="width220" filterable v-model="obj.queryParams.uploadBy" placeholder="请选择导入人"
                    clearable>
                    <el-option v-for="item in uploadByOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">上传</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="文件编号" align="center" prop="fileNo" />
            <el-table-column label="文件名称" align="center" prop="fileName" />
            <el-table-column label="文件描述" align="center" prop="fileDesc" />
            <el-table-column label="上传人" align="center" prop="uploadBy" />
            <el-table-column label="上传时间" align="center" prop="uploadTime" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button link type="primary" icon="Download" @click="handleDownload(scope.row)">下载</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="48%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-suffix=":"
                label-width="auto">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="12">
                        <el-form-item label="文件类型" prop="fileType">
                            <el-radio-group v-model="obj.dialogForm.fileType">
                                <el-radio value="1">政策类</el-radio>
                                <el-radio value="2">制度类</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="可见范围" prop="visibleRange">
                            <el-radio-group v-model="obj.dialogForm.visibleRange">
                                <el-radio value="1" border>销售部</el-radio>
                                <el-radio value="2" border>客服部</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="文件描述" prop="fileDesc">
                    <el-input class="width420" type="textarea" :rows="3" v-model="obj.dialogForm.fileDesc"
                        placeholder="请输入文件描述" />
                </el-form-item>
                <el-divider content-position="left">文件上传</el-divider>
                <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="PolicyDocument_upload">
import { listImport } from "@/api/reonApi/import";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 文件类型选项
const fileTypeOptions = [
    { value: '1', label: '政策类' },
    { value: '2', label: '制度类' }
];

// 导入人选项
const uploadByOptions = [
    { value: 'admin', label: 'admin' },
    { value: 'user1', label: 'user1' },
    { value: 'user2', label: 'user2' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileType: null,
        uploadBy: null,
    },//查询表单
    rules: {
        fileType: [{ required: true, message: '请选择文件类型', trigger: 'change' }],
        visibleRange: [{ required: true, message: '请选择可见范围', trigger: 'change' }],
        fileDesc: [{ required: true, message: '请输入文件描述', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            fileNo: 'POL20230501001',
            fileName: '社保政策文件A.pdf',
            fileType: '1',
            fileDesc: '社保政策文件A的描述信息',
            uploadBy: 'admin',
            uploadTime: '2023-05-01 10:00:00',
            visibleRange: '1'
        },
        {
            id: 2,
            fileNo: 'POL20230502001',
            fileName: '公积金政策文件B.docx',
            fileType: '1',
            fileDesc: '公积金政策文件B的描述信息',
            uploadBy: 'admin',
            uploadTime: '2023-05-02 11:00:00',
            visibleRange: '1'
        },
        {
            id: 3,
            fileNo: 'SYS20230503001',
            fileName: '人事制度文件C.docx',
            fileType: '2',
            fileDesc: '人事制度文件C的描述信息',
            uploadBy: 'user1',
            uploadTime: '2023-05-03 12:00:00',
            visibleRange: '2'
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = JSON.parse(JSON.stringify(row || obj.tableData.find(item => obj.ids.includes(item.id))));
    obj.dialogShow = true;
    obj.title = "修改";
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}
/** 下载文件 */
function handleDownload(row) {
    proxy.$modal.msgSuccess('开始下载文件：' + row.fileName);
    // 模拟下载文件
    const link = document.createElement('a');
    link.href = '#';
    link.setAttribute('download', row.fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

getList();
</script>
<style lang="scss" scoped></style>