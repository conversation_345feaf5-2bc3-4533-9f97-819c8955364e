<!-- 供应商实做报表模块 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-select class="width220" v-model="obj.queryParams.supplierName" placeholder="请选择供应商名称" clearable>
                    <el-option v-for="item in supplierNameOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商账单模板:" prop="billTemplate">
                <el-select class="width220" v-model="obj.queryParams.billTemplate" placeholder="请选择账单模板" clearable>
                    <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报表年月:" prop="reportMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.reportMonth" type="month"
                    placeholder="请选择报表年月" clearable />
            </el-form-item>
            <el-form-item label="锁定状态:" prop="lockStatus">
                <el-select class="width220" v-model="obj.queryParams.lockStatus" placeholder="请选择锁定状态" clearable>
                    <el-option v-for="item in lockStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="生成状态:" prop="generateStatus">
                <el-select class="width220" v-model="obj.queryParams.generateStatus" placeholder="请选择生成状态" clearable>
                    <el-option v-for="item in generateStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付状态:" prop="payStatus">
                <el-select class="width220" v-model="obj.queryParams.payStatus" placeholder="请选择支付状态" clearable>
                    <el-option v-for="item in payStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="warning" icon="Download" @click="handleExport">导出报表</el-button>
                        <el-button type="warning" icon="Download" @click="handleExportAll">导出所有报表</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleGenerate">生成报表</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Lock" @click="handleLock">锁定</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Unlock" @click="handleUnlock">解锁</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商" align="center" fixed prop="supplierName" />
            <el-table-column label="账单模板名称" align="center" fixed prop="billTemplate" />
            <el-table-column label="是否单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="账单金额" align="center" prop="billAmount" />
            <el-table-column label="服务费" align="center" prop="serviceFee" />
            <el-table-column label="服务人次" align="center" prop="serviceCount" />
            <el-table-column label="锁定年月" align="center" prop="lockMonth" />
            <el-table-column label="生成状态" align="center" prop="generateStatus" />
            <el-table-column label="锁定人" align="center" prop="lockUser" />
            <el-table-column label="锁定状态" align="center" prop="lockStatus" />
            <el-table-column label="支付状态" align="center" prop="payStatus" />
            <el-table-column label="锁定时间" align="center" prop="lockTime" width="180" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>


<script setup name="SupplierImplementationManage_statement">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 锁定状态选项
const lockStatusOptions = [
    { value: '0', label: '未锁定' },
    { value: '1', label: '已锁定' }
];

// 生成状态选项
const generateStatusOptions = [
    { value: '0', label: '生成中' },
    { value: '1', label: '成功' },
    { value: '2', label: '失败' }
];

// 支付状态选项
const payStatusOptions = [
    { value: '0', label: '未支付' },
    { value: '1', label: '支付中' },
    { value: '2', label: '已支付' }

];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        billTemplate: null,
        reportMonth: null,
        lockStatus: null,
        generateStatus: null,
        payStatus: null,
    },//查询表单
    total: 10,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})


/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                supplierName: '供应商A',
                billTemplate: '模板1',
                billAmount: 10000,
                serviceFee: 500,
                serviceCount: 100,
                lockMonth: '2023-05',
                generateStatus: '已生成',
                lockUser: '张三',
                lockStatus: '已锁定',
                payStatus: '已支付',
                lockTime: '2023-05-15 10:30:00'
            },
            {
                id: 2,
                supplierName: '供应商B',
                billTemplate: '模板2',
                billAmount: 15000,
                serviceFee: 750,
                serviceCount: 150,
                lockMonth: '2023-05',
                generateStatus: '已生成',
                lockUser: '李四',
                lockStatus: '已锁定',
                payStatus: '支付中',
                lockTime: '2023-05-16 14:20:00'
            },
            {
                id: 3,
                supplierName: '供应商C',
                billTemplate: '模板3',
                billAmount: 8000,
                serviceFee: 400,
                serviceCount: 80,
                lockMonth: '2023-06',
                generateStatus: '未生成',
                lockUser: '',
                lockStatus: '未锁定',
                payStatus: '未支付',
                lockTime: ''
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 生成报表
function handleGenerate() {
    proxy.$modal.msgSuccess("报表生成成功");
    getList();
}

// 锁定
function handleLock() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要锁定的数据");
        return;
    }
    proxy.$modal.msgSuccess("锁定成功");
    getList();
}

// 解锁
function handleUnlock() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要解锁的数据");
        return;
    }
    proxy.$modal.msgSuccess("解锁成功");
    getList();
}

// 导出报表
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 导出所有报表
function handleExportAll() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

getList();
</script>
<style lang="scss" scoped></style>