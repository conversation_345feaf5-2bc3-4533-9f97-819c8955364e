<!-- 员工合同 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input readonly class="width220" @click="obj.customerDialogShow = true"
                    v-model="obj.queryParams.customerName" placeholder="请选择客户名称" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="签署日期>:" prop="signDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.signDateStart" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="签署日期<:" prop="signDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.signDateEnd" type="date" placeholder="请选择结束日期"
                    clearable />
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractCode">
                <el-input class="width220" v-model="obj.queryParams.contractCode" placeholder="请输入合同编号" clearable />
            </el-form-item>
            <el-form-item label="合同类型:" prop="contractType">
                <el-select class="width220" v-model="obj.queryParams.contractType" placeholder="请选择合同类型" clearable>
                    <el-option label="劳动合同" value="1" />
                    <el-option label="劳务派遣协议" value="2" />
                    <el-option label="实习协议" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同开始日期>:" prop="contractStartDate">
                <el-date-picker class="width220" v-model="obj.queryParams.contractStartDate" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="合同结束日期<:" prop="contractEndDate">
                <el-date-picker class="width220" v-model="obj.queryParams.contractEndDate" type="date"
                    placeholder="请选择结束日期" clearable />
            </el-form-item>
            <el-form-item label="签署状态:" prop="signStatus">
                <el-select class="width220" v-model="obj.queryParams.signStatus" placeholder="请选择签署状态" clearable>
                    <el-option label="待签署" value="0" />
                    <el-option label="已签署" value="1" />
                    <el-option label="已撤回" value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单客服:" prop="dispatchServiceStaff">
                <el-input class="width220" v-model="obj.queryParams.dispatchServiceStaff" placeholder="请输入派单客服"
                    clearable />
            </el-form-item>
            <el-form-item label="接单客服:" prop="receiveServiceStaff">
                <el-input class="width220" v-model="obj.queryParams.receiveServiceStaff" placeholder="请输入接单客服"
                    clearable />
            </el-form-item>
            <el-form-item label="入离职状态:" prop="employmentStatus">
                <el-select class="width220" v-model="obj.queryParams.employmentStatus" placeholder="请选择入离职状态" clearable>
                    <el-option label="在职" value="1" />
                    <el-option label="离职" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="增减员状态:" prop="staffChangeStatus">
                <el-select class="width220" v-model="obj.queryParams.staffChangeStatus" placeholder="请选择增减员状态"
                    clearable>
                    <el-option label="增员" value="1" />
                    <el-option label="减员" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单号:" prop="orderNumber">
                <el-input class="width220" v-model="obj.queryParams.orderNumber" placeholder="请输入订单号" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24" class="button-group">
                <el-button type="primary" plain icon="Check" @click="handleSubmit">提交</el-button>
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增员工合同</el-button>
                <el-button type="primary" plain icon="Edit" :disabled="obj.single"
                    @click="handleUpdate">修改员工合同</el-button>
                <el-button type="warning" plain icon="RefreshRight" @click="handleReSign">重新签署员工合同</el-button>
                <el-button type="info" plain icon="View" :disabled="obj.single" @click="handleView">查看员工合同</el-button>
                <el-button type="success" plain icon="Connection" @click="handleRenew">续约劳动合同</el-button>
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
                <el-button type="warning" plain icon="Close" @click="handleTerminate">终止劳动合同</el-button>
                <el-button type="info" plain icon="List" :disabled="obj.single"
                    @click="handleHistory">查看历史记录</el-button>
                <el-button type="success" plain icon="Download" @click="handleExport">导出数据</el-button>
                <el-button type="info" plain icon="Document" @click="handleDownloadTemplate">下载导入模版</el-button>
                <el-button type="primary" plain icon="Upload" @click="handleBatchImport">批量导入</el-button>
                <el-button type="primary" plain icon="Upload" @click="handleUploadContract">上传劳动合同</el-button>
                <el-button type="primary" plain icon="Upload" @click="handleBatchUploadContract">批量上传劳动合同</el-button>
                <el-button type="warning" plain icon="EditPen" :disabled="obj.single"
                    @click="handleModifyStatus">修改签署状态</el-button>
                <el-button type="primary" plain icon="Lock" @click="handleContractLock">契约锁</el-button>
            </el-col>
        </el-row>
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleView">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="签署日期" align="center" prop="signDate" />
            <el-table-column label="合同开始日期" align="center" prop="contractStartDate" />
            <el-table-column label="合同结束日期" align="center" prop="contractEndDate" />
            <el-table-column label="签署状态" align="center" prop="signStatus" />
            <el-table-column label="入离职状态" align="center" prop="employmentStatus" />
            <el-table-column label="增减员状态" align="center" prop="staffChangeStatus" />
        </el-table>

        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 客户名称 -->
        <Client :width="'35%'" v-model:show="obj.customerDialogShow" :isShow="false" @select="handleSelect" />

        <!-- 新增员工合同 -->
        <Contract v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm" :isAdd="obj.isAdd"
            :isUpdate="obj.isUpdate" :isLook="obj.isLook" :isRenewal="obj.isRenewal" menuName="employeeContract" />

        <!-- 查看历史记录 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="60%" append-to-body draggable
            @close="handleClose">
            <el-table :data="obj.tableData" border>
                <el-table-column label="员工合同编号" align="center" prop="contractCode" />
                <el-table-column label="正式工资" align="center" prop="formalSalary" />
                <el-table-column label="派遣期限起" align="center" prop="dispatchStartDate" />
                <el-table-column label="派遣期限止" align="center" prop="dispatchEndDate" />
                <el-table-column label="签署日期" align="center" prop="signDate" />
                <el-table-column label="劳动合同起始时间" align="center" prop="contractStartDate" />
                <el-table-column label="劳动合同结束时间" align="center" prop="contractEndDate" />
            </el-table>
        </el-dialog>

        <!-- 批量上传劳动合同 -->
        <el-dialog v-model="obj.dialogShow3" :title="obj.title" width="25%" append-to-body draggable
            @close="handleClose">
            <file-upload />
            <template #footer>
                <el-button type="primary" @click="handleUploadContract">上 传</el-button>
                <el-button @click="obj.dialogShow3 = false">取 消</el-button>
            </template>
        </el-dialog>

        <!-- 批量导入 -->
        <ImportRecord v-model:dialogShow="obj.dialogShow4" :title="obj.title" menuName="employeeContract" />
    </div>
</template>

<script setup name="EmployeeContract">
import Client from '@/views/reonManage/components/client.vue'
import Contract from '@/views/reonManage/components/dialog/contract.vue';
import ImportRecord from '@/views/reonManage/components/dialog/importRecord.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        employeeName: null,
        uniqueId: null,
        idNumber: null,
        signDateStart: null,
        signDateEnd: null,
        contractCode: null,
        contractType: null,
        contractStartDate: null,
        contractEndDate: null,
        signStatus: null,
        dispatchServiceStaff: null,
        receiveServiceStaff: null,
        employmentStatus: null,
        staffChangeStatus: null,
        orderNumber: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    showCustomerName: false,//显示客户名称
    dialogShow: false,//显示雇员姓名
    dialogShow2: false,//显示查看历史记录
    dialogShow3: false,//显示批量上传劳动合同
    dialogShow4: false,//显示批量导入
    customerDialogShow: false,//显示客户名称
    dialogForm: {},
    ids: [],//选中的id
    title: "",//标题

    isAdd: false,//新增
    isUpdate: false,//修改
    isLook: false,//查看
    isRenewal: false,//续签
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeName: '张三',
                uniqueId: 'EMP001',
                idNumber: '110101199001011234',
                customerName: '客户名称1',
                contractCode: 'CONTRACT001',
                contractType: '劳动合同',
                signDate: '2023-01-01',
                contractStartDate: '2023-01-01',
                contractEndDate: '2024-01-01',
                signStatus: '已签署',
                employmentStatus: '在职',
                staffChangeStatus: '增员',
                formalSalary: '10000',
                dispatchStartDate: '2023-01-01',
                dispatchEndDate: '2024-01-01'
            },
            {
                id: 2,
                employeeName: '李四',
                uniqueId: 'EMP002',
                idNumber: '110101199001021234',
                customerName: '客户名称2',
                contractCode: 'CONTRACT002',
                contractType: '劳务派遣协议',
                signDate: '2023-02-01',
                contractStartDate: '2023-02-01',
                contractEndDate: '2024-02-01',
                signStatus: '待签署',
                employmentStatus: '在职',
                staffChangeStatus: '增员',
                formalSalary: '12000',
                dispatchStartDate: '2023-02-01',
                dispatchEndDate: '2024-02-01'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}
/** 选择 */
function handleSelect(row) {
    if (row) {
        obj.queryParams.customerName = row.name;
        if (obj.dialogShow) {
            obj.dialogForm.customerName = row.name;
            obj.dialogForm.groupName = row.groupName || '';
        }
    } else {
        obj.queryParams.customerName = null;
        if (obj.dialogShow) {
            obj.dialogForm.customerName = null;
            obj.dialogForm.groupName = '';
        }
    }
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 关闭对话框
function handleClose() {
    obj.isAdd = false;
    obj.isUpdate = false;
    obj.isLook = false;
    obj.isRenewal = false;
    obj.dialogShow = false;
    obj.dialogForm = {};
}

// 新增员工合同
function handleAdd() {
    obj.dialogShow = true;
    obj.title = "新增";
    obj.isAdd = true;
}

// 修改员工合同
function handleUpdate() {
    obj.dialogShow = true;
    obj.title = "修改";
    obj.isUpdate = true;
}

// 重新签署员工合同
function handleReSign() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要重新签署的员工合同数据');
        return;
    }
    obj.dialogShow = true;
    obj.title = "重新签署员工合同";
    obj.lookShow = true;
    // 这里可以调用API获取员工合同详情
    const selectedRow = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedRow) {
        obj.dialogForm = JSON.parse(JSON.stringify(selectedRow));
    }
}

// 查看员工合同
function handleView() {
    obj.dialogShow = true;
    obj.title = "查看";
    obj.isLook = true;
}

// 续约劳动合同
function handleRenew() {
    obj.dialogShow = true;
    obj.title = "续签";
    obj.isRenewal = true;
}

// 终止劳动合同
function handleTerminate() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要终止的员工合同数据');
        return;
    }
    proxy.$modal.confirm('是否确认终止选中的员工合同？').then(function () {
        // 这里可以调用API终止员工合同
        proxy.$modal.msgSuccess('终止劳动合同成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 查看历史记录
function handleHistory() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要查看历史记录的员工合同数据');
        return;
    }
    obj.dialogShow2 = true;
    obj.title = "查看员工合同历史";
    // 这里可以调用API获取员工合同历史记录
}

// 导出数据
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 下载导入模版
function handleDownloadTemplate() {
    proxy.$modal.msgSuccess('下载导入模版成功');
}

// 批量导入
function handleBatchImport() {
    obj.dialogShow4 = true;
    obj.title = "批量导入";
}

// 上传劳动合同
function handleUploadContract() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要上传劳动合同的数据');
        return;
    }
    proxy.$modal.msgSuccess('上传劳动合同成功');
}

// 批量上传劳动合同
function handleBatchUploadContract() {
    obj.dialogShow3 = true;
    obj.title = "批量上传劳动合同";
}

// 修改签署状态
function handleModifyStatus() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要修改签署状态的数据');
        return;
    }
    proxy.$modal.msgSuccess('修改签署状态成功');
}

// 契约锁
function handleContractLock() {
    proxy.$modal.msgSuccess('契约锁操作成功');
}

// 提交
function handleSubmit() {
    proxy.$modal.msgSuccess('提交成功');
    obj.dialogShow = false;
}

getList();
</script>
<style lang="scss" scoped>
.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    /* 这里控制按钮之间的间距 */
}

.button-group .el-button {
    margin: 5px 0;
    /* 这里控制按钮上下的间距 */
    margin-right: 0;
    /* 覆盖Element UI的默认右边距 */
}
</style>