<!-- 实做停办 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="收费截止月:" prop="chargeEndMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.chargeEndMonth" placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-select class="width220" v-model="obj.queryParams.customer" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="单立户名称:" prop="standAloneAccountName">
                <el-select class="width220" v-model="obj.queryParams.standAloneAccountName" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="入离职状态:" prop="employmentStatus">
                <el-select class="width220" v-model="obj.queryParams.employmentStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单客服:" prop="orderCustomerService">
                <el-input class="width220" v-model="obj.queryParams.orderCustomerService" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="离职日期>=:" prop="resignDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.resignDateStart" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="离职日期<=:" prop="resignDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.resignDateEnd" placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="办理日期>=:" prop="processingDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.processingDateStart" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="办理日期<=:" prop="processingDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.processingDateEnd" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNumber">
                <el-input class="width220" v-model="obj.queryParams.orderNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="办理人:" prop="processor">
                <el-input class="width220" v-model="obj.queryParams.processor" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Search" @click="personalOrderBtn">查询个人订单</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="stopBtn">停办</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="View" @click="viewBtn">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="exportDataBtn">导出数据</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.employeeList"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="是否完全核销" align="center" prop="isFullyWrittenOff" />
            <el-table-column label="状态" align="center" prop="status" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="手机号码" align="center" prop="phoneNumber" />
            <el-table-column label="订单编号" align="center" prop="orderNumber" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="福利包编号" align="center" prop="welfarePackageNumber" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户编号" align="center" prop="customerNumber" />
            <el-table-column label="服务区域类型" align="center" prop="serviceAreaType" />
            <el-table-column label="小合同" align="center" prop="subContract" />
            <el-table-column label="签约方抬头" align="center" prop="signingPartyHeader" />
            <el-table-column label="派单方" align="center" prop="dispatchParty" />
            <el-table-column label="接单方" align="center" prop="receiveParty" />
            <el-table-column label="派单方客服" align="center" prop="dispatchCustomerService" />
            <el-table-column label="接单方客服" align="center" prop="receiveCustomerService" />
            <el-table-column label="社保工资" align="center" prop="socialSecuritySalary" />
            <el-table-column label="公积金工资" align="center" prop="housingFundSalary" />
            <el-table-column label="退回备注" align="center" prop="returnRemark" />
            <el-table-column label="入职日期" align="center" prop="entryDate" />
            <el-table-column label="福利起始月" align="center" prop="welfareStartMonth" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 查看个人订单 -->
        <order-contract-reserve-fund type="ActuallyStopDoingIt" v-model:dialogShow="obj.dialogShow" title="查看个人订单"
            :form="obj.dialogForm" :tableData="obj.employeeList" :tableData_no="obj.tableData_no" :isDetail="true" />

        <!-- 停办 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="20%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" label-width="auto">
                <el-form-item label="福利截止月" prop="welfareEndMonth">
                    <el-select class="width220" v-model="obj.dialogForm.welfareEndMonth" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="停办备注" prop="stopRemark">
                    <el-input type="textarea" v-model="obj.dialogForm.stopRemark" placeholder="请输入" clearable />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="obj.dialogShow2 = false">取消</el-button>
                <el-button type="primary" @click="save">停办</el-button>
            </template>
        </el-dialog>
        <!-- 查看 -->
        <management-information v-model:dialogShow="obj.dialogShow3" title="查看" :form="obj.dialogForm"
            :tableData="obj.employeeList" :tableData2="obj.tableData2" :isDetail="obj.isDetail" />


    </div>
</template>

<script setup name="ImplementationSuspension">
import OrderContractReserveFund from '@/views/reonManage/components/dialog/orderContractReserveFund.vue'
import ManagementInformation from '@/views/reonManage/components/dialog/managementInformation.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        welfarePackageName: null, // 福利包名称
        chargeEndMonth: null, // 收费截止月
        employeeName: null, // 雇员姓名
        idNumber: null, // 证件号码
        customer: null, // 客户
        standAloneAccountName: null, // 单立户名称
        employmentStatus: null, // 入离职状态
        orderCustomerService: null, // 接单客服
        resignDateStart: null, // 离职日期开始
        resignDateEnd: null, // 离职日期结束
        processingDateStart: null, // 办理日期开始
        processingDateEnd: null, // 办理日期结束
        orderNumber: null, // 订单编号
        processor: null, // 办理人
    },//查询表单
    total: 0,//总条数
    employeeList: [
        {
            id: 1,
            employeeName: "张三",
            isFullyWrittenOff: "是",
            status: "已停办",
            idNumber: "110101199001011234",
            phoneNumber: "***********",
            orderNumber: "ORD20230001",
            welfareHandler: "社保局",
            welfarePackageName: "标准社保套餐",
            welfarePackageNumber: "**********",
            customerName: "ABC公司",
            customerNumber: "CUS20230001",
            serviceAreaType: "一线城市",
            subContract: "**********",
            signingPartyHeader: "ABC公司人力资源部",
            dispatchParty: "总部",
            receiveParty: "分部",
            dispatchCustomerService: "李四",
            receiveCustomerService: "王五",
            socialSecuritySalary: 10000,
            housingFundSalary: 10000,
            returnRemark: "",
            entryDate: "2023-01-01",
            welfareStartMonth: "2023-02"
        },
    ],//雇员列表
    dialogForm: {},//表单
    dialogShow: false,//显示个人订单对话框
    dialogShow2: false,//显示停办对话框
    dialogShow3: false,//显示查看对话框
    title: '',//对话框标题
    selectedIds: [],//选中的id
    isDetail: false,//是否查看
    tableData_no: [], // 非社保公积金数据
    tableData2: [], // 查看对话框表格数据
})

/** 列表 */
function getList() {
    // obj.loading = true;
    // listScale(obj.queryParams).then(response => {
    //     obj.employeeList = response.rows;
    //     obj.total = response.total;
    //     obj.loading = false;
    // });
}



// 重置表单
function resetForm() {
    obj.dialogForm = {
        welfareEndMonth: '',
        stopRemark: ''
    };
    proxy.resetForm("formRef");
}

// 保存停办信息
function save() {
    // 这里可以添加表单验证
    proxy.$modal.confirm('确认停办选中的记录吗？').then(function () {
        // 这里可以调用API保存数据
        console.log('停办信息:', obj.dialogForm);

        // 模拟保存成功
        proxy.$modal.msgSuccess("停办成功");
        obj.dialogShow2 = false;
        resetForm();
        getList();
    }).catch(() => { });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框
function handleSelectionChange(selection) {
    obj.selectedIds = selection.map(item => item.id)
}

// 查询个人订单
function personalOrderBtn() {
    obj.dialogShow = true;
}
/** 关闭弹窗 */
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {};
    obj.isDetail = false;
}

// 停办
function stopBtn() {
    obj.dialogShow2 = true;
    obj.title = '停办';
}

// 查看
function viewBtn() {
    obj.dialogShow3 = true;
    obj.isDetail = true;
}
function handleClose2() {
    obj.dialogShow3 = false;
    obj.dialogForm = {};
    obj.isDetail = false;
}
// 导出数据
function exportDataBtn() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}


getList();
</script>
<style lang="scss" scoped></style>