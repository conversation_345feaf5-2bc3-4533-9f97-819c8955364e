<!-- 商保产品 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="供应商/联系人" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="保险名称:" prop="province">
                <el-select class="width220" v-model="obj.dialogForm.type" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="保险类型:" prop="city">
                <el-select class="width220" v-model="obj.dialogForm.type" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="保险形式:" prop="city">
                <el-select class="width220" v-model="obj.dialogForm.type" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="状态:" prop="city">
                <el-select class="width220" v-model="obj.dialogForm.type" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增产品</el-button>
            </el-col>
            <el-col :span="1.5">
                <!--  :disabled="obj.single" -->
                <el-button type="success" plain icon="Edit" @click="handleUpdate">修改产品</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Open" @click="handleEnable">启用</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="TurnOff" @click="handleForbidden">禁用</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="SwitchButton" :disabled="obj.multiple"
                    @click="handleDelete">暂停</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single"
                    @click="handleReaddition">追加产品</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="合同编号" align="center" prop="contractNo" />
            <el-table-column label="合同名称" align="center" prop="contractName">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.contractName" />
                </template>
            </el-table-column>
            <el-table-column label="保险名称" align="center" prop="insuranceName">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.insuranceName" />
                </template>
            </el-table-column>
            <el-table-column label="保险形式" align="center" prop="insuranceForm">
                <template #default="scope">
                    <span>{{ scope.row.insuranceForm }}</span>
                </template>
            </el-table-column>
            <el-table-column label="保险类型" align="center" prop="insuranceType">
                <template #default="scope">
                    <dict-tag :options="sys_job_group" :value="scope.row.insuranceType" />
                </template>
            </el-table-column>
            <el-table-column label="供应商" align="center" prop="supplierName">
                <template #default="scope">
                    <span>{{ scope.row.supplierName }}</span>
                </template>
            </el-table-column>
            <el-table-column label="合同开始时间" align="center" prop="contractStartTime">
                <template #default="scope">
                    <span>{{ scope.row.contractStartTime }}</span>
                </template>
            </el-table-column>
            <el-table-column label="是否启用" align="center" prop="isActive">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isActive" />
                </template>
            </el-table-column>
            <!-- <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column> -->
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="55%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="供应商:" prop="type">
                    <el-select :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.supplierName"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="保险名称:" prop="type">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.insuranceName"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="保险形式:" prop="type">
                    <el-select :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.insuranceForm"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="保险类型:" prop="type">
                    <el-select :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.insuranceType"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>

                <el-form-item v-if="obj.isAdd" label="供应商合同:" prop="supplierContract">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.supplierContract"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item v-if="obj.isEdit || obj.isDetail" label="合同名称:" prop="contractName">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.contractName"
                        placeholder="请输入" />
                </el-form-item>

                <el-form-item v-if="obj.isReaddition || obj.isDetail" label="合同开始日期:" prop="contractStartTime">
                    <el-date-picker :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.contractStartTime"
                        type="date" placeholder="请选择" />
                </el-form-item>
                <el-form-item v-if="obj.isReaddition || obj.isDetail" label="合同结束日期:" prop="contractEndTime">
                    <el-date-picker :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.contractEndTime"
                        type="date" placeholder="请选择" />
                </el-form-item>
                <el-form-item v-if="obj.isReaddition || obj.isDetail" label="账单日:" prop="billDay">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.billDay"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item v-if="obj.isReaddition || obj.isDetail" label="付款日:" prop="paymentDay">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.paymentDay"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item v-if="obj.isReaddition || obj.isDetail" label="备注:" prop="remark">
                    <el-input :disabled="obj.isDetail" class="width420" type="textarea" v-model="obj.dialogForm.remark"
                        placeholder="请输入内容" />
                </el-form-item>
                <div v-if="obj.isEdit || obj.isReaddition || obj.isDetail">
                    <el-divider content-position="left">固定保额</el-divider>
                    <div class="mt10 mb10" v-if="obj.isReaddition">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增产品</el-button>
                        <el-button type="danger" plain icon="Delete" @click="handleDelete">删除产品</el-button>
                    </div>
                    <el-table :data="obj.tableData" border>
                        <el-table-column label="序号" align="center" type="index" width="60" />
                        <el-table-column label="产品名称" align="center" prop="productName" />
                        <el-table-column label="基准月付成本" align="center" prop="monthlyCost" />
                        <el-table-column label="基准年付成本" align="center" prop="yearlyCost" />
                        <el-table-column label="指导价系数" align="center" prop="guidePriceFactor" />
                        <el-table-column label="基准月付指导价" align="center" width="120" prop="monthlyGuidePrice" />
                        <el-table-column label="基准年付指导价" align="center" width="120" prop="yearlyGuidePrice" />
                        <el-table-column label="说明" align="center" prop="description" />
                        <el-table-column label="是否启用" align="center" prop="isActive" />
                        <el-table-column label="特别说明" align="center" prop="specialNote" />
                    </el-table>
                    <el-divider content-position="left">固定保额子类</el-divider>
                    <div class="mt10 mb10" v-if="obj.isReaddition">
                        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增保险项</el-button>
                        <el-button type="danger" plain icon="Delete" @click="handleDelete">删除保险项</el-button>
                    </div>
                    <el-table :data="obj.tableData" border>
                        <el-table-column label="产品名称" align="center" prop="productName" />
                        <el-table-column label="产品类型" align="center" prop="productType" />
                        <el-table-column label="基准保额是否依据说明" align="center" prop="isBaseOnDescription" />
                        <el-table-column label="说明" align="center" prop="description" />
                        <el-table-column label="基准保额(万元)" align="center" prop="baseInsuranceAmount" />
                    </el-table>
                </div>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup name="CommercialInsurance_products">
import { listInstance, delInstance } from '@/api/reonApi/instance';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        type: null,
        contact: null,
        phone: null,
        address: null,
    },//查询表单
    rules: {
        name: [
            { required: true, message: "供应商名称不能为空", trigger: "blur" }
        ],
        type: [
            { required: true, message: "供应商类型不能为空", trigger: "blur" }
        ],
        contact: [
            { required: true, message: "联系人不能为空", trigger: "blur" }
        ],
        phone: [
            { required: true, message: "联系电话不能为空", trigger: "blur" }
        ],
        address: [
            { required: true, message: "供应商地址不能为空", trigger: "blur" }
        ],
    },
    total: 0,//总条数
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
    isAdd: false,//是否新增
    isEdit: false,//是否编辑
    isDetail: false,//是否详情
    isReaddition: false,//是否追加
    tableData: [
        {
            id: 1,
            contractNo: 'HT20230701001',
            contractName: '北京科技有限公司商保合同',
            insuranceName: '企业员工综合医疗保险A',
            insuranceForm: '团体保险',
            insuranceType: '医疗保险',
            supplierName: '北京保险有限公司',
            contractStartTime: '2023-07-01',
            contractEndTime: '2024-06-30',
            isActive: '1',
            name: '企业员工综合医疗保险A',
            type: '医疗保险',
            isEdit: '0'
        },
        {
            id: 2,
            contractNo: 'HT20230702001',
            contractName: '上海信息技术有限公司商保合同',
            insuranceName: '企业员工意外伤害保险B',
            insuranceForm: '个人保险',
            insuranceType: '意外保险',
            supplierName: '上海保险有限公司',
            contractStartTime: '2023-07-02',
            contractEndTime: '2024-07-01',
            isActive: '0',
            name: '企业员工意外伤害保险B',
            type: '意外保险',
            isEdit: '1'
        }
    ]//列表
});


/** 列表 */
function getList() {
    obj.loading = true;
    listInstance(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


// 表单重置
function reset() {
    obj.dialogForm = {};
    obj.isAdd = false;
    obj.isEdit = false;
    obj.isDetail = false;
    obj.isReaddition = false;

    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    console.log(obj.queryParams);
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.isAdd = true;
    obj.dialogShow = true;
    obj.title = "新增报价单";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    obj.isEdit = true;
    obj.dialogShow = true;
    obj.title = "编辑产品";
    return
    const _id = row.id || obj.ids
    getUsers(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改报价单";
    });
}
// 详情
function handleDetail(row) {
    console.log(row);
    reset();
    obj.isDetail = true;
    obj.dialogShow = true;
    obj.title = "详情";
}
// 追加
function handleReaddition() {
    reset();
    obj.isReaddition = true;
    obj.dialogShow = true;
    obj.title = "追加";
}
// 启用
function handleEnable() {
    proxy.$modal.confirm('是否确认启用？').then(function () {
        return delUsers(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("启用成功");
    }).catch(() => { });
}
// 禁用
function handleForbidden() {
    proxy.$modal.confirm('是否确认禁用？').then(function () {
        return delUsers(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("禁用成功");
    }).catch(() => { });
}


/** 提交按钮 */
function submitForm() {
    console.log(obj.dialogForm);
    return
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

getList();
</script>
<style lang="scss" scoped></style>