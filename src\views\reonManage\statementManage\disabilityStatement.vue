<!-- 残障金报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="接单方:" prop="receiver">
                <el-select class="width220" v-model="obj.queryParams.receiver" placeholder="请选择接单方" clearable>
                    <el-option v-for="item in receiverOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单年月(起):" prop="billMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonthStart" type="month"
                    placeholder="请选择起始月份" clearable />
            </el-form-item>
            <el-form-item label="账单年月(止):" prop="billMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonthEnd" type="month"
                    placeholder="请选择结束月份" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="withholdingAgent">
                <el-input class="width220" v-model="obj.queryParams.withholdingAgent" placeholder="请输入扣缴义务人名称"
                    clearable />
            </el-form-item>
            <el-form-item label="账单类型:" prop="billType">
                <el-select class="width220" v-model="obj.queryParams.billType" placeholder="请选择账单类型" clearable>
                    <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row class="mb8" :gutter="10">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
                        <el-button icon="Refresh" @click="handleReset">重置</el-button>
                        <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" border style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="groupName" label="集团名称" align="center" />
            <el-table-column prop="customerCode" label="客户编号" align="center" />
            <el-table-column prop="customerName" label="客户名称" align="center" />
            <el-table-column prop="contractCode" label="合同编号" align="center" />
            <el-table-column prop="contractName" label="合同名称" align="center" />
            <el-table-column prop="contractType" label="合同类型" align="center" />
            <el-table-column prop="contractSubtype" label="合同二级类型" align="center" />
            <el-table-column prop="dispatchLocation" label="派单地" align="center" />
            <el-table-column prop="dispatchService" label="派单客服" align="center" />
            <el-table-column prop="billMonth" label="账单年月" align="center" />
            <el-table-column prop="employeeName" label="员工姓名" align="center" />
            <el-table-column prop="idNumber" label="身份证号" align="center" />
            <el-table-column prop="billType" label="账单类型" align="center" />
            <el-table-column prop="insuredCity" label="参保城市" align="center" />
            <el-table-column prop="receiver" label="接单方" align="center" />
            <el-table-column prop="chargeMonth" label="收费年月" align="center" />
            <el-table-column prop="companyBase" label="企业基数" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.companyBase) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="companyRatio" label="企业比例(%)" align="center">
                <template #default="scope">
                    <span>{{ scope.row.companyRatio }}%</span>
                </template>
            </el-table-column>
            <el-table-column prop="socialSecurityDisabilityAmount" label="社保残保金金额" align="center" width="140">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.socialSecurityDisabilityAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="salaryCity" label="发薪城市" align="center" />
            <el-table-column prop="withholdingAgent" label="扣缴义务人名字" align="center" width="140" />
            <el-table-column prop="salaryMonth" label="工资所属月" align="center" />
            <el-table-column prop="salaryAmount" label="应发工资金额" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.salaryAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="salaryDisabilityAmount" label="工资残保金额" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.salaryDisabilityAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="disabilityFilingDocument" label="残障金备案文件" align="center">
                <template #default="scope">
                    <el-button text @click="handleViewDocument(scope.row)"
                        v-if="scope.row.disabilityFilingDocument">查看</el-button>
                    <span v-else>无</span>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="DisabilityStatement">

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 接单方选项
const receiverOptions = ref([
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '个人' }
]);

// 账单类型选项
const billTypeOptions = ref([
    { value: '1', label: '社保残保金' },
    { value: '2', label: '工资残保金' }
]);

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    tableData: [], // 表格数据
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        receiver: null,
        billMonthStart: null,
        billMonthEnd: null,
        customerName: null,
        withholdingAgent: null,
        billType: null
    } // 查询表单
})



/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                groupName: '集团A',
                customerCode: 'KH20230001',
                customerName: '客户A',
                contractCode: 'HT20230001',
                contractName: '合同1',
                contractType: '劳务派遣',
                contractSubtype: '社保代缴',
                dispatchLocation: '北京',
                dispatchService: '客服1',
                billMonth: '2023-05',
                employeeName: '张三',
                idNumber: '110101199001011234',
                billType: '社保残保金',
                insuredCity: '北京',
                receiver: '公司A',
                chargeMonth: '2023-05',
                companyBase: 10000.00,
                companyRatio: 1.5,
                socialSecurityDisabilityAmount: 150.00,
                salaryCity: '北京',
                withholdingAgent: '北京公司',
                salaryMonth: '2023-05',
                salaryAmount: 8000.00,
                salaryDisabilityAmount: 120.00,
                disabilityFilingDocument: '文件1.pdf'
            },
            {
                id: 2,
                groupName: '集团B',
                customerCode: 'KH20230002',
                customerName: '客户B',
                contractCode: 'HT20230002',
                contractName: '合同2',
                contractType: '劳务外包',
                contractSubtype: '工资代发',
                dispatchLocation: '上海',
                dispatchService: '客服2',
                billMonth: '2023-06',
                employeeName: '李四',
                idNumber: '310101199002022345',
                billType: '工资残保金',
                insuredCity: '上海',
                receiver: '公司B',
                chargeMonth: '2023-06',
                companyBase: 12000.00,
                companyRatio: 1.6,
                socialSecurityDisabilityAmount: 192.00,
                salaryCity: '上海',
                withholdingAgent: '上海公司',
                salaryMonth: '2023-06',
                salaryAmount: 10000.00,
                salaryDisabilityAmount: 160.00,
                disabilityFilingDocument: null
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 查看文件操作 */
function handleViewDocument(row) {
    proxy.$modal.msgInfo('查看文件：' + row.disabilityFilingDocument);
    // 实际开发时可以调用文件预览接口
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>