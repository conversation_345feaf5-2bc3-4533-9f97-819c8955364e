import useTagsViewStore from "@/store/modules/tagsView";
import router from "@/router";

export default {
  /**
   * 刷新当前tab页签
   * 该函数用于刷新当前页面，以实现重新加载页面数据或重置页面状态
   * @param {Object} obj - 可选参数，包含需要刷新的页面的名称、路径和查询参数
   *                      如果未提供，则函数会尝试根据当前路由信息构建此对象
   * @returns {Promise} - 返回一个Promise对象，表示页面刷新操作的异步结果
   */
  refreshPage(obj) {
    // 获取当前路由的路径、查询参数和匹配的路由信息
    const { path, query, matched } = router.currentRoute.value;

    // 如果未提供obj参数，则根据当前路由信息构建
    if (obj === undefined) {
      matched.forEach((m) => {
        // 检查匹配的路由信息中是否包含有效的组件名称
        if (m.components && m.components.default && m.components.default.name) {
          // 排除布局和父视图组件，以找到需要刷新的实际页面组件
          if (!["Layout", "ParentView"].includes(m.components.default.name)) {
            obj = { name: m.components.default.name, path: path, query: query };
          }
        }
      });
    }

    // 调用TagsViewStore的delCachedView方法删除缓存的视图，并在删除成功后执行页面刷新
    return useTagsViewStore()
      .delCachedView(obj)
      .then(() => {
        // 获取需要刷新的页面的路径和查询参数
        const { path, query } = obj;
        // 通过替换路由到重定向路径来实现页面刷新
        router.replace({
          path: "/redirect" + path,
          query: query,
        });
      });
  },
  /**
   * 关闭当前tab页签，打开新页签
   *
   * @param {Object} obj - 可选参数，包含新页签的相关信息
   * @returns {void} 如果提供了obj参数，则导航到新页签
   */
  closeOpenPage(obj) {
    // 删除当前视图以关闭当前tab页签
    useTagsViewStore().delView(router.currentRoute.value);

    // 如果提供了新页签的信息，则导航到新页签
    if (obj !== undefined) {
      return router.push(obj);
    }
  },
  /**
   * 关闭指定的tab页签
   * 如果未提供具体对象，则关闭当前页面的tab
   * @param {Object} obj - 可选参数，指定要关闭的tab对象
   * @returns {Promise} - 当关闭当前tab时，返回一个Promise对象，用于处理路由跳转
   */
  closePage(obj) {
    // 当obj参数未定义时，表示需要关闭当前tab
    if (obj === undefined) {
      // 使用useTagsViewStore删除当前视图，并获取更新后的访问视图列表
      return useTagsViewStore()
        .delView(router.currentRoute.value)
        .then(({ visitedViews }) => {
          // 获取最新的视图，即访问视图列表中的最后一个视图
          const latestView = visitedViews.slice(-1)[0];
          // 如果存在最新的视图，则跳转到该视图的路径
          if (latestView) {
            return router.push(latestView.fullPath);
          }
          // 如果不存在最新的视图，则跳转到根路径
          return router.push("/");
        });
    }
    // 当obj参数已定义时，直接删除指定的视图tab
    return useTagsViewStore().delView(obj);
  },
  /**
   * 关闭所有tab页签
   *
   * 本函数通过调用TagsViewStore的delAllViews方法来删除所有已打开的视图，实现关闭所有tab页签的功能
   * 不接受任何参数
   *
   * @returns {any} 返回delAllViews方法的执行结果，具体类型取决于delAllViews的实现
   */
  closeAllPage() {
    return useTagsViewStore().delAllViews();
  },
  /**
   * 关闭左侧tab页签
   *
   * 该方法用于关闭当前选中tab左侧的所有tab页签它通过调用TagsViewStore中的delLeftTags方法来实现
   * 如果提供了一个对象参数obj，则使用该对象作为参数；否则，使用当前路由作为参数
   *
   * @param {Object} obj - 可选参数，包含路由信息的对象，如果没有提供，则使用当前路由
   * @returns {Any} - 返回值取决于TagsViewStore中delLeftTags方法的实现，可能是修改后的tags列表或其他类型的返回值
   */
  closeLeftPage(obj) {
    return useTagsViewStore().delLeftTags(obj || router.currentRoute.value);
  },
  /**
   * 关闭右侧tab页签
   *
   * 此函数用于关闭当前选中tab右侧的所有tab页签它通过调用useTagsViewStore的delRightTags方法来实现
   * 如果没有提供特定的对象作为参数，则默认使用当前路由作为参数
   *
   * @param {Object} obj - 可选参数，指定要关闭右侧tab的起始tab对象如果不传递此参数，则使用当前路由对应的tab
   * @returns {Any} - 返回值取决于useTagsViewStore().delRightTags方法的返回值
   */
  closeRightPage(obj) {
    return useTagsViewStore().delRightTags(obj || router.currentRoute.value);
  },
  /**
   * 关闭其他tab页签
   *
   * 此函数用于关闭除给定对象所在tab之外的所有tab页签它通过调用useTagsViewStore().delOthersViews方法来实现
   * 如果没有提供对象，则使用当前路由作为参数
   *
   * @param {Object} obj - 可选参数如果提供，将关闭除该对象所在tab之外的所有tab页签
   * @returns {Any} - 返回值类型取决于useTagsViewStore().delOthersViews方法的实现
   */
  closeOtherPage(obj) {
    return useTagsViewStore().delOthersViews(obj || router.currentRoute.value);
  },
  /**
   * 打开新的tab页签
   * @param {string} url - 需要打开的页面URL
   * @returns {Promise} - 返回路由跳转的Promise对象
   */
  openPage(url) {
    return router.push(url);
  },
  /**
   * 修改tab页签
   *
   * @param {Object} obj - 包含需要更新的页面信息的对象
   * @returns {Promise} - 返回一个Promise对象，表示异步操作的结果
   */
  updatePage(obj) {
    return useTagsViewStore().updateVisitedView(obj);
  },
};
