<!-- 个税申报信息采集 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="收款方:" prop="payee">
                <el-input class="width220" v-model="obj.queryParams.payee" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="收款方银行卡:" prop="payeeBankCard">
                <el-input class="width220" v-model="obj.queryParams.payeeBankCard" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="支付类型:" prop="paymentType">
                <el-select class="width220" v-model="obj.queryParams.paymentType" placeholder="请选择" clearable>
                    <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请人:" prop="applicant">
                <el-input class="width220" v-model="obj.queryParams.applicant" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="支付方式:" prop="paymentMethod">
                <el-select class="width220" v-model="obj.queryParams.paymentMethod" placeholder="请选择" clearable>
                    <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请支付时间>=:" prop="applyTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeStart" type="date"
                    placeholder="选择开始日期" />
            </el-form-item>
            <el-form-item label="申请支付时间<=:" prop="applyTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeEnd" type="date"
                    placeholder="选择结束日期" />
            </el-form-item>
            <el-form-item label="工资支付日期:" prop="salaryPaymentDate">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryPaymentDate" type="date"
                    placeholder="选择日期" />
            </el-form-item>
            <el-form-item label="制单状态:" prop="documentStatus">
                <el-select class="width220" v-model="obj.queryParams.documentStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in documentStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否退票重发:" prop="isReissue">
                <el-select class="width220" v-model="obj.queryParams.isReissue" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="申报个税公司名称" align="center" prop="taxCompanyName" />
            <el-table-column label="扣缴义务人类型" align="center" prop="withholdingType" />
            <el-table-column label="工号" align="center" prop="employeeId" />
            <el-table-column label="姓名" align="center" prop="employeeName" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="证件类型" align="center" prop="idType" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="支付方式" align="center" prop="paymentMethod" />
            <el-table-column label="支付状态" align="center" prop="paymentStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.paymentStatus === '1' ? 'success' : scope.row.paymentStatus === '2' ? 'warning' : 'info'">
                        {{ scope.row.paymentStatus === '1' ? '已支付' : scope.row.paymentStatus === '2' ? '处理中' : '未支付' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="申请支付时间" align="center" prop="applyTime" />
            <el-table-column label="工资支付日期" align="center" prop="salaryPaymentDate" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="PayYourOwnWagesDocument">

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 支付类型选项
const paymentTypeOptions = [
    { value: '1', label: '工资' },
    { value: '2', label: '奖金' },
    { value: '3', label: '津贴' },
    { value: '4', label: '报销' },
    { value: '5', label: '其他' }
];

// 支付方式选项
const paymentMethodOptions = [
    { value: '1', label: '银行转账' },
    { value: '2', label: '现金' },
    { value: '3', label: '支票' },
    { value: '4', label: '电子支付' }
];

// 客户选项
const customerOptions = [
    { value: 'C001', label: '北京科技有限公司' },
    { value: 'C002', label: '上海贸易有限公司' },
    { value: 'C003', label: '广州电子有限公司' },
    { value: 'C004', label: '深圳科技有限公司' },
    { value: 'C005', label: '成都信息有限公司' }
];

// 制单状态选项
const documentStatusOptions = [
    { value: '1', label: '草稿' },
    { value: '2', label: '已提交' },
    { value: '3', label: '已审核' },
    { value: '4', label: '已驳回' }
];

// 页面数据对象
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        payee: null, // 收款方
        payeeBankCard: null, // 收款方银行卡
        paymentType: null, // 支付类型
        applicant: null, // 申请人
        paymentMethod: null, // 支付方式
        customerName: null, // 客户名称
        applyTimeStart: null, // 申请支付开始时间
        applyTimeEnd: null, // 申请支付结束时间
        salaryPaymentDate: null, // 工资支付日期
        documentStatus: null, // 制单状态
        isReissue: null, // 是否退票重发
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表数据
    ids: [], // 选中的id
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        obj.tableData = [
            {
                id: 1,
                taxCompanyName: '北京科技有限公司',
                withholdingType: '企业',
                employeeId: 'EMP001',
                employeeName: '张三',
                idNumber: '110101199001011234',
                idType: '身份证',
                customerName: '北京科技有限公司',
                paymentMethod: '银行转账',
                paymentStatus: '1',
                applyTime: '2023-06-01',
                salaryPaymentDate: '2023-06-05'
            },
            {
                id: 2,
                taxCompanyName: '上海贸易有限公司',
                withholdingType: '企业',
                employeeId: 'EMP002',
                employeeName: '李四',
                idNumber: '310101199002022345',
                idType: '身份证',
                customerName: '上海贸易有限公司',
                paymentMethod: '银行转账',
                paymentStatus: '2',
                applyTime: '2023-06-02',
                salaryPaymentDate: '2023-06-06'
            },
            {
                id: 3,
                taxCompanyName: '广州电子有限公司',
                withholdingType: '企业',
                employeeId: 'EMP003',
                employeeName: '王五',
                idNumber: '440101199003033456',
                idType: '身份证',
                customerName: '广州电子有限公司',
                paymentMethod: '电子支付',
                paymentStatus: '0',
                applyTime: '2023-06-03',
                salaryPaymentDate: '2023-06-07'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}


getList();
</script>
<style lang="scss" scoped></style>