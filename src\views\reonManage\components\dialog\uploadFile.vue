<template>
    <el-dialog v-model="dialogShow" :title="props.title" width="30%" append-to-body draggable @close="handleClose">
        <el-form class="formHight" ref="formRef" :model="props.dialogForm" :rules="props.rules" label-width="auto">
            <el-form-item
                v-if="props.type == 'payrollPayment_documentation' || props.type == 'payrollPayment_review' || props.type == 'payrollProcessApproval'
                    || props.type == 'personalIncomeTaxDeclaration_data' || props.type == 'deductionsFromWages' || props.type == 'actualDataImport'"
                label="扣缴义务人:" prop="withholdingAgent">
                <el-select v-model="props.dialogForm.withholdingAgent" placeholder=" 请选择" clearable>
                    <el-option v-for="item in withholdingAgentOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <!-- v-if="props.type == 'batchresignationApplication' || props.type == 'batchActualAdjustment' || props.type == 'practiceHandlingFeedback_import' 
             || props.type == 'practiceHandlingFeedback_search' || props.type == 'batchSalaryModification_import'" -->
            <el-form-item label="备注:" prop="remark">
                <el-input type="textarea" :rows="3" v-model="props.dialogForm.remark" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item v-if="props.type == 'batchAddPersonal_oneTimeFees'" label="接单变更备注:" prop="changeRemark">
                <el-input type="textarea" v-model="props.dialogForm.changeRemark" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="上传文件:" prop="fileList">
                <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button type="primary" @click="handleUpload">开始上传</el-button>
            <el-button @click="handleClose">取消</el-button>
        </template>
    </el-dialog>
</template>

<script setup>
const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        default: ''
    },
    title: {
        type: String,
        default: ''
    },
    dialogForm: {
        type: Object,
        default: () => ({})
    },
    rules: {
        type: Object,
        default: () => ({})
    }
})
// 使用本地变量来跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.show的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});

const emit = defineEmits(['close', 'update:dialogShow'])
function handleClose() {
    dialogShow.value = false;
    emit('close')
}

</script>
