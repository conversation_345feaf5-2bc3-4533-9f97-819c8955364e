<!-- 批量导入实做账号 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-select class="width220" v-model="obj.queryParams.customer" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable>
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入类型:" prop="importType">
                <el-select class="width220" v-model="obj.queryParams.importType" placeholder="请选择" clearable>
                    <el-option v-for="item in importTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入日期从:" prop="importDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateStart" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateEnd" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleDownload">下载模版</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="导入编号" align="center" prop="importNo" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="导入类型" align="center" prop="importType">
                <template #default="scope">
                    <el-tag type="info">
                        {{ scope.row.importType === '1' ? '新增实做账号' : (scope.row.importType === '2' ? '停用实做账号' :
                            '批量修改密码') }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="导入文件" align="center" width="220">
                <template #default="scope">
                    <el-link type="primary" @click="downloadFile(scope.row)">{{ scope.row.importFile }}</el-link>
                </template>
            </el-table-column>
            <el-table-column label="导入日期" align="center" prop="importDate" />
            <el-table-column label="成功数" align="center" prop="successCount" />
            <el-table-column label="失败数" align="center" prop="failCount" />
            <el-table-column label="处理状态" align="center" prop="processStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.processStatus === '1' ? 'success' : (scope.row.processStatus === '2' ? 'danger' : 'warning')">
                        {{ scope.row.processStatus === '1' ? '处理完成' : (scope.row.processStatus === '2' ? '处理失败' : '处理中')
                        }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="导入人" align="center" prop="creator" />
            <el-table-column label="导入结果查看" align="center">
                <template #default="scope">
                    <el-button type="primary" text icon="View" @click="handleDetail(scope.row)">详情</el-button>

                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 导入 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="40%" append-to-body>
            <el-form :model="obj.dialogForm" ref="dialogRef" :rules="rules" inline label-width="auto">
                <el-form-item label="福利办理方:" prop="welfareHandler">
                    <el-select class="width220" v-model="obj.dialogForm.welfareHandler" placeholder="请选择" clearable>
                        <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="大户/单立户:" prop="accountType">
                    <el-select class="width220" v-model="obj.dialogForm.accountType" placeholder="请选择" clearable>
                        <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户:" prop="customer">
                    <el-select class="width220" v-model="obj.dialogForm.customer" placeholder="请选择" clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="福利包名称:" prop="welfarePackageName">
                    <el-select class="width220" v-model="obj.dialogForm.welfarePackageName" placeholder="请选择" clearable>
                        <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="导入类型:" prop="importType">
                    <el-select class="width220" v-model="obj.dialogForm.importType" placeholder="请选择" clearable>
                        <el-option v-for="item in importTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注:" prop="remark">
                    <el-input class="width420" type="textarea" :rows="3" v-model="obj.dialogForm.remark"
                        placeholder="请输入备注" />
                </el-form-item>
                <el-form-item label="文件上传:" prop="file">
                    <FileUpload v-model="obj.dialogForm.file" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancelImport">取消</el-button>
                <el-button type="primary" @click="submitImport">开始上传</el-button>
            </template>
        </el-dialog>
        <!-- 历史信息查看 -->
        <el-dialog v-model="obj.dialogShowHistory" title="历史信息查看" width="40%" append-to-body>
            <el-button class="mb20" type="primary" @click="handleExport">导出数据</el-button>
            <el-table border :data="obj.tableDataHistory">
                <el-table-column label="导入编号" align="center" prop="importNo" />
                <el-table-column label="行号" align="center" prop="rowNo" />
                <el-table-column label="错误描述" align="center" prop="errorDescription" />
                <el-table-column label="提醒描述" align="center" prop="reminderDescription" />
                <el-table-column label="导入结果" align="center" prop="importResult" />
                <el-table-column label="导入信息" align="center" prop="importInfo" />
                <el-table-column label="创建人" align="center" prop="creator" />
                <el-table-column label="创建时间" align="center" prop="createTime" />
            </el-table>
        </el-dialog>
    </div>
</template>


<script setup name="BatchImplementationAccount_import">
import { listScale } from "@/api/reonApi/scale";


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 导入类型选项
const importTypeOptions = [
    { value: '1', label: '新增实做账号' },
    { value: '2', label: '停用实做账号' },
    { value: '3', label: '批量修改密码' }
];

// 处理状态选项
const processStatusOptions = [
    { value: '0', label: '处理中' },
    { value: '1', label: '处理完成' },
    { value: '2', label: '处理失败' }
];

// 表单验证规则
const rules = {
    welfareHandler: [
        { required: true, message: '请选择福利办理方', trigger: 'change' }
    ],
    accountType: [
        { required: true, message: '请选择大户/单立户', trigger: 'change' }
    ],
    customer: [
        { required: true, message: '请选择客户', trigger: 'change' }
    ],
    welfarePackageName: [
        { required: true, message: '请选择福利包名称', trigger: 'change' }
    ],
    importType: [
        { required: true, message: '请选择导入类型', trigger: 'change' }
    ],
    remark: [
        { required: true, message: '请输入备注', trigger: 'blur' }
    ],
    file: [
        { required: true, message: '请选择要上传的文件', trigger: 'change' }
    ]
};

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        importNo: 'IMP20230001',
        welfarePackageName: '标准福利包',
        importType: '1',
        importFile: '新增实做账号_202301.xlsx',
        importDate: '2023-01-15',
        successCount: 120,
        failCount: 0,
        processStatus: '1',
        creator: '张三'
    },
    {
        id: 2,
        importNo: 'IMP20230002',
        welfarePackageName: '高级福利包',
        importType: '2',
        importFile: '停用实做账号_202302.xlsx',
        importDate: '2023-02-15',
        successCount: 85,
        failCount: 3,
        processStatus: '1',
        creator: '李四'
    },
    {
        id: 3,
        importNo: 'IMP20230003',
        welfarePackageName: '基础福利包',
        importType: '3',
        importFile: '批量修改密码_202303.xlsx',
        importDate: '2023-03-15',
        successCount: 0,
        failCount: 0,
        processStatus: '0',
        creator: '王五'
    }
];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        importType: null, // 导入类型
        importDateStart: null, // 导入日期从
        importDateEnd: null, // 导入日期到
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        importType: null, // 导入类型
        remark: '', // 备注
        file: null // 文件
    }, // 导入表单
    dialogShow: false, // 导入弹窗
    ids: [], // 选中的id
    title: "", // 标题


})


/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 导入
function handleImport() {
    obj.dialogShow = true;
    obj.title = "上传导入数据";
    resetForm();
}

// 取消导入
function cancelImport() {
    obj.dialogShow = false;
    resetForm();
}

// 提交导入
function submitImport() {
    proxy.$refs["dialogRef"].validate(valid => {
        if (valid) {
            if (!obj.dialogForm.file) {
                proxy.$modal.msgError('请选择要上传的文件');
                return;
            }

            // 实际项目中应该调用API进行文件上传
            proxy.$modal.msgSuccess('文件上传成功');
            obj.dialogShow = false;
            resetForm();
            getList(); // 刷新列表
        }
    });
}

// 下载模版
function handleDownload() {
    // 实际项目中应该调用API进行下载模板
    proxy.$modal.msgSuccess('模板下载成功');

    // 模拟下载文件
    const fileName = '实做账号导入模板.xlsx';
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 订单详情
function handleDetail(row) {
    obj.dialogShowHistory = true;
}

// 重置表单
function resetForm() {
    obj.dialogForm = {
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        importType: null, // 导入类型
        remark: '', // 备注
        file: null // 文件
    };
    proxy.resetForm("dialogRef");
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

getList();
</script>
<style lang="scss" scoped></style>