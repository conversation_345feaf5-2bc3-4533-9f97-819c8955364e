<!-- 实做汇总表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="大区:" prop="region">
                <el-select class="width220" v-model="obj.queryParams.region" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利办理方" prop="implementationSummary">
                <el-input class="width220" v-model="obj.queryParams.implementationSummary" placeholder="请输入福利办理方"
                    clearable />
            </el-form-item>
            <el-form-item label="大户/单立户" prop="singleOrLarge">
                <el-select class="width220" v-model="obj.queryParams.singleOrLarge" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报表年月:" prop="implementationSummaryYearMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.implementationSummaryYearMonth" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="报表年月止:" prop="implementationSummaryYearMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.implementationSummaryYearMonthEnd" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="产品类型" prop="productType">
                <el-select class="width220" v-model="obj.queryParams.productType" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-button type="primary" icon="Download" @click="handleExport">导出明细</el-button>
                <el-button icon="Refresh" @click="handleReset">重置</el-button>
            </el-row>
        </el-form>
    </div>
</template>

<script setup name="ImplementationSummary">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


const obj = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单

})

/** 重置按钮操作 */
function handleReset() {
    obj.queryParams = {};
}

/** 导出明细按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

</script>
<style lang="scss" scoped></style>