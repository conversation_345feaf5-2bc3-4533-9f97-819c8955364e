<template>
    <el-button :type="type" :plain="plain" :icon="icon" v-print="printObj" v-bind="$attrs">
        <slot>{{ text }}</slot>
    </el-button>
</template>

<script setup>
import { computed } from 'vue';
const props = defineProps({
    printId: { type: String, required: true },
    printTitle: { type: String, default: '' },
    text: { type: String, default: '打印' },
    type: { type: String, default: 'primary' },
    plain: { type: Boolean, default: true },
    icon: { type: String, default: 'Printer' },
});

const printObj = computed(() => ({
    id: props.printId,
    popTitle: props.printTitle,
}));
</script>

<style lang="scss" scoped></style>
