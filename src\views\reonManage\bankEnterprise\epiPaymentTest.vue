<!-- 招商银企直连支付测试 -->
<template>
    <div class="app-container">
        <el-tabs type="border-card">
            <el-tab-pane label="单个支付">
                <el-form :model="formParams" ref="queryRef" inline label-width="auto">
                    <el-form-item label="转出账号:" prop="transferOutAccount">
                        <el-input class="width220" v-model="formParams.transferOutAccount" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-form-item label="收方帐号:" prop="transferInAccount">
                        <el-input class="width220" v-model="formParams.transferInAccount" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-divider />
                    <el-form-item label="收方户名:" prop="transferInAccountName">
                        <el-input class="width220" v-model="formParams.transferInAccountName" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-form-item label="收方开户行名称:" prop="transferInBankName">
                        <el-input class="width220" v-model="formParams.transferInBankName" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-form-item label="收方开户行地址:" prop="transferInBankAddress">
                        <el-input class="width220" v-model="formParams.transferInBankAddress" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-divider />
                    <el-form-item label="用途 展示在回单,最长100汉字:" prop="purpose">
                        <el-input class="width220" v-model="formParams.purpose" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-divider />
                    <el-form-item>
                        <el-button type="primary" @click="handleSubmit">确定</el-button>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="代发代扣">
                <el-form :model="formParams" ref="queryRef" inline label-width="auto">
                    <border-box title="代发代扣">
                        <el-form-item label="转出账号:" prop="transferOutAccount">
                            <el-input class="width220" v-model="formParams.transferOutAccount" placeholder="请输入"
                                clearable />
                        </el-form-item>
                        <el-form-item label="转出户名:" prop="transferOutAccountName">
                            <el-input class="width220" v-model="formParams.transferOutAccountName" placeholder="请输入"
                                clearable />
                        </el-form-item>
                        <el-form-item label="收方帐号:" prop="transferInAccount">
                            <el-input class="width220" v-model="formParams.transferInAccount" placeholder="请输入"
                                clearable />
                        </el-form-item>
                        <el-divider />
                        <el-form-item label="收方户名:" prop="transferInAccountName">
                            <el-input class="width220" v-model="formParams.transferInAccountName" placeholder="请输入"
                                clearable />
                        </el-form-item>
                        <el-form-item label="是否超过一千笔:" prop="isOverOneThousand">
                            <el-select class="width220" v-model="formParams.isOverOneThousand" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-divider />
                        <el-form-item>
                            <el-button type="primary" @click="handleSubmit">确定</el-button>
                        </el-form-item>
                    </border-box>
                </el-form>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup name="EpiPaymentTest">

const { proxy } = getCurrentInstance();

const { sys_yes_no } = proxy.useDict('sys_yes_no')


const formParams = ref({
    transferOutAccount: null,
    transferOutAccountName: null,
    transferInAccount: null,
    transferInAccountName: null,
    isOverOneThousand: null,
})

/** 确定 */
function handleSubmit() {
    formParams.value = {}
}

</script>
<style lang="scss" scoped></style>