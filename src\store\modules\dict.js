const useDictStore = defineStore("dict", {
  state: () => ({
    dict: new Array(),
  }),
  actions: {
    /**
     * 根据键获取字典值
     * @param {_key} 键值，用于查找字典
     * @returns 返回对应的字典值，如果找不到则返回null
     */
    getDict(_key) {
      // 检查键是否为null或空字符串，如果是，则返回null
      if (_key == null && _key == "") {
        return null;
      }
      try {
        // 遍历字典数组，寻找匹配的键
        for (let i = 0; i < this.dict.length; i++) {
          // 当找到匹配的键时，返回对应的值
          if (this.dict[i].key == _key) {
            return this.dict[i].value;
          }
        }
      } catch (e) {
        // 如果发生异常，返回null
        return null;
      }
    },
    /**
     * 设置字典
     * 此方法用于向字典中添加新的键值对
     * @param {_key} 键，用于字典中的查找和引用
     * @param {value} 值，与键关联的数据
     * 仅当键不为空时，才将键值对添加到字典中，以避免无效或错误的条目
     */
    setDict(_key, value) {
      if (_key !== null && _key !== "") {
        this.dict.push({
          key: _key,
          value: value,
        });
      }
    },
    /**
     * 删除字典
     * @param {string} _key - 需要删除的字典的键
     * @returns {boolean} - 如果成功删除字典项，则返回true；否则返回false
     */
    removeDict(_key) {
      var bln = false;
      try {
        // 遍历字典数组，寻找匹配的键
        for (let i = 0; i < this.dict.length; i++) {
          // 如果找到匹配的键，删除对应的字典项，并返回true表示成功删除
          if (this.dict[i].key == _key) {
            this.dict.splice(i, 1);
            return true;
          }
        }
      } catch (e) {
        // 如果发生异常，设置bln为false，表示删除失败
        bln = false;
      }
      // 如果未找到匹配的键或发生异常，返回bln（默认为false），表示删除失败
      return bln;
    },
    // 清空字典
    cleanDict() {
      this.dict = new Array();
    },
    // 初始字典
    initDict() {},
  },
});

export default useDictStore;
