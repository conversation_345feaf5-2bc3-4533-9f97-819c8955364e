<!-- 账单一次性废除与导出 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplier">
                <el-select class="width220" v-model="obj.queryParams.supplier" placeholder="请选择" clearable>
                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商账单模板:" prop="billTemplate">
                <el-select class="width220" v-model="obj.queryParams.billTemplate" placeholder="请选择" clearable>
                    <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报表年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择年月"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleConfirm">废除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="upload" @click="handleReject">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <BillOneTime :tableData="obj.tableData" :loading="obj.loading" menuName="billCancellation_exportAtOneTime"
            @selectionChange="handleSelectionChange" />
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="BillCancellation_exportAtOneTime">
import { listScale } from "@/api/reonApi/scale";
import BillOneTime from '@/views/reonManage/components/table/billOneTime.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 供应商选项
const supplierOptions = [
    { value: '1', label: '供应商A' },
    { value: '2', label: '供应商B' },
    { value: '3', label: '供应商C' }
];

// 账单模板选项
const billTemplateOptions = [
    { value: '1', label: '模板1' },
    { value: '2', label: '模板2' },
    { value: '3', label: '模板3' }
];


// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        supplier: '1',
        billTemplate: '1',
        billMonth: '2023-01',
        firstCategory: '1',
        secondCategory: '1',
        amount: 10000,
        amountWithoutTax: 9433.96,
        taxRate: 6,
        tax: 566.04,
        totalPeople: 100,
        oneTimeSupportPeople: 5,
        reportLockStatus: '1',
        status: '1',
        remark: '正常账单',
        submitter: '张三',
        submitTime: '2023-01-15 10:30:45'
    },
    {
        id: 2,
        supplier: '2',
        billTemplate: '2',
        billMonth: '2023-02',
        firstCategory: '2',
        secondCategory: '4',
        amount: 8000,
        amountWithoutTax: 7547.17,
        taxRate: 6,
        tax: 452.83,
        totalPeople: 80,
        oneTimeSupportPeople: 0,
        reportLockStatus: '0',
        status: '1',
        remark: '正常账单',
        submitter: '李四',
        submitTime: '2023-02-20 14:25:30'
    },
    {
        id: 3,
        supplier: '2',
        billTemplate: '2',
        billMonth: '2023-02',
        firstCategory: '2',
        secondCategory: '4',
        amount: 8000,
        amountWithoutTax: 7547.17,
        taxRate: 6,
        tax: 452.83,
        totalPeople: 80,
        oneTimeSupportPeople: 0,
        reportLockStatus: '2',
        status: '1',
        remark: '正常账单',
        submitter: '李四',
        submitTime: '2023-02-20 14:25:30'
    }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplier: null, // 供应商
        billTemplate: null, // 账单模板
        billMonth: null, // 报表年月
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表

    ids: [], // 选中的id
})

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.supplier) {
            filteredData = filteredData.filter(item => item.supplier === obj.queryParams.supplier);
        }

        if (obj.queryParams.billTemplate) {
            filteredData = filteredData.filter(item => item.billTemplate === obj.queryParams.billTemplate);
        }

        if (obj.queryParams.billMonth) {
            const month = new Date(obj.queryParams.billMonth).toISOString().slice(0, 7);
            filteredData = filteredData.filter(item => item.billMonth === month);
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
    console.log(obj.ids)
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}


// 废除
function handleConfirm() {
    proxy.$modal.confirm('确认废除所有数据吗？').then(() => {
        proxy.$modal.msgSuccess('废除成功');
    }).catch(() => { });
}

// 导出
function handleReject() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped></style>