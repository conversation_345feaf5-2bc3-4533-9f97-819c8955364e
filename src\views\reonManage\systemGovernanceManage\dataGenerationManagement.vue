<!-- 数据生成管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="queryParams" inline label-width="auto">
            <el-row class="mb8">
                <el-form-item>
                    手动生成合同账单报表
                </el-form-item>
                <el-form-item label="账单年月:">
                    <el-date-picker class="width220" v-model="queryParams.billMonth" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    手动生成客户是否本地单记录表
                </el-form-item>
                <el-form-item label="账单年月:">
                    <el-date-picker class="width220" v-model="queryParams.billMonth" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
                <el-form-item>
                    <el-checkbox v-model="queryParams.isFilter" label="是否过滤已过期产品" size="large" border />
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    手动生成残障金报表
                </el-form-item>
                <el-form-item label="账单年月:">
                    <el-date-picker class="width220" v-model="queryParams.billMonth" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    手动生成往期订单服务月表数据(删除重新生成)
                </el-form-item>
                <el-form-item label="订单编号(手填,不填写生成全部):">
                    <el-input class="width220" v-model="queryParams.orderNumber" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    手动合并往期订单服务月表数据(Cache合并到serviceMonth)
                </el-form-item>
                <el-form-item label="订单编号(手填,不填写生成全部):">
                    <el-input class="width220" v-model="queryParams.orderNumber" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    手动刷新工资供应商成本
                </el-form-item>
                <el-form-item label="账单年月:">
                    <el-date-picker class="width220" v-model="queryParams.billMonth" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    根据非社保公积金变更Id进行数据回滚,可批量（逗号分隔）
                </el-form-item>
                <el-form-item label="变更ID:">
                    <el-input class="width220" v-model="queryParams.changeId" placeholder="请输入导入人" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleRollback">回滚数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                修复往期服务费月表数据,只会新增(service_month_cache),不会删除
            </el-row>
            <el-row class="mb8">
                <el-form-item label="账单年月(必填):">
                    <el-date-picker class="width220" v-model="queryParams.billMonth" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="合同编号:">
                    <el-input class="width220" v-model="queryParams.contractNumber" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="账单模板id:">
                    <el-input class="width220" v-model="queryParams.billTemplateId" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="订单编号(可以多个用,隔开):">
                    <el-input class="width220" type="textarea" v-model="queryParams.orderNumber" placeholder="请输入"
                        clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    刷新增减追踪报表
                </el-form-item>
                <el-form-item label="账单年月:">
                    <el-date-picker class="width220" v-model="queryParams.billMonth" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    处理契约锁客服签章审批
                </el-form-item>
                <el-form-item>
                    <el-button class="ml10" type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
        </el-form>

    </div>
</template>


<script setup name="DataGenerationManagement">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const queryParams = ref({
    billMonth: null,
    isFilter: false,
    orderNumber: null,
    contractNumber: null,
    billTemplateId: null,
    changeId: null,
})

/** 生成数据 */
function handleGenerate() {
    console.log('生成数据');
}
/** 回滚数据 */
function handleRollback() {
    console.log('回滚数据');
}


</script>
<style lang="scss" scoped></style>