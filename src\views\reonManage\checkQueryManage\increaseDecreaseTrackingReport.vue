<!-- 增减追踪报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="集团名称:" prop="groupName">
                <el-select class="width220" v-model="obj.queryParams.groupName" placeholder="请选择集团名称" clearable>
                    <el-option label="集团A" value="A" />
                    <el-option label="集团B" value="B" />
                    <el-option label="集团C" value="C" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择客户名称" clearable>
                    <el-option label="客户名称1" value="1" />
                    <el-option label="客户名称2" value="2" />
                    <el-option label="客户名称3" value="3" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" icon="Download" @click="handleExport">数据导出</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column label="集团名称" align="center" fixed prop="groupName" />
            <el-table-column label="客户编号" align="center" fixed prop="customerCode" />
            <el-table-column label="客户名称" align="center" fixed prop="customerName" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="签单地" align="center" prop="signPlace" />
            <el-table-column label="签单分公司(派单分公司)" align="center" width="180" prop="signBranch" />
            <el-table-column label="签约方抬头 (合同我司抬头及收款公司)" align="center" width="260" prop="contractCompany" />
            <el-table-column label="当月实际在保人数" align="center" width="140" prop="currentMonthInsured" />
            <el-table-column label="上月底在保人数" align="center" width="140" prop="lastMonthInsured" />
            <el-table-column label="对比上月在保人数结果" align="center" width="160" prop="compareLastMonth" />
            <el-table-column label="当前月份-1对比结果" align="center" width="160" prop="compareMonth1" />
            <el-table-column label="当前月份-2对比结果" align="center" width="160" prop="compareMonth2" />
            <el-table-column label="当前月份-3对比结果" align="center" width="160" prop="compareMonth3" />
            <el-table-column label="当前月份-4对比结果" align="center" width="160" prop="compareMonth4" />
            <el-table-column label="当前月份-5对比结果" align="center" width="160" prop="compareMonth5" />
            <el-table-column label="是否前三个月连续减员大于30%" align="center" width="220" prop="isConsecutiveThreeMonths" />
            <el-table-column label="半年内是否非连续3个月减员大于30%" align="center" width="260" prop="isNonConsecutiveThreeMonths" />
            <el-table-column label="集团名称当月实际在保人数" align="center" width="200" prop="groupCurrentMonthInsured" />
            <el-table-column label="集团名称上月底在保人数" align="center" width="180" prop="groupLastMonthInsured" />
            <el-table-column label="集团名称对比上月在保人数结果" align="center" width="260" prop="groupCompareLastMonth" />
            <el-table-column label="集团名称当前月份-1对比结果" align="center" width="220" prop="groupCompareMonth1" />
            <el-table-column label="集团名称当前月份-2对比结果" align="center" width="220" prop="groupCompareMonth2" />
            <el-table-column label="集团名称当前月份-3对比结果" align="center" width="220" prop="groupCompareMonth3" />
            <el-table-column label="集团名称当前月份-4对比结果" align="center" width="220" prop="groupCompareMonth4" />
            <el-table-column label="集团名称当前月份-5对比结果" align="center" width="220" prop="groupCompareMonth5" />
            <el-table-column label="集团名称是否前三个月连续减员大于30%" align="center" width="260"
                prop="groupIsConsecutiveThreeMonths" />
            <el-table-column label="集团名称半年内是否非连续3个月减员大于30%" align="center" width="300"
                prop="groupIsNonConsecutiveThreeMonths" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="IncreaseDecreaseTrackingReport">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null,
        customerName: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                groupName: '集团A',
                customerCode: 'CUST001',
                customerName: '客户名称1',
                contractCode: 'CONTRACT001',
                signPlace: '上海',
                signBranch: '上海分公司',
                contractCompany: '上海鼎捷数智软件有限公司',
                currentMonthInsured: 100,
                lastMonthInsured: 95,
                compareLastMonth: '+5.26%',
                compareMonth1: '+3.09%',
                compareMonth2: '+2.04%',
                compareMonth3: '+1.01%',
                compareMonth4: '+0.00%',
                compareMonth5: '-1.96%',
                isConsecutiveThreeMonths: '否',
                isNonConsecutiveThreeMonths: '否',
                groupCurrentMonthInsured: 500,
                groupLastMonthInsured: 480,
                groupCompareLastMonth: '+4.17%',
                groupCompareMonth1: '+2.56%',
                groupCompareMonth2: '+1.69%',
                groupCompareMonth3: '+0.84%',
                groupCompareMonth4: '+0.00%',
                groupCompareMonth5: '-0.83%',
                groupIsConsecutiveThreeMonths: '否',
                groupIsNonConsecutiveThreeMonths: '否'
            },
            {
                id: 2,
                groupName: '集团B',
                customerCode: 'CUST002',
                customerName: '客户名称2',
                contractCode: 'CONTRACT002',
                signPlace: '北京',
                signBranch: '北京分公司',
                contractCompany: '北京鼎捷数智软件有限公司',
                currentMonthInsured: 80,
                lastMonthInsured: 120,
                compareLastMonth: '-33.33%',
                compareMonth1: '-30.43%',
                compareMonth2: '-27.27%',
                compareMonth3: '-33.33%',
                compareMonth4: '-38.46%',
                compareMonth5: '-42.86%',
                isConsecutiveThreeMonths: '是',
                isNonConsecutiveThreeMonths: '是',
                groupCurrentMonthInsured: 300,
                groupLastMonthInsured: 400,
                groupCompareLastMonth: '-25.00%',
                groupCompareMonth1: '-21.05%',
                groupCompareMonth2: '-16.67%',
                groupCompareMonth3: '-25.00%',
                groupCompareMonth4: '-33.33%',
                groupCompareMonth5: '-40.00%',
                groupIsConsecutiveThreeMonths: '是',
                groupIsNonConsecutiveThreeMonths: '是'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 数据导出
function handleExport() {
    proxy.$modal.msgSuccess('数据导出成功');
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>