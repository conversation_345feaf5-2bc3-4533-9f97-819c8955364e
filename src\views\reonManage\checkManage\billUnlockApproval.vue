<!-- 账单解锁审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="账单模板名称:" prop="billTemplateName">
                <el-select class="width220" v-model="obj.queryParams.billTemplateName" placeholder="请选择账单模板" clearable>
                    <el-option label="模板1" value="模板1" />
                    <el-option label="模板2" value="模板2" />
                    <el-option label="模板3" value="模板3" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择账单月"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="账单模板名称" align="center" prop="billTemplateName" />
            <el-table-column label="收费月" align="center" prop="chargeMonth" />
            <el-table-column label="收费金额" align="center" prop="chargeAmount" />
            <el-table-column label="账单状态" align="center" prop="billStatus" />
            <el-table-column label="提交时间" align="center" prop="submitTime" />
            <el-table-column label="提交人" align="center" prop="submitter" />
            <el-table-column label="操作" align="center" prop="id" width="120">
                <template #default="scope">
                    <el-button type="primary" icon="Lock" @click="handleLock">锁定</el-button>
                    <el-button icon="Unlock" @click="handleUnlock">解锁</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="BillUnlockApproval">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    contractName: [
        { required: false, message: '合同名称不能为空', trigger: 'blur' }
    ],
    billTemplateName: [
        { required: false, message: '账单模板名称不能为空', trigger: 'change' }
    ],
    billMonth: [
        { required: false, message: '账单月不能为空', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractName: null,
        billTemplateName: null,
        billMonth: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerName: '客户名称1',
                contractName: '合同名称1',
                billTemplateName: '模板1',
                chargeMonth: '2023-01',
                chargeAmount: '10000',
                billStatus: '已锁定',
                submitTime: '2023-01-15 10:00:00',
                submitter: '张三'
            },
            {
                id: 2,
                customerName: '客户名称2',
                contractName: '合同名称2',
                billTemplateName: '模板2',
                chargeMonth: '2023-02',
                chargeAmount: '20000',
                billStatus: '已锁定',
                submitTime: '2023-02-15 10:00:00',
                submitter: '李四'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 锁定按钮操作 */
function handleLock() {
    proxy.$modal.msgSuccess('锁定成功');
}

/** 解锁按钮操作 */
function handleUnlock() {
    proxy.$modal.msgSuccess('解锁成功');
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>