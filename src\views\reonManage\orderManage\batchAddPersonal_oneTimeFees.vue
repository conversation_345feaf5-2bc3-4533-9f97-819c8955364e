<!-- 批量添加个人一次性费用 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateStart">
                <el-date-picker v-model="obj.queryParams.importDateStart" type="date" placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateEnd">
                <el-date-picker v-model="obj.queryParams.importDateEnd" type="date" placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="导入编号:" prop="importCode">
                <el-input class="width220" v-model="obj.queryParams.importCode" placeholder="请输入导入编号" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleTemplate">下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <ExportTable2 :tableData="obj.tableData" :loading="obj.loading" :total="obj.total"
            :page="obj.queryParams.pageNum" :limit="obj.queryParams.pageSize" menuName="batchAddPersonal_oneTimeFees"
            @row-dblclick="handleRowDblClick" @handlePagination="handlePagination" />
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" title="上传导入数据" type="batchAddPersonal_oneTimeFees"
            :dialogForm="obj.dialogForm" :rules="obj.rules" />
        <!-- 历史信息查看 -->
        <HistoricalInformation :dialogShow="obj.dialogShow2" :title="obj.title" :tableData="obj.tableData"
            @close="handleHistoryClose" />
    </div>
</template>

<script setup name="BatchAddPersonal_oneTimeFees">


import { listScale } from "@/api/reonApi/scale";


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateStart: null,
        importDateEnd: null,
        importCode: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {},//导入表单
    dialogShow: false,//导入弹窗
    ids: [],//选中的id
    title: "",//标题
    rules: {}
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                importCode: 'DR20230001',
                importPerson: '导入人1',
                importTime: '2023-01-01 10:00:00',
                successCount: 10,
                failCount: 0,
                importFile: '文件1.xlsx',
                processStatus: '处理成功'
            },
            {
                id: 2,
                importCode: '**********',
                importPerson: '导入人2',
                importTime: '2023-02-01 10:00:00',
                successCount: 8,
                failCount: 2,
                importFile: '文件2.xlsx',
                processStatus: '处理成功'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
// 双击行
function handleRowDblClick(row) {
    obj.dialogShow2 = true;
    obj.title = '历史信息查看';
}

// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}
// 导入
function handleImport() {
    obj.dialogShow = true;
}

// 关闭导入弹窗
function handleClose() {
    obj.dialogShow = false;
}

// 下载模板
function handleTemplate() {
    proxy.download('system/scale/export', {
        ...obj.queryParams
    }, `scale_${new Date().getTime()}.xlsx`)
}

/** 历史信息关闭 */
function handleHistoryClose() {
    obj.dialogShow2 = false;
}
// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>