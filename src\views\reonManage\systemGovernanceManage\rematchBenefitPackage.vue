<!-- 订单重新匹配福利包主页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="queryParams" ref="queryRef" inline label-width="auto">
            <el-row class="mb8">
                <el-form-item>
                    根据订单号删除已有实做，并重新生成
                </el-form-item>
                <el-form-item label="福利办理方:">
                    <el-select class="width220" v-model="queryParams.welfareHandler" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleExecute">执行</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    根据订单号生成缺少的实做产品
                </el-form-item>
                <el-form-item label="订单号:">
                    <FileUpload :fileList="fileList" @upload="handleUpload" :isShowTip="false" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleUpload">开始上传</el-button>
                </el-form-item>
                <el-form-item>
                    <el-checkbox v-model="queryParams.expiredProducts" label="是否过滤已过期产品" size="large" border />
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    添加客户白名单
                </el-form-item>
                <el-form-item label="客户Id:">
                    <el-input class="width220" v-model="queryParams.customerId" placeholder="请输入导入人" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleAdd">添加</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    根据小合同号修改实做福利包code
                </el-form-item>
                <el-form-item label="小合同号:">
                    <el-input class="width220" v-model="queryParams.contractNumber_small" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleExecute">执行</el-button>
                </el-form-item>
            </el-row>
        </el-form>
    </div>
</template>


<script setup name="RematchBenefitPackage">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const queryParams = ref({
    welfareHandler: '',// 福利办理方
    expiredProducts: false,// 是否过滤已过期产品
    customerId: '',// 客户Id
    contractNumber_small: '',// 小合同号
})
const fileList = ref([])

function handleUpload() {
    console.log(fileList.value)
}


</script>
<style lang="scss" scoped>
.upload-file {
    display: flex;
}
</style>