<!-- 供应商账单比对差异 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-input class="width220" v-model="obj.queryParams.supplierName" placeholder="请输入供应商名称" />
            </el-form-item>
            <el-form-item label="供应商账单模板:" prop="billTemplate">
                <el-input class="width220" v-model="obj.queryParams.billTemplate" placeholder="请输入账单模板" />
            </el-form-item>
            <el-form-item label="报表年月:" prop="reportMonth">
                <el-input class="width220" v-model="obj.queryParams.reportMonth" placeholder="请输入报表年月" />
            </el-form-item>
            <el-form-item label="比较类型:" prop="compareType">
                <el-select class="width220" v-model="obj.queryParams.compareType" placeholder="请选择比较类型">
                    <el-option v-for="item in compareTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>

            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <!-- <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button> -->
                        <!-- <el-button icon="Refresh" @click="resetQuery">重置</el-button> -->
                        <el-button type="primary" icon="Download" @click="handleDownload">导出模版</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="产品编码" align="center" prop="productCode" />
            <el-table-column label="产品名称" align="center" prop="productName" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <div style="width: 50%;">
            <fileUpload ref="importRef" :show="obj.dialogShow" :title="'导入供应商账单比对差异'" :form="obj.dialogForm"
                :fileList="obj.fileList" @close="handleClose" @submit="handleSubmit" />
            <el-button style="float: right;" type="primary" @click="handleCompare">进行对比</el-button>
        </div>
    </div>
</template>


<script setup name="SupplierBillingComparisonVariance">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 比较类型选项
const compareTypeOptions = [
    { value: '1', label: '全部比较' },
    { value: '2', label: '金额比较' },

];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        billTemplate: null,
        reportMonth: null,
        compareType: null,
    },//查询表单
    total: 10,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})


/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                supplierName: '供应商A',
                billTemplate: '模板1',
                reportMonth: '2023-05',
                compareType: '数量差异',
                productCode: 'P001',
                productName: '产品A',
                systemQuantity: 100,
                billQuantity: 95,
                diffQuantity: 5,
                createTime: '2023-05-15 10:30:00'
            },
            {
                id: 2,
                supplierName: '供应商B',
                billTemplate: '模板2',
                reportMonth: '2023-05',
                compareType: '金额差异',
                productCode: 'P002',
                productName: '产品B',
                systemQuantity: 200,
                billQuantity: 190,
                diffQuantity: 10,
                createTime: '2023-05-16 14:20:00'
            },
            {
                id: 3,
                supplierName: '供应商C',
                billTemplate: '模板3',
                reportMonth: '2023-06',
                compareType: '全部差异',
                productCode: 'P003',
                productName: '产品C',
                systemQuantity: 150,
                billQuantity: 145,
                diffQuantity: 5,
                createTime: '2023-06-10 09:15:00'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 导入
function handleImport() {
    obj.dialogShow = true;
}

// 关闭导入弹窗
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {
        remark: '',
        file: null
    };
}

// 下载模版
function handleDownload() {
    // 模拟下载模板文件
    const link = document.createElement('a');
    link.href = '#';
    link.setAttribute('download', '供应商账单比对差异模板.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

getList();
</script>
<style lang="scss" scoped></style>