<!-- 残障金收取备案 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="城市:" prop="cityCode">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractNo">
                <el-input class="width220" v-model="obj.queryParams.contractNo" placeholder="请输入合同编号"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="备案类型:" prop="recordType">
                <el-select class="width220" filterable v-model="obj.queryParams.recordType" placeholder="请选择备案类型"
                    clearable>
                    <el-option v-for="item in recordTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方:" prop="orderReceiver">
                <el-select class="width220" filterable v-model="obj.queryParams.orderReceiver" placeholder="请选择接单方"
                    clearable>
                    <el-option v-for="item in orderReceiverOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="withholdingAgent">
                <el-select class="width220" filterable v-model="obj.queryParams.withholdingAgent" placeholder="请选择扣缴义务人"
                    clearable>
                    <el-option v-for="item in withholdingAgentOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="View" :disabled="obj.single" @click="handleUpdate">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" :disabled="obj.single"
                    @click="handleExport">导出</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" type="index" width="80" align="center" />
            <el-table-column label="城市名称" align="center" prop="cityName" />
            <el-table-column label="合同编号" align="center" prop="contractNo" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="扣缴义务人名称" align="center" prop="withholdingAgentName" />
            <el-table-column label="接单方" align="center" prop="orderReceiver" />
            <el-table-column label="备案类型" align="center" prop="recordType">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.recordType === '1' ? 'success' : scope.row.recordType === '2' ? 'warning' : 'info'">
                        {{ scope.row.recordType === '1' ? '正常备案' : scope.row.recordType === '2' ? '特殊备案' : '其他备案' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="备案内容" align="center" prop="recordContent" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="35%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item label="城市" prop="cityCode">
                    <el-select class="width420" v-model="obj.dialogForm.cityCode" placeholder="请选择城市" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同名称" prop="contractName">
                    <el-input readonly class="width420" v-model="obj.dialogForm.contractName" placeholder="请选择合同"
                        @click="handleCustomer" />
                </el-form-item>
                <el-form-item label="合同编号" prop="contractNo">
                    <el-input readonly class="width420" v-model="obj.dialogForm.contractNo" placeholder="自动带出" />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-input readonly class="width420" v-model="obj.dialogForm.customerName" placeholder="自动带出" />
                </el-form-item>
                <el-form-item label="备案类型" prop="recordType">
                    <el-select class="width420" v-model="obj.dialogForm.recordType" placeholder="请选择备案类型" clearable>
                        <el-option v-for="item in recordTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="接单方" prop="orderReceiver">
                    <el-select class="width420" v-model="obj.dialogForm.orderReceiver" placeholder="请选择接单方" clearable>
                        <el-option v-for="item in orderReceiverOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="扣缴义务人名称" prop="withholdingAgentName">
                    <el-select class="width420" v-model="obj.dialogForm.withholdingAgentName" placeholder="请选择扣缴义务人"
                        clearable>
                        <el-option v-for="item in withholdingAgentOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="扣缴义务人类型" prop="withholdingAgentType">
                    <el-select class="width420" v-model="obj.dialogForm.withholdingAgentType" placeholder="请选择扣缴义务人类型"
                        clearable>
                        <el-option v-for="item in withholdingAgentTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="文件上传" prop="fileUpload">
                    <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
                </el-form-item>
                <el-form-item label="备案内容" prop="recordContent">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.recordContent"
                        placeholder="请输入备案内容" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 客户信息 -->
        <el-dialog v-model="obj.customerDialogShow" width="35%" append-to-body draggable>
            <el-row :gutter="10" class="mb8">
                <el-col :span="14">
                    <el-input class="width320" v-model="obj.queryParams.sales" placeholder="客户名称/编号/合同名称/编号" clearable>
                        <template #append>
                            <el-button @click="handleCustomerQuery" icon="Search" />
                        </template>
                    </el-input>
                </el-col>
                <el-col :span="10">
                    <el-form-item>
                        <el-button type="primary" @click="handleCustomerQuery">选择</el-button>
                        <el-button icon="Refresh" @click="resetCustomerQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-table :data="obj.customerTableData" border @selection-change="handleCustomerSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="合同编号" align="center" prop="contractNo" />
                <el-table-column label="合同名称" align="center" prop="contractName" />
                <el-table-column label="客户编号" align="center" prop="customerNo" />
                <el-table-column label="客户名称" align="center" prop="customerName" />
                <el-table-column label="合同类型" align="center" prop="contractType" />
            </el-table>
            <!-- 分页 -->
            <pagination v-show="obj.customerTotal > 0" :total="obj.customerTotal"
                v-model:page="obj.customerQueryParams.pageNum" v-model:limit="obj.customerQueryParams.pageSize"
                @pagination="getCustomerList" />
        </el-dialog>
    </div>
</template>

<script setup name="DisabilityAllowanceCollectRecord">

import { useAreaStore } from '@/store/modules/area'
import { listImport } from "@/api/reonApi/import";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据

// 备案类型选项
const recordTypeOptions = [
    { value: '1', label: '正常备案' },
    { value: '2', label: '特殊备案' },
    { value: '3', label: '其他备案' }
];

// 接单方选项
const orderReceiverOptions = [
    { value: '1', label: '总公司' },
    { value: '2', label: '分公司' },
    { value: '3', label: '分支机构' },
    { value: '4', label: '业务部门' }
];

// 扣缴义务人选项
const withholdingAgentOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 扣缴义务人类型选项
const withholdingAgentTypeOptions = [
    { value: '1', label: '企业' },
    { value: '2', label: '个人' },
    { value: '3', label: '其他' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cityCode: null,
        contractNo: null,
        customerName: null,
        recordType: null,
        orderReceiver: null,
        withholdingAgent: null,
    },//查询表单
    customerQueryParams: {
        pageNum: 1,
        pageSize: 10,
    },//客户查询表单
    rules: {
        cityCode: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        contractName: [{ required: true, message: '请选择合同', trigger: 'blur' }],
        recordType: [{ required: true, message: '请选择备案类型', trigger: 'blur' }],
        orderReceiver: [{ required: true, message: '请选择接单方', trigger: 'blur' }],
        withholdingAgentName: [{ required: true, message: '请选择扣缴义务人', trigger: 'blur' }],
        recordContent: [{ required: true, message: '请输入备案内容', trigger: 'blur' }],
    },
    total: 0,//总条数
    customerTotal: 0,//客户总条数
    tableData: [
        {
            id: 1,
            cityName: '北京市',
            cityCode: '110000',
            contractNo: 'HT20230501001',
            contractType: '残保金服务合同',
            customerName: '客户A',
            withholdingAgentName: '公司A',
            withholdingAgent: '1',
            orderReceiver: '总公司',
            recordType: '1',
            recordContent: '残保金收取备案内容A'
        },
        {
            id: 2,
            cityName: '上海市',
            cityCode: '310000',
            contractNo: 'HT20230502001',
            contractType: '残保金服务合同',
            customerName: '客户B',
            withholdingAgentName: '公司B',
            withholdingAgent: '2',
            orderReceiver: '分公司',
            recordType: '2',
            recordContent: '残保金收取备案内容B'
        },
        {
            id: 3,
            cityName: '广州市',
            cityCode: '440100',
            contractNo: 'HT20230503001',
            contractType: '残保金服务合同',
            customerName: '客户C',
            withholdingAgentName: '公司C',
            withholdingAgent: '3',
            orderReceiver: '分支机构',
            recordType: '3',
            recordContent: '残保金收取备案内容C'
        }
    ],//列表
    customerTableData: [
        {
            id: 1,
            contractNo: 'HT20230501001',
            contractName: '残保金服务合同A',
            customerNo: 'KH20230501001',
            customerName: '客户A',
            contractType: '残保金服务合同'
        },
        {
            id: 2,
            contractNo: 'HT20230502001',
            contractName: '残保金服务合同B',
            customerNo: 'KH20230502001',
            customerName: '客户B',
            contractType: '残保金服务合同'
        },
        {
            id: 3,
            contractNo: 'HT20230503001',
            contractName: '残保金服务合同C',
            customerNo: 'KH20230503001',
            customerName: '客户C',
            contractType: '残保金服务合同'
        }
    ],//客户列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = JSON.parse(JSON.stringify(row || obj.tableData.find(item => obj.ids.includes(item.id))));
    obj.dialogShow = true;
    obj.title = "修改";
}

/** 客户信息 */
function handleCustomer() {
    obj.customerDialogShow = true;
}

/** 客户查询 */
function handleCustomerQuery() {
    // 模拟查询客户信息
    obj.customerTotal = obj.customerTableData.length;
}

/** 重置客户查询 */
function resetCustomerQuery() {
    obj.customerQueryParams = {
        pageNum: 1,
        pageSize: 10,
    };
    handleCustomerQuery();
}

/** 客户选择 */
function handleCustomerSelectionChange(selection) {
    if (selection.length > 0) {
        const selectedCustomer = selection[0];
        obj.dialogForm.contractName = selectedCustomer.contractName;
        obj.dialogForm.contractNo = selectedCustomer.contractNo;
        obj.dialogForm.customerName = selectedCustomer.customerName;
        obj.customerDialogShow = false;
    }
}

/** 获取客户列表 */
function getCustomerList() {
    // 模拟获取客户列表
    obj.customerTotal = obj.customerTableData.length;
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped></style>