<!-- 税率表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="税率表名称:" prop="rateName">
                <el-input class="width220" v-model="obj.queryParams.rateName" placeholder="请输入税率表名称" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @row-dblclick="handleRowDblclick">
            <el-table-column label="序号" type="index" align="center" width="80" />
            <el-table-column label="税率表名称" align="center" prop="name" />
            <el-table-column label="费用扣除额" align="center" prop="startPoint" />
            <el-table-column label="说明" align="center" prop="remark" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 详情 -->
        <el-dialog v-model="obj.dialogShow" title="查看税率表详情" width="50%">
            <el-form :model="obj.dialogForm" ref="dialogRef" inline label-width="auto">
                <el-form-item label="税率表名称:" prop="name">
                    <el-input class="width220" v-model="obj.dialogForm.name" placeholder="请输入税率表名称" readonly />
                </el-form-item>
                <el-form-item label="起征点:" prop="startPoint">
                    <el-input class="width220" v-model="obj.dialogForm.startPoint" placeholder="请输入起征点" readonly />
                </el-form-item>
                <el-form-item label="说明:" prop="remark">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入说明"
                        readonly />
                </el-form-item>
                <border-box title="计算区间">
                    <el-table :data="obj.dialogForm.tableData" border>
                        <el-table-column label="序号" type="index" align="center" width="80" />
                        <el-table-column label="税率级次" align="center" prop="level" />
                        <el-table-column label="最小值" align="center" prop="minValue" />
                        <el-table-column label="最大值" align="center" prop="maxValue" />
                        <el-table-column label="税率(%)" align="center" prop="rate" />
                        <el-table-column label="速算扣除数" align="center" prop="quickDeduction" />
                    </el-table>
                    <pagination v-show="obj.dialogForm.total > 0" :total="obj.dialogForm.total"
                        v-model:page="obj.dialogForm.pageNum" v-model:limit="obj.dialogForm.pageSize"
                        @pagination="getList" />
                </border-box>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup name="TaxRateTable">

import { listImport } from "@/api/reonApi/import";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    showSearch: true,//显示搜索条件
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        rateName: null,
    },//查询表单

    total: 0,//总条数
    tableData: [
        {
            id: 1,
            name: '个人所得税税率表',
            startPoint: '5000',
            remark: '个人所得税税率表，起征点5000元'
        },
        {
            id: 2,
            name: '企业所得税税率表',
            startPoint: '0',
            remark: '企业所得税税率表，统一税率25%'
        },
        {
            id: 3,
            name: '增值税税率表',
            startPoint: '0',
            remark: '增值税税率表，一般税率13%'
        }
    ],//列表

    dialogShow: false,//详情弹窗
    dialogForm: {
        tableData: [
            {
                id: 1,
                level: 1,
                minValue: '0',
                maxValue: '36000',
                rate: '3',
                quickDeduction: '0'
            },
            {
                id: 2,
                level: 2,
                minValue: '36000',
                maxValue: '144000',
                rate: '10',
                quickDeduction: '2520'
            },
            {
                id: 3,
                level: 3,
                minValue: '144000',
                maxValue: '300000',
                rate: '20',
                quickDeduction: '16920'
            },
            {
                id: 4,
                level: 4,
                minValue: '300000',
                maxValue: '420000',
                rate: '25',
                quickDeduction: '31920'
            },
            {
                id: 5,
                level: 5,
                minValue: '420000',
                maxValue: '660000',
                rate: '30',
                quickDeduction: '52920'
            },
            {
                id: 6,
                level: 6,
                minValue: '660000',
                maxValue: '960000',
                rate: '35',
                quickDeduction: '85920'
            },
            {
                id: 7,
                level: 7,
                minValue: '960000',
                maxValue: '无限大',
                rate: '45',
                quickDeduction: '181920'
            }
        ],
        total: 7,
        pageNum: 1,
        pageSize: 10
    },//详情表单
})

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 双击行 */
function handleRowDblclick(row) {
    obj.dialogForm = {
        ...obj.dialogForm,
        ...row
    };

    // 根据税率表名称设置不同的计算区间数据
    if (row.name === '个人所得税税率表') {
        obj.dialogForm.tableData = [
            {
                id: 1,
                level: 1,
                minValue: '0',
                maxValue: '36000',
                rate: '3',
                quickDeduction: '0'
            },
            {
                id: 2,
                level: 2,
                minValue: '36000',
                maxValue: '144000',
                rate: '10',
                quickDeduction: '2520'
            },
            {
                id: 3,
                level: 3,
                minValue: '144000',
                maxValue: '300000',
                rate: '20',
                quickDeduction: '16920'
            },
            {
                id: 4,
                level: 4,
                minValue: '300000',
                maxValue: '420000',
                rate: '25',
                quickDeduction: '31920'
            },
            {
                id: 5,
                level: 5,
                minValue: '420000',
                maxValue: '660000',
                rate: '30',
                quickDeduction: '52920'
            },
            {
                id: 6,
                level: 6,
                minValue: '660000',
                maxValue: '960000',
                rate: '35',
                quickDeduction: '85920'
            },
            {
                id: 7,
                level: 7,
                minValue: '960000',
                maxValue: '无限大',
                rate: '45',
                quickDeduction: '181920'
            }
        ];
        obj.dialogForm.total = 7;
    } else if (row.name === '企业所得税税率表') {
        obj.dialogForm.tableData = [
            {
                id: 1,
                level: 1,
                minValue: '0',
                maxValue: '无限大',
                rate: '25',
                quickDeduction: '0'
            }
        ];
        obj.dialogForm.total = 1;
    } else if (row.name === '增值税税率表') {
        obj.dialogForm.tableData = [
            {
                id: 1,
                level: 1,
                minValue: '0',
                maxValue: '无限大',
                rate: '13',
                quickDeduction: '0'
            },
            {
                id: 2,
                level: 2,
                minValue: '0',
                maxValue: '无限大',
                rate: '9',
                quickDeduction: '0'
            },
            {
                id: 3,
                level: 3,
                minValue: '0',
                maxValue: '无限大',
                rate: '6',
                quickDeduction: '0'
            }
        ];
        obj.dialogForm.total = 3;
    }

    obj.dialogShow = true;
}

getList();
</script>
<style lang="scss" scoped></style>