<!-- 银企直连-复核-自有工资支付 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="收款方:" prop="receiver">
                <el-input class="width220" v-model="obj.queryParams.receiver" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="支付类型:" prop="payType">
                <el-select class="width220" v-model="obj.queryParams.payType" placeholder="请选择" clearable>
                    <el-option v-for="item in payTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请人:" prop="applicant">
                <el-select class="width220" v-model="obj.queryParams.applicant" placeholder="请选择" clearable>
                    <el-option v-for="item in applicantOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付方式:" prop="paymentMethod">
                <el-select class="width220" v-model="obj.queryParams.paymentMethod" placeholder="请选择" clearable>
                    <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="申请支付时间从:" prop="applyTimeFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeFrom" type="date" placeholder="请选择日期"
                    value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="申请支付时间到:" prop="applyTimeTo">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeTo" type="date" placeholder="请选择日期"
                    value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="工资支付日期:" prop="salaryPayDate">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryPayDate" type="date" placeholder="请选择日期"
                    value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="是否退票重发:" prop="isRefund">
                <el-select class="width220" v-model="obj.queryParams.isRefund" placeholder="请选择" clearable>
                    <el-option v-for="item in refundOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">出款提交</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="bank-recheck-ownWagePayment" />
            <el-button type="primary" plain icon="Download" @click="handleExport">导出</el-button>
            <el-button type="primary" plain icon="Printer" @click="handlePrint">打印</el-button>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column v-if="obj.selectedColumns.includes('companyName')" label="出款公司名称" align="center"
                min-width="150" prop="companyName" />
            <el-table-column v-if="obj.selectedColumns.includes('customerName')" label="客户名称" align="center"
                min-width="150" prop="customerName" />
            <el-table-column v-if="obj.selectedColumns.includes('receiver')" label="收款方" align="center" width="100"
                prop="receiver" />
            <el-table-column v-if="obj.selectedColumns.includes('salaryPayDate')" label="工资支付日期" align="center"
                width="120" prop="salaryPayDate" />
            <el-table-column v-if="obj.selectedColumns.includes('total')" label="总计" align="center" width="100"
                prop="total">
                <template #default="scope">
                    {{ formatAmount(scope.row.total) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('productPlan')" label="产品方案" align="center" width="120"
                prop="productPlan" />
            <el-table-column v-if="obj.selectedColumns.includes('payType')" label="支付类型" align="center" width="100"
                prop="payType">
                <template #default="scope">
                    {{ getPayTypeName(scope.row.payType) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('isRefund')" label="是否退票重发" align="center" width="120"
                prop="isRefund">
                <template #default="scope">
                    {{ getRefundLabel(scope.row.isRefund) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('payMonth')" label="支付所属年月" align="center" width="120"
                prop="payMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('actualSalary')" label="实付工资款" align="center"
                width="120" prop="actualSalary">
                <template #default="scope">
                    {{ formatAmount(scope.row.actualSalary) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('personalTax')" label="个税款" align="center" width="100"
                prop="personalTax">
                <template #default="scope">
                    {{ formatAmount(scope.row.personalTax) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('compensation')" label="补偿金" align="center" width="100"
                prop="compensation">
                <template #default="scope">
                    {{ formatAmount(scope.row.compensation) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('compensationTax')" label="补偿金个税" align="center"
                width="120" prop="compensationTax">
                <template #default="scope">
                    {{ formatAmount(scope.row.compensationTax) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('annualBonus')" label="年终奖" align="center" width="100"
                prop="annualBonus">
                <template #default="scope">
                    {{ formatAmount(scope.row.annualBonus) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('annualBonusTax')" label="年终奖个税" align="center"
                width="120" prop="annualBonusTax">
                <template #default="scope">
                    {{ formatAmount(scope.row.annualBonusTax) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('laborSalary')" label="劳务工资" align="center" width="100"
                prop="laborSalary">
                <template #default="scope">
                    {{ formatAmount(scope.row.laborSalary) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('laborSalaryTax')" label="劳务工资个税" align="center"
                width="120" prop="laborSalaryTax">
                <template #default="scope">
                    {{ formatAmount(scope.row.laborSalaryTax) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('serviceFee')" label="服务费" align="center" width="100"
                prop="serviceFee">
                <template #default="scope">
                    {{ formatAmount(scope.row.serviceFee) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('disabilityFund')" label="残障金" align="center"
                width="100" prop="disabilityFund">
                <template #default="scope">
                    {{ formatAmount(scope.row.disabilityFund) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('crossBankFee')" label="跨行手续费" align="center"
                width="120" prop="crossBankFee">
                <template #default="scope">
                    {{ formatAmount(scope.row.crossBankFee) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('unionFee')" label="工会费" align="center" width="100"
                prop="unionFee">
                <template #default="scope">
                    {{ formatAmount(scope.row.unionFee) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('totalTax')" label="税金合计" align="center" width="100"
                prop="totalTax">
                <template #default="scope">
                    {{ formatAmount(scope.row.totalTax) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('applicant')" label="申请人" align="center" width="100"
                prop="applicant">
                <template #default="scope">
                    {{ getApplicantName(scope.row.applicant) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('status')" label="制单状态" align="center" width="100"
                prop="status">
                <template #default="scope">
                    {{ getStatusName(scope.row.status) }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="Bank_recheck_ownWagePayment">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 支付类型选项
const payTypeOptions = [
    { value: '1', label: '工资' },
    { value: '2', label: '年终奖' },
    { value: '3', label: '补偿金' },
    { value: '4', label: '劳务工资' },
    { value: '5', label: '其他' }
];

// 申请人选项
const applicantOptions = [
    { value: '1', label: '张三' },
    { value: '2', label: '李四' },
    { value: '3', label: '王五' },
    { value: '4', label: '赵六' },
    { value: '5', label: '孙七' }
];

// 支付方式选项
const paymentMethodOptions = [
    { value: '1', label: '银行转账' },
    { value: '2', label: '现金支付' },
    { value: '3', label: '支票支付' },
    { value: '4', label: '其他方式' }
];

// 是否退票重发选项
const refundOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' }
];

// 制单状态选项
const statusOptions = [
    { value: '1', label: '待复核' },
    { value: '2', label: '已复核' },
    { value: '3', label: '已驳回' },
    { value: '4', label: '已取消' }
];

// 银企直连-复核-自有工资支付数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        receiver: '', // 收款方
        payType: '', // 支付类型
        applicant: '', // 申请人
        paymentMethod: '', // 支付方式
        customerName: '', // 客户名称
        applyTimeFrom: '', // 申请支付时间开始
        applyTimeTo: '', // 申请支付时间结束
        salaryPayDate: '', // 工资支付日期
        isRefund: '' // 是否退票重发
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            companyName: '北京科技有限公司',
            customerName: '北京科技有限公司',
            receiver: '张三',
            salaryPayDate: '2023-05-01',
            total: 100000.00,
            productPlan: '标准方案',
            payType: '1',
            isRefund: '0',
            payMonth: '2023-05',
            actualSalary: 80000.00,
            personalTax: 10000.00,
            compensation: 0.00,
            compensationTax: 0.00,
            annualBonus: 0.00,
            annualBonusTax: 0.00,
            laborSalary: 0.00,
            laborSalaryTax: 0.00,
            serviceFee: 5000.00,
            disabilityFund: 2000.00,
            crossBankFee: 0.00,
            unionFee: 3000.00,
            totalTax: 10000.00,
            applicant: '1',
            status: '1'
        },
        {
            id: 2,
            companyName: '上海贸易有限公司',
            customerName: '上海贸易有限公司',
            receiver: '李四',
            salaryPayDate: '2023-05-02',
            total: 150000.00,
            productPlan: '高级方案',
            payType: '1',
            isRefund: '0',
            payMonth: '2023-05',
            actualSalary: 120000.00,
            personalTax: 15000.00,
            compensation: 0.00,
            compensationTax: 0.00,
            annualBonus: 0.00,
            annualBonusTax: 0.00,
            laborSalary: 0.00,
            laborSalaryTax: 0.00,
            serviceFee: 7500.00,
            disabilityFund: 3000.00,
            crossBankFee: 0.00,
            unionFee: 4500.00,
            totalTax: 15000.00,
            applicant: '2',
            status: '1'
        },
        {
            id: 3,
            companyName: '广州电子有限公司',
            customerName: '广州电子有限公司',
            receiver: '王五',
            salaryPayDate: '2023-05-03',
            total: 80000.00,
            productPlan: '基础方案',
            payType: '1',
            isRefund: '0',
            payMonth: '2023-05',
            actualSalary: 64000.00,
            personalTax: 8000.00,
            compensation: 0.00,
            compensationTax: 0.00,
            annualBonus: 0.00,
            annualBonusTax: 0.00,
            laborSalary: 0.00,
            laborSalaryTax: 0.00,
            serviceFee: 4000.00,
            disabilityFund: 1600.00,
            crossBankFee: 0.00,
            unionFee: 2400.00,
            totalTax: 8000.00,
            applicant: '3',
            status: '2'
        },
        {
            id: 4,
            companyName: '深圳科技有限公司',
            customerName: '深圳科技有限公司',
            receiver: '赵六',
            salaryPayDate: '2023-05-04',
            total: 200000.00,
            productPlan: '高级方案',
            payType: '2',
            isRefund: '0',
            payMonth: '2023-05',
            actualSalary: 0.00,
            personalTax: 0.00,
            compensation: 0.00,
            compensationTax: 0.00,
            annualBonus: 160000.00,
            annualBonusTax: 20000.00,
            laborSalary: 0.00,
            laborSalaryTax: 0.00,
            serviceFee: 10000.00,
            disabilityFund: 4000.00,
            crossBankFee: 0.00,
            unionFee: 6000.00,
            totalTax: 20000.00,
            applicant: '4',
            status: '3'
        },
        {
            id: 5,
            companyName: '成都信息有限公司',
            customerName: '成都信息有限公司',
            receiver: '孙七',
            salaryPayDate: '2023-05-05',
            total: 120000.00,
            productPlan: '标准方案',
            payType: '3',
            isRefund: '1',
            payMonth: '2023-05',
            actualSalary: 0.00,
            personalTax: 0.00,
            compensation: 96000.00,
            compensationTax: 12000.00,
            annualBonus: 0.00,
            annualBonusTax: 0.00,
            laborSalary: 0.00,
            laborSalaryTax: 0.00,
            serviceFee: 6000.00,
            disabilityFund: 2400.00,
            crossBankFee: 0.00,
            unionFee: 3600.00,
            totalTax: 12000.00,
            applicant: '5',
            status: '4'
        }
    ],

    // 表单相关数据
    dialogForm: {}, // 表单数据
    dialogShow: false, // 弹出框显示状态
    title: '', // 弹出框标题
    ids: [], // 选中的id

    // 列筛选相关数据
    columnOptions: [
        { prop: 'companyName', label: '出款公司名称' },
        { prop: 'customerName', label: '客户名称' },
        { prop: 'receiver', label: '收款方' },
        { prop: 'salaryPayDate', label: '工资支付日期' },
        { prop: 'total', label: '总计' },
        { prop: 'productPlan', label: '产品方案' },
        { prop: 'payType', label: '支付类型' },
        { prop: 'isRefund', label: '是否退票重发' },
        { prop: 'payMonth', label: '支付所属年月' },
        { prop: 'actualSalary', label: '实付工资款' },
        { prop: 'personalTax', label: '个税款' },
        { prop: 'compensation', label: '补偿金' },
        { prop: 'compensationTax', label: '补偿金个税' },
        { prop: 'annualBonus', label: '年终奖' },
        { prop: 'annualBonusTax', label: '年终奖个税' },
        { prop: 'laborSalary', label: '劳务工资' },
        { prop: 'laborSalaryTax', label: '劳务工资个税' },
        { prop: 'serviceFee', label: '服务费' },
        { prop: 'disabilityFund', label: '残障金' },
        { prop: 'crossBankFee', label: '跨行手续费' },
        { prop: 'unionFee', label: '工会费' },
        { prop: 'totalTax', label: '税金合计' },
        { prop: 'applicant', label: '申请人' },
        { prop: 'status', label: '制单状态' }
    ],
    selectedColumns: [
        'companyName', 'customerName', 'receiver', 'salaryPayDate', 'total', 'productPlan',
        'payType', 'isRefund', 'payMonth', 'actualSalary', 'personalTax', 'compensation',
        'compensationTax', 'annualBonus', 'annualBonusTax', 'laborSalary', 'laborSalaryTax',
        'serviceFee', 'disabilityFund', 'crossBankFee', 'unionFee', 'totalTax', 'applicant', 'status'
    ]
});

/**
 * 获取支付类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getPayTypeName(typeId) {
    if (!typeId) return '-';

    const type = payTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}

/**
 * 获取申请人名称
 * @param {string} applicantId 申请人ID
 * @returns {string} 申请人名称
 */
function getApplicantName(applicantId) {
    if (!applicantId) return '-';

    const applicant = applicantOptions.find(item => item.value === applicantId);
    return applicant ? applicant.label : '-';
}

/**
 * 获取是否退票重发标签
 * @param {string} value 值
 * @returns {string} 标签
 */
function getRefundLabel(value) {
    return value === '1' ? '是' : '否';
}

/**
 * 获取制单状态名称
 * @param {string} statusId 状态ID
 * @returns {string} 状态名称
 */
function getStatusName(statusId) {
    if (!statusId) return '-';

    const status = statusOptions.find(item => item.value === statusId);
    return status ? status.label : '-';
}



/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.receiver) {
            filteredData = filteredData.filter(item =>
                item.receiver.includes(obj.queryParams.receiver)
            );
        }

        if (obj.queryParams.payType) {
            filteredData = filteredData.filter(item =>
                item.payType === obj.queryParams.payType
            );
        }

        if (obj.queryParams.applicant) {
            filteredData = filteredData.filter(item =>
                item.applicant === obj.queryParams.applicant
            );
        }

        if (obj.queryParams.paymentMethod) {
            // 这里简化处理，实际应用中需要根据实际字段进行筛选
        }

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item =>
                item.customerName.includes(obj.queryParams.customerName)
            );
        }

        if (obj.queryParams.applyTimeFrom) {
            // 这里简化处理，实际应用中需要根据实际字段进行筛选
        }

        if (obj.queryParams.applyTimeTo) {
            // 这里简化处理，实际应用中需要根据实际字段进行筛选
        }

        if (obj.queryParams.salaryPayDate) {
            filteredData = filteredData.filter(item =>
                item.salaryPayDate === obj.queryParams.salaryPayDate
            );
        }

        if (obj.queryParams.isRefund) {
            filteredData = filteredData.filter(item =>
                item.isRefund === obj.queryParams.isRefund
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 出款提交按钮操作 */
function handleAdd() {
    // 重置表单
    obj.dialogForm = {};
    if (proxy.$refs["formRef"]) {
        proxy.$refs["formRef"].resetFields();
    }

    obj.dialogShow = true;
    obj.title = "出款提交";
}

/** 导出按钮操作 */
function handleExport() {

}

/** 打印按钮操作 */
function handlePrint() {

}

// 初始化加载数据
onMounted(() => {
    getList();
});

</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>