<!-- 单立户模块 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="单立户ID:" prop="accountId">
                <el-input v-model="obj.queryParams.accountId" placeholder="请输入单立户ID" clearable />
            </el-form-item>
            <el-form-item label="单立户名称:" prop="accountName">
                <el-input v-model="obj.queryParams.accountName" placeholder="请输入单立户名称" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="所属城市:" prop="city">
                <el-input v-model="obj.queryParams.city" placeholder="请输入所属城市" clearable />
            </el-form-item>
            <el-form-item label="小合同/福利包Code:" prop="contractCode">
                <el-input v-model="obj.queryParams.contractCode" placeholder="请输入小合同/福利包Code" clearable />
            </el-form-item>
            <el-form-item label="关联类型:" prop="relationType">
                <el-select v-model="obj.queryParams.relationType" placeholder="请选择关联类型" clearable>
                    <el-option label="小合同" value="1" />
                    <el-option label="福利包" value="2" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="ID" align="center" prop="id" />
            <el-table-column label="单立户名称" align="center" prop="accountName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="关联编号" align="center" prop="relationCode" />
            <el-table-column label="类型" align="center" prop="relationType" />
            <el-table-column label="小合同/福利包" align="center" prop="contractName" />
            <el-table-column label="城市" align="center" prop="city" />
            <el-table-column label="创建人" align="center" prop="createBy" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 新增 -->
        <el-dialog v-model="obj.dialogShow" title="新增" width="50%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <el-form-item label="单立户名称:" prop="accountName">
                    <el-input class="width180" v-model="obj.dialogForm.accountName" placeholder="请输入单立户名称" clearable />
                </el-form-item>
                <el-form-item label="客户名称:" prop="customerName">
                    <el-input class="width180" v-model="obj.dialogForm.customerName" placeholder="请输入客户名称" clearable />
                </el-form-item>
                <el-form-item label="客户编号:" prop="customerCode">
                    <el-input class="width180" v-model="obj.dialogForm.customerCode" placeholder="请输入客户编号" clearable />
                </el-form-item>
                <el-form-item label="关联类型:" prop="relationType">
                    <el-select class="width180" v-model="obj.dialogForm.relationType" placeholder="请选择关联类型" clearable>
                        <el-option label="小合同" value="1" />
                        <el-option label="福利包" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="小合同/福利包:" prop="contractName">
                    <el-input class="width180" v-model="obj.dialogForm.contractName" placeholder="请输入小合同/福利包"
                        clearable />
                </el-form-item>
                <el-form-item label="所属城市:" prop="city">
                    <el-select class="width180" v-model="obj.dialogForm.city" placeholder="请选择所属城市" clearable>
                        <el-option label="北京" value="北京" />
                        <el-option label="上海" value="上海" />
                        <el-option label="广州" value="广州" />
                        <el-option label="深圳" value="深圳" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleAdd">保存</el-button>
                <el-button type="primary" @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>

        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow2" title="上传导入数据" type="singleAccountModule"
            :dialogForm="obj.dialogForm" :rules="rules" />
    </div>
</template>

<script setup name="SingleAccountModule">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    accountName: [
        { required: true, message: '单立户名称不能为空', trigger: 'blur' }
    ],
    customerName: [
        { required: true, message: '客户名称不能为空', trigger: 'blur' }
    ],
    city: [
        { required: true, message: '所属城市不能为空', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountId: null,
        accountName: null,
        customerName: null,
        city: null,
        contractCode: null,
        relationType: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {},//表单数据
    dialogShow: false,//新增弹窗
    dialogShow2: false,//导入弹窗
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                accountName: '单立户名称1',
                customerName: '客户名称1',
                customerCode: 'KH20230001',
                relationCode: 'GL20230001',
                relationType: '小合同',
                contractName: '小合同名称1',
                city: '北京',
                createBy: '创建人1'
            },
            {
                id: 2,
                accountName: '单立户名称2',
                customerName: '客户名称2',
                customerCode: '**********',
                relationCode: '**********',
                relationType: '福利包',
                contractName: '福利包名称1',
                city: '上海',
                createBy: '创建人2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    obj.dialogShow = true;
    obj.dialogForm = {
        accountName: '',
        customerName: '',
        customerCode: '',
        relationType: '',
        contractName: '',
        city: ''
    };
}

/** 修改按钮操作 */
function handleEdit() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条数据进行修改');
        return;
    }
    obj.dialogShow = true;
    // 模拟数据，实际开发时可以从表格中获取
    const row = obj.tableData.find(item => item.id === obj.ids[0]);
    if (row) {
        obj.dialogForm = { ...row };
    }
}

/** 导入按钮操作 */
function handleImport() {
    obj.dialogShow2 = true;
    obj.dialogForm = {
        remark: '',
        importFile: []
    };
}

/** 关闭导入弹窗 */
function handleClose() {
    obj.dialogShow2 = false;
    obj.dialogForm = {};
}

/** 删除按钮操作 */
function handleDelete(row) {
    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>