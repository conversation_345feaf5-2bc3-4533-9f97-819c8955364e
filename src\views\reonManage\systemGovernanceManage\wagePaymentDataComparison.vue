<!-- 工资支付数据比对 -->
<template>
    <div class="app-container">
        <el-form :model="generate" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-row class="mb8">
                <el-form-item>
                    生成工资支付个税对比结果
                </el-form-item>
                <el-form-item label="计税月:" prop="taxMonth">
                    <el-input class="width220" v-model="generate.taxMonth" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="个税申报类型:" prop="taxType">
                    <el-select class="width220" v-model="generate.taxType" placeholder="请选择">
                        <el-option label="个税申报类型" value="1" />
                        <el-option label="个税申报类型" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleCompareData">生成个税比对数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    生成工资支付实发对比结果
                </el-form-item>
                <el-form-item label="支付年月:" prop="paymentYearMonth">
                    <el-input class="width220" v-model="generate.paymentYearMonth" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerateData">生成数据</el-button>
                </el-form-item>
            </el-row>
        </el-form>

        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="姓名:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="证件号:" prop="idCard">
                <el-input class="width220" v-model="obj.queryParams.idCard" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="数据年月:" prop="dataYearMonth">
                <el-date-picker v-model="obj.queryParams.dataYearMonth" type="month" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="deductibleName">
                <el-input class="width220" v-model="obj.queryParams.deductibleName" placeholder="请输入" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="Upload" @click="handleExport">导出</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="证件号码" align="center" prop="idCard" />
            <el-table-column label="工资比较类型" align="center" prop="wageCompareType" />
            <el-table-column label="差异" align="center" prop="difference" />
            <el-table-column label="支付年月" align="center" prop="paymentYearMonth" />
            <el-table-column label="计税月" align="center" prop="taxMonth" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="项目客服" align="center" prop="projectCustomer" />
            <el-table-column label="薪资创建人" align="center" prop="salaryCreator" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="扣缴义务人名称" align="center" prop="deductibleName" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>


<script setup name="WagePaymentDataComparison">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const generate = ref({
    taxMonth: '',
    paymentYearMonth: '',
})

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})


/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        obj.tableData = [
            {
                name: '张三',
                idCard: '123456789012345678',
                wageCompareType: '工资比较类型',
                difference: '差异',
                paymentYearMonth: '支付年月',
            }
        ];
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}
/** 生成个税比对数据 */
function handleCompareData() {

}

/** 生成工资支付实发对比结果 */
function handleGenerateData() {

}
/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped></style>