<!-- 批量变更账单模板 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="变更名称:" prop="changeName">
                <el-input class="width220" v-model="obj.queryParams.changeName" placeholder="请输入变更名称" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="开始时间:" prop="startDate">
                <el-date-picker v-model="obj.queryParams.startDate" type="date" placeholder="请选择开始日期" />
            </el-form-item>
            <el-form-item label="结束时间:" prop="endDate">
                <el-date-picker v-model="obj.queryParams.endDate" type="date" placeholder="请选择结束日期" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleAdd">新增变更</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleInfo">查看变更信息</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleInfo">
            <el-table-column type="selection" width="55" />
            <el-table-column label="变更编号" align="center" prop="changeCode" />
            <el-table-column label="变更名称" align="center" prop="changeName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="原账单模板" align="center" prop="originalBillingTemplate" />
            <el-table-column label="原收费模板" align="center" prop="originalChargeTemplate" />
            <el-table-column label="新账单模板" align="center" prop="newBillingTemplate" />
            <el-table-column label="新收费模板" align="center" prop="newChargeTemplate" />
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" plain @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增 -->
        <el-dialog v-model="obj.dialogShow" title="新增变更" width="65%" append-to-body draggable @close="handleClose">
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <border-box title="变更设定条件">
                    <el-form-item label="客户:" prop="typeCode">
                        <el-input class="width220" v-model="obj.dialogForm.typeCode" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="大合同:" prop="typeCode">
                        <el-input class="width220" v-model="obj.dialogForm.typeCode" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="原账单模板:" prop="typeCode">
                        <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                            <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="原收费模板:" prop="typeCode">
                        <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                            <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="1.5">
                            <el-button type="primary" plain @click="handleQuery">查询</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button plain @click="handleQuery">变更条件</el-button>
                        </el-col>
                    </el-row>
                </border-box>
                <border-box title="查询结果">
                    <el-row :gutter="10" class="mb8">
                        <el-form-item label="唯一号/姓名/证件号码/订单号:" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.typeCode" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="小合同名称/小合同编号:" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.typeCode" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="入离职状态:" prop="typeCode">
                            <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                    </el-row>
                    <el-table :data="obj.dialogForm.tableData" border @selection-change="handleSelectionChange">
                        <el-table-column type="selection" width="55" />
                        <el-table-column label="序号" type="index" align="center" width="60" />
                        <el-table-column label="姓名" align="center" prop="employeeName" />
                        <el-table-column label="订单号" align="center" prop="orderCode" />
                        <el-table-column label="唯一号" align="center" prop="uniqueId" />
                        <el-table-column label="证件类型" align="center" prop="idType" />
                        <el-table-column label="证件号码" align="center" prop="idNumber" />
                        <el-table-column label="入离职状态" align="center" prop="employmentStatus" />
                        <el-table-column label="小合同编号" align="center" prop="smallContractCode" />
                        <el-table-column label="小合同名称" align="center" prop="smallContractName" />
                        <el-table-column label="操作" align="center">
                            <template #default="scope">
                                <el-button type="primary" plain @click="handleDetail(scope.row)">详情</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                        v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
                </border-box>
                <border-box title="变更信息">
                    <el-form-item label="变更名称:" prop="typeCode">
                        <el-input class="width220" v-model="obj.dialogForm.typeCode" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="客户:" prop="typeCode">
                        <el-input class="width220" v-model="obj.dialogForm.typeCode" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="新账单模版:" prop="typeCode">
                        <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                            <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="起始月:" prop="typeCode">
                        <el-date-picker class="width220" v-model="obj.dialogForm.typeCode" type="date"
                            placeholder="请选择日期" />
                    </el-form-item>
                </border-box>
                <border-box title="产品信息">

                </border-box>
                <el-form-item label="备注:" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.typeCode" placeholder="备注"
                        clearable />
                </el-form-item>
            </el-form>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog v-model="obj.dialogShow2" title="详情" width="65%" append-to-body draggable @close="handleClose">
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <border-box title="变更信息">
                    <el-form-item label="变更名称:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="变更编号:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="大合同编号:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="客户:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="客户编号:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="原账单模板:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="原收费模板:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="起始月:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="创建人:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="新账单模板:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="新收费模板:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="创建时间:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.typeCode"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="备注:" prop="typeCode">
                        <el-input :disabled="obj.isDetail" class="width420" type="textarea"
                            v-model="obj.dialogForm.typeCode" placeholder="备注" clearable />
                    </el-form-item>
                </border-box>
                <border-box title="变更雇员列表">
                    <el-table :data="obj.dialogForm2.tableData" border @selection-change="handleSelectionChange">
                        <el-table-column label="序号" type="index" align="center" width="60" />
                        <el-table-column label="姓名" align="center" prop="employeeName" />
                        <el-table-column label="唯一号" align="center" prop="uniqueId" />
                        <el-table-column label="订单号" align="center" prop="orderCode" />
                        <el-table-column label="入离职状态" align="center" prop="employmentStatus" />
                        <el-table-column label="证件类型" align="center" prop="idType" />
                        <el-table-column label="证件号码" align="center" prop="idNumber" />
                        <el-table-column label="小合同编号" align="center" prop="smallContractCode" />
                        <el-table-column label="小合同名称" align="center" prop="smallContractName" />
                        <el-table-column label="失败原因" align="center" prop="failReason" />
                        <el-table-column label="操作" align="center">
                            <template #default="scope">
                                <el-button type="primary" plain @click="handleDetail2(scope.row)">查看</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                        v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
                </border-box>
            </el-form>
        </el-dialog>
        <order-contract-reserve-fund type="BatchChangeBilling_templates" v-model:dialogShow="obj.dialogShow3" title="查看"
            :form="obj.dialogForm" :tableData="obj.tableData" :tableData_no="obj.tableData_no" :isDetail="true" />
    </div>
</template>

<script setup name="BatchChangeBilling_templates">

import { listScale } from "@/api/reonApi/scale";
import OrderContractReserveFund from '@/views/reonManage/components/dialog/orderContractReserveFund.vue'
import BorderBox from '@/views/reonManage/components/borderBox.vue'

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 表单验证规则
const rules = {
    // 可以根据需要添加验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        changeName: null,
        customerName: null,
        startDate: null,
        endDate: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    tableData_no: [],//非社保公积金列表
    dialogForm: {
        tableData: []
    },//导入表单
    dialogForm2: {
        tableData: []
    },//详情表单
    dialogShow: false,//导入弹窗
    dialogShow2: false,//详情弹窗
    dialogShow3: false,//详情弹窗
    ids: [],//选中的id
    title: "",//标题
    isDetail: false,//是否详情
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                changeCode: 'BG20230001',
                changeName: '变更名称1',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                originalBillingTemplate: '原账单模板1',
                originalChargeTemplate: '原收费模板1',
                newBillingTemplate: '新账单模板1',
                newChargeTemplate: '新收费模板1',
                createBy: '创建人1',
                createTime: '2023-01-01 10:00:00'
            },
            {
                id: 2,
                changeCode: 'BG20230002',
                changeName: '变更名称2',
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                originalBillingTemplate: '原账单模板2',
                originalChargeTemplate: '原收费模板2',
                newBillingTemplate: '新账单模板2',
                newChargeTemplate: '新收费模板2',
                createBy: '创建人2',
                createTime: '2023-02-01 10:00:00'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 新增变更
function handleAdd() {
    obj.dialogShow = true;
    obj.dialogForm = {
        tableData: [
            {
                id: 1,
                employeeName: '张三',
                orderCode: '**********',
                uniqueId: '**********',
                idType: '身份证',
                idNumber: '110101199001011234',
                employmentStatus: '在职',
                smallContractCode: 'XHT20230001',
                smallContractName: '小合同名称1'
            },
            {
                id: 2,
                employeeName: '李四',
                orderCode: '**********',
                uniqueId: '**********',
                idType: '身份证',
                idNumber: '110101199001011235',
                employmentStatus: '在职',
                smallContractCode: 'XHT20230002',
                smallContractName: '小合同名称2'
            }
        ]
    };
}

// 查看变更信息
function handleInfo() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要查看的变更信息');
        return;
    }
    obj.isDetail = true;
    obj.dialogShow2 = true;
    obj.dialogForm2 = {
        tableData: [
            {
                id: 1,
                employeeName: '张三',
                orderCode: '**********',
                uniqueId: '**********',
                idType: '身份证',
                idNumber: '110101199001011234',
                employmentStatus: '在职',
                smallContractCode: 'XHT20230001',
                smallContractName: '小合同名称1',
                failReason: ''
            },
            {
                id: 2,
                employeeName: '李四',
                orderCode: '**********',
                uniqueId: '**********',
                idType: '身份证',
                idNumber: '110101199001011235',
                employmentStatus: '在职',
                smallContractCode: 'XHT20230002',
                smallContractName: '小合同名称2',
                failReason: ''
            }
        ]
    };
}

// 关闭弹窗
function handleClose() {
    obj.isDetail = false;
    obj.dialogForm = {
        tableData: []
    };
    obj.dialogForm2 = {
        tableData: []
    };
    obj.dialogShow = false;
    obj.dialogShow2 = false;
}

// 查看详情
function handleDetail(row) {
    obj.isDetail = true;
    obj.dialogShow2 = true;
    obj.dialogForm2 = {
        tableData: [
            {
                id: 1,
                employeeName: '张三',
                orderCode: '**********',
                uniqueId: '**********',
                idType: '身份证',
                idNumber: '110101199001011234',
                employmentStatus: '在职',
                smallContractCode: 'XHT20230001',
                smallContractName: '小合同名称1',
                failReason: ''
            }
        ]
    };
}

// 查看详情
function handleDetail2(row) {
    obj.dialogShow3 = true;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>