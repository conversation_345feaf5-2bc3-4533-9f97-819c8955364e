<!-- 招商银企直连根据业务参考号查询页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form @submit.prevent :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="业务参考号:" prop="referenceNo">
                <el-input class="width220" v-model="obj.queryParams.referenceNo" placeholder="请输入业务参考号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="参考号" align="center" width="130" prop="referenceNo" />
            <el-table-column label="用户姓名" align="center" width="110" prop="userName" />
            <el-table-column label="交易金额" align="center" width="90" prop="amount" />
            <el-table-column label="币种" align="center" width="130" prop="currency" />
            <el-table-column label="收方帐号" align="center" width="80" prop="receiverAccount" />
            <el-table-column label="收方行地址" align="center" width="110" prop="receiverBankAddress" />
            <el-table-column label="收方分行号" align="center" width="110" prop="receiverBranchNo" />
            <el-table-column label="收方行名称" align="center" width="130" prop="receiverBankName" />
            <el-table-column label="收方名称" align="center" width="130" prop="receiverName" />
            <el-table-column label="付方帐号" align="center" width="120" prop="payerAccount" />
            <el-table-column label="转出分行号" align="center" width="90" prop="payerBranchNo" />
            <el-table-column label="付方帐户名" align="center" width="90" prop="payerName" />
            <el-table-column label="付方记账子单元编号" align="center" width="150" prop="payerSubUnitNo" />
            <el-table-column label="期望日" align="center" width="90" prop="expectedDate" />
            <el-table-column label="期望时间" align="center" width="130" prop="expectedTime" />
            <el-table-column label="用户名" align="center" width="110" prop="username" />
            <el-table-column label="通知方式一" align="center" width="130" prop="notificationMethod1" />
            <el-table-column label="通知方式二" align="center" width="90" prop="notificationMethod2" />
            <el-table-column label="用途" align="center" width="90" prop="purpose" />
            <el-table-column label="操作别名" align="center" width="130" prop="operationAlias" />
            <el-table-column label="经办日期" align="center" width="90" prop="operationDate" />
            <el-table-column label="待处理操作序列" align="center" width="130" prop="pendingOperationSequence" />
            <el-table-column label="收方大额行号" align="center" width="110" prop="receiverLargeBankNo" />
            <el-table-column label="流程实例号" align="center" width="110" prop="flowInstanceNo" />
            <el-table-column label="请求状态" align="center" width="110" prop="requestStatus" />
            <el-table-column label="业务处理结果" align="center" width="130" prop="businessResult" />
            <el-table-column label="失败原因" align="center" width="110" prop="failureReason" />
            <el-table-column label="结算通路" align="center" width="110" prop="settlementChannel" />
            <el-table-column label="业务种类" align="center" width="110" prop="businessType" />
            <el-table-column label="账务流水" align="center" width="110" prop="accountingFlow" />
            <el-table-column label="账务套号" align="center" width="110" prop="accountingSetNo" />
            <el-table-column label="是否有附件信息" align="center" width="130" prop="hasAttachment" />
            <el-table-column label="系统内外标志" align="center" width="110" prop="systemFlag" />
            <el-table-column label="业务编码" align="center" width="110" prop="businessCode" />
            <el-table-column label="业务模式" align="center" width="110" prop="businessMode" />
            <el-table-column label="业务摘要" align="center" width="110" prop="businessSummary" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="EpiAccordingBusinessReferenceNumber_query">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        referenceNo: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                referenceNo: 'REF20230501001',
                userName: '张三',
                amount: 5000.00,
                currency: 'CNY',
                receiverAccount: '****************',
                receiverBankAddress: '北京市海淀区中关村大街',
                receiverBranchNo: '0001',
                receiverBankName: '中国银行北京分行',
                receiverName: '李四',
                payerAccount: '****************',
                payerBranchNo: '0002',
                payerName: '王五',
                payerSubUnitNo: 'SU001',
                expectedDate: '2023-05-15',
                expectedTime: '10:30:00',
                username: 'user001',
                notificationMethod1: '短信',
                notificationMethod2: '邮件',
                purpose: '工资支付',
                operationAlias: '转账',
                operationDate: '2023-05-15',
                pendingOperationSequence: 'POS001',
                receiverLargeBankNo: 'LB001',
                flowInstanceNo: 'FI001',
                requestStatus: '已完成',
                businessResult: '成功',
                failureReason: '',
                settlementChannel: '实时清算',
                businessType: '转账',
                accountingFlow: 'AF001',
                accountingSetNo: 'AS001',
                hasAttachment: '否',
                systemFlag: '内部',
                businessCode: 'BC001',
                businessMode: '单笔转账',
                businessSummary: '工资支付'
            },
            {
                id: 2,
                referenceNo: 'REF20230502001',
                userName: '赵六',
                amount: 10000.00,
                currency: 'CNY',
                receiverAccount: '****************',
                receiverBankAddress: '上海市浦东新区陆家嘴路',
                receiverBranchNo: '0003',
                receiverBankName: '中国建设银行上海分行',
                receiverName: '孙七',
                payerAccount: '****************',
                payerBranchNo: '0004',
                payerName: '周八',
                payerSubUnitNo: 'SU002',
                expectedDate: '2023-05-16',
                expectedTime: '14:20:00',
                username: 'user002',
                notificationMethod1: '短信',
                notificationMethod2: '',
                purpose: '供应商付款',
                operationAlias: '转账',
                operationDate: '2023-05-16',
                pendingOperationSequence: 'POS002',
                receiverLargeBankNo: 'LB002',
                flowInstanceNo: 'FI002',
                requestStatus: '已完成',
                businessResult: '成功',
                failureReason: '',
                settlementChannel: '实时清算',
                businessType: '转账',
                accountingFlow: 'AF002',
                accountingSetNo: 'AS002',
                hasAttachment: '是',
                systemFlag: '内部',
                businessCode: 'BC002',
                businessMode: '单笔转账',
                businessSummary: '供应商付款'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}



/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

getList();
</script>
<style lang="scss" scoped></style>