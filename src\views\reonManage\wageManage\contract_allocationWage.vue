<!-- 合同分配薪资客服 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="是否分配:" prop="isAllocated">
                <el-select class="width220" v-model="obj.queryParams.isAllocated" placeholder="请选择" clearable>
                    <el-option v-for="item in allocationOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单分公司:" prop="branchCompany">
                <el-select class="width220" v-model="obj.queryParams.branchCompany" placeholder="请选择" clearable>
                    <el-option v-for="item in branchOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资客服:" prop="serviceName">
                <el-select class="width220" v-model="obj.queryParams.serviceName" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" @click="handleAllocation">分配</el-button>
                <el-button type="primary" @click="handleAllocationHistory">分配历史日志</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" prop="id" width="60" />
            <el-table-column label="客户名称" align="center" prop="customerName" min-width="150" />
            <el-table-column label="合同编号" align="center" prop="contractNo" width="120" />
            <el-table-column label="合同名称" align="center" prop="contractName" min-width="150" />
            <el-table-column label="派单分公司" align="center" width="120">
                <template #default="scope">
                    {{ getBranchName(scope.row.branchCompany) }}
                </template>
            </el-table-column>
            <el-table-column label="薪资客服" align="center" width="100">
                <template #default="scope">
                    {{ getServiceName(scope.row.serviceName) }}
                </template>
            </el-table-column>
            <el-table-column label="生效日期" align="center" prop="effectiveDate" width="100" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 分配日志 -->
        <DistributionHistoryLog v-model:dialogShow="obj.dialogShow" :tableData="obj.historyData" />

        <!-- 分配对话框 -->
        <el-dialog v-model="obj.allocationDialogShow" :title="obj.title" width="40%">
            <el-form :model="obj.allocationForm" label-width="auto">
                <el-form-item label="客服类型:" required>
                    <el-select v-model="obj.allocationForm.serviceType" placeholder="请选择客服类型">
                        <el-option v-for="item in serviceTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="薪资客服:" required>
                    <el-select v-model="obj.allocationForm.serviceName" placeholder="请选择薪资客服">
                        <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="生效日期:" required>
                    <el-date-picker v-model="obj.allocationForm.effectiveDate" type="date" placeholder="请选择生效日期"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="submitAllocation">确定分配</el-button>
                <el-button @click="obj.allocationDialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ContractAllocationWage">

import DistributionHistoryLog from '@/views/reonManage/components/dialog/distributionHistoryLog.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 是否分配选项
const allocationOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' }
];

// 分公司选项
const branchOptions = [
    { value: '1', label: '北京分公司' },
    { value: '2', label: '上海分公司' },
    { value: '3', label: '广州分公司' },
    { value: '4', label: '深圳分公司' },
    { value: '5', label: '成都分公司' }
];

// 薪资客服选项
const serviceOptions = [
    { value: '1', label: '张三' },
    { value: '2', label: '李四' },
    { value: '3', label: '王五' },
    { value: '4', label: '赵六' },
    { value: '5', label: '孙七' }
];

// 客服类型选项
const serviceTypeOptions = [
    { value: '1', label: '薪资客服' },
    { value: '2', label: '理赔客服' },
    { value: '3', label: '综合客服' }
];

// 合同分配薪资客服数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: '', // 客户名称
        contractName: '', // 合同名称
        isAllocated: '', // 是否分配
        branchCompany: '', // 派单分公司
        serviceName: '' // 薪资客服名称
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            customerName: '北京科技有限公司',
            contractNo: 'HT20230501001',
            contractName: '商业医疗险服务合同',
            branchCompany: '1',
            serviceName: '1',
            effectiveDate: '2023-05-01'
        },
        {
            id: 2,
            customerName: '上海贸易有限公司',
            contractNo: 'HT20230502001',
            contractName: '团体寿险服务合同',
            branchCompany: '2',
            serviceName: '2',
            effectiveDate: '2023-05-02'
        },
        {
            id: 3,
            customerName: '广州电子有限公司',
            contractNo: 'HT20230503001',
            contractName: '团体重疾险服务合同',
            branchCompany: '3',
            serviceName: '',
            effectiveDate: '2023-05-03'
        },
        {
            id: 4,
            customerName: '深圳科技有限公司',
            contractNo: 'HT20230504001',
            contractName: '团体意外险服务合同',
            branchCompany: '4',
            serviceName: '4',
            effectiveDate: '2023-05-04'
        },
        {
            id: 5,
            customerName: '成都信息有限公司',
            contractNo: 'HT20230505001',
            contractName: '综合保险服务合同',
            branchCompany: '5',
            serviceName: '',
            effectiveDate: '2023-05-05'
        }
    ],

    // 分配对话框相关数据
    dialogShow: false, // 分配历史日志对话框
    allocationDialogShow: false, // 分配对话框
    allocationForm: {
        serviceType: '1',
        serviceName: '',
        effectiveDate: ''
    },
    ids: [], // 选中的id
    title: '' // 对话框标题
})

/**
 * 获取分公司名称
 * @param {string} branchId 分公司ID
 * @returns {string} 分公司名称
 */
function getBranchName(branchId) {
    if (!branchId) return '-';

    const branch = branchOptions.find(item => item.value === branchId);
    return branch ? branch.label : '-';
}

/**
 * 获取客服名称
 * @param {string} serviceId 客服ID
 * @returns {string} 客服名称
 */
function getServiceName(serviceId) {
    if (!serviceId) return '-';

    const service = serviceOptions.find(item => item.value === serviceId);
    return service ? service.label : '-';
}

/**
 * 获取客服类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getServiceTypeName(typeId) {
    if (!typeId) return '-';

    const type = serviceTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item =>
                item.customerName.includes(obj.queryParams.customerName)
            );
        }

        if (obj.queryParams.contractName) {
            filteredData = filteredData.filter(item =>
                item.contractName.includes(obj.queryParams.contractName)
            );
        }

        if (obj.queryParams.isAllocated) {
            const hasService = obj.queryParams.isAllocated === '1';
            filteredData = filteredData.filter(item =>
                hasService ? !!item.serviceName : !item.serviceName
            );
        }

        if (obj.queryParams.branchCompany) {
            filteredData = filteredData.filter(item =>
                item.branchCompany === obj.queryParams.branchCompany
            );
        }

        if (obj.queryParams.serviceName) {
            filteredData = filteredData.filter(item =>
                item.serviceName === obj.queryParams.serviceName
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 分配薪资客服 */
function handleAllocation() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgInfo('请选择要分配的合同');
        return;
    }

    // 重置表单
    obj.allocationForm = {
        serviceType: '1',
        serviceName: '',
        effectiveDate: new Date().toISOString().split('T')[0] // 默认为当前日期
    };

    obj.allocationDialogShow = true;
    obj.title = '分配薪资客服';
}

/** 提交分配 */
function submitAllocation() {
    if (!obj.allocationForm.serviceName) {
        proxy.$modal.msgInfo('请选择薪资客服');
        return;
    }

    if (!obj.allocationForm.effectiveDate) {
        proxy.$modal.msgInfo('请选择生效日期');
        return;
    }

    // 模拟分配操作
    const selectedContracts = obj.tableData.filter(item => obj.ids.includes(item.id));

    selectedContracts.forEach(contract => {
        contract.serviceName = obj.allocationForm.serviceName;
        contract.effectiveDate = obj.allocationForm.effectiveDate;

        // 添加到分配历史
        obj.historyData.unshift({
            id: obj.historyData.length + 1,
            customerName: contract.customerName,
            contractName: contract.contractName,
            subContractName: '子合同' + contract.id,
            serviceType: obj.allocationForm.serviceType,
            serviceName: obj.allocationForm.serviceName,
            effectiveDate: obj.allocationForm.effectiveDate,
            branchCompany: contract.branchCompany,
            operationTime: new Date().toLocaleString(),
            operator: '管理员'
        });
    });

    proxy.$modal.msgSuccess('分配成功');
    obj.allocationDialogShow = false;
    getList();
}

/** 分配历史日志 */
function handleAllocationHistory() {
    obj.dialogShow = true;
}

/** 关闭分配历史日志 */
function handleClose() {
    obj.dialogShow = false;
}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped></style>