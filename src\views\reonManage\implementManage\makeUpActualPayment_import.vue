<!-- 补缴实做导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-select class="width220" v-model="obj.queryParams.customer" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable>
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入名称:" prop="importName">
                <el-input class="width220" v-model="obj.queryParams.importName" placeholder="请输入导入名称" clearable />
            </el-form-item>
            <el-form-item label="导入类型:" prop="importType">
                <el-select class="width220" v-model="obj.queryParams.importType" placeholder="请选择" clearable>
                    <el-option v-for="item in importTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="补差方式:" prop="compensationMethod">
                <el-select class="width220" v-model="obj.queryParams.compensationMethod" placeholder="请选择" clearable>
                    <el-option v-for="item in compensationMethodOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="补缴发生月:" prop="paymentMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentMonth" type="month" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="导入状态:" prop="importStatus">
                <el-select class="width220" v-model="obj.queryParams.importStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in importStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleDownload">下载模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新建导入配置</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain @click="handleCopy">复制模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon='View' @click="handleDetail">查看导入明细</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDblClick">
            <el-table-column type="selection" width="55" />
            <el-table-column label="调整任务号" align="center" prop="taskNo" />
            <el-table-column label="调整名称" align="center" prop="adjustName" />
            <el-table-column label="福利包编号" align="center" prop="welfarePackageNo" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="调整起始年月" align="center" prop="adjustStartMonth" />
            <el-table-column label="详情" align="center" prop="details" />
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 新增调整 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="65%" append-to-body>
            <el-form class="mt20" :model="obj.dialogForm" ref="dialogRef" :rules="rules" inline label-width="auto">
                <el-form-item label="福利办理方:" prop="welfareHandler">
                    <el-select class="width220" v-model="obj.dialogForm.welfareHandler" placeholder="请选择" clearable>
                        <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="大户/单立户:" prop="accountType">
                    <el-select class="width220" v-model="obj.dialogForm.accountType" placeholder="请选择" clearable>
                        <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户:" prop="customer">
                    <el-select class="width220" v-model="obj.dialogForm.customer" placeholder="请选择" clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="福利包名称:" prop="welfarePackageName">
                    <el-select class="width220" v-model="obj.dialogForm.welfarePackageName" placeholder="请选择" clearable>
                        <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="导入名称:" prop="importName">
                    <el-input class="width220" v-model="obj.dialogForm.importName" placeholder="请输入导入名称" clearable />
                </el-form-item>
                <el-form-item label="导入类型:" prop="importType">
                    <el-select class="width220" v-model="obj.dialogForm.importType" placeholder="请选择" clearable>
                        <el-option v-for="item in importTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="补差方式:" prop="compensationMethod">
                    <el-select class="width220" v-model="obj.dialogForm.compensationMethod" placeholder="请选择" clearable>
                        <el-option v-for="item in compensationMethodOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="补缴发生月:" prop="paymentMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.paymentMonth" type="month"
                        placeholder="请选择" clearable />
                </el-form-item>
            </el-form>
            <el-row class="mt20 mb10">
                <el-col :span="24">
                    <el-button type="primary" @click="handleAddProduct">新增</el-button>
                    <el-button type="danger" @click="handleDeleteProduct">删除</el-button>
                </el-col>
            </el-row>
            <el-table :data="obj.productData" border @selection-change="handleProductSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="产品名称" align="center">
                    <template #default="scope">
                        <el-select v-model="scope.row.name" placeholder="请选择" clearable>
                            <el-option v-for="item in productOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="顺序" align="center" type="index" />
            </el-table>
            <template #footer>
                <el-button type="primary" @click="submitForm">保存</el-button>
                <el-button @click="cancelForm">取消</el-button>
            </template>
        </el-dialog>
        <!-- 查看导入明细 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="65%" append-to-body>
            <el-button class="mt20 mb10" type="primary" @click="handleExport">导出数据</el-button>
            <el-table :data="obj.importDetailData" border>
                <el-table-column label="导入编号" align="center" prop="importNo" />
                <el-table-column label="行号" align="center" prop="rowNo" />
                <el-table-column label="错误描述" align="center" prop="errorDesc" />
                <el-table-column label="提醒描述" align="center" prop="warningDesc" />
                <el-table-column label="导入结果" align="center" prop="importResult">
                    <template #default="scope">
                        <el-tag :type="scope.row.importResult === '成功' ? 'success' : 'danger'">
                            {{ scope.row.importResult }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="导入信息" align="center" prop="importInfo" />
                <el-table-column label="创建人" align="center" prop="creator" />
                <el-table-column label="创建时间" align="center" prop="createTime" />
            </el-table>
        </el-dialog>
    </div>
</template>


<script setup name="MakeUpActualPayment_import">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 导入类型选项
const importTypeOptions = [
    { value: '1', label: '新增实做账号' },
    { value: '2', label: '停用实做账号' },
    { value: '3', label: '批量修改密码' }
];

// 补差方式选项
const compensationMethodOptions = [
    { value: '1', label: '按比例补差' },
    { value: '2', label: '按金额补差' },
    { value: '3', label: '按基数补差' }
];

// 导入状态选项
const importStatusOptions = [
    { value: '0', label: '待处理' },
    { value: '1', label: '处理中' },
    { value: '2', label: '处理完成' },
    { value: '3', label: '处理失败' }
];

// 产品选项
const productOptions = [
    { value: '1', label: '养老保险' },
    { value: '2', label: '医疗保险' },
    { value: '3', label: '失业保险' },
    { value: '4', label: '生育保险' },
    { value: '5', label: '公积金' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        taskNo: 'ADJ20230001',
        adjustName: '社保基数调整',
        welfarePackageNo: 'WP001',
        welfarePackageName: '标准福利包',
        adjustStartMonth: '2023-01',
        details: '社保基数从8000调整到10000',
        creator: '张三',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 2,
        taskNo: 'ADJ20230002',
        adjustName: '公积金比例调整',
        welfarePackageNo: 'WP002',
        welfarePackageName: '高级福利包',
        adjustStartMonth: '2023-02',
        details: '公积金比例从8%调整到12%',
        creator: '李四',
        createTime: '2023-02-15 09:15:00'
    },
    {
        id: 3,
        taskNo: 'ADJ20230003',
        adjustName: '医保比例调整',
        welfarePackageNo: 'WP003',
        welfarePackageName: '基础福利包',
        adjustStartMonth: '2023-03',
        details: '医保比例从2%调整到3%',
        creator: '王五',
        createTime: '2023-03-15 11:45:00'
    }
];

// 模拟导入明细数据
const mockImportDetailData = [
    {
        id: 1,
        importNo: 'IMP20230001',
        rowNo: 1,
        errorDesc: '',
        warningDesc: '',
        importResult: '成功',
        importInfo: '导入成功',
        creator: '张三',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 2,
        importNo: 'IMP20230001',
        rowNo: 2,
        errorDesc: '证件号码格式错误',
        warningDesc: '',
        importResult: '失败',
        importInfo: '证件号码格式错误',
        creator: '张三',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 3,
        importNo: 'IMP20230002',
        rowNo: 1,
        errorDesc: '',
        warningDesc: '基数过高',
        importResult: '成功',
        importInfo: '导入成功，有警告',
        creator: '李四',
        createTime: '2023-02-15 09:15:00'
    }
];

// 模拟产品数据
const mockProductData = [
    {
        id: 1,
        name: '1',
        order: 1
    },
    {
        id: 2,
        name: '2',
        order: 2
    }
];

// 表单验证规则
const rules = {
    welfareHandler: [
        { required: true, message: '请选择福利办理方', trigger: 'change' }
    ],
    accountType: [
        { required: true, message: '请选择大户/单立户', trigger: 'change' }
    ],
    customer: [
        { required: true, message: '请选择客户', trigger: 'change' }
    ],
    welfarePackageName: [
        { required: true, message: '请选择福利包名称', trigger: 'change' }
    ],
    importName: [
        { required: true, message: '请输入导入名称', trigger: 'blur' }
    ],
    importType: [
        { required: true, message: '请选择导入类型', trigger: 'change' }
    ],
    compensationMethod: [
        { required: true, message: '请选择补差方式', trigger: 'change' }
    ],
    paymentMonth: [
        { required: true, message: '请选择补缴发生月', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        importName: null, // 导入名称
        importType: null, // 导入类型
        compensationMethod: null, // 补差方式
        paymentMonth: null, // 补缴发生月
        importStatus: null, // 导入状态
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    importDetailData: mockImportDetailData, // 导入明细数据
    productData: mockProductData, // 产品数据
    dialogForm: {
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        importName: '', // 导入名称
        importType: null, // 导入类型
        compensationMethod: null, // 补差方式
        paymentMonth: null, // 补缴发生月
    }, // 导入表单
    dialogShow: false, // 新增调整弹窗
    dialogShow2: false, // 查看导入明细弹窗
    ids: [], // 选中的id
    productIds: [], // 选中的产品id
    title: "", // 标题
})


/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        // obj.total = response.total;
        // obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.welfareHandler) {
            filteredData = filteredData.filter(item => item.welfareHandler && item.welfareHandler.includes(obj.queryParams.welfareHandler));
        }

        if (obj.queryParams.welfarePackageName) {
            filteredData = filteredData.filter(item => item.welfarePackageName && item.welfarePackageName.includes(obj.queryParams.welfarePackageName));
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 选中产品数据改变
function handleProductSelectionChange(selection) {
    obj.productIds = selection.map(item => item.id);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 下载模版 */
function handleDownload() {
    // 实际项目中应该调用API进行下载模板
    proxy.$modal.msgSuccess('模板下载成功');

    // 模拟下载文件
    const fileName = '补缴实做导入模板.xlsx';
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 新增调整
function handleAdd() {
    obj.dialogShow = true;
    obj.title = '新建导入配置';
    resetForm();
}

/** 导入 */
function handleImport() {
    obj.dialogShow = true;
    obj.title = '导入数据';
    resetForm();
}

/** 复制模版 */
function handleCopy() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行复制');
        return;
    }
    proxy.$modal.msgSuccess('模板复制成功');
}

/** 查看导入明细 */
function handleDetail() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行查看');
        return;
    }
    obj.dialogShow2 = true;
    obj.title = '导入明细查看';
}

// 双击行
function handleDblClick(row) {
    obj.dialogShow = true;
    obj.title = '编辑导入配置';
    resetForm();
}
// 新增产品
function handleAddProduct() {
    obj.productData.push({
        id: obj.productData.length + 1,
        name: '',
        order: obj.productData.length + 1
    });
}

// 删除产品
function handleDeleteProduct() {
    if (obj.productIds.length === 0) {
        proxy.$modal.msgError('请选择要删除的产品');
        return;
    }

    obj.productData = obj.productData.filter(item => !obj.productIds.includes(item.id));
    obj.productIds = [];
}

// 重置表单
function resetForm() {
    obj.dialogForm = {
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        importName: '', // 导入名称
        importType: null, // 导入类型
        compensationMethod: null, // 补差方式
        paymentMonth: null, // 补缴发生月
    };
    proxy.resetForm("dialogRef");
}

// 导出数据
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 取消表单
function cancelForm() {
    obj.dialogShow = false;
    resetForm();
}

// 提交表单
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

getList();
</script>
<style lang="scss" scoped></style>