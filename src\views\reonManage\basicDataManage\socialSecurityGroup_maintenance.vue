<!-- 社保组维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="所属城市:" prop="cityCode">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="组名称:" prop="groupName">
                <el-input class="width220" v-model="obj.queryParams.groupName" placeholder="请输入组名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="组类别:" prop="groupType">
                <el-select class="width220" filterable v-model="obj.queryParams.groupType" placeholder="请选择组类别"
                    clearable>
                    <el-option v-for="item in groupTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="年度调整月:" prop="adjustMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.adjustMonth" type="month"
                    placeholder="请选择年度调整月" clearable />
            </el-form-item>
            <el-form-item label="办理日(起):" prop="handleDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.handleDateStart" type="date"
                    placeholder="请选择办理日(起)" clearable />
            </el-form-item>
            <el-form-item label="办理日(止):" prop="handleDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.handleDateEnd" type="date"
                    placeholder="请选择办理日(止)" clearable />
            </el-form-item>
            <el-form-item label="办停日(起):" prop="stopDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.stopDateStart" type="date"
                    placeholder="请选择办停日(起)" clearable />
            </el-form-item>
            <el-form-item label="办停日(止):" prop="stopDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.stopDateEnd" type="date"
                    placeholder="请选择办停日(止)" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="View" :disabled="obj.single" @click="handleDetail">查看</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="所属城市" align="center" prop="cityName" />
            <el-table-column label="组名称" align="center" prop="groupName" />
            <el-table-column label="社保组编号" align="center" prop="groupNo" width="120" />
            <el-table-column label="组类别" align="center" prop="groupType" />
            <el-table-column label="年度调整月" align="center" prop="adjustMonth" width="120" />
            <el-table-column label="办理日" align="center" prop="handleDate" />
            <el-table-column label="办停日" align="center" prop="stopDate" />
            <el-table-column label="托收方式" align="center" prop="collectionMethod" />
            <el-table-column label="停办方式" align="center" prop="stopMethod" />
            <el-table-column label="首次补缴方式" align="center" prop="firstPaymentMethod" width="120" />
            <el-table-column label="企业金额汇缴方式" align="center" prop="companyPaymentMethod" width="150" />
            <el-table-column label="个人金额汇缴" align="center" prop="personalPaymentMethod" width="120" />
            <el-table-column label="说明" align="center" prop="remark" />
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
            <el-table-column label="修改人" align="center" prop="updateBy" />
            <el-table-column label="修改时间" align="center" prop="updateTime" width="150" />
            <el-table-column label="操作" align="center" width="140">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"></el-button>
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="65%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="所属城市" prop="cityCode">
                    <el-select :disabled="obj.isDetails" class="width220" filterable v-model="obj.dialogForm.cityCode"
                        placeholder="请选择城市" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="组名称" prop="groupName">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.groupName"
                        placeholder="请输入组名称" clearable />
                </el-form-item>
                <el-form-item label="组类型" prop="groupType">
                    <el-select :disabled="obj.isDetails" class="width220" filterable v-model="obj.dialogForm.groupType"
                        placeholder="请选择组类型" clearable @change="handleChange">
                        <el-option v-for="item in groupTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="年度调整月" prop="adjustMonth">
                    <el-date-picker :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.adjustMonth"
                        type="month" placeholder="请选择年度调整月" clearable />
                </el-form-item>
                <el-form-item label="办理日" prop="handleDate">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.handleDate"
                        placeholder="例如：1-15日" clearable />
                </el-form-item>
                <el-form-item label="办停日" prop="stopDate">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.stopDate"
                        placeholder="例如：16-31日" clearable />
                </el-form-item>
                <el-form-item label="托收方式" prop="collectionMethod">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.collectionMethod" placeholder="请选择托收方式" clearable>
                        <el-option v-for="item in collectionMethodOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="停办方式" prop="stopMethod">
                    <el-select :disabled="obj.isDetails" class="width220" filterable v-model="obj.dialogForm.stopMethod"
                        placeholder="请选择停办方式" clearable>
                        <el-option v-for="item in stopMethodOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="首次补缴" prop="firstPaymentMethod">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.firstPaymentMethod" placeholder="请选择首次补缴方式" clearable>
                        <el-option v-for="item in firstPaymentMethodOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="企业汇缴" prop="companyPaymentMethod">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.companyPaymentMethod" placeholder="请选择企业汇缴方式" clearable>
                        <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="个人汇缴" prop="personalPaymentMethod">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.personalPaymentMethod" placeholder="请选择个人汇缴方式" clearable>
                        <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否申请" prop="isApply">
                    <el-select :disabled="obj.isDetails" class="width220" filterable v-model="obj.dialogForm.isApply"
                        placeholder="请选择是否申请" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-row>
                    <el-form-item label="说明" prop="remark">
                        <el-input :disabled="obj.isDetails" class="width420" type="textarea" :rows="3"
                            v-model="obj.dialogForm.remark" placeholder="请输入说明" clearable />
                    </el-form-item>
                </el-row>
                <el-row :gutter="10" class="mb8" v-if="!obj.isDetails">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="addFormToTable">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" plain icon="Delete" @click="delFormToTable">删除</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" plain @click="handleAdd">设为默认</el-button>
                    </el-col>
                </el-row>
                <el-table ref="formTableRef" :data="obj.dialogForm.formTable" border
                    @selection-change="selectFormToTable" @row-click="rowClickFormToTable">
                    <el-table-column type="selection" width="55" />
                    <el-table-column type="index" label="序号" align="center" width="60" />
                    <el-table-column label="比例" align="center" prop="id">
                        <template #default="scope">
                            <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_job_group" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="产品" align="center" prop="id" />
                    <el-table-column label="是否默认" align="center" prop="id">
                        <template #default="scope">
                            <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_job_group" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center">
                        <template #default="scope">
                            <el-button link type="primary" icon="Plus" @click="addFormToTable(scope.row)">新增</el-button>
                            <el-button link type="primary" icon="Delete"
                                @click="delFormToTable(scope.row, scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-row class="mt10 mb10">
                    下属产品:
                </el-row>
                <el-row class="mt10 mb10">
                    <el-col :span="24" v-if="!obj.isDetails">
                        <el-button type="primary" @click="handleAdd">类似设定</el-button>
                    </el-col>
                </el-row>
                <el-table :data="obj.dialogForm.formTable2" border>
                    <el-table-column type="index" label="序号" align="center" width="60" />
                    <el-table-column label="产品" align="center" prop="id" />
                    <el-table-column label="是否必上" align="center" prop="id">
                        <template #default="scope">
                            <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="支付选项" align="center">
                        <el-table-column prop="name" label="企业汇缴" align="center" width="120">
                            <template #default="scope">
                                <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                    clearable>
                                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="个人汇缴" align="center" width="120">
                            <template #default="scope">
                                <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                    clearable>
                                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="企业补缴" align="center" width="120">
                            <template #default="scope">
                                <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                    clearable>
                                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="个人补缴" align="center" width="120">
                            <template #default="scope">
                                <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                    clearable>
                                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="基数绑定级次" align="center" width="120" prop="id">
                        <template #default="scope">
                            <el-input style="width: 100%;" v-model="scope.row.id" placeholder="请输入" clearable />
                        </template>
                    </el-table-column>
                    <el-table-column label="每年基数调整时间" align="center" width="140" prop="id">
                        <template #default="scope">
                            <el-input style="width: 100%;" v-model="scope.row.id" placeholder="请输入" clearable />
                        </template>
                    </el-table-column>
                    <el-table-column label="补缴月份数" align="center" width="120" prop="id">
                        <template #default="scope">
                            <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否有利息" align="center" width="120" prop="id">
                        <template #default="scope">
                            <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否可跨年补缴" align="center" width="120" prop="id">
                        <template #default="scope">
                            <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否按照年度平均" align="center" width="140" prop="id">
                        <template #default="scope">
                            <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否在职补差" align="center" width="120" prop="id">
                        <template #default="scope">
                            <el-select style="width: 100%;" filterable v-model="scope.row.id" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="SocialSecurityGroupMaintenance">

import { useAreaStore } from '@/store/modules/area'
import { listGroup, getGroup, delGroup, addGroup, updateGroup } from "@/api/reonApi/group";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();

const { sys_job_group } = proxy.useDict('sys_job_group')
const { sys_yes_no } = proxy.useDict('sys_yes_no');
const provinces = areaStore.provinces // 获取省份数据

// 组类别选项
const groupTypeOptions = [
    { value: '1', label: '养老保险组' },
    { value: '2', label: '医疗保险组' },
    { value: '3', label: '失业保险组' },
    { value: '4', label: '生育保险组' },
    { value: '5', label: '工伤保险组' },
    { value: '6', label: '公积金组' }
];

// 托收方式选项
const collectionMethodOptions = [
    { value: '1', label: '银行托收' },
    { value: '2', label: '现金缴纳' },
    { value: '3', label: '其他方式' }
];

// 停办方式选项
const stopMethodOptions = [
    { value: '1', label: '自动停办' },
    { value: '2', label: '手动停办' },
    { value: '3', label: '其他方式' }
];

// 首次补缴方式选项
const firstPaymentMethodOptions = [
    { value: '1', label: '一次性补缴' },
    { value: '2', label: '分月补缴' },
    { value: '3', label: '其他方式' }
];

// 汇缴方式选项
const paymentMethodOptions = [
    { value: '1', label: '银行汇缴' },
    { value: '2', label: '现金缴纳' },
    { value: '3', label: '其他方式' }
];




const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cityCode: null,
        groupName: null,
        groupType: null,
        adjustMonth: null,
        handleDateStart: null,
        handleDateEnd: null,
        stopDateStart: null,
        stopDateEnd: null,
    },//查询表单
    rules: {
        cityCode: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        groupName: [{ required: true, message: '请输入组名称', trigger: 'blur' }],
        groupType: [{ required: true, message: '请选择组类别', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
    isDetails: false,//是否详情
})

/** 列表 */
function getList() {
    obj.loading = true;
    listGroup(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {
        formTable: [],
        formTable2: []
    };
    obj.isDetails = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}
/** 查看按钮操作 */
function handleDetail(row) {
    obj.isDetails = true;
    obj.title = "详情";
    obj.dialogForm = row;
    obj.dialogShow = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    obj.dialogForm = JSON.parse(JSON.stringify(row));
    obj.title = "修改";
    obj.dialogShow = true;

    return
    const _id = row.id || obj.ids
    getGroup(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改";
    });
}
// 表单-表格选中
const changeFormTable = ref([])
// 表单-表格添加
function addFormToTable() {
    obj.dialogForm.formTable.push({})
    obj.dialogForm.formTable2.push({})
}
// 表单-表格删除
function delFormToTable(row, index) {
    if (index < 0 && changeFormTable.value.length == 0) {
        proxy.$modal.msgWarning("请选择数据!");
        return
    }
    proxy.$modal.confirm('确定要删除吗?').then(() => {
        if (changeFormTable.value.length > 0) {
            changeFormTable.value.forEach(row => {
                const index = obj.dialogForm.formTable.indexOf(row)
                if (index > -1) {
                    obj.dialogForm.formTable.splice(index, 1)
                    obj.dialogForm.formTable2.splice(index, 1)
                }
            })
        } else {
            obj.dialogForm.formTable.splice(index, 1)
            obj.dialogForm.formTable2.splice(index, 1)
        }
        proxy.$modal.msgSuccess("删除成功!");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}
const formTableRef = ref(null)
// 表单-表格选中
function selectFormToTable(selection) {
    changeFormTable.value = selection
}
// 表单-表格点击
function rowClickFormToTable(row, column) {
    if (column.label == '序号') {
        formTableRef.value.toggleRowSelection(row)
    }
}
function handleChange(value) {
    // 根据组类型生成组名称
    if (obj.dialogForm.cityCode && value) {
        const cityName = provinces.find(item => item.code === obj.dialogForm.cityCode)?.province || '';
        const typeName = groupTypeOptions.find(item => item.value === value)?.label || '';
        if (cityName && typeName) {
            obj.dialogForm.groupName = cityName + typeName;

            // 生成组编号
            const cityCode = cityName.substring(0, 2).toUpperCase();
            const typeCode = typeName.substring(0, 2).toUpperCase();
            const random = Math.floor(Math.random() * 900) + 100;
            obj.dialogForm.groupNo = `${cityCode}-${typeCode}-${random}`;
        }
    }
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateGroup(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addGroup(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row?.id || obj.ids;
    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delGroup(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

getList();
</script>
<style lang="scss" scoped></style>