import useUserStore from "@/store/modules/user";

export default {
  /**
   * 当指令被挂载到元素上时执行的钩子函数
   * @param {HTMLElement} el - 指令所绑定的元素
   * @param {Object} binding - 一个对象，包含指令的相关信息
   * @param {VNode} vnode - Vue 编译生成的虚拟节点
   */
  mounted(el, binding, vnode) {
    // 从 binding 对象中解构出 value 属性
    const { value } = binding;
    // 定义一个表示超级管理员的字符串
    const super_admin = "admin";
    // 从用户存储中获取当前用户的角色列表
    const roles = useUserStore().roles;

    // 检查 value 是否是一个非空数组
    if (value && value instanceof Array && value.length > 0) {
      // 将 value 赋值给 roleFlag 变量
      const roleFlag = value;

      // 检查当前用户的角色列表中是否存在满足条件的角色
      const hasRole = roles.some((role) => {
        // 如果角色是超级管理员或者角色列表中包含该角色，则返回 true
        return super_admin === role || roleFlag.includes(role);
      });

      // 如果用户没有权限，则移除该元素
      if (!hasRole) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      // 如果 value 不是一个有效的数组，则抛出错误
      throw new Error(`请设置角色权限标签值`);
    }
  },
};
