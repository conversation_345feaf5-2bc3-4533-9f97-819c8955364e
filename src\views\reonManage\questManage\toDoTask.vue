<!-- 待办任务 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="流程类型:" prop="processType">
                <el-select class="width220" v-model="obj.queryParams.processType" placeholder="请选择" clearable>
                    <el-option v-for="item in processTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="流程类型" align="center" prop="processType" />
            <el-table-column label="节点名称" align="center" prop="nodeName" />
            <el-table-column label="待处理人" align="center" prop="processor" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="操作" align="center" width="180">
                <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="ToDoTask">
import { listTask } from "@/api/reonApi/task";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 流程类型选项
const processTypeOptions = [
    { value: '1', label: '合同审批' },
    { value: '2', label: '报价审批' },
    { value: '3', label: '付款审批' },
    { value: '4', label: '供应商合同审批' },
    { value: '5', label: '其他审批' }
];

const obj = reactive({
    loading: false, // 加载状态
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        processType: null,
        nodeName: null,
        processor: null
    }, // 查询表单

    tableData: [], // 表格数据
    total: 0, // 总条数
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    listTask(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 查看详情 */
function handleDetail(row) {
    proxy.$modal.msgInfo(`查看ID为${row.id}的详细信息`);
}

getList();
</script>
<style lang="scss" scoped></style>