<!-- 查看发放批次 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="发放批次编号/名称:" prop="batchCode">
                <el-input class="width220" v-model="obj.queryParams.batchCode" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="审批状态:" prop="approvalStatus">
                <el-select class="width220" v-model="obj.queryParams.approvalStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in approvalStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="生成日期起:" prop="startDate">
                <el-date-picker class="width220" v-model="obj.queryParams.startDate" type="date" placeholder="请选择日期"
                    clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="生成日期止:" prop="endDate">
                <el-date-picker class="width220" v-model="obj.queryParams.endDate" type="date" placeholder="请选择日期"
                    clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="创建人:" prop="creator">
                <el-input class="width220" v-model="obj.queryParams.creator" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否退票重发:" prop="isRefund">
                <el-select class="width220" v-model="obj.queryParams.isRefund" placeholder="请选择" clearable>
                    <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资类别名称:" prop="salaryTypeName">
                <el-input class="width220" v-model="obj.queryParams.salaryTypeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发放名称:" prop="releaseName">
                <el-input class="width220" v-model="obj.queryParams.releaseName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectService">
                <el-input class="width220" v-model="obj.queryParams.projectService" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="薪资客服:" prop="salaryService">
                <el-select class="width220" v-model="obj.queryParams.salaryService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="lifePaymentAudit">生活支付审核</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="editPaymentAudit">编辑支付审核</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="editPaymentDate">编辑工资支付日与到款信息</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="55" />
            <el-table-column label="发放批次名称" align="center" prop="batchName" />
            <el-table-column label="发放批次编号" align="center" prop="batchCode" />
            <el-table-column label="申请金额" align="center" prop="applyAmount" />
            <el-table-column label="生成时间" align="center" prop="createTime" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="发放编号" align="center" prop="releaseCode" />
            <el-table-column label="发放名称" align="center" prop="releaseName" />
            <el-table-column label="薪资类别编号" align="center" prop="salaryTypeCode" />
            <el-table-column label="薪资类别名称" align="center" prop="salaryTypeName" />
            <el-table-column label="审批状态" align="center" prop="approvalStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.approvalStatus === '2' ? 'success' : scope.row.approvalStatus === '3' ? 'danger' : 'info'">
                        {{ scope.row.approvalStatusName }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="是否退票重发" align="center" prop="isRefund">
                <template #default="scope">
                    <el-tag :type="scope.row.isRefund === '1' ? 'warning' : ''">
                        {{ scope.row.isRefund === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="终止原因" align="center" prop="terminationReason" />
            <el-table-column label="派单地" align="center" prop="dispatchPlace" />
            <el-table-column label="接单地" align="center" prop="receivePlace" />
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="批次详情" align="center" width="150">
                <template #default="scope">
                    <el-button text icon="View" @click="handleDetail(scope.row)">查看详情</el-button>
                    <el-button text icon="Download" @click="handleDownload(scope.row)">下载</el-button>
                </template>
            </el-table-column>
            <el-table-column label="查看明细" align="center" width="150">
                <template #default="scope">
                    <el-button text icon="View" @click="handleDetail(scope.row)">查看详情</el-button>
                    <el-button text icon="Download" @click="handleDownload(scope.row)">下载</el-button>
                </template>
            </el-table-column>
            <el-table-column label="项目客服" align="center" prop="projectService" />
            <el-table-column label="薪资客服" align="center" prop="salaryService" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 对话框 -->
        <PaymentApplication v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm"
            menuName="releaseBatch" />
    </div>
</template>

<script setup name="ReleaseBatch">
import PaymentApplication from '@/views/reonManage/components/dialog/paymentApplication.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

/** 表格行样式 */
function tableRowClassName({ row, rowIndex }) {
    if (row.reportLockStatus === '1') {
        return 'red-row'
    } else if (row.reportLockStatus === '0') {
        return 'blue-row'
    } else if (row.reportLockStatus === '2') {
        return 'gray-row'
    }
    return '';
}
// 审批状态选项
const approvalStatusOptions = [
    { value: '0', label: '未审批' },
    { value: '1', label: '审批中' },
    { value: '2', label: '已审批' },
    { value: '3', label: '已驳回' }
];

// 是/否选项
const yesNoOptions = [
    { value: '0', label: '否' },
    { value: '1', label: '是' }
];

// 客服选项
const serviceOptions = [
    { value: '1', label: '客服A' },
    { value: '2', label: '客服B' },
    { value: '3', label: '客服C' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        batchCode: null, // 发放批次编号/名称
        customerName: null, // 客户名称
        approvalStatus: null, // 审批状态
        startDate: null, // 生成日期起
        endDate: null, // 生成日期止
        creator: null, // 创建人
        isRefund: null, // 是否退票重发
        salaryTypeName: null, // 薪资类别名称
        releaseName: null, // 发放名称
        projectService: null, // 项目客服
        salaryService: null, // 薪资客服
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 显示对话框
    dialogShow2: false, // 显示对话框
    title: '', // 对话框标题
    ids: [], // 选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                batchName: '五月工资发放批次',
                batchCode: 'FB20230501',
                applyAmount: 125000.00,
                createTime: '2023-05-10',
                customerName: '某某科技有限公司',
                releaseCode: 'R20230501',
                releaseName: '五月工资发放',
                salaryTypeCode: 'ST001',
                salaryTypeName: '正式工资',
                approvalStatus: '2',
                approvalStatusName: '已审批',
                isRefund: '0',
                remark: '正常发放',
                terminationReason: '',
                dispatchPlace: '北京',
                receivePlace: '上海',
                creator: '管理员',
                projectService: '王五',
                salaryService: '赵六'
            },
            {
                id: 2,
                batchName: '六月工资发放批次',
                batchCode: 'FB20230601',
                applyAmount: 135000.00,
                createTime: '2023-06-10',
                customerName: '某某信息技术有限公司',
                releaseCode: 'R20230601',
                releaseName: '六月工资发放',
                salaryTypeCode: 'ST001',
                salaryTypeName: '正式工资',
                approvalStatus: '1',
                approvalStatusName: '审批中',
                isRefund: '0',
                remark: '正常发放',
                terminationReason: '',
                dispatchPlace: '北京',
                receivePlace: '广州',
                creator: '管理员',
                projectService: '赵六',
                salaryService: '钱七'
            },
            {
                id: 3,
                batchName: '退票重发批次',
                batchCode: 'FB20230701',
                applyAmount: 45000.00,
                createTime: '2023-07-05',
                customerName: '某某软件有限公司',
                releaseCode: 'R20230701',
                releaseName: '退票重发',
                salaryTypeCode: 'ST002',
                salaryTypeName: '退票重发',
                approvalStatus: '3',
                approvalStatusName: '已驳回',
                isRefund: '1',
                remark: '退票重发处理',
                terminationReason: '信息不完整',
                dispatchPlace: '北京',
                receivePlace: '深圳',
                creator: '管理员',
                projectService: '钱七',
                salaryService: '孙八'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}



// 表单重置
function resetForm() {
    obj.dialogForm = {
        formTable: []
    };
    proxy.resetForm("formRef");
}

// 提交表单
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 查看详情
function handleDetail(row) {
    proxy.$modal.msgSuccess('查看详情：' + row.batchName);
}

// 下载
function handleDownload(row) {
    proxy.$modal.msgSuccess('下载成功：' + row.batchName);
}

// 新增行
function addRow() {
    proxy.$modal.msgSuccess('新增行成功');
}

// 删除行
function deleteRow() {
    proxy.$modal.msgSuccess('删除行成功');
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

// 删除
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

// 生活支付审核
function lifePaymentAudit() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要审核的数据');
        return;
    }
    proxy.$modal.msgSuccess('审核操作成功');
}

// 编辑支付审核
function editPaymentAudit() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要编辑的数据');
        return;
    }
    proxy.$modal.msgSuccess('编辑操作成功');
}

// 编辑工资支付日与到款信息
function editPaymentDate() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要编辑的数据');
        return;
    }
    obj.title = '编辑工资支付日与到款信息';
    obj.dialogShow = true;
}



getList();
</script>
<style lang="scss" scoped>
:deep(.red-row) {
    color: #f00;
}

:deep(.blue-row) {
    color: #4186D5;
}

:deep(.gray-row) {
    color: #d3d3d3;
}
</style>