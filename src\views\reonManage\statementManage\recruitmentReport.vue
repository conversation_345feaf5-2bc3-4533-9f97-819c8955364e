<!-- 增员报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="报入职起始时间:" prop="reportStartDate">
                <el-date-picker class="width220" v-model="obj.queryParams.reportStartDate" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="报入职截止时间:" prop="reportEndDate">
                <el-date-picker class="width220" v-model="obj.queryParams.reportEndDate" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="客户名称" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="项目城市" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单城市:" prop="receiveCity">
                <el-select class="width220" v-model="obj.queryParams.receiveCity" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方客服:" prop="receiveSales">
                <el-select class="width220" v-model="obj.queryParams.receiveSales" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方:" prop="receiveSalesName">
                <el-input class="width220" v-model="obj.queryParams.receiveSalesName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="是否单立户:" prop="receiveSingle">
                <el-select class="width220" v-model="obj.queryParams.receiveSingle" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script setup name="RecruitmentReport">


const obj = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
    },//查询表单

})

/** 导出操作 */
function handleExport() {
    console.log(obj.queryParams);
}

</script>
<style lang="scss" scoped></style>