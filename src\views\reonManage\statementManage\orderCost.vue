<!-- 订单费用查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="分公司/供应商:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNumber">
                <el-input class="width220" v-model="obj.queryParams.orderNumber" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="身份证号:" prop="idCard">
                <el-input class="width220" v-model="obj.queryParams.idCard" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="服务开始月:" prop="startMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.startMonth" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="服务结束月:" prop="endMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.endMonth" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
            </el-row>
        </el-form>
    </div>
</template>

<script setup name="OrderCost">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    queryParams: {

    },//查询表单
})

/** 导出明细按钮操作 */
function handleExport() {
    if (!obj.queryParams.startMonth) {
        proxy.$modal.msgWarning("请选择服务开始月");
        return;
    }
    if (!obj.queryParams.endMonth) {
        proxy.$modal.msgWarning("请选择服务结束月");
        return;
    }
}

</script>
<style lang="scss" scoped></style>