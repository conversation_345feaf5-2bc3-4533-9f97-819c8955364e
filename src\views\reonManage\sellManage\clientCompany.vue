<!-- 客户公司 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户公司名称:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入公司名称" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleEdit">编辑</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户公司名称" align="center" prop="companyName" min-width="200" />
            <el-table-column label="创建人" align="center" prop="creator" width="100" />
            <el-table-column label="公司地址" align="center" prop="address" min-width="250" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
            <el-table-column label="操作" align="center" width="120" fixed="right">
                <template #default="scope">
                    <el-button text type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 新增/编辑 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="25%" append-to-body draggable>
            <el-form :model="obj.dialogForm" ref="dialogRef" :rules="obj.rules" label-width="auto">
                <el-form-item label="客户公司名称:" prop="companyName">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.companyName" placeholder="请输入客户公司名称" />
                </el-form-item>
                <el-form-item label="公司地址:" prop="address">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.address" placeholder="请输入公司地址" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button @click="handleCancel">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup name="ClientCompany">



const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客户公司数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: '', // 客户公司名称
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            companyName: '北京科技有限公司',
            creator: '张三',
            address: '北京市海淀区西二旗路100号',
            createTime: '2023-05-01 10:00:00'
        },
        {
            id: 2,
            companyName: '上海贸易有限公司',
            creator: '李四',
            address: '上海市浦东新区张江路500号',
            createTime: '2023-05-02 14:30:00'
        },
        {
            id: 3,
            companyName: '广州电子有限公司',
            creator: '王五',
            address: '广州市天河区体育西路123号',
            createTime: '2023-05-03 09:15:00'
        },
        {
            id: 4,
            companyName: '深圳科技有限公司',
            creator: '赵六',
            address: '深圳市南山区科技园路10号',
            createTime: '2023-05-04 11:20:00'
        },
        {
            id: 5,
            companyName: '成都信息有限公司',
            creator: '孙七',
            address: '成都市高新区天府路888号',
            createTime: '2023-05-05 15:45:00'
        }
    ],

    // 对话框相关数据
    dialogForm: {
        id: '',
        companyName: '',
        address: ''
    },
    dialogShow: false, // 对话框显示状态
    ids: [], // 选中的id
    title: '', // 对话框标题
    rules: {
        companyName: [
            { required: true, message: '请输入客户公司名称', trigger: 'blur' }
        ],
        address: [
            { required: true, message: '请输入公司地址', trigger: 'blur' }
        ]
    }
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];
        if (obj.queryParams.companyName) {
            filteredData = filteredData.filter(item =>
                item.companyName.includes(obj.queryParams.companyName)
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 新增客户公司 */
function handleAdd() {
    // 重置表单
    obj.dialogForm = {
        id: '',
        companyName: '',
        address: ''
    };

    obj.dialogShow = true;
    obj.title = "新增客户公司";
}

/** 编辑客户公司 */
function handleEdit(row) {
    const companyInfo = row || (obj.single ? null : obj.tableData.find(item => item.id === obj.ids[0]));
    if (!companyInfo) {
        proxy.$modal.msgInfo('请选择要编辑的客户公司');
        return;
    }

    // 复制数据到表单
    obj.dialogForm = JSON.parse(JSON.stringify(companyInfo));
    obj.dialogShow = true;
    obj.title = "编辑客户公司";
}

/** 保存客户公司信息 */
function handleSave() {
    if (!proxy.$refs["dialogRef"]) return;

    proxy.$refs["dialogRef"].validate(valid => {
        if (valid) {
            // 模拟保存操作
            const isNew = !obj.dialogForm.id;

            if (isNew) {
                // 新增记录
                const newCompany = {
                    ...obj.dialogForm,
                    id: obj.tableData.length + 1,
                    creator: '当前用户',
                    createTime: new Date().toLocaleString()
                };

                obj.tableData.unshift(newCompany);
                proxy.$modal.msgSuccess('新增成功');
            } else {
                // 编辑记录
                const index = obj.tableData.findIndex(item => item.id === obj.dialogForm.id);
                if (index !== -1) {
                    obj.tableData[index] = {
                        ...obj.tableData[index],
                        ...obj.dialogForm
                    };
                }
                proxy.$modal.msgSuccess('编辑成功');
            }

            obj.dialogShow = false;
            obj.total = obj.tableData.length;
        }
    });
}

/** 取消操作 */
function handleCancel() {
    obj.dialogShow = false;
}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped></style>