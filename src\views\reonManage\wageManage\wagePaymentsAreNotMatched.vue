<!-- 工资支付未匹配-查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="出款公司名称:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收款方:" prop="payee">
                <el-select class="width220" v-model="obj.queryParams.payee" placeholder="请选择" clearable>
                    <el-option v-for="item in payeeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付类型:" prop="paymentType">
                <el-select class="width220" v-model="obj.queryParams.paymentType" placeholder="请选择" clearable>
                    <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请人:" prop="applicant">
                <el-input class="width220" v-model="obj.queryParams.applicant" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付方式:" prop="paymentMethod">
                <el-input class="width220" v-model="obj.queryParams.paymentMethod" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请支付时间>=:" prop="applyTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeStart" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="申请支付时间<=:" prop="applyTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeEnd" type="date" placeholder="请选择日期"
                    clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="工资支付日期:" prop="salaryPaymentDate">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryPaymentDate" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="是否退票重发:" prop="isRefund">
                <el-select class="width220" v-model="obj.queryParams.isRefund" placeholder="请选择" clearable>
                    <el-option v-for="item in isRefundOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资复核日期:" prop="salaryReviewDate">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryReviewDate" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="自有工资复核日期:" prop="ownSalaryReviewDate">
                <el-date-picker class="width220" v-model="obj.queryParams.ownSalaryReviewDate" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="供应商发薪时间:" prop="supplierPaymentTime">
                <el-date-picker class="width220" v-model="obj.queryParams.supplierPaymentTime" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="支付关联抬头:" prop="paymentHeader">
                <el-input class="width220" v-model="obj.queryParams.paymentHeader" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectService">
                <el-select class="width220" v-model="obj.queryParams.projectService" placeholder="请选择" clearable>
                    <el-option v-for="item in projectServiceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资客服:" prop="salaryService">
                <el-select class="width220" v-model="obj.queryParams.salaryService" placeholder="请选择" clearable>
                    <el-option v-for="item in salaryServiceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleExport">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleEditPaymentDate">编辑到款信息</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="出款公司名称" align="center" sortable prop="companyName" />
            <el-table-column label="客户名称" align="center" sortable prop="customerName" />
            <el-table-column label="收款方" align="center" sortable prop="payee" />
            <el-table-column label="工资支付日期" align="center" sortable prop="salaryPaymentDate" />
            <el-table-column label="总计" align="center" sortable prop="totalAmount" />
            <el-table-column label="产品方案" align="center" sortable prop="productPlan" />
            <el-table-column label="制单状态" align="center" sortable prop="orderStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.orderStatus === '1' ? 'success' : scope.row.orderStatus === '2' ? 'warning' : 'info'">
                        {{ getOrderStatusName(scope.row.orderStatus) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="审批状态" align="center" sortable prop="approvalStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.approvalStatus === '2' ? 'success' : scope.row.approvalStatus === '3' ? 'danger' : scope.row.approvalStatus === '1' ? 'warning' : 'info'">
                        {{ getApprovalStatusName(scope.row.approvalStatus) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="支付所属年月" align="center" sortable prop="paymentMonth" />
            <el-table-column label="实发工资款" align="center" sortable prop="actualSalary" />
            <el-table-column label="个税款" align="center" sortable prop="personalTax" />
            <el-table-column label="补偿金" align="center" sortable prop="compensation" />
            <el-table-column label="补偿金个税" align="center" sortable prop="compensationTax" />
            <el-table-column label="年终奖" align="center" sortable prop="annualBonus" />
            <el-table-column label="年终奖个税" align="center" sortable prop="annualBonusTax" />
            <el-table-column label="劳务工资" align="center" sortable prop="laborSalary" />
            <el-table-column label="劳务工资个税" align="center" sortable prop="laborTax" />
            <el-table-column label="服务费" align="center" sortable prop="serviceFee" />
            <el-table-column label="残障金" align="center" sortable prop="disabilityFund" />
            <el-table-column label="跨行手续费" align="center" sortable prop="crossBankFee" />
            <el-table-column label="工会费" align="center" sortable prop="unionFee" />
            <el-table-column label="税金合计" align="center" sortable prop="totalTax" />
            <el-table-column label="复合时间" align="center" sortable prop="reviewTime" />
            <el-table-column label="自有工资复合时间" align="center" sortable prop="ownSalaryReviewTime" />
            <el-table-column label="申请人" align="center" sortable prop="applicant" />
            <el-table-column label="支付类型" align="center" sortable prop="paymentType">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.paymentType === '1' ? 'primary' : scope.row.paymentType === '2' ? 'success' : 'warning'">
                        {{ getPaymentTypeName(scope.row.paymentType) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="是否退票重发" align="center" sortable prop="isRefund">
                <template #default="scope">
                    <el-tag :type="scope.row.isRefund === '1' ? 'warning' : 'info'">
                        {{ scope.row.isRefund === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="供应商发薪时间" align="center" sortable prop="supplierPaymentTime" />
            <el-table-column label="支付关联抬头" align="center" sortable prop="paymentHeader" />
            <el-table-column label="支付ID" align="center" sortable prop="paymentId" />
            <el-table-column label="审批信息" align="center">
                <template #default="scope">
                    <el-button text @click="handleDetail(scope.row)">查看详情</el-button>
                </template>
            </el-table-column>
            <el-table-column label="查看人员明细" align="center">
                <template #default="scope">
                    <el-button text @click="handleDetail(scope.row)">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 对话框 -->
        <PaymentApplication v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm"
            menuName="wagePaymentsAreNotMatched" />
    </div>
</template>

<script setup name="WagePaymentsAreNotMatched">
import PaymentApplication from '@/views/reonManage/components/dialog/paymentApplication.vue';


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 制单状态选项
const orderStatusOptions = [
    { value: '0', label: '未制单' },
    { value: '1', label: '已制单' },
    { value: '2', label: '制单中' }
];

// 审批状态选项
const approvalStatusOptions = [
    { value: '0', label: '未审批' },
    { value: '1', label: '审批中' },
    { value: '2', label: '已审批' },
    { value: '3', label: '已驳回' }
];

// 支付类型选项
const paymentTypeOptions = [
    { value: '1', label: '工资' },
    { value: '2', label: '年终奖' },
    { value: '3', label: '补偿金' }
];

// 是否退票重发选项
const isRefundOptions = [
    { value: '0', label: '否' },
    { value: '1', label: '是' }
];

// 项目客服选项
const projectServiceOptions = [
    { value: '1', label: '客服1' },
    { value: '2', label: '客服2' },
    { value: '3', label: '客服3' }
];

// 薪资客服选项
const salaryServiceOptions = [
    { value: '1', label: '客服1' },
    { value: '2', label: '客服2' },
    { value: '3', label: '客服3' }
];

// 收款方选项
const payeeOptions = [
    { value: '1', label: '供应商A' },
    { value: '2', label: '供应商B' },
    { value: '3', label: '供应商C' }
];

// 表单验证规则
const rules = {
    paymentType: [
        { required: true, message: '请选择支付类型', trigger: 'change' }
    ],
    paymentHeader: [
        { required: true, message: '请输入支付抬头', trigger: 'blur' }
    ],
    paymentMethod: [
        { required: true, message: '请输入支付方式', trigger: 'blur' }
    ],
    payee: [
        { required: true, message: '请选择收款方', trigger: 'change' }
    ],
    bankCardNo: [
        { required: true, message: '请选择银行卡号', trigger: 'change' }
    ],
    salaryPaymentDate: [
        { required: true, message: '请选择工资支付日期', trigger: 'change' }
    ]
};

// 预计到款数据
const expectedPaymentData = ref([]);

// 已核销到款数据
const verifiedPaymentData = ref([]);

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null, // 出款公司名称
        payee: null, // 收款方
        paymentType: null, // 支付类型
        applicant: null, // 申请人
        paymentMethod: null, // 支付方式
        customerName: null, // 客户名称
        applyTimeStart: null, // 申请支付时间>=
        applyTimeEnd: null, // 申请支付时间<=
        salaryPaymentDate: null, // 工资支付日期
        isRefund: null, // 是否退票重发
        salaryReviewDate: null, // 工资复核日期
        ownSalaryReviewDate: null, // 自有工资复核日期
        supplierPaymentTime: null, // 供应商发薪时间
        paymentHeader: null, // 支付关联抬头
        projectService: null, // 项目客服
        salaryService: null, // 薪资客服
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {
        paymentType: '',
        paymentHeader: '',
        paymentMethod: '',
        detailPaymentMethod: '',
        payee: '',
        bankCardNo: '',
        bankName: '',
        bankBranch: '',
        accountNo: '',
        documentCount: '',
        totalAmount: '',
        payableAmount: '',
        salaryPaymentDate: '',
        paymentMonth: '',
        customer: '',
        dispatchPlace: '',
        dispatchHeader: '',
        isRefund: '0',
        supplierPaymentTime: '',
        paymentContent: '',
        dispatchPurpose: '',
        remark: ''
    }, // 表单
    dialogShow: false, // 显示对话框
    title: '编辑到款信息', // 对话框标题
    ids: [], // 选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                companyName: '某某科技有限公司',
                customerName: '客户A',
                payee: '供应商A',
                salaryPaymentDate: '2023-05-15',
                totalAmount: 100000.00,
                productPlan: '标准方案',
                orderStatus: '1',
                approvalStatus: '2',
                paymentMonth: '2023-05',
                actualSalary: 85000.00,
                personalTax: 5000.00,
                compensation: 0.00,
                compensationTax: 0.00,
                annualBonus: 0.00,
                annualBonusTax: 0.00,
                laborSalary: 0.00,
                laborTax: 0.00,
                serviceFee: 8000.00,
                disabilityFund: 1000.00,
                crossBankFee: 500.00,
                unionFee: 500.00,
                totalTax: 15000.00,
                reviewTime: '2023-05-10 10:30:00',
                ownSalaryReviewTime: '2023-05-10 11:00:00',
                applicant: '李四',
                paymentType: '1',
                isRefund: '0',
                supplierPaymentTime: '2023-05-12',
                paymentHeader: '工资支付',
                paymentId: 'PAY20230515001'
            },
            {
                id: 2,
                companyName: '某某信息技术有限公司',
                customerName: '客户B',
                payee: '供应商B',
                salaryPaymentDate: '2023-06-15',
                totalAmount: 120000.00,
                productPlan: '高级方案',
                orderStatus: '1',
                approvalStatus: '2',
                paymentMonth: '2023-06',
                actualSalary: 100000.00,
                personalTax: 6000.00,
                compensation: 0.00,
                compensationTax: 0.00,
                annualBonus: 0.00,
                annualBonusTax: 0.00,
                laborSalary: 0.00,
                laborTax: 0.00,
                serviceFee: 10000.00,
                disabilityFund: 2000.00,
                crossBankFee: 1000.00,
                unionFee: 1000.00,
                totalTax: 20000.00,
                reviewTime: '2023-06-10 10:30:00',
                ownSalaryReviewTime: '2023-06-10 11:00:00',
                applicant: '王五',
                paymentType: '1',
                isRefund: '0',
                supplierPaymentTime: '2023-06-12',
                paymentHeader: '工资支付',
                paymentId: 'PAY20230615001'
            },
            {
                id: 3,
                companyName: '某某软件有限公司',
                customerName: '客户C',
                payee: '供应商C',
                salaryPaymentDate: '2023-07-15',
                totalAmount: 50000.00,
                productPlan: '基础方案',
                orderStatus: '2',
                approvalStatus: '1',
                paymentMonth: '2023-07',
                actualSalary: 40000.00,
                personalTax: 2000.00,
                compensation: 0.00,
                compensationTax: 0.00,
                annualBonus: 0.00,
                annualBonusTax: 0.00,
                laborSalary: 0.00,
                laborTax: 0.00,
                serviceFee: 5000.00,
                disabilityFund: 1000.00,
                crossBankFee: 1000.00,
                unionFee: 1000.00,
                totalTax: 10000.00,
                reviewTime: '2023-07-10 10:30:00',
                ownSalaryReviewTime: '2023-07-10 11:00:00',
                applicant: '赵六',
                paymentType: '2',
                isRefund: '1',
                supplierPaymentTime: '2023-07-12',
                paymentHeader: '年终奖支付',
                paymentId: 'PAY20230715001'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}



// 表单重置
function resetForm() {
    obj.dialogForm = {
        paymentType: '',
        paymentHeader: '',
        paymentMethod: '',
        detailPaymentMethod: '',
        payee: '',
        bankCardNo: '',
        bankName: '',
        bankBranch: '',
        accountNo: '',
        documentCount: '',
        totalAmount: '',
        payableAmount: '',
        salaryPaymentDate: '',
        paymentMonth: '',
        customer: '',
        dispatchPlace: '',
        dispatchHeader: '',
        isRefund: '0',
        supplierPaymentTime: '',
        paymentContent: '',
        dispatchPurpose: '',
        remark: ''
    };
    proxy.resetForm("formRef");
}

// 获取制单状态名称
function getOrderStatusName(status) {
    const statusMap = {
        '0': '未制单',
        '1': '已制单',
        '2': '制单中'
    };
    return statusMap[status] || '未知状态';
}

// 获取审批状态名称
function getApprovalStatusName(status) {
    const statusMap = {
        '0': '未审批',
        '1': '审批中',
        '2': '已审批',
        '3': '已驳回'
    };
    return statusMap[status] || '未知状态';
}

// 获取支付类型名称
function getPaymentTypeName(type) {
    const typeMap = {
        '1': '工资',
        '2': '年终奖',
        '3': '补偿金'
    };
    return typeMap[type] || '未知类型';
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 编辑到款信息
function handleEditPaymentDate() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要编辑的记录');
        return;
    }

    obj.dialogShow = true;
    obj.title = '编辑到款信息';
    resetForm();

    // 模拟预计到款数据
    expectedPaymentData.value = [
        {
            id: 1,
            customer: '客户A',
            expectedDate: '2023-05-16',
            amount: 100000.00,
            paymentHeader: '工资支付'
        }
    ];

    // 模拟已核销到款数据
    verifiedPaymentData.value = [
        {
            id: 1,
            customer: '客户A',
            verifiedDate: '2023-05-16',
            amount: 100000.00,
            paymentHeader: '工资支付'
        }
    ];
}

// 新增行
function addRow() {
    expectedPaymentData.value.push({
        id: expectedPaymentData.value.length + 1,
        customer: '',
        expectedDate: '',
        amount: 0,
        paymentHeader: ''
    });
}

// 删除行
function deleteRow() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要删除的行');
        return;
    }

    // 实际项目中应该删除选中的行
    proxy.$modal.msgSuccess('删除成功');
}

// 查看详情
function handleDetail(row) {
    proxy.$modal.msgSuccess('查看详情：' + row.paymentId);
    // 实际项目中应该跳转到详情页面
}

// 提交表单
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}



getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}

.mt10 {
    margin-top: 10px;
}

.mb10 {
    margin-bottom: 10px;
}

.dialog-footer {
    text-align: center;
    padding-top: 10px;
}

.border-box {
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;

    .title {
        position: absolute;
        top: -12px;
        left: 20px;
        padding: 0 10px;
        background: #fff;
        font-size: 14px;
        color: #606266;
    }

    .content {
        margin-top: 10px;
    }
}
</style>