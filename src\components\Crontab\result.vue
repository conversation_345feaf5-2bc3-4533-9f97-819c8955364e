<template>
    <div class="popup-result">
        <p class="title">最近5次运行时间</p>
        <ul class="popup-result-scroll">
            <template v-if='isShow'>
                <li v-for='item in resultList' :key="item">{{ item }}</li>
            </template>
            <li v-else>计算结果中...</li>
        </ul>
    </div>
</template>

<script setup>
const props = defineProps({
    ex: {
        type: String,
        default: ''
    }
})
const dayRule = ref('')
const dayRuleSup = ref('')
const dateArr = ref([])
const resultList = ref([])
const isShow = ref(false)
watch(() => props.ex, () => expressionChange())
// 表达式值变化时，开始去计算结果
function expressionChange() {
    // 计算开始-隐藏结果
    isShow.value = false;
    // 获取规则数组[0秒、1分、2时、3日、4月、5星期、6年]
    let ruleArr = props.ex.split(' ');
    // 用于记录进入循环的次数
    let nums = 0;
    // 用于暂时存符号时间规则结果的数组
    let resultArr = [];
    // 获取当前时间精确至[年、月、日、时、分、秒]
    let nTime = new Date();
    let nYear = nTime.getFullYear();
    let nMonth = nTime.getMonth() + 1;
    let nDay = nTime.getDate();
    let nHour = nTime.getHours();
    let nMin = nTime.getMinutes();
    let nSecond = nTime.getSeconds();
    // 根据规则获取到近100年可能年数组、月数组等等
    getSecondArr(ruleArr[0]);
    getMinArr(ruleArr[1]);
    getHourArr(ruleArr[2]);
    getDayArr(ruleArr[3]);
    getMonthArr(ruleArr[4]);
    getWeekArr(ruleArr[5]);
    getYearArr(ruleArr[6], nYear);
    // 将获取到的数组赋值-方便使用
    let sDate = dateArr.value[0];
    let mDate = dateArr.value[1];
    let hDate = dateArr.value[2];
    let DDate = dateArr.value[3];
    let MDate = dateArr.value[4];
    let YDate = dateArr.value[5];
    // 获取当前时间在数组中的索引
    let sIdx = getIndex(sDate, nSecond);
    let mIdx = getIndex(mDate, nMin);
    let hIdx = getIndex(hDate, nHour);
    let DIdx = getIndex(DDate, nDay);
    let MIdx = getIndex(MDate, nMonth);
    let YIdx = getIndex(YDate, nYear);
    // 重置月日时分秒的函数(后面用的比较多)
    const resetSecond = function () {
        sIdx = 0;
        nSecond = sDate[sIdx]
    }
    const resetMin = function () {
        mIdx = 0;
        nMin = mDate[mIdx]
        resetSecond();
    }
    const resetHour = function () {
        hIdx = 0;
        nHour = hDate[hIdx]
        resetMin();
    }
    const resetDay = function () {
        DIdx = 0;
        nDay = DDate[DIdx]
        resetHour();
    }
    const resetMonth = function () {
        MIdx = 0;
        nMonth = MDate[MIdx]
        resetDay();
    }
    // 如果当前年份不为数组中当前值
    if (nYear !== YDate[YIdx]) {
        resetMonth();
    }
    // 如果当前月份不为数组中当前值
    if (nMonth !== MDate[MIdx]) {
        resetDay();
    }
    // 如果当前“日”不为数组中当前值
    if (nDay !== DDate[DIdx]) {
        resetHour();
    }
    // 如果当前“时”不为数组中当前值
    if (nHour !== hDate[hIdx]) {
        resetMin();
    }
    // 如果当前“分”不为数组中当前值
    if (nMin !== mDate[mIdx]) {
        resetSecond();
    }
    // 循环年份数组
    goYear: for (let Yi = YIdx; Yi < YDate.length; Yi++) {
        let YY = YDate[Yi];
        // 如果到达最大值时
        if (nMonth > MDate[MDate.length - 1]) {
            resetMonth();
            continue;
        }
        // 循环月份数组
        goMonth: for (let Mi = MIdx; Mi < MDate.length; Mi++) {
            // 赋值、方便后面运算
            let MM = MDate[Mi];
            MM = MM < 10 ? '0' + MM : MM;
            // 如果到达最大值时
            if (nDay > DDate[DDate.length - 1]) {
                resetDay();
                if (Mi === MDate.length - 1) {
                    resetMonth();
                    continue goYear;
                }
                continue;
            }
            // 循环日期数组
            goDay: for (let Di = DIdx; Di < DDate.length; Di++) {
                // 赋值、方便后面运算
                let DD = DDate[Di];
                let thisDD = DD < 10 ? '0' + DD : DD;
                // 如果到达最大值时
                if (nHour > hDate[hDate.length - 1]) {
                    resetHour();
                    if (Di === DDate.length - 1) {
                        resetDay();
                        if (Mi === MDate.length - 1) {
                            resetMonth();
                            continue goYear;
                        }
                        continue goMonth;
                    }
                    continue;
                }
                // 判断日期的合法性，不合法的话也是跳出当前循环
                if (checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true && dayRule.value !== 'workDay' && dayRule.value !== 'lastWeek' && dayRule.value !== 'lastDay') {
                    resetDay();
                    continue goMonth;
                }
                // 如果日期规则中有值时
                if (dayRule.value === 'lastDay') {
                    // 如果不是合法日期则需要将前将日期调到合法日期即月末最后一天
                    if (checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {
                        while (DD > 0 && checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {
                            DD--;
                            thisDD = DD < 10 ? '0' + DD : DD;
                        }
                    }
                } else if (dayRule.value === 'workDay') {
                    // 校验并调整如果是2月30号这种日期传进来时需调整至正常月底
                    if (checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {
                        while (DD > 0 && checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {
                            DD--;
                            thisDD = DD < 10 ? '0' + DD : DD;
                        }
                    }
                    // 获取达到条件的日期是星期X
                    let thisWeek = formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');
                    // 当星期日时
                    if (thisWeek === 1) {
                        // 先找下一个日，并判断是否为月底
                        DD++;
                        thisDD = DD < 10 ? '0' + DD : DD;
                        // 判断下一日已经不是合法日期
                        if (checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {
                            DD -= 3;
                        }
                    } else if (thisWeek === 7) {
                        // 当星期6时只需判断不是1号就可进行操作
                        if (dayRuleSup.value !== 1) {
                            DD--;
                        } else {
                            DD += 2;
                        }
                    }
                } else if (dayRule.value === 'weekDay') {
                    // 如果指定了是星期几
                    // 获取当前日期是属于星期几
                    let thisWeek = formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');
                    // 校验当前星期是否在星期池（dayRuleSup）中
                    if (dayRuleSup.value.indexOf(thisWeek) < 0) {
                        // 如果到达最大值时
                        if (Di === DDate.length - 1) {
                            resetDay();
                            if (Mi === MDate.length - 1) {
                                resetMonth();
                                continue goYear;
                            }
                            continue goMonth;
                        }
                        continue;
                    }
                } else if (dayRule.value === 'assWeek') {
                    // 如果指定了是第几周的星期几
                    // 获取每月1号是属于星期几
                    let thisWeek = formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');
                    if (dayRuleSup.value[1] >= thisWeek) {
                        DD = (dayRuleSup.value[0] - 1) * 7 + dayRuleSup.value[1] - thisWeek + 1;
                    } else {
                        DD = dayRuleSup.value[0] * 7 + dayRuleSup.value[1] - thisWeek + 1;
                    }
                } else if (dayRule.value === 'lastWeek') {
                    // 如果指定了每月最后一个星期几
                    // 校验并调整如果是2月30号这种日期传进来时需调整至正常月底
                    if (checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {
                        while (DD > 0 && checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') !== true) {
                            DD--;
                            thisDD = DD < 10 ? '0' + DD : DD;
                        }
                    }
                    // 获取月末最后一天是星期几
                    let thisWeek = formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');
                    // 找到要求中最近的那个星期几
                    if (dayRuleSup.value < thisWeek) {
                        DD -= thisWeek - dayRuleSup.value;
                    } else if (dayRuleSup.value > thisWeek) {
                        DD -= 7 - (dayRuleSup.value - thisWeek)
                    }
                }
                // 判断时间值是否小于10置换成“05”这种格式
                DD = DD < 10 ? '0' + DD : DD;
                // 循环“时”数组
                goHour: for (let hi = hIdx; hi < hDate.length; hi++) {
                    let hh = hDate[hi] < 10 ? '0' + hDate[hi] : hDate[hi]
                    // 如果到达最大值时
                    if (nMin > mDate[mDate.length - 1]) {
                        resetMin();
                        if (hi === hDate.length - 1) {
                            resetHour();
                            if (Di === DDate.length - 1) {
                                resetDay();
                                if (Mi === MDate.length - 1) {
                                    resetMonth();
                                    continue goYear;
                                }
                                continue goMonth;
                            }
                            continue goDay;
                        }
                        continue;
                    }
                    // 循环"分"数组
                    goMin: for (let mi = mIdx; mi < mDate.length; mi++) {
                        let mm = mDate[mi] < 10 ? '0' + mDate[mi] : mDate[mi];
                        // 如果到达最大值时
                        if (nSecond > sDate[sDate.length - 1]) {
                            resetSecond();
                            if (mi === mDate.length - 1) {
                                resetMin();
                                if (hi === hDate.length - 1) {
                                    resetHour();
                                    if (Di === DDate.length - 1) {
                                        resetDay();
                                        if (Mi === MDate.length - 1) {
                                            resetMonth();
                                            continue goYear;
                                        }
                                        continue goMonth;
                                    }
                                    continue goDay;
                                }
                                continue goHour;
                            }
                            continue;
                        }
                        // 循环"秒"数组
                        goSecond: for (let si = sIdx; si <= sDate.length - 1; si++) {
                            let ss = sDate[si] < 10 ? '0' + sDate[si] : sDate[si];
                            // 添加当前时间（时间合法性在日期循环时已经判断）
                            if (MM !== '00' && DD !== '00') {
                                resultArr.push(YY + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss)
                                nums++;
                            }
                            // 如果条数满了就退出循环
                            if (nums === 5) break goYear;
                            // 如果到达最大值时
                            if (si === sDate.length - 1) {
                                resetSecond();
                                if (mi === mDate.length - 1) {
                                    resetMin();
                                    if (hi === hDate.length - 1) {
                                        resetHour();
                                        if (Di === DDate.length - 1) {
                                            resetDay();
                                            if (Mi === MDate.length - 1) {
                                                resetMonth();
                                                continue goYear;
                                            }
                                            continue goMonth;
                                        }
                                        continue goDay;
                                    }
                                    continue goHour;
                                }
                                continue goMin;
                            }
                        } //goSecond
                    } //goMin
                }//goHour
            }//goDay
        }//goMonth
    }
    // 判断100年内的结果条数
    if (resultArr.length === 0) {
        resultList.value = ['没有达到条件的结果！'];
    } else {
        resultList.value = resultArr;
        if (resultArr.length !== 5) {
            resultList.value.push('最近100年内只有上面' + resultArr.length + '条结果！')
        }
    }
    // 计算完成-显示结果
    isShow.value = true;
}
/**
 * 用于计算某位数字在数组中的索引
 * 当数字小于或等于数组中第一个元素或大于数组中最后一个元素时，返回0
 * 否则，返回数字所在区间的较高索引
 * 
 * @param {Array} arr - 一个升序排列的数组
 * @param {number} value - 需要查找索引的数字
 * @returns {number} - 数字在数组中的索引
 */
function getIndex(arr, value) {
    // 检查数字是否在数组的范围之外，如果是，则返回0
    if (value <= arr[0] || value > arr[arr.length - 1]) {
        return 0;
    } else {
        // 遍历数组，找到数字所在区间的较高索引
        for (let i = 0; i < arr.length - 1; i++) {
            if (value > arr[i] && value <= arr[i + 1]) {
                return i + 1;
            }
        }
    }
}
/**
 * 获取"年"数组
 * 根据规则和起始年份生成年份数组
 * @param {string} rule - 年份规则，可以是具体的年份，也可以是包含特殊字符的规则字符串
 * @param {number} year - 起始年份
 */
function getYearArr(rule, year) {
    // 初始化年份数组，包含从起始年份到起始年份后100年的年份
    dateArr.value[5] = getOrderArr(year, year + 100);

    // 如果规则存在，根据不同的规则生成相应的年份数组
    if (rule !== undefined) {
        // 如果规则包含'-'，表示生成连续年份数组
        if (rule.indexOf('-') >= 0) {
            dateArr.value[5] = getCycleArr(rule, year + 100, false)
        } else if (rule.indexOf('/') >= 0) {
            // 如果规则包含'/'，表示生成等间隔年份数组
            dateArr.value[5] = getAverageArr(rule, year + 100)
        } else if (rule !== '*') {
            // 如果规则不为'*'，表示生成指定年份数组
            dateArr.value[5] = getAssignArr(rule)
        }
    }
}
/**
 * 获取"月"数组
 * 根据不同的规则生成月份数组
 * @param {string} rule - 用于生成月份数组的规则
 */
function getMonthArr(rule) {
    // 初始化月份数组为1到12月
    dateArr.value[4] = getOrderArr(1, 12);

    // 如果规则中包含'-', 则使用区间方式生成月份数组
    if (rule.indexOf('-') >= 0) {
        dateArr.value[4] = getCycleArr(rule, 12, false)
    } else if (rule.indexOf('/') >= 0) {
        // 如果规则中包含'/', 则使用步长方式生成月份数组
        dateArr.value[4] = getAverageArr(rule, 12)
    } else if (rule !== '*') {
        // 如果规则不是'*', 则根据指定的规则生成月份数组
        dateArr.value[4] = getAssignArr(rule)
    }
}
// 获取"日"数组-主要为日期规则
function getWeekArr(rule) {
    // 只有当日期规则的两个值均为“”时则表达日期是有选项的
    if (dayRule.value === '' && dayRuleSup.value === '') {
        // 当规则中包含'-'时，设置日期规则为周日，并计算周期数组
        if (rule.indexOf('-') >= 0) {
            dayRule.value = 'weekDay';
            dayRuleSup.value = getCycleArr(rule, 7, false)
            // 当规则中包含'#'时，设置日期规则为特定周，并解析规则中的周数和日期
        } else if (rule.indexOf('#') >= 0) {
            dayRule.value = 'assWeek';
            let matchRule = rule.match(/[0-9]{1}/g);
            dayRuleSup.value = [Number(matchRule[1]), Number(matchRule[0])];
            dateArr.value[3] = [1];
            // 将周日的值从7改为0
            if (dayRuleSup.value[1] === 7) {
                dayRuleSup.value[1] = 0;
            }
            // 当规则中包含'L'时，设置日期规则为月末周，并解析规则中的周数
        } else if (rule.indexOf('L') >= 0) {
            dayRule.value = 'lastWeek';
            dayRuleSup.value = Number(rule.match(/[0-9]{1,2}/g)[0]);
            dateArr.value[3] = [31];
            // 将周日的值从7改为0
            if (dayRuleSup.value === 7) {
                dayRuleSup.value = 0;
            }
            // 当规则不为通配符且不为空时，设置日期规则为周日，并计算指定数组
        } else if (rule !== '*' && rule !== '?') {
            dayRule.value = 'weekDay';
            dayRuleSup.value = getAssignArr(rule)
        }
    }
}
// 获取"日"数组-少量为日期规则
/**
 * 根据传入的规则参数，解析并更新日期数组以及规则变量
 * @param {string} rule - 日期规则字符串，用于解析生成相应的日期数组
 */
function getDayArr(rule) {
    // 初始化日期数组的第四个元素，表示日（1到31）
    dateArr.value[3] = getOrderArr(1, 31);
    // 初始化日期规则变量
    dayRule.value = '';
    dayRuleSup.value = '';
    // 当规则中包含'-'时，表示循环范围
    if (rule.indexOf('-') >= 0) {
        // 更新日期数组为根据循环规则生成的数组，不跨越月份
        dateArr.value[3] = getCycleArr(rule, 31, false)
        // 设置补充规则为null
        dayRuleSup.value = 'null';
    } else if (rule.indexOf('/') >= 0) {
        // 当规则中包含'/'时，表示平均间隔
        // 更新日期数组为根据平均间隔规则生成的数组
        dateArr.value[3] = getAverageArr(rule, 31)
        // 设置补充规则为null
        dayRuleSup.value = 'null';
    } else if (rule.indexOf('W') >= 0) {
        // 当规则中包含'W'时，表示工作日规则
        // 设置日期规则变量为'workDay'
        dayRule.value = 'workDay';
        // 提取规则中的数字作为补充规则
        dayRuleSup.value = Number(rule.match(/[0-9]{1,2}/g)[0]);
        // 更新日期数组为只包含补充规则的数组
        dateArr.value[3] = [dayRuleSup.value];
    } else if (rule.indexOf('L') >= 0) {
        // 当规则中包含'L'时，表示月末规则
        // 设置日期规则变量为'lastDay'
        dayRule.value = 'lastDay';
        // 设置补充规则为null
        dayRuleSup.value = 'null';
        // 更新日期数组为只包含31的数组，表示月末
        dateArr.value[3] = [31];
    } else if (rule !== '*' && rule !== '?') {
        // 当规则不是通配符'*'和 '?'时，表示指定日期
        // 更新日期数组为根据指定规则生成的数组
        dateArr.value[3] = getAssignArr(rule)
        // 设置补充规则为null
        dayRuleSup.value = 'null';
    } else if (rule === '*') {
        // 当规则是通配符'*'时，表示任意日期
        // 设置补充规则为null
        dayRuleSup.value = 'null';
    }
}
/**
 * 获取"时"数组
 * 根据不同的规则生成不同的小时数组
 * @param {string} rule - 时间规则，用于生成不同的小时数组
 */
function getHourArr(rule) {
    // 初始化小时数组，包含一天中的所有小时
    dateArr.value[2] = getOrderArr(0, 23);

    // 如果规则中包含'-'，则根据规则生成连续小时数组
    if (rule.indexOf('-') >= 0) {
        dateArr.value[2] = getCycleArr(rule, 24, true)
    } else if (rule.indexOf('/') >= 0) {
        // 如果规则中包含'/'，则根据规则生成等间隔小时数组
        dateArr.value[2] = getAverageArr(rule, 23)
    } else if (rule !== '*') {
        // 如果规则不是'*'，则根据规则生成指定小时数组
        dateArr.value[2] = getAssignArr(rule)
    }
}
/**
 * 获取"分"数组
 * 根据不同的规则生成不同的分钟数组
 * @param {string} rule - 时间规则，用于生成不同的分钟数组
 */
function getMinArr(rule) {
    // 初始化分钟数组，范围为0到59
    dateArr.value[1] = getOrderArr(0, 59);

    // 如果规则中包含'-'，则根据规则生成连续范围的分钟数组
    if (rule.indexOf('-') >= 0) {
        dateArr.value[1] = getCycleArr(rule, 60, true)
    } else if (rule.indexOf('/') >= 0) {
        // 如果规则中包含'/'，则根据规则生成等间隔的分钟数组
        dateArr.value[1] = getAverageArr(rule, 59)
    } else if (rule !== '*') {
        // 如果规则不是'*'，则根据规则生成指定的分钟数组
        dateArr.value[1] = getAssignArr(rule)
    }
}
/**
 * 获取"秒"数组
 * 根据不同的规则生成秒的数组，支持周期、平均和指定值等多种规则
 * @param {string} rule - cron表达式中的秒规则部分
 */
function getSecondArr(rule) {
    // 初始化秒数组，包含所有可能的秒值
    dateArr.value[0] = getOrderArr(0, 59);

    // 根据规则中的'-'符号判断是否为周期规则
    if (rule.indexOf('-') >= 0) {
        // 是周期规则，则调用getCycleArr函数生成周期数组
        dateArr.value[0] = getCycleArr(rule, 60, true)
    } else if (rule.indexOf('/') >= 0) {
        // 根据规则中的'/'符号判断是否为平均规则
        // 是平均规则，则调用getAverageArr函数生成平均数组
        dateArr.value[0] = getAverageArr(rule, 59)
    } else if (rule !== '*') {
        // 不是周期规则也不是平均规则，并且规则不为'*'
        // 则认为是指定值规则，调用getAssignArr函数生成指定值数组
        dateArr.value[0] = getAssignArr(rule)
    }
}
/**
 * 根据传进来的min和max返回一个顺序的数组
 * @param {number} min - 数组的最小值
 * @param {number} max - 数组的最大值
 * @returns {number[]} 一个从min到max的顺序数组
 */
function getOrderArr(min, max) {
    // 初始化一个空数组用于存储最终的顺序数组
    let arr = [];
    // 遍历从min到max的每一个整数，并将其添加到数组中
    for (let i = min; i <= max; i++) {
        arr.push(i);
    }
    // 返回填充了顺序数字的数组
    return arr;
}
// 根据规则中指定的零散值返回一个数组
/**
 * 将给定的规则字符串转换为排序后的数字数组
 * 规则字符串应包含由逗号分隔的数值
 * @param {string} rule - 包含由逗号分隔的数值的规则字符串
 * @returns {number[]} - 排序后的数字数组
 */
function getAssignArr(rule) {
    // 初始化一个空数组用于存储最终的数字数组
    let arr = [];
    // 将规则字符串按逗号分割成数组
    let assiginArr = rule.split(',');
    // 遍历分割后的数组，将每个元素转换为数字并赋值给最终数组
    for (let i = 0; i < assiginArr.length; i++) {
        arr[i] = Number(assiginArr[i])
    }
    // 对数组进行排序
    arr.sort(compare)
    // 返回排序后的数组
    return arr;
}
/**
 * 根据一定算术规则计算返回一个数组
 * 该函数用于生成一个数组，数组中的元素根据起始值和步长逐步增加，直到达到或超过限定值
 * 
 * @param {string} rule - 规则字符串，格式为"起始值/步长"，用于定义数组的起始值和步长
 * @param {number} limit - 限定值，数组中的元素不会超过该值
 * @returns {number[]} 一个按规则生成的数字数组
 */
function getAverageArr(rule, limit) {
    // 初始化空数组，用于存储计算结果
    let arr = [];
    // 将规则字符串分割为起始值和步长，并转换为数字
    let agArr = rule.split('/');
    let min = Number(agArr[0]);
    let step = Number(agArr[1]);
    // 循环，从起始值开始，每次增加步长，直到达到或超过限定值
    while (min <= limit) {
        // 将当前值添加到数组中
        arr.push(min);
        // 增加当前值，以步长为单位
        min += step;
    }
    // 返回计算结果数组
    return arr;
}
// 根据规则返回一个具有周期性的数组
function getCycleArr(rule, limit, status) {
    // status--表示是否从0开始（则从1开始）
    // 初始化为空数组，用于存储最终生成的周期性数组
    let arr = [];
    // 将规则字符串分割为最小值和最大值，用于确定周期性数组的范围
    let cycleArr = rule.split('-');
    // 将最小值和最大值转换为数字类型
    let min = Number(cycleArr[0]);
    let max = Number(cycleArr[1]);
    // 如果最小值大于最大值，表示跨越了周期的边界，需要调整最大值
    if (min > max) {
        max += limit;
    }
    // 遍历从最小值到最大值的范围，生成周期性数组
    for (let i = min; i <= max; i++) {
        // 初始化增加值为0，用于调整数组元素的值
        let add = 0;
        // 如果status为false且当前值能被limit整除，表示需要跨越周期的边界
        if (status === false && i % limit === 0) {
            // 增加limit，以确保数组元素的值不会是0
            add = limit;
        }
        // 将计算后的值添加到数组中，确保数组元素的值在1到limit之间
        arr.push(Math.round(i % limit + add))
    }
    // 对数组进行排序，以确保数组元素的顺序
    arr.sort(compare)
    // 返回生成的周期性数组
    return arr;
}
/**
 * 比较两个数字的大小，用于数组排序时确定元素的顺序
 * @param {number} value1 - 第一个数字
 * @param {number} value2 - 第二个数字
 * @returns {number} - 返回-1表示value1应该排在value2之前，返回1表示value1应该排在value2之后
 */
function compare(value1, value2) {
    // 如果value2大于value1，则value1应该排在value2之前
    if (value2 - value1 > 0) {
        return -1;
    } else {
        // 如果value1大于value2，则value1应该排在value2之后
        return 1;
    }
}
// 格式化日期格式如：2017-9-19 18:04:33
function formatDate(value, type) {
    // 计算日期相关值
    let time = typeof value == 'number' ? new Date(value) : value;
    let Y = time.getFullYear();
    let M = time.getMonth() + 1;
    let D = time.getDate();
    let h = time.getHours();
    let m = time.getMinutes();
    let s = time.getSeconds();
    let week = time.getDay();
    // 如果传递了type的话
    if (type === undefined) {
        return Y + '-' + (M < 10 ? '0' + M : M) + '-' + (D < 10 ? '0' + D : D) + ' ' + (h < 10 ? '0' + h : h) + ':' + (m < 10 ? '0' + m : m) + ':' + (s < 10 ? '0' + s : s);
    } else if (type === 'week') {
        // 在quartz中 1为星期日
        return week + 1;
    }
}
/**
 * 检查日期是否存在
 * 此函数用于验证给定的日期字符串是否代表一个有效的日期
 * 它通过将日期字符串转换为Date对象，然后格式化回字符串形式，与原始值进行比较来实现
 * 
 * @param {string} value 日期字符串，表示要检查的日期
 * @returns {boolean} 如果日期字符串代表一个有效的日期，则返回true；否则返回false
 */
function checkDate(value) {
    // 创建一个新的Date对象，用于验证传入的日期字符串
    let time = new Date(value);
    // 将Date对象格式化为字符串，以便与原始值进行比较
    let format = formatDate(time)
    // 比较原始值和格式化后的值，以确定日期是否有效
    return value === format;
}
onMounted(() => {
    expressionChange()
})
</script>