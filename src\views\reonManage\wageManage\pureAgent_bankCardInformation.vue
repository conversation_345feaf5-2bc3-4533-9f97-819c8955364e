<!-- 纯代发人员银行卡信息维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-row class="mb8">
                <el-form-item label="唯一号:" prop="uniqueId">
                    <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入" clearable
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="银行卡号：" prop="bankCardNo">
                    <el-input class="width220" v-model="obj.queryParams.bankCardNo" placeholder="请输入" clearable
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="证件号码:" prop="idNumber">
                    <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="客户:" prop="customerName">
                    <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="View" @click="handleExport">导出数据</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="银行卡号" align="center" prop="bankCardNo" />
            <el-table-column label="开户人姓名" align="center" prop="accountName" />
            <el-table-column label="开户分支行" align="center" prop="bankBranch" />
            <el-table-column label="开户银行" align="center" prop="bankName" />
            <el-table-column label="其他银行" align="center" prop="otherBank" />
            <el-table-column label="开户行省份" align="center" prop="bankProvince" />
            <el-table-column label="开户行城市" align="center" prop="bankCity" />
            <el-table-column label="是否有效" align="center" prop="isValid">
                <template #default="scope">
                    <el-tag :type="scope.row.isValid === '1' ? 'success' : 'info'">
                        {{ scope.row.isValid === '1' ? '有效' : '无效' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="业务类型" align="center" prop="businessType" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="是否竞业员工" align="center" prop="isCompetitiveEmployee">
                <template #default="scope">
                    {{ scope.row.isCompetitiveEmployee === '1' ? '是' : '否' }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 修改 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="50%" @close="handleClose">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" :rules="rules" inline label-width="auto">
                <el-form-item label="唯一号" prop="uniqueId">
                    <el-input class="width220" v-model="obj.dialogForm.uniqueId" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="雇员姓名" prop="employeeName">
                    <el-input class="width220" v-model="obj.dialogForm.employeeName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="证件号" prop="idNumber" v-if="obj.edit">
                    <el-input class="width220" v-model="obj.dialogForm.idNumber" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开户人姓名" prop="accountName" required>
                    <el-input class="width220" v-model="obj.dialogForm.accountName" placeholder="请输入" />
                </el-form-item>

                <el-form-item label="银行卡号" prop="bankCardNo" required>
                    <el-input class="width220" v-model="obj.dialogForm.bankCardNo" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="业务类型" prop="businessType" required>
                    <el-select class="width220" v-model="obj.dialogForm.businessType" placeholder="请选择" clearable>
                        <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="开户银行" prop="bankName" required>
                    <el-select class="width220" v-model="obj.dialogForm.bankName" placeholder="请选择" clearable>
                        <el-option v-for="item in bankOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="其他银行" prop="otherBank">
                    <el-input class="width220" v-model="obj.dialogForm.otherBank" placeholder="请输入"
                        :disabled="obj.dialogForm.bankName !== 'other'" />
                </el-form-item>
                <el-form-item label="开户分支行" prop="bankBranch">
                    <el-input class="width220" v-model="obj.dialogForm.bankBranch" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开户行省份" prop="bankProvince">
                    <el-select class="width220" v-model="obj.dialogForm.bankProvince" placeholder="请选择" clearable
                        @change="handleProvinceChange">
                        <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="开户行城市" prop="bankCity" required>
                    <el-select class="width220" v-model="obj.dialogForm.bankCity" placeholder="请选择" clearable>
                        <el-option v-for="item in cityOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否有效" prop="isValid">
                    <el-select class="width220" v-model="obj.dialogForm.isValid" placeholder="请选择" clearable>
                        <el-option v-for="item in validOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                    <el-input class="width220" v-model="obj.dialogForm.email" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="手机" prop="phoneNumber">
                    <el-input class="width220" v-model="obj.dialogForm.phoneNumber" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="是否竞业员工" prop="isCompetitiveEmployee">
                    <el-select class="width220" v-model="obj.dialogForm.isCompetitiveEmployee" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in competitiveEmployeeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName" v-if="obj.edit">
                    <el-select class="width220" v-model="obj.dialogForm.customerName" placeholder="请选择" clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" class="width420" :rows="3" v-model="obj.dialogForm.remark"
                        placeholder="请输入" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="submit">提交</el-button>
                <el-button @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="PureAgent_bankCardInformation">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客户选项
const customerOptions = [
    { value: 'C001', label: '北京科技有限公司' },
    { value: 'C002', label: '上海贸易有限公司' },
    { value: 'C003', label: '广州电子有限公司' },
    { value: 'C004', label: '深圳科技有限公司' },
    { value: 'C005', label: '成都信息有限公司' }
];

// 银行选项
const bankOptions = [
    { value: 'ICBC', label: '中国工商银行' },
    { value: 'ABC', label: '中国农业银行' },
    { value: 'BOC', label: '中国银行' },
    { value: 'CCB', label: '中国建设银行' },
    { value: 'CMBC', label: '中国民生银行' },
    { value: 'CIB', label: '兴业银行' },
    { value: 'SPDB', label: '上海浦东发展银行' },
    { value: 'other', label: '其他银行' }
];

// 业务类型选项
const businessTypeOptions = [
    { value: '1', label: '工资发放' },
    { value: '2', label: '社保缴纳' },
    { value: '3', label: '公积金缴纳' },
    { value: '4', label: '个税缴纳' },
    { value: '5', label: '其他业务' }
];

// 有效状态选项
const validOptions = [
    { value: '1', label: '有效' },
    { value: '0', label: '无效' }
];

// 省份选项
const provinceOptions = [
    { value: '11', label: '北京市' },
    { value: '31', label: '上海市' },
    { value: '44', label: '广东省' },
    { value: '51', label: '四川省' },
    { value: '33', label: '浙江省' }
];

// 城市选项
const cityOptions = ref([]);

// 处理省份变化
function handleProvinceChange(provinceCode) {
    if (provinceCode === '11') {
        cityOptions.value = [{ value: '1101', label: '北京市' }];
    } else if (provinceCode === '31') {
        cityOptions.value = [{ value: '3101', label: '上海市' }];
    } else if (provinceCode === '44') {
        cityOptions.value = [
            { value: '4401', label: '广州市' },
            { value: '4403', label: '深圳市' },
            { value: '4406', label: '佛山市' }
        ];
    } else if (provinceCode === '51') {
        cityOptions.value = [
            { value: '5101', label: '成都市' },
            { value: '5107', label: '绵阳市' },
            { value: '5115', label: '宜宾市' }
        ];
    } else if (provinceCode === '33') {
        cityOptions.value = [
            { value: '3301', label: '杭州市' },
            { value: '3302', label: '宁波市' },
            { value: '3303', label: '温州市' }
        ];
    } else {
        cityOptions.value = [];
    }
    obj.dialogForm.bankCity = null;
}

// 表单验证规则
const rules = {
    accountName: [{ required: true, message: '请输入开户人姓名', trigger: 'blur' }],
    bankCardNo: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
    businessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
    bankName: [{ required: true, message: '请选择开户银行', trigger: 'change' }],
    bankCity: [{ required: true, message: '请选择开户行城市', trigger: 'change' }],
    otherBank: [{ required: true, message: '请输入其他银行名称', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null, // 唯一号
        bankCardNo: null, // 银行卡号
        idNumber: null, // 证件号码
        customerName: null, // 客户名称
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    dialogShow2: false, // 历史记录弹出框
    ids: [], // 选中的id
    title: "", // 标题
    edit: false, // 是否编辑
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        obj.tableData = [
            {
                id: 1,
                uniqueId: 'UID001',
                employeeName: '张三',
                idNumber: '110101199001011234',
                bankCardNo: '6222021234567890123',
                accountName: '张三',
                bankBranch: '北京市海淀支行',
                bankName: 'ICBC',
                otherBank: '',
                bankProvince: '11',
                bankCity: '1101',
                isValid: '1',
                businessType: '1',
                email: '<EMAIL>',
                customerName: 'C001',
                remark: '工资发放账户'
            },
            {
                id: 2,
                uniqueId: 'UID002',
                employeeName: '李四',
                idNumber: '310101199002022345',
                bankCardNo: '6222021234567890124',
                accountName: '李四',
                bankBranch: '上海市浦东支行',
                bankName: 'ABC',
                otherBank: '',
                bankProvince: '31',
                bankCity: '3101',
                isValid: '1',
                businessType: '1',
                email: '<EMAIL>',
                customerName: 'C002',
                remark: '工资发放账户'
            },
            {
                id: 3,
                uniqueId: 'UID003',
                employeeName: '王五',
                idNumber: '******************',
                bankCardNo: '6222021234567890125',
                accountName: '王五',
                bankBranch: '广州市天河支行',
                bankName: 'BOC',
                otherBank: '',
                bankProvince: '44',
                bankCity: '4401',
                isValid: '0',
                businessType: '2',
                email: '<EMAIL>',
                customerName: 'C003',
                remark: '社保缴纳账户'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    obj.dialogForm = {
        uniqueId: '',
        employeeName: '',
        idNumber: '',
        bankCardNo: '',
        accountName: '',
        bankBranch: '',
        bankName: '',
        otherBank: '',
        bankProvince: '',
        bankCity: '',
        isValid: '1',
        businessType: '',
        email: '',
        customerName: '',
        remark: ''
    };
    obj.dialogShow = true;
    obj.title = '新增';
}

/** 修改按钮操作 */
function handleEdit() {
    if (obj.single) {
        proxy.$modal.msgError("请选择一条记录进行修改");
        return;
    }
    obj.edit = true;
    // 获取选中记录
    const id = obj.ids[0];
    const record = obj.tableData.find(item => item.id === id);

    if (!record) {
        proxy.$modal.msgError("找不到选中的记录");
        return;
    }

    // 复制记录到表单
    obj.dialogForm = JSON.parse(JSON.stringify(record));

    // 如果有省份，加载对应的城市
    if (obj.dialogForm.bankProvince) {
        handleProvinceChange(obj.dialogForm.bankProvince);
    }

    obj.dialogShow = true;
    obj.title = '修改';
}

/** 导出数据 */
function handleExport() {
    if (obj.single) {
        proxy.$modal.msgError("请选择一条记录导出");
        return;
    }


}

/** 关闭弹窗 */
function handleClose() {
    obj.edit = false;
    obj.dialogForm = {};
}
/** 提交表单 */
function submit() {
    proxy.$refs.formRef.validate(valid => {
        if (valid) {
            // 如果选择了其他银行，验证是否填写了其他银行名称
            if (obj.dialogForm.bankName === 'other' && !obj.dialogForm.otherBank) {
                proxy.$modal.msgError("请填写其他银行名称");
                return;
            }

            // 模拟保存操作
            proxy.$modal.loading("正在保存数据，请稍候...");
            setTimeout(() => {
                proxy.$modal.closeLoading();
                proxy.$modal.msgSuccess("保存成功");
                obj.dialogShow = false;
                getList();
            }, 500);
        }
    });
}


getList();
</script>
<style lang="scss" scoped></style>