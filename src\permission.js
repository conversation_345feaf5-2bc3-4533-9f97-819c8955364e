import router from "./router";
import { ElMessage } from "element-plus";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getToken } from "@/utils/auth";
import { isHttp } from "@/utils/validate";
import { isRelogin } from "@/utils/request";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";

NProgress.configure({ showSpinner: false });

const whiteList = ["/login", "/register"];

/**
 * 全局前置守卫，用于在路由跳转前进行权限验证和处理
 * @param {Object} to - 即将要进入的目标路由对象
 * @param {Object} from - 当前导航正要离开的路由对象
 * @param {Function} next - 一定要调用该方法来 resolve 这个钩子。执行效果依赖 next 方法的调用参数。
 */
router.beforeEach((to, from, next) => {
  // 开始加载进度条
  NProgress.start();
  // 判断是否存在token
  if (getToken()) {
    // 如果目标路由有meta.title，则设置页面标题
    to.meta.title && useSettingsStore().setTitle(to.meta.title);
    /* 有token*/
    // 如果目标路由是登录页，则直接跳转到首页
    if (to.path === "/login") {
      next({ path: "/" });
      // 结束加载进度条
      NProgress.done();
    } else if (whiteList.indexOf(to.path) !== -1) {
      // 如果目标路由在白名单中，则直接放行
      next();
    } else {
      // 如果用户角色列表为空
      if (useUserStore().roles.length === 0) {
        // 显示重新登录提示
        isRelogin.show = true;
        // 判断当前用户是否已拉取完user_info信息
        useUserStore()
          .getInfo()
          .then(() => {
            // 隐藏重新登录提示
            isRelogin.show = false;
            // 生成可访问的路由表
            usePermissionStore()
              .generateRoutes()
              .then((accessRoutes) => {
                // 根据roles权限生成可访问的路由表
                accessRoutes.forEach((route) => {
                  // 如果路由路径不是http链接，则动态添加路由
                  if (!isHttp(route.path)) {
                    router.addRoute(route); // 动态添加可访问路由表
                  }
                });
                // hack方法 确保addRoutes已完成
                next({ ...to, replace: true });
              });
          })
          .catch((err) => {
            // 退出登录
            useUserStore()
              .logOut()
              .then(() => {
                // 显示错误消息
                ElMessage.error(err);
                // 跳转到登录页
                next({ path: "/" });
              });
          });
      } else {
        // 如果用户角色列表不为空，则直接放行
        next();
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      // 否则全部重定向到登录页，并携带当前页面的路径作为参数，以便登录成功后返回当前页面
      next(`/login?redirect=${to.fullPath}`);
      // 结束加载进度条
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
