<!-- 请求日志管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="业务类型编号:" prop="businessTypeCode">
                <el-input class="width220" v-model="obj.queryParams.businessTypeCode" placeholder="请输入业务类型编号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="业务参考号:" prop="referenceNo">
                <el-input class="width220" v-model="obj.queryParams.referenceNo" placeholder="请输入业务参考号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="响应处理类型:" prop="responseProcessType">
                <el-select class="width220" v-model="obj.queryParams.responseProcessType" placeholder="请选择响应处理类型"
                    clearable>
                    <el-option v-for="item in responseProcessTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否过滤查询类型:" prop="filterQueryType">
                <el-select class="width220" v-model="obj.queryParams.filterQueryType" placeholder="请选择是否过滤查询类型"
                    clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="业务处理状态:" prop="businessProcessStatus">
                <el-select class="width220" v-model="obj.queryParams.businessProcessStatus" placeholder="请选择业务处理状态"
                    clearable>
                    <el-option v-for="item in businessProcessStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="补单用的ref_no:" prop="supplementRefNo">
                <el-input class="width220" v-model="obj.queryParams.supplementRefNo" placeholder="请输入补单用的ref_no"
                    clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="请求处理状态:" prop="requestProcessStatus">
                <el-select class="width220" v-model="obj.queryParams.requestProcessStatus" placeholder="请选择请求处理状态"
                    clearable>
                    <el-option v-for="item in requestProcessStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="类型:" prop="type">
                <el-select class="width220" v-model="obj.queryParams.type" placeholder="请选择类型" clearable>
                    <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="view" @click="handleDetailRequest">查看请求报文</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="view" @click="handleDetailResponse">查看响应报文</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="edit" @click="handleUpdateRequest">修改请求报文</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table style="max-height: calc(100vh - 445px);" v-loading="obj.loading" show-overflow-tooltip border
            :data="obj.tableData" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="业务类型编号" align="center" prop="businessTypeCode" width="120" />
            <el-table-column label="业务类型名称" align="center" prop="businessTypeName" width="120" />
            <el-table-column label="业务参考号" align="center" prop="referenceNo" width="120" />
            <el-table-column label="关联业务号" align="center" prop="relatedBusinessNo" width="120" />
            <el-table-column label="请求报文" align="center" prop="requestMessage" width="120">
                <template #default="scope">
                    <el-button type="primary" link @click="handleViewRequestMessage(scope.row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column label="响应报文" align="center" prop="responseMessage" width="120">
                <template #default="scope">
                    <el-button type="primary" link @click="handleViewResponseMessage(scope.row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column label="响应处理类型" align="center" prop="responseProcessType" width="120" />
            <el-table-column label="业务处理状态" align="center" prop="businessProcessStatus" width="120" />
            <el-table-column label="请求处理状态" align="center" prop="requestProcessStatus" width="120" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
            <el-table-column label="修改时间" align="center" prop="updateTime" width="180" />
            <el-table-column label="各业务的扩展信息" align="center" prop="businessExtendInfo" width="160" />
            <el-table-column label="补单用的ref_no" align="center" prop="supplementRefNo" width="140" />
            <el-table-column label="操作描述" align="center" prop="operationDescription" />
            <el-table-column label="环境" align="center" prop="environment" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <div class="mart30" style="font-size: 14px;">
            企银支付单笔经办_BB1PAYOP、企银支付业务查询_BB1PAYQR、代发经办_BB6BTHHL、代发批次与明细查询_BB6BPDQY、可经办业务模式查询_DCLISMOD、
            代发明细对账单查询_DCAGPPDF、账户交易信息查询_trsQryByBreakPoint、账户详细信息查询_NTQACINF、电子回单异步查询_ASY</div>

        <!-- 查看请求报文 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="65%" append-to-body draggable>
            <border-box title="通知">
                <el-table :data="obj.tableData" border>
                    <el-table-column label="通知键值" prop="key" align="center" />
                    <el-table-column label="通知编号" prop="value" align="center" />
                    <el-table-column label="通知类型" prop="value" align="center" />
                    <el-table-column label="签名内容" prop="value" align="center" />
                    <el-table-column label="签名时间" prop="value" align="center" />
                    <el-table-column label="用户编号" prop="value" align="center" />
                </el-table>
            </border-box>
            <border-box title="通知内容">
                <el-table :data="obj.tableData" border>
                    <el-table-column label="参考号" prop="key" align="center" width="120" />
                    <el-table-column label="用户姓名" prop="user_name" align="center" width="120" />
                    <el-table-column label="交易金额" prop="transaction_amount" align="center" width="120" />
                    <el-table-column label="币种" prop="currency" align="center" width="120" />
                    <el-table-column label="收方帐号" prop="receiver_account" align="center" width="120" />
                    <el-table-column label="收方行地址" prop="receiver_bank_address" align="center" width="120" />
                    <el-table-column label="收方分行号" prop="receiver_branch_code" align="center" width="120" />
                    <el-table-column label="收方行名称" prop="receiver_bank_name" align="center" width="120" />
                    <el-table-column label="收方名称" prop="receiver_name" align="center" width="120" />
                    <el-table-column label="付方帐号" prop="payer_account" align="center" width="120" />
                    <el-table-column label="转出分行号" prop="payer_branch_code" align="center" width="120" />
                    <el-table-column label="付方帐户名" prop="payer_account_name" align="center" width="120" />
                    <el-table-column label="付方记账子单元编号" prop="payer_sub_unit_code" align="center" width="120" />
                    <el-table-column label="期望日" prop="expected_date" align="center" width="120" />
                    <el-table-column label="期望时间" prop="expected_time" align="center" width="120" />
                    <el-table-column label="用户名" prop="user_id" align="center" width="120" />
                    <el-table-column label="通知方式一" prop="notification_method_1" align="center" width="120" />
                    <el-table-column label="通知方式二" prop="notification_method_2" align="center" width="120" />
                    <el-table-column label="用途" prop="purpose" align="center" width="120" />
                    <el-table-column label="操作别名" prop="operation_alias" align="center" width="120" />
                    <el-table-column label="经办日期" prop="processing_date" align="center" width="120" />
                    <el-table-column label="待处理操作序列" prop="pending_operation_sequence" align="center" width="120" />
                    <el-table-column label="收方大额行号" prop="receiver_large_bank_code" align="center" width="120" />
                    <el-table-column label="流程实例号" prop="process_instance_id" align="center" width="120" />
                    <el-table-column label="请求状态" prop="request_status" align="center" width="120" />
                    <el-table-column label="业务处理结果" prop="business_process_result" align="center" width="120" />
                    <el-table-column label="失败原因" prop="failure_reason" align="center" width="120" />
                    <el-table-column label="结算通路" prop="settlement_route" align="center" width="120" />
                    <el-table-column label="业务种类" prop="business_type" align="center" width="120" />
                    <el-table-column label="账务流水" prop="financial_transaction_id" align="center" width="120" />
                    <el-table-column label="账务套号" prop="financial_suite_id" align="center" width="120" />
                    <el-table-column label="是否有附件信息" prop="has_attachment_info" align="center" width="120" />
                    <el-table-column label="系统内外标志" prop="system_internal_external_flag" align="center" width="120" />
                    <el-table-column label="业务编码" prop="business_code" align="center" width="120" />
                    <el-table-column label="业务模式" prop="business_mode" align="center" width="120" />
                    <el-table-column label="业务摘要" prop="business_summary" align="center" width="120" />
                </el-table>
            </border-box>
        </el-dialog>

        <!-- 查看响应报文 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="65%" append-to-body draggable>
            <border-box title="请求头">
                <el-table :data="obj.tableData" border>
                    <el-table-column label="接口名称" prop="key" align="center" width="120" />
                    <el-table-column label="请求Id" prop="value" align="center" width="120" />
                    <el-table-column label="用户Id" prop="value" align="center" width="120" />
                    <el-table-column label="响应编号" prop="value" align="center" width="120" />
                    <el-table-column label="响应内容" prop="value" align="center" width="120" />
                    <el-table-column label="响应ID" prop="value" align="center" width="120" />
                </el-table>
            </border-box>
            <border-box title="签名">
                <el-table :data="obj.tableData" border>
                    <el-table-column label="签名内容" prop="key" align="center" width="120" />
                    <el-table-column label="签名时间" prop="value" align="center" width="120" />
                </el-table>
            </border-box>
            <border-box title="响应体">

            </border-box>
        </el-dialog>
    </div>
</template>

<script setup name="RequestLogManagement">


const { proxy } = getCurrentInstance();

const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 响应处理类型选项
const responseProcessTypeOptions = [
    { value: '1', label: '直连接口' },
    { value: '2', label: '异步通知' },
    { value: '3', label: '查询接口' }
];


// 业务处理状态选项
const businessProcessStatusOptions = [
    { value: '1', label: '初始' },
    { value: '2', label: '发送请求时候异常' },
    { value: '3', label: '发送失败' },
    { value: '4', label: '未响应' },
    { value: '5', label: '已响应' }
];

// 请求处理状态选项
const requestProcessStatusOptions = [
    { value: '1', label: '已发送' },
    { value: '2', label: '发送失败' },
    { value: '3', label: '待发送' },
    { value: '4', label: '已完成' }
];

// 类型选项
const typeOptions = [
    { value: '1', label: 'dev' },
    { value: '2', label: 'mock' },
    { value: '3', label: 'prod' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessTypeCode: null,
        referenceNo: null,
        responseProcessType: null,
        filterQueryType: null,
        businessProcessStatus: null,
        supplementRefNo: null,
        requestProcessStatus: null,
        type: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {}, //表单
    dialogShow: false, //请求报文弹出框
    dialogShow2: false, //响应报文弹出框
    ids: [],//选中的id
    title: "",//标题
    currentRow: {}, //当前选中行
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                businessTypeCode: 'BTC001',
                businessTypeName: '转账',
                referenceNo: 'REF20230501001',
                relatedBusinessNo: 'RBN001',
                requestMessage: '{"请求头":{"API名称":"transfer","请求ID":"req001","用户ID":"user001"},"请求体":{"amount":"5000","fromAccount":"****************","toAccount":"****************"}}',
                responseMessage: '{"响应头":{"API名称":"transfer","响应ID":"resp001","用户ID":"user001"},"响应体":{"code":"0000","message":"成功","transactionId":"T20230501001"}}',
                responseProcessType: '同步',
                businessProcessStatus: '处理成功',
                requestProcessStatus: '已完成',
                createTime: '2023-05-01 10:30:00',
                updateTime: '2023-05-01 10:31:00',
                businessExtendInfo: '{"交易类型":"单笔转账"}',
                supplementRefNo: '',
                operationDescription: '转账操作',
                environment: '生产'
            },
            {
                id: 2,
                businessTypeCode: 'BTC002',
                businessTypeName: '查询余额',
                referenceNo: 'REF20230502001',
                relatedBusinessNo: 'RBN002',
                requestMessage: '{"请求头":{"API名称":"queryBalance","请求ID":"req002","用户ID":"user001"},"请求体":{"account":"****************"}}',
                responseMessage: '{"响应头":{"API名称":"queryBalance","响应ID":"resp002","用户ID":"user001"},"响应体":{"code":"0000","message":"成功","balance":"10000.00"}}',
                responseProcessType: '同步',
                businessProcessStatus: '处理成功',
                requestProcessStatus: '已完成',
                createTime: '2023-05-02 14:20:00',
                updateTime: '2023-05-02 14:20:30',
                businessExtendInfo: '{"查询类型":"活期账户"}',
                supplementRefNo: '',
                operationDescription: '查询余额操作',
                environment: '生产'
            },
            {
                id: 3,
                businessTypeCode: 'BTC003',
                businessTypeName: '工资发放',
                referenceNo: 'REF20230503001',
                relatedBusinessNo: 'RBN003',
                requestMessage: '{"请求头":{"API名称":"batchTransfer","请求ID":"req003","用户ID":"user002"},"请求体":{"totalAmount":"50000","fromAccount":"****************","batchNo":"B20230503001"}}',
                responseMessage: '{"响应头":{"API名称":"batchTransfer","响应ID":"resp003","用户ID":"user002"},"响应体":{"code":"0000","message":"成功","batchId":"B20230503001"}}',
                responseProcessType: '异步',
                businessProcessStatus: '处理中',
                requestProcessStatus: '已发送',
                createTime: '2023-05-03 09:15:00',
                updateTime: '2023-05-03 09:15:30',
                businessExtendInfo: '{"批次类型":"工资发放","总笔数":"100"}',
                supplementRefNo: '',
                operationDescription: '工资发放操作',
                environment: '生产'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}





/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 查看请求报文
function handleViewRequestMessage(row) {
    obj.currentRow = row;
    obj.title = "查看通知内容";
    obj.dialogShow = true;
}

// 查看响应报文
function handleViewResponseMessage(row) {
    obj.currentRow = row;
    obj.title = "查看响应报文";
    obj.dialogShow2 = true;
}

// 查看请求报文按钮
function handleDetailRequest() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError("请选择一条记录");
        return;
    }
    const row = obj.tableData.find(item => item.id === obj.ids[0]);
    handleViewRequestMessage(row);
}

// 查看响应报文按钮
function handleDetailResponse() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError("请选择一条记录");
        return;
    }
    const row = obj.tableData.find(item => item.id === obj.ids[0]);
    handleViewResponseMessage(row);
}

// 修改请求报文
function handleUpdateRequest() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError("请选择一条记录");
        return;
    }
    proxy.$modal.msgSuccess("修改请求报文功能开发中");
}



getList();
</script>
<style lang="scss" scoped></style>