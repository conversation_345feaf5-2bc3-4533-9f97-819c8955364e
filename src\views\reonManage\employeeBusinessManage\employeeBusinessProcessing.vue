<!-- 员工业务办理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="订单编号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="员工姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入员工姓名" clearable />
            </el-form-item>
            <el-form-item label="客户编号:" prop="customerNo">
                <el-input class="width220" v-model="obj.queryParams.customerNo" placeholder="请输入客户编号" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="接单方:" prop="orderReceiver">
                <el-select class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请选择" clearable>
                    <el-option v-for="item in orderReceiverOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方客服:" prop="receiverService">
                <el-input class="width220" v-model="obj.queryParams.receiverService" placeholder="请输入接单方客服" clearable />
            </el-form-item>
            <el-form-item label="业务大类:" prop="businessCategory">
                <el-select class="width220" v-model="obj.queryParams.businessCategory" placeholder="请选择" clearable>
                    <el-option v-for="item in businessCategoryOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="业务小类:" prop="businessSubcategory">
                <el-select class="width220" v-model="obj.queryParams.businessSubcategory" placeholder="请选择" clearable>
                    <el-option v-for="item in businessSubcategoryOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="具体业务:" prop="specificBusiness">
                <el-select class="width220" v-model="obj.queryParams.specificBusiness" placeholder="请选择" clearable>
                    <el-option v-for="item in specificBusinessOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="业务进度:" prop="businessProgress">
                <el-select class="width220" v-model="obj.queryParams.businessProgress" placeholder="请选择" clearable>
                    <el-option v-for="item in businessProgressOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleModifyAmount">修改票据/报销金额</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Edit" @click="handleReceiveReject">接收/驳回</el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button type="success" plain icon="Upload" @click="handleSubmit">提交</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleProcessReturn">办理/退回</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain @click="handleProcessResult">办理成功/办理失败</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain @click="handleCancel">取消办理</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon='View' @click="handleDetail">查看详情</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <Employee :tableData="obj.tableData" :loading="obj.loading" @selection-change="handleSelectionChange"
            @rowDblClick="handleDetail" />
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增/编辑 -->
        <EmployeeBusiness v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm"
            :isEdit="obj.isEdit" />
    </div>
</template>


<script setup name="EmployeeBusinessProcessing">
import { listScale } from "@/api/reonApi/scale";
import Employee from "@/views/reonManage/components/table/employee.vue";
import EmployeeBusiness from "@/views/reonManage/components/dialog/employeeBusiness.vue";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 接单方选项
const orderReceiverOptions = [
    { value: '1', label: '分公司A' },
    { value: '2', label: '分公司B' },
    { value: '3', label: '分公司C' }
];

// 城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' }
];

// 业务大类选项
const businessCategoryOptions = [
    { value: '1', label: '社保类' },
    { value: '2', label: '公积金类' },
    { value: '3', label: '人事类' }
];

// 业务小类选项
const businessSubcategoryOptions = [
    { value: '1', label: '养老保险' },
    { value: '2', label: '医疗保险' },
    { value: '3', label: '失业保险' },
    { value: '4', label: '公积金' },
    { value: '5', label: '入职办理' },
    { value: '6', label: '离职办理' }
];

// 具体业务选项
const specificBusinessOptions = [
    { value: '1', label: '养老保险变更' },
    { value: '2', label: '医疗保险变更' },
    { value: '3', label: '失业保险变更' },
    { value: '4', label: '公积金变更' },
    { value: '5', label: '入职办理' },
    { value: '6', label: '离职办理' }
];

// 业务进度选项
const businessProgressOptions = [
    { value: '1', label: '待提交' },
    { value: '2', label: '待审核' },
    { value: '3', label: '已审核' },
    { value: '4', label: '已办理' },
    { value: '5', label: '办理失败' },
    { value: '6', label: '已取消' }
];


// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        employeeName: '张三',
        orderNo: 'ORD20230001',
        idType: '1',
        idNumber: '110101199001011234',
        employmentStatus: '1',
        customerNo: 'C001',
        customerName: '客户A',
        contractType: '1',
        smallContractNo: 'SC001',
        smallContractName: '小合同1',
        orderReceiver: '1',
        city: '1',
        dispatcherService: '王五',
        receiverService: '李四',
        businessCategory: '1',
        businessSubcategory: '1',
        specificBusiness: '1',
        businessDate: '2023-01-15',
        businessProgress: '4',
        handleResult: '1',
        reimbursementAmount: 1000,
        invoiceAmount: 1200,
        needPayment: '1'
    },
    {
        id: 2,
        employeeName: '李四',
        orderNo: 'ORD20230002',
        idType: '1',
        idNumber: '110101199001021234',
        employmentStatus: '1',
        customerNo: 'C002',
        customerName: '客户B',
        contractType: '2',
        smallContractNo: 'SC002',
        smallContractName: '小合同2',
        orderReceiver: '2',
        city: '2',
        dispatcherService: '赵六',
        receiverService: '王五',
        businessCategory: '2',
        businessSubcategory: '4',
        specificBusiness: '4',
        businessDate: '2023-02-15',
        businessProgress: '3',
        handleResult: '',
        reimbursementAmount: 2000,
        invoiceAmount: 2200,
        needPayment: '1'
    },
    {
        id: 3,
        employeeName: '王五',
        orderNo: 'ORD20230003',
        idType: '2',
        idNumber: '110101199001031234',
        employmentStatus: '2',
        customerNo: 'C003',
        customerName: '客户C',
        contractType: '1',
        smallContractNo: 'SC003',
        smallContractName: '小合同3',
        orderReceiver: '3',
        city: '3',
        dispatcherService: '张三',
        receiverService: '赵六',
        businessCategory: '3',
        businessSubcategory: '5',
        specificBusiness: '5',
        businessDate: '2023-03-15',
        businessProgress: '5',
        handleResult: '2',
        reimbursementAmount: 0,
        invoiceAmount: 0,
        needPayment: '0'
    }
];

// 模拟具体业务数据
const mockSpecificBusinessData = [
    {
        id: 1,
        specificBusiness: '养老保险变更',
        businessTimeLimit: '30天',
        handleTimeLimit: '15天',
        needPayment: '1',
        bankAccount: '6222021234567890123',
        accountName: '张三',
        bankName: '中国建设银行',
        paymentObject: '社保中心',
        materials: '身份证复印件，申请表',
        deactivatedStatus: '0'
    },
    {
        id: 2,
        specificBusiness: '医疗保险变更',
        businessTimeLimit: '30天',
        handleTimeLimit: '15天',
        needPayment: '1',
        bankAccount: '6222021234567890123',
        accountName: '张三',
        bankName: '中国建设银行',
        paymentObject: '社保中心',
        materials: '身份证复印件，申请表',
        deactivatedStatus: '0'
    },
    {
        id: 3,
        specificBusiness: '失业保险变更',
        businessTimeLimit: '30天',
        handleTimeLimit: '15天',
        needPayment: '1',
        bankAccount: '6222021234567890123',
        accountName: '张三',
        bankName: '中国建设银行',
        paymentObject: '社保中心',
        materials: '身份证复印件，申请表',
        deactivatedStatus: '1'
    }
];

// 模拟办理资料数据
const mockMaterialsData = [
    { id: 1, name: '身份证复印件' },
    { id: 2, name: '申请表' },
    { id: 3, name: '单位证明' },
    { id: 4, name: '其他证明材料' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null, // 订单编号
        employeeName: null, // 员工姓名
        customerNo: null, // 客户编号
        customerName: null, // 客户名称
        idNumber: null, // 证件号码
        orderReceiver: null, // 接单方
        receiverService: null, // 接单方客服
        businessCategory: null, // 业务大类
        businessSubcategory: null, // 业务小类
        specificBusiness: null, // 具体业务
        businessProgress: null, // 业务进度
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    specificBusinessData: mockSpecificBusinessData, // 具体业务数据
    materialsData: mockMaterialsData, // 办理资料数据
    dialogForm: {

    }, // 表单
    dialogShow: false, // 新增/编辑弹窗
    ids: [], // 选中的id
    title: "", // 标题
    isEdit: false, // 是否编辑
});

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 提交
function handleSubmit() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要提交的记录');
        return;
    }

    proxy.$modal.confirm('确认要提交选中的记录吗？').then(() => {
        // 实际项目中应该调用API进行提交
        proxy.$modal.msgSuccess('提交成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

// 取消办理
function handleCancel() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要取消办理的记录');
        return;
    }

    proxy.$modal.confirm('确认要取消办理选中的记录吗？').then(() => {
        // 实际项目中应该调用API进行取消办理
        proxy.$modal.msgSuccess('取消办理成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

// 查看详情
function handleDetail() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行查看');
        return;
    }
    obj.dialogShow = true;
    obj.title = "查看详情";
    obj.isEdit = false;

    // 实际项目中应该调用API获取详细数据
    const row = obj.tableData.find(item => item.id === obj.ids[0]);
    if (row) {
        obj.dialogForm = { ...row };
    }
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 修改票据/报销金额
function handleModifyAmount() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行修改');
        return;
    }
    obj.dialogShow = true;
    obj.title = "修改票据/报销金额";
    obj.isEdit = true;

    // 实际项目中应该调用API获取详细数据
    const row = obj.tableData.find(item => item.id === obj.ids[0]);
    if (row) {
        obj.dialogForm = { ...row };
    }
}

// 接收/驳回
function handleReceiveReject() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的记录');
        return;
    }

    proxy.$modal.confirm('确认要接收选中的记录吗？').then(() => {
        // 实际项目中应该调用API进行接收
        proxy.$modal.msgSuccess('接收成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

// 办理/退回
function handleProcessReturn() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的记录');
        return;
    }

    proxy.$modal.confirm('确认要办理选中的记录吗？').then(() => {
        // 实际项目中应该调用API进行办理
        proxy.$modal.msgSuccess('办理成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

// 办理成功/办理失败
function handleProcessResult() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的记录');
        return;
    }

    proxy.$modal.confirm('确认要标记为办理成功吗？').then(() => {
        // 实际项目中应该调用API进行标记
        proxy.$modal.msgSuccess('标记成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped></style>