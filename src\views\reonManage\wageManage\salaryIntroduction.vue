<!-- 薪资导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="薪资类别名称:" prop="salaryTypeName">
                <el-input class="width220" v-model="obj.queryParams.salaryTypeName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="工资所属月起:" prop="salaryMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryMonth" type="month" placeholder="请选择月份"
                    clearable value-format="YYYY-MM" />
            </el-form-item>
            <el-form-item label="客户:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="处理状态:" prop="processStatus">
                <el-select class="width220" v-model="obj.queryParams.processStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="warning" plain icon='Upload' @click="handleImportSalaryFile">导入工资文件</el-button>
                <el-button type="primary" plain icon="View" @click="handleExportData">查看导入明细</el-button>
                <el-button type="success" plain icon="Download" @click="handleDownloadTemplate">下载工资导入模版</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="发放编号" align="center" prop="releaseCode" />
            <el-table-column label="发放名称" align="center" prop="releaseName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="薪资类别" align="center" prop="salaryTypeName" />
            <el-table-column label="所属年月" align="center" prop="salaryMonth" />
            <el-table-column label="导入人" align="center" prop="importUser" />
            <el-table-column label="导入时间" align="center" prop="importTime" />
            <el-table-column label="成功记录数" align="center" prop="successCount">
                <template #default="scope">
                    <el-tag type="success">{{ scope.row.successCount }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="失败记录数" align="center" prop="failCount">
                <template #default="scope">
                    <el-tag type="danger">{{ scope.row.failCount }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="提醒记录数" align="center" prop="warningCount">
                <template #default="scope">
                    <el-tag type="warning">{{ scope.row.warningCount }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="导入文件" align="center" prop="importFile" />
            <el-table-column label="处理状态" align="center" prop="processStatus">
                <template #default="scope">
                    <dict-tag :options="sys_process_status" :value="scope.row.processStatus" />
                </template>
            </el-table-column>
            <el-table-column label="历史信息查看" align="center">
                <template #default="scope">
                    <el-button type="primary" plain icon="View" :disabled="obj.single"
                        @click="handleView">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 导入工资文件 -->
        <el-dialog v-model="obj.dialogShow" title="导入工资文件" width="40%" append-to-body draggable>
            <el-form :model="obj.dialogForm" ref="formRef" label-width="auto" inline>
                <el-form-item label="薪资类别名称:" prop="salaryTypeName">
                    <el-input class="width220" v-model="obj.dialogForm.salaryTypeName" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="工资所属月起:" prop="salaryMonthStart">
                    <el-date-picker class="width220" v-model="obj.dialogForm.salaryMonthStart" type="month"
                        placeholder="请选择" clearable value-format="YYYY-MM" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                </el-form-item>

                <el-table :data="obj.dialogTableData" border>
                    <el-table-column label="发放编号" prop="releaseCode" />
                    <el-table-column label="发放名称" prop="releaseName" />
                    <el-table-column label="客户名称" prop="customerName" />
                    <el-table-column label="薪资类别名称" prop="salaryTypeName" />
                    <el-table-column label="所属月" prop="salaryMonth" />
                    <el-table-column label="工资计税月" prop="salaryMonthEnd" />
                    <el-table-column label="客户账单月" prop="customerBillMonthEnd" />
                </el-table>
                <el-form-item class="mt20" label="客户工资文件上传:">
                    <file-upload :fileList="obj.dialogTableData" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span style="color: #ff0000;margin-right: 10px;">导入年终奖或补偿金时需要有工资或者做0申报</span>
                <el-button type="primary" @click="handleSaveSalaryFile">保存</el-button>
                <el-button @click="handleCloseDialog">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="SalaryIntroduction">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 处理状态选项
const statusOptions = [
    { value: '0', label: '未处理' },
    { value: '1', label: '处理中' },
    { value: '2', label: '处理完成' },
    { value: '3', label: '处理失败' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        salaryTypeName: null, // 薪资类别名称
        salaryMonth: null, // 工资所属月起
        customerName: null, // 客户
        processStatus: null, // 处理状态
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    ids: [], // 选中的id
    title: "", // 标题
    dialogShow: false, // 导入工资文件对话框
    dialogForm: {
        importFile: null,
    }, // 导入工资文件表单
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                releaseCode: 'R20230501',
                releaseName: '五月工资发放',
                customerName: '客户A',
                salaryTypeName: '正式工资',
                salaryMonth: '2023-05',
                importUser: '管理员',
                importTime: '2023-05-10 10:30:00',
                successCount: 120,
                failCount: 5,
                warningCount: 10
            },
            {
                id: 2,
                releaseCode: 'R20230601',
                releaseName: '六月工资发放',
                customerName: '客户B',
                salaryTypeName: '正式工资',
                salaryMonth: '2023-06',
                importUser: '管理员',
                importTime: '2023-06-10 11:15:00',
                successCount: 135,
                failCount: 0,
                warningCount: 8
            },
            {
                id: 3,
                releaseCode: 'R20230701',
                releaseName: '七月工资发放',
                customerName: '客户C',
                salaryTypeName: '正式工资',
                salaryMonth: '2023-07',
                importUser: '管理员',
                importTime: '2023-07-10 09:45:00',
                successCount: 142,
                failCount: 3,
                warningCount: 5
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 导入工资文件
function handleImportSalaryFile() {
    obj.dialogShow = true;
}

// 下载模版
function handleDownloadTemplate() {
    proxy.$modal.msgSuccess('模版下载成功');
    // 实际项目中应该调用下载接口

    // 模拟下载文件
    const fileName = '工资导入模版.xlsx';
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 查看导入明细
function handleExportData() {
    proxy.$modal.msgSuccess('查看导入明细');
    // 实际项目中应该跳转到明细页面
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>