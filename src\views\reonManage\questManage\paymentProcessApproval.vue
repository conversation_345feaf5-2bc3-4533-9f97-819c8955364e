<!-- 支付流程审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="支付类型:" prop="paymentType">
                <el-select class="width220" v-model="obj.queryParams.paymentType" placeholder="请选择" clearable>
                    <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="提交人:" prop="submitter">
                <el-select class="width220" v-model="obj.queryParams.submitter" placeholder="请选择" clearable>
                    <el-option v-for="item in submitterOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请支付时间起始:" prop="startTime">
                <el-date-picker class="width220" v-model="obj.queryParams.startTime" type="date" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="申请支付时间截止:" prop="endTime">
                <el-date-picker class="width220" v-model="obj.queryParams.endTime" type="date" placeholder="请选择" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">批量审批</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="payment_process_approval" />
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="支付类型" align="center" prop="paymentType" />
            <el-table-column label="支付方式" align="center" prop="paymentMethod" />
            <el-table-column label="银行账号/支票号" align="center" prop="accountNo" />
            <el-table-column label="应付金额" align="center" prop="payableAmount" />
            <el-table-column label="申请金额" align="center" prop="applyAmount" />
            <el-table-column label="支付所属年月" align="center" prop="paymentMonth" />
            <el-table-column label="申请人" align="center" prop="applicant" />
            <el-table-column label="申请时间" align="center" prop="applyTime" />
            <el-table-column label="支付地" align="center" prop="paymentPlace" />
            <el-table-column label="福利办理方" align="center" prop="benefitsHandler" />
            <el-table-column label="操作" align="center" width="180">
                <template #default="scope">
                    <el-button type="primary" link @click="handleApproval(scope.row)">审批</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 审批 -->
        <el-dialog v-model="obj.dialogShow" title="支付审批" width="50%">
            <el-form class="mt20" :model="obj.dialogForm" ref="dialogRef" inline label-width="auto">
                <el-tabs type="border-card">
                    <el-tab-pane label="支付申请">
                        <el-form-item label="福利办理方" prop="benefitsHandler">
                            <el-select class="width220" v-model="obj.dialogForm.benefitsHandler" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in benefitsHandlerOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="福利包名称" prop="benefitsPackage">
                            <el-select class="width220" v-model="obj.dialogForm.benefitsPackage" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in benefitsPackageOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="报表年月" prop="reportMonth">
                            <el-date-picker class="width220" v-model="obj.dialogForm.reportMonth" type="month"
                                placeholder="请选择" />
                        </el-form-item>
                        <el-form-item label="支付详细类型" prop="paymentDetailType">
                            <el-select class="width220" v-model="obj.dialogForm.paymentDetailType" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in paymentDetailTypeOptions" :key="item.value"
                                    :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" plain @click="handleAdd">选择支付费用</el-button>
                            <el-button type="success" plain @click="handleAdd">重新选中金额</el-button>
                        </el-form-item>
                        <el-form-item label="支付方式" prop="paymentMethod">
                            <el-select class="width220" v-model="obj.dialogForm.paymentMethod" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="详细支付方式" prop="detailedPaymentMethod">
                            <el-select class="width220" v-model="obj.dialogForm.detailedPaymentMethod" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in detailedPaymentMethodOptions" :key="item.value"
                                    :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="收款方" prop="payee">
                            <el-input class="width220" v-model="obj.dialogForm.payee" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="收款银行" prop="payeeBank">
                            <el-input class="width220" v-model="obj.dialogForm.payeeBank" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="支付方式" prop="paymentMethod">
                            <el-select class="width220" v-model="obj.dialogForm.paymentMethod" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="详细支付方式" prop="detailedPaymentMethod">
                            <el-select class="width220" v-model="obj.dialogForm.detailedPaymentMethod" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in detailedPaymentMethodOptions" :key="item.value"
                                    :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="收款方" prop="payee">
                            <el-input class="width220" v-model="obj.dialogForm.payee" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="收款银行" prop="payeeBank">
                            <el-input class="width220" v-model="obj.dialogForm.payeeBank" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="收款银行开户行" prop="payeeBankBranch">
                            <el-input class="width220" v-model="obj.dialogForm.payeeBankBranch" placeholder="请输入"
                                clearable />
                        </el-form-item>
                        <el-form-item label="银行账号/支票号" prop="accountNo">
                            <el-input class="width220" v-model="obj.dialogForm.accountNo" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="单据数量" prop="documentCount">
                            <el-input class="width220" v-model="obj.dialogForm.documentCount" />
                        </el-form-item>
                        <el-form-item label="申请支付总额" prop="totalApplyAmount">
                            <el-input class="width220" v-model="obj.dialogForm.totalApplyAmount" />
                        </el-form-item>
                        <el-form-item label="应付总额" prop="totalPayableAmount">
                            <el-input class="width220" v-model="obj.dialogForm.totalPayableAmount" />
                        </el-form-item>
                        <el-form-item label="最晚支付日期" prop="latestPaymentDate">
                            <el-date-picker class="width220" v-model="obj.dialogForm.latestPaymentDate" type="date"
                                placeholder="请选择" />
                        </el-form-item>
                        <el-form-item label="支付所属年月" prop="paymentMonth">
                            <el-date-picker class="width220" v-model="obj.dialogForm.paymentMonth" type="month"
                                placeholder="请选择" />
                        </el-form-item>
                        <el-form-item label="支付地" prop="paymentPlace">
                            <el-select class="width220" v-model="obj.dialogForm.paymentPlace" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in paymentPlaceOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="支付类型" prop="paymentType">
                            <el-select class="width220" v-model="obj.dialogForm.paymentType" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="差异金额" prop="differenceAmount">
                            <el-input class="width220" v-model="obj.dialogForm.differenceAmount" />
                        </el-form-item>
                        <el-form-item label="历史差异" prop="historicalDifference">
                            <el-input class="width220" v-model="obj.dialogForm.historicalDifference" />
                        </el-form-item>
                        <el-form-item label="支付用途" prop="paymentPurpose">
                            <el-input class="width420" type="textarea" v-model="obj.dialogForm.paymentPurpose"
                                placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input class="width420" type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入"
                                clearable />
                        </el-form-item>
                        <el-form-item label="附件" prop="file">
                            <el-upload class="upload-demo" action="#" :auto-upload="false" :limit="5" multiple>
                                <template #trigger>
                                    <el-button type="primary">选取文件</el-button>
                                </template>
                                <template #tip>
                                    <div class="el-upload__tip">支持上传jpg/png/pdf文件</div>
                                </template>
                            </el-upload>
                        </el-form-item>
                        <el-divider content-position="left">审批操作</el-divider>
                        <el-form-item>
                            <el-button type="primary" @click="handleApprove">同意</el-button>
                            <el-button type="danger" @click="handleReject">驳回</el-button>
                            <el-button @click="dialogShow = false">取消</el-button>
                        </el-form-item>

                    </el-tab-pane>
                    <el-tab-pane label="流程信息">
                        <ProcessInformation :tableData="processData" />
                    </el-tab-pane>
                </el-tabs>
            </el-form>

        </el-dialog>



    </div>
</template>

<script setup name="PaymentProcessApproval">
import { listInstance, delInstance } from '@/api/reonApi/instance';
import ProcessInformation from '@/views/reonManage/components/processInformation.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 支付类型选项
const paymentTypeOptions = [
    { value: '1', label: '工资支付' },
    { value: '2', label: '社保支付' },
    { value: '3', label: '公积金支付' },
    { value: '4', label: '福利支付' },
    { value: '5', label: '其他支付' }
];

// 提交人选项
const submitterOptions = [
    { value: '1', label: '张三' },
    { value: '2', label: '李四' },
    { value: '3', label: '王五' },
    { value: '4', label: '赵六' },
    { value: '5', label: '钱七' }
];

// 福利办理方选项
const benefitsHandlerOptions = [
    { value: '1', label: '总部' },
    { value: '2', label: '分公司' },
    { value: '3', label: '第三方机构' }
];

// 福利包选项
const benefitsPackageOptions = [
    { value: '1', label: '基本福利包' },
    { value: '2', label: '补充福利包' },
    { value: '3', label: '特殊福利包' }
];

// 支付详细类型选项
const paymentDetailTypeOptions = [
    { value: '1', label: '工资' },
    { value: '2', label: '社保' },
    { value: '3', label: '公积金' },
    { value: '4', label: '年终奖' },
    { value: '5', label: '福利补贴' }
];

// 支付方式选项
const paymentMethodOptions = [
    { value: '1', label: '银行转账' },
    { value: '2', label: '支票' },
    { value: '3', label: '现金' },
    { value: '4', label: '其他' }
];

// 详细支付方式选项
const detailedPaymentMethodOptions = [
    { value: '1', label: '工资卡' },
    { value: '2', label: '储蓄卡' },
    { value: '3', label: '信用卡' },
    { value: '4', label: '其他银行卡' },
    { value: '5', label: '现金支票' },
    { value: '6', label: '转账支票' }
];

// 支付地选项
const paymentPlaceOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '成都' },
    { value: '6', label: '其他' }
];

// 流程数据
const processData = ref([
    {
        id: 1,
        processor: '张三',
        processType: '提交申请',
        comment: '请审批',
        processTime: '2023-07-01 10:00:00'
    },
    {
        id: 2,
        processor: '李四',
        processType: '部门审批',
        comment: '同意',
        processTime: '2023-07-01 11:30:00'
    },
    {
        id: 3,
        processor: '王五',
        processType: '财务审批',
        comment: '同意',
        processTime: '2023-07-01 14:15:00'
    }
]);


const obj = reactive({
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        paymentType: null,
        submitter: null,
        startTime: null,
        endTime: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {},//弹窗表单
    dialogShow: false,//弹窗
    ids: [],//选中的id
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    columnOptions: [
        { label: '支付类型', prop: 'paymentType' },
        { label: '支付方式', prop: 'paymentMethod' },
        { label: '银行账号/支票号', prop: 'accountNo' },
        { label: '应付金额', prop: 'payableAmount' },
        { label: '申请金额', prop: 'applyAmount' },
        { label: '支付所属年月', prop: 'paymentMonth' },
        { label: '申请人', prop: 'applicant' },
        { label: '申请时间', prop: 'applyTime' },
        { label: '支付地', prop: 'paymentPlace' },
        { label: '福利办理方', prop: 'benefitsHandler' }
    ],//列配置选项
    selectedColumns: ['paymentType', 'paymentMethod', 'accountNo', 'payableAmount', 'applyAmount', 'paymentMonth', 'applicant', 'applyTime', 'paymentPlace', 'benefitsHandler'],//默认显示所有列
})




/** 获取列表数据 */
function getList() {
    obj.loading = true;
    listInstance(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 导出操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 审批操作 */
function handleApproval(row) {
    obj.dialogShow = true;
    proxy.$modal.msgInfo(`审批ID为${row.id}的支付申请`);
}

/** 同意审批 */
function handleApprove() {
    proxy.$modal.msgSuccess("审批通过");
    obj.dialogShow = false;
}

/** 驳回审批 */
function handleReject() {
    proxy.$modal.msgError("审批驳回");
    obj.dialogShow = false;
}


getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>