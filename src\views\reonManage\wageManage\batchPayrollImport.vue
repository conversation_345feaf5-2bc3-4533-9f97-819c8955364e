<!-- 批量薪资计算导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateFrom" type="date"
                    placeholder="请选择日期" value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateTo" type="date" placeholder="请选择日期"
                    value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="导入人:" prop="importer">
                <el-select class="width220" v-model="obj.queryParams.importer" placeholder="请选择" clearable>
                    <el-option v-for="item in importerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入编号:" prop="importCode">
                <el-select class="width220" v-model="obj.queryParams.importCode" placeholder="请选择" clearable>
                    <el-option v-for="item in importCodeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="warning" plain icon="Upload" @click="handleExport">导入</el-button>
                <el-button type="primary" plain icon="Download" @click="handleDownloadTemplate">下载模版</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <ExportTable2 :tableData="obj.tableData" :loading="obj.loading" :total="obj.total"
            :page="obj.queryParams.pageNum" :limit="obj.queryParams.pageSize" menuName="batchPayrollImport"
            @row-dblclick="handleRowDblClick" @handlePagination="handlePagination" />
        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" title="上传导入数据" :dialogForm="obj.dialogForm"
            :type="'batchPayrollImport'" :rules="importRules" />
        <!-- 历史信息查看 -->
        <HistoricalInformation :dialogShow="obj.dialogShow2" :title="obj.title" :tableData="obj.tableData"
            @close="handleHistoryClose" />
    </div>
</template>

<script setup name="BatchPayrollImport">



const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 导入人选项
const importerOptions = [
    { value: '1', label: '张三' },
    { value: '2', label: '李四' },
    { value: '3', label: '王五' },
    { value: '4', label: '赵六' },
    { value: '5', label: '孙七' }
];

// 导入编号选项
const importCodeOptions = [
    { value: 'IMP20230701001', label: 'IMP20230701001' },
    { value: 'IMP20230702001', label: 'IMP20230702001' },
    { value: 'IMP20230703001', label: 'IMP20230703001' },
    { value: 'IMP20230704001', label: 'IMP20230704001' },
    { value: 'IMP20230705001', label: 'IMP20230705001' }
];

// 处理状态选项
const statusOptions = [
    { value: '1', label: '处理中' },
    { value: '2', label: '处理成功' },
    { value: '3', label: '处理失败' },
    { value: '4', label: '已取消' }
];

// 批量工资导入数据
const obj = reactive({
    showSearch: true, // 显示搜索
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateFrom: '', // 导入日期从
        importDateTo: '', // 导入日期到
        importer: '', // 导入人
        importCode: '', // 导入编号
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            importCode: 'IMP20230701001',
            importer: '1',
            importTime: '2023-07-01 10:00:00',
            remark: '批量工资导入',
            successCount: 100,
            failCount: 5,
            importFile: '工资数据20230701.xlsx',
            status: '2',
            creator: '张三',
            createTime: '2023-07-01 09:50:00',
            modifier: '张三',
            modifyTime: '2023-07-01 10:05:00'
        },
        {
            id: 2,
            importCode: 'IMP20230702001',
            importer: '2',
            importTime: '2023-07-02 11:00:00',
            remark: '新入职员工工资导入',
            successCount: 50,
            failCount: 2,
            importFile: '新入职员工工资数据20230702.xlsx',
            status: '2',
            creator: '李四',
            createTime: '2023-07-02 10:50:00',
            modifier: '李四',
            modifyTime: '2023-07-02 11:05:00'
        },
        {
            id: 3,
            importCode: 'IMP20230703001',
            importer: '3',
            importTime: '2023-07-03 14:00:00',
            remark: '社保代缴工资导入',
            successCount: 30,
            failCount: 10,
            importFile: '社保代缴工资数据20230703.xlsx',
            status: '3',
            creator: '王五',
            createTime: '2023-07-03 13:50:00',
            modifier: '王五',
            modifyTime: '2023-07-03 14:05:00'
        }
    ],
    tableData2: [], // 详情表格数据

    // 导入对话框相关数据
    dialogShow: false,
    dialogShow2: false,
    dialogForm: {
        remark: '',
        fileList: []
    },
    formRules: {
        fileList: [{ required: true, message: '请上传文件', trigger: 'change' }]
    }
});

/**
 * 获取导入人名称
 * @param {string} importerId 导入人ID
 * @returns {string} 导入人名称
 */
function getImporterName(importerId) {
    if (!importerId) return '-';

    const importer = importerOptions.find(item => item.value === importerId);
    return importer ? importer.label : '-';
}

/**
 * 获取处理状态名称
 * @param {string} statusId 状态ID
 * @returns {string} 状态名称
 */
function getStatusName(statusId) {
    if (!statusId) return '-';

    const status = statusOptions.find(item => item.value === statusId);
    return status ? status.label : '-';
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.importDateFrom) {
            filteredData = filteredData.filter(item =>
                item.importTime >= obj.queryParams.importDateFrom + ' 00:00:00'
            );
        }

        if (obj.queryParams.importDateTo) {
            filteredData = filteredData.filter(item =>
                item.importTime <= obj.queryParams.importDateTo + ' 23:59:59'
            );
        }

        if (obj.queryParams.importer) {
            filteredData = filteredData.filter(item =>
                item.importer === obj.queryParams.importer
            );
        }

        if (obj.queryParams.importCode) {
            filteredData = filteredData.filter(item =>
                item.importCode === obj.queryParams.importCode
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}
// 双击行
function handleRowDblClick(row) {
    obj.dialogShow2 = true;
    obj.title = '历史信息查看';
}

// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}

/** 导入按钮操作 */
function handleExport() {
    obj.dialogShow = true;
    obj.dialogForm = {
        remark: '',
        fileList: []
    };
}

/** 下载模版 */
function handleDownloadTemplate() {

}

/** 导入关闭 */
function handleImportClose() {
    obj.dialogShow = false;
}
/** 历史信息关闭 */
function handleHistoryClose() {
    obj.dialogShow2 = false;
}
// 初始化加载数据
onMounted(() => {
    getList();
});
</script>
<style lang="scss" scoped></style>