<!-- 批量核销开票 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="大集团:" prop="groupName">
                <el-select class="width220" v-model="obj.queryParams.groupName" placeholder="请选择大集团" clearable>
                    <el-option label="集团A" value="A" />
                    <el-option label="集团B" value="B" />
                    <el-option label="集团C" value="C" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="合同:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="签约方抬头:" prop="signTitle">
                <el-select class="width220" v-model="obj.queryParams.signTitle" placeholder="请选择签约方抬头" clearable>
                    <el-option label="抬头1" value="1" />
                    <el-option label="抬头2" value="2" />
                    <el-option label="抬头3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单类型(必填):" prop="billType" required>
                <el-select class="width220" v-model="obj.queryParams.billType" placeholder="请选择账单类型" clearable>
                    <el-option label="类型1" value="1" />
                    <el-option label="类型2" value="2" />
                    <el-option label="类型3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单月(必填):" prop="billMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择账单月"
                    clearable />
            </el-form-item>
            <el-form-item label="核销状态:" prop="writeOffStatus">
                <el-select class="width220" v-model="obj.queryParams.writeOffStatus" placeholder="请选择核销状态" clearable>
                    <el-option label="未核销" value="0" />
                    <el-option label="已核销" value="1" />
                    <el-option label="核销中" value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="开票状态:" prop="invoiceStatus">
                <el-select class="width220" v-model="obj.queryParams.invoiceStatus" placeholder="请选择开票状态" clearable>
                    <el-option label="未开票" value="0" />
                    <el-option label="已开票" value="1" />
                    <el-option label="开票中" value="2" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" icon="Check" plain :disabled="obj.single"
                    @click="handleWriteOff">核销开票账单</el-button>
                <el-button type="success" icon="Check" plain :disabled="obj.multiple"
                    @click="handleBatchWriteOff">批量核销</el-button>
                <el-button type="warning" icon="Tickets" plain :disabled="obj.multiple"
                    @click="handleBatchInvoice">批量开票</el-button>
            </el-col>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户帐套" align="center" prop="customerAccount" />
            <el-table-column label="账单类型" align="center" prop="billType" />
            <el-table-column label="账单年月" align="center" prop="billMonth" />
            <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
            <el-table-column label="账单人数" align="center" prop="billPersonCount" />
            <el-table-column label="应收金额" align="center" prop="receivableAmount" />
            <el-table-column label="账单状态" align="center" prop="billStatus" />
            <el-table-column label="首次生成时间" align="center" prop="firstGenerateTime" />
            <el-table-column label="核销状态" align="center" prop="writeOffStatus" />
            <el-table-column label="开票状态" align="center" prop="invoiceStatus" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="BatchVerificationInvoicing">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null,
        customerName: null,
        contractName: null,
        signTitle: null,
        billType: null,
        billMonth: null,
        writeOffStatus: null,
        invoiceStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerName: '客户名称1',
                customerAccount: '帐套1',
                billType: '类型1',
                billMonth: '2023-01',
                financialReceivableMonth: '2023-01',
                billPersonCount: 10,
                receivableAmount: 10000.00,
                billStatus: '已生成',
                firstGenerateTime: '2023-01-01 10:00:00',
                writeOffStatus: '未核销',
                invoiceStatus: '未开票'
            },
            {
                id: 2,
                customerName: '客户名称2',
                customerAccount: '帐套2',
                billType: '类型2',
                billMonth: '2023-02',
                financialReceivableMonth: '2023-02',
                billPersonCount: 20,
                receivableAmount: 20000.00,
                billStatus: '已生成',
                firstGenerateTime: '2023-02-01 10:00:00',
                writeOffStatus: '已核销',
                invoiceStatus: '已开票'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 核销开票账单
function handleWriteOff() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要核销开票的账单');
        return;
    }
    proxy.$modal.confirm('是否确认核销选中的账单？').then(function () {
        // 这里可以调用API核销账单
        proxy.$modal.msgSuccess('核销开票账单成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 批量核销
function handleBatchWriteOff() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要批量核销的账单');
        return;
    }
    proxy.$modal.confirm('是否确认批量核销选中的账单？').then(function () {
        // 这里可以调用API批量核销账单
        proxy.$modal.msgSuccess('批量核销成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 批量开票
function handleBatchInvoice() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要批量开票的账单');
        return;
    }
    proxy.$modal.confirm('是否确认批量开票选中的账单？').then(function () {
        // 这里可以调用API批量开票账单
        proxy.$modal.msgSuccess('批量开票成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>