<template>
  <div :class="{ 'show': show }" class="header-search">
    <svg-icon class-name="search-icon" icon-class="search" @click.stop="click" />
    <el-select ref="headerSearchSelectRef" v-model="search" :remote-method="querySearch" filterable default-first-option
      remote placeholder="Search" class="header-search-select" @change="change">
      <el-option v-for="option in options" :key="option.item.path" :value="option.item"
        :label="option.item.title.join(' > ')" />
    </el-select>
  </div>
</template>

<script setup>
import Fuse from 'fuse.js'
import { getNormalPath } from '@/utils/ruoyi'
import { isHttp } from '@/utils/validate'
import usePermissionStore from '@/store/modules/permission'

const search = ref('');
const options = ref([]);
const searchPool = ref([]);
const show = ref(false);
const fuse = ref(undefined);
const headerSearchSelectRef = ref(null);
const router = useRouter();
const routes = computed(() => usePermissionStore().routes);

/**
 * 点击事件处理函数
 * 该函数主要用于切换搜索框的显示状态，并在显示时聚焦搜索框
 */
function click() {
  // 反转show的值，以控制搜索框的显示与隐藏
  show.value = !show.value
  // 当show为真时，即搜索框显示时，尝试聚焦搜索框
  if (show.value) {
    // 检查headerSearchSelectRef是否有值，如果有则调用其focus方法聚焦搜索框
    headerSearchSelectRef.value && headerSearchSelectRef.value.focus()
  }
}
/**
 * 关闭搜索功能的函数
 * 这个函数在用户想要关闭搜索框时调用，负责清除搜索框的焦点并重置相关状态
 */
function close() {
  // 如果headerSearchSelectRef有值，则调用其blur方法移除焦点
  headerSearchSelectRef.value && headerSearchSelectRef.value.blur()

  // 清空options数组，重置下拉列表的选项
  options.value = []

  // 设置show为false，隐藏搜索框
  show.value = false
}
/**
 * 根据提供的路径和查询参数导航到不同的页面
 * 如果路径是HTTP(S)链接，则在新窗口中打开；否则，使用Vue Router进行页面跳转
 * @param {Object} val - 包含路径和查询参数的对象
 */
function change(val) {
  // 提取路径和查询参数
  const path = val.path;
  const query = val.query;

  // 检查路径是否为HTTP(S)链接
  if (isHttp(path)) {
    // http(s):// 路径新窗口打开
    const pindex = path.indexOf("http");
    window.open(path.substr(pindex, path.length), "_blank");
  } else {
    // 根据查询参数的存在与否进行相应的页面跳转
    if (query) {
      router.push({ path: path, query: JSON.parse(query) });
    } else {
      router.push(path)
    }
  }

  // 重置搜索和选项值
  search.value = ''
  options.value = []

  // 在下一个Tick中隐藏某个元素或组件
  nextTick(() => {
    show.value = false
  })
}
/**
 * 初始化Fuse搜索实例
 * 
 * @param {Array} list - 需要进行搜索的列表，通常包含多个对象
 */
function initFuse(list) {
  // 创建一个新的Fuse实例，并配置搜索选项
  fuse.value = new Fuse(list, {
    // 启用排序功能，以确保更匹配的项目排在前面
    shouldSort: true,
    // 设置匹配阈值，影响搜索结果的宽松程度
    threshold: 0.4,
    // 忽略位置因素，不考虑搜索词在文本中的具体位置
    location: 0,
    // 设置最大搜索距离，限制搜索词与匹配项之间的距离
    distance: 100,
    // 设置最小匹配字符长度，以减少无效搜索
    minMatchCharLength: 1,
    // 指定搜索的键，以及它们的权重，以影响搜索结果的排序
    keys: [
      {
        name: 'title',
        weight: 0.7
      },
      {
        name: 'path',
        weight: 0.3
      }
    ]
  })
}
// 过滤出可以在侧边栏中显示的路由
// 生成国际化标题
/**
 * 生成带有国际化标题的路由
 * @param {Array} routes - 路由配置数组
 * @param {String} basePath - 基础路径，默认为空
 * @param {Array} prefixTitle - 标题前缀，默认为空数组
 * @returns {Array} 返回处理后的路由数组
 */
function generateRoutes(routes, basePath = '', prefixTitle = []) {
  let res = []

  for (const r of routes) {
    // 跳过隐藏的路由
    if (r.hidden) { continue }
    // 拼接路由路径，确保其以 '/' 开头
    const p = r.path.length > 0 && r.path[0] === '/' ? r.path : '/' + r.path;
    const data = {
      // 判断路径是否为外部链接或需要与基础路径拼接
      path: !isHttp(r.path) ? getNormalPath(basePath + p) : r.path,
      // 继承标题前缀
      title: [...prefixTitle]
    }

    // 处理带有标题且未隐藏的路由
    if (r.meta && r.meta.title) {
      // 拼接当前路由的标题
      data.title = [...data.title, r.meta.title]

      // 只推送带有标题的路由
      // 特殊情况：需要排除没有重定向的父路由
      if (r.redirect !== 'noRedirect') {
        res.push(data)
      }
    }
    // 添加查询参数（如果存在）
    if (r.query) {
      data.query = r.query
    }

    // 递归处理子路由
    if (r.children) {
      // 递归处理子路由
      const tempRoutes = generateRoutes(r.children, data.path, data.title)
      // 将子路由合并到结果数组中
      if (tempRoutes.length >= 1) {
        res = [...res, ...tempRoutes]
      }
    }
  }
  // 返回处理后的路由数组
  return res
}
/**
 * 根据用户输入的查询内容来搜索选项
 * 当查询内容不为空时，使用fuse.js库进行模糊搜索，以query为关键词更新options数组
 * 如果查询内容为空，则清空options数组，确保没有不相关的搜索结果
 * 
 * @param {string} query - 用户输入的查询关键词
 */
function querySearch(query) {
  if (query !== '') {
    options.value = fuse.value.search(query)
  } else {
    options.value = []
  }
}

onMounted(() => {
  searchPool.value = generateRoutes(routes.value);
})

watchEffect(() => {
  searchPool.value = generateRoutes(routes.value)
})

watch(show, (value) => {
  if (value) {
    document.body.addEventListener('click', close)
  } else {
    document.body.removeEventListener('click', close)
  }
})

watch(searchPool, (list) => {
  initFuse(list)
})
</script>

<style lang='scss' scoped>
.header-search {
  font-size: 0 !important;

  .search-icon {
    cursor: pointer;
    font-size: 18px;
    vertical-align: middle;
  }

  .header-search-select {
    font-size: 18px;
    transition: width 0.2s;
    width: 0;
    overflow: hidden;
    background: transparent;
    border-radius: 0;
    display: inline-block;
    vertical-align: middle;

    :deep(.el-input__inner) {
      border-radius: 0;
      border: 0;
      padding-left: 0;
      padding-right: 0;
      box-shadow: none !important;
      border-bottom: 1px solid #d9d9d9;
      vertical-align: middle;
    }
  }

  &.show {
    .header-search-select {
      width: 210px;
      margin-left: 10px;
    }
  }
}
</style>