<!-- 实做补缴查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-input class="width220" v-model="obj.queryParams.customer" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable>
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="补缴发生月:" prop="paymentMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentMonth" type="month" placeholder="请选择月份"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" plain icon="Search" @click="handleExport">查询个人订单</el-button>
                <el-button type="warning" plain icon='View' @click="handleDetail">查看</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="订单编号" align="center" prop="orderNo" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="福利包编号" align="center" prop="welfarePackageNo" />
            <el-table-column label="补缴发生月" align="center" prop="paymentMonth" />
            <el-table-column label="产品名称" align="center" prop="productName" />
            <el-table-column label="收费开始月" align="center" prop="chargeStartMonth" />
            <el-table-column label="收费截止月" align="center" prop="chargeEndMonth" />
            <el-table-column label="比例编号" align="center" prop="ratioNo" />
            <el-table-column label="比例名称" align="center" prop="ratioName" />
            <el-table-column label="企业基数" align="center" prop="companyBase" />
            <el-table-column label="个人基数" align="center" prop="personalBase" />
            <el-table-column label="企业金额" align="center" prop="companyAmount" />
            <el-table-column label="个人金额" align="center" prop="personalAmount" />
            <el-table-column label="企业滞纳金" align="center" prop="companyLateFee" />
            <el-table-column label="个人滞纳金" align="center" prop="personalLateFee" />
            <el-table-column label="补缴类型" align="center" prop="paymentType" />
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 查看个人订单 -->
        <order-contract-reserve-fund type="ActualApplication" v-model:dialogShow="obj.dialogShow" title="查看个人订单"
            :form="obj.dialogForm" :tableData="obj.tableData" :tableData_no="obj.tableData_no" :isDetail="true" />
        <!-- 查看 -->
        <management-information v-model:dialogShow="obj.dialogShow2" title="查看" :form="obj.dialogForm"
            :tableData="obj.tableData" :tableData2="obj.tableData2" :isDetail="obj.isDetail" />

    </div>
</template>

<script setup name="MakeActualPayment_query">
import { listScale } from "@/api/reonApi/scale";
import OrderContractReserveFund from '@/views/reonManage/components/dialog/orderContractReserveFund.vue'
import ManagementInformation from '@/views/reonManage/components/dialog/managementInformation.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 证件类型选项
const idTypeOptions = [
    { value: '1', label: '身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '军官证' }
];

// 模拟补缴查询数据
const mockPaymentData = [
    {
        id: 1,
        employeeName: '张三',
        idNumber: '110101199001011234',
        orderNo: 'ORD20230001',
        welfareHandler: '公司A',
        welfarePackageName: '标准福利包',
        welfarePackageNo: 'WP001',
        paymentMonth: '2023-01',
        productName: '养老保险',
        chargeStartMonth: '2023-01',
        chargeEndMonth: '2023-12',
        ratioNo: 'R001',
        ratioName: '养老保险比例',
        companyBase: 8000,
        personalBase: 8000,
        companyAmount: 1600,
        personalAmount: 800,
        companyLateFee: 0,
        personalLateFee: 0,
        paymentType: '正常补缴',
        creator: '李四',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 2,
        employeeName: '李四',
        idNumber: '110101199001021234',
        orderNo: 'ORD20230002',
        welfareHandler: '公司B',
        welfarePackageName: '高级福利包',
        welfarePackageNo: 'WP002',
        paymentMonth: '2023-02',
        productName: '医疗保险',
        chargeStartMonth: '2023-02',
        chargeEndMonth: '2023-12',
        ratioNo: 'R002',
        ratioName: '医疗保险比例',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 800,
        personalAmount: 200,
        companyLateFee: 0,
        personalLateFee: 0,
        paymentType: '补差补缴',
        creator: '王五',
        createTime: '2023-02-15 09:15:00'
    },
    {
        id: 3,
        employeeName: '王五',
        idNumber: '110101199001031234',
        orderNo: 'ORD20230003',
        welfareHandler: '公司C',
        welfarePackageName: '基础福利包',
        welfarePackageNo: 'WP003',
        paymentMonth: '2023-03',
        productName: '失业保险',
        chargeStartMonth: '2023-03',
        chargeEndMonth: '2023-12',
        ratioNo: 'R003',
        ratioName: '失业保险比例',
        companyBase: 12000,
        personalBase: 12000,
        companyAmount: 240,
        personalAmount: 120,
        companyLateFee: 0,
        personalLateFee: 0,
        paymentType: '补缴补缴',
        creator: '赵六',
        createTime: '2023-03-15 11:45:00'
    }
];

// 模拟产品办理信息
const mockProductData = [
    {
        id: 1,
        productName: '养老保险',
        ratioNo: 'R001',
        ratioName: '养老保险比例',
        companyBase: 8000,
        personalBase: 8000,
        companyAmount: 1600,
        personalAmount: 800,
        companyRatio: '20%',
        personalRatio: '10%',
        welfareHandleMonth: '2023-01',
        welfareStartMonth: '2023-01',
        welfareEndMonth: '2023-12',
        total: 2400
    },
    {
        id: 2,
        productName: '医疗保险',
        ratioNo: 'R002',
        ratioName: '医疗保险比例',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 800,
        personalAmount: 200,
        companyRatio: '8%',
        personalRatio: '2%',
        welfareHandleMonth: '2023-02',
        welfareStartMonth: '2023-02',
        welfareEndMonth: '2023-12',
        total: 1000
    }
];

// 模拟补缴信息
const mockPaymentDetailData = [
    {
        id: 1,
        productName: '养老保险',
        ratioNo: 'R001',
        ratioName: '养老保险比例',
        paymentStartMonth: '2023-01',
        paymentEndMonth: '2023-01',
        paymentMonth: '2023-01',
        companyBase: 8000,
        personalBase: 8000,
        companyAmount: 1600,
        personalAmount: 800,
        companyRatio: '20%',
        personalRatio: '10%',
        companyLateFee: 0,
        personalLateFee: 0
    },
    {
        id: 2,
        productName: '医疗保险',
        ratioNo: 'R002',
        ratioName: '医疗保险比例',
        paymentStartMonth: '2023-02',
        paymentEndMonth: '2023-02',
        paymentMonth: '2023-02',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 800,
        personalAmount: 200,
        companyRatio: '8%',
        personalRatio: '2%',
        companyLateFee: 0,
        personalLateFee: 0
    }
];

// 表单验证规则
const rules = {
    // 这里主要是查询表单，不需要验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        orderNo: null, // 订单编号
        employeeName: null, // 雇员姓名
        idNumber: null, // 证件号码
        paymentMonth: null, // 补缴发生月
    }, // 查询表单
    total: mockPaymentData.length, // 总条数
    tableData: mockPaymentData, // 列表
    tableData2: [],
    dialogShow: false, // 查看个人订单对话框
    dialogShow2: false, // 查看详情对话框
    dialogForm: {
        welfareHandler: '公司A', // 福利办理方
        welfarePackageName: '标准福利包', // 福利包名称
        orderStatus: '正常', // 订单状态
        changeStatus: '无变更', // 变更状态
        employmentStatus: '在职', // 入离职状态
        customerId: 'C001', // 客户编号
        customerName: '客户A', // 客户名称
        customerService: '张三', // 接单客服
        implementStatus: '已实做', // 实作状态
        uniqueId: 'U001', // 唯一号
        idNumber: '110101199001011234', // 证件号码
        entryTime: '2023-01-01', // 入职时间
        handler: '李四', // 办理人
        addMethod: '正常新增', // 新增方式
        addRemark: '无', // 新办备注
        stopHandler: '', // 办停人
        idType: '1', // 证件类型
        stopTime: '', // 办停时间
        stopRemark: '', // 办停备注
        modifyRemark: '', // 修改备注
        returnRemark: '', // 退回备注
        modifyProcess: '' // 修改过程
    }, // 详情表单
    productData: mockProductData, // 产品办理信息
    paymentDetailData: mockPaymentDetailData, // 补缴信息
    ids: [], // 选中的id
    title: '查看详情', // 标题
    tableData_no: [] // 无需办理表格数据
})

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockPaymentData];

        if (obj.queryParams.welfareHandler) {
            filteredData = filteredData.filter(item => item.welfareHandler.includes(obj.queryParams.welfareHandler));
        }

        if (obj.queryParams.accountType) {
            // 这里模拟数据中没有accountType字段，实际项目中应该有
        }

        if (obj.queryParams.customer) {
            // 这里模拟数据中没有customer字段，实际项目中应该有
        }

        if (obj.queryParams.welfarePackageName) {
            filteredData = filteredData.filter(item => item.welfarePackageName.includes(obj.queryParams.welfarePackageName));
        }

        if (obj.queryParams.orderNo) {
            filteredData = filteredData.filter(item => item.orderNo.includes(obj.queryParams.orderNo));
        }

        if (obj.queryParams.employeeName) {
            filteredData = filteredData.filter(item => item.employeeName.includes(obj.queryParams.employeeName));
        }

        if (obj.queryParams.idNumber) {
            filteredData = filteredData.filter(item => item.idNumber.includes(obj.queryParams.idNumber));
        }

        if (obj.queryParams.paymentMonth) {
            const paymentMonth = obj.queryParams.paymentMonth.substring(0, 7); // 取年月部分
            filteredData = filteredData.filter(item => item.paymentMonth === paymentMonth);
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 查询个人订单
function handleExport() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行查询');
        return;
    }
    obj.dialogShow = true;
}

// 查看详情
function handleDetail() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行查看');
        return;
    }
    obj.dialogShow2 = true;
}

// 关闭个人订单对话框
function handleClose() {
    obj.dialogShow = false;
}
// 关闭个人订单对话框
function handleClose2() {
    obj.dialogShow2 = false;
}
getList();
</script>
<style lang="scss" scoped></style>