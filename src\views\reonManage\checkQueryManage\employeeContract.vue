<!-- 员工合同 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="签署日期>:" prop="signDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.signDateStart" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="签署日期<:" prop="signDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.signDateEnd" type="date" placeholder="请选择结束日期"
                    clearable />
            </el-form-item>
            <el-form-item label="合同开始日期>:" prop="contractStartDate">
                <el-date-picker class="width220" v-model="obj.queryParams.contractStartDate" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="合同结束日期<:" prop="contractEndDate">
                <el-date-picker class="width220" v-model="obj.queryParams.contractEndDate" type="date"
                    placeholder="请选择结束日期" clearable />
            </el-form-item>
            <el-form-item label="签署状态:" prop="signStatus">
                <el-select class="width220" v-model="obj.queryParams.signStatus" placeholder="请选择签署状态" clearable>
                    <el-option label="待签署" value="0" />
                    <el-option label="已签署" value="1" />
                    <el-option label="已撤回" value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单客服:" prop="dispatchServiceStaff">
                <el-input class="width220" v-model="obj.queryParams.dispatchServiceStaff" placeholder="请输入派单客服"
                    clearable />
            </el-form-item>
            <el-form-item label="接单客服:" prop="receiveServiceStaff">
                <el-input class="width220" v-model="obj.queryParams.receiveServiceStaff" placeholder="请输入接单客服"
                    clearable />
            </el-form-item>
            <el-form-item label="入离职状态:" prop="employmentStatus">
                <el-select class="width220" v-model="obj.queryParams.employmentStatus" placeholder="请选择入离职状态" clearable>
                    <el-option label="在职" value="1" />
                    <el-option label="离职" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="增减员状态:" prop="staffChangeStatus">
                <el-select class="width220" v-model="obj.queryParams.staffChangeStatus" placeholder="请选择增减员状态"
                    clearable>
                    <el-option label="增员" value="1" />
                    <el-option label="减员" value="0" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" @click="handleExport">导出数据</el-button>
            </el-col>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="员工合同编号" align="center" prop="contractCode" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件类型" align="center" prop="idType" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="增减员状态" align="center" prop="staffChangeStatus" />
            <el-table-column label="入离职状态" align="center" prop="employmentStatus" />
            <el-table-column label="正式工资" align="center" prop="formalSalary" />
            <el-table-column label="是否需要呼叫" align="center" prop="needCall" />
            <el-table-column label="签订操作人" align="center" prop="signOperator" />
            <el-table-column label="签署状态" align="center" prop="signStatus" />
            <el-table-column label="签署日期" align="center" prop="signDate" />
            <el-table-column label="工作制" align="center" prop="workSystem" />
            <el-table-column label="劳动合同起始时间" align="center" prop="contractStartDate" />
            <el-table-column label="劳动合同结束时间" align="center" prop="contractEndDate" />
            <el-table-column label="合同版本" align="center" prop="contractVersion" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="终止聘用原因" align="center" prop="terminationReason" />
            <el-table-column label="派单客服" align="center" prop="dispatchServiceStaff" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <Contract v-model:dialogShow="obj.dialogShow" title="查看" :menuName="'employeeContract1'" :isAdd="false"
            :isUpdate="false" :isLook="true" :isRenewal="false" />
    </div>
</template>

<script setup name="EmployeeContract_checkQuery">
import Contract from '@/views/reonManage/components/dialog/contract.vue'


const { proxy } = getCurrentInstance();
const { sys_yes_no } = proxy.useDict('sys_yes_no');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        employeeName: null,
        uniqueId: null,
        idNumber: null,
        signDateStart: null,
        signDateEnd: null,
        contractStartDate: null,
        contractEndDate: null,
        signStatus: null,
        dispatchServiceStaff: null,
        receiveServiceStaff: null,
        employmentStatus: null,
        staffChangeStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogShow: false,//弹窗
    dialogForm: {},
    ids: [],//选中的id
    title: "",//标题
    lookShow: false,//查看模式
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                contractCode: 'CONTRACT001',
                uniqueId: 'EMP001',
                employeeName: '张三',
                idType: '身份证',
                idNumber: '110101199001011234',
                customerCode: 'CUST001',
                customerName: '客户名称1',
                staffChangeStatus: '增员',
                employmentStatus: '在职',
                formalSalary: '10000',
                needCall: '否',
                signOperator: '李四',
                signStatus: '已签署',
                signDate: '2023-01-01',
                workSystem: '标准工时制',
                contractStartDate: '2023-01-01',
                contractEndDate: '2024-01-01',
                contractVersion: 'V1.0',
                contractType: '劳动合同',
                terminationReason: '',
                dispatchServiceStaff: '王五',
                dispatchStartDate: '2023-01-01',
                dispatchEndDate: '2024-01-01',
                hasProbation: '是',
                probationStartDate: '2023-01-01',
                probationMonths: '3',
                probationEndDate: '2023-04-01',
                probationSalary: '8000',
                contractSignPlace: '上海',
                contractVersionPlace: '上海',
                contractPrinciple: '平等自愿',
                entryTime: '2023-01-01 10:00:00',
                employmentUnit: '上海鼎捷数智软件有限公司',
                workPlace: '上海市',
                workPosition: '软件工程师',
                addOperationRecord: '新增操作记录内容',
                updateOperationRecord: '修改操作记录内容',
                renewalOperationRecord: '续签操作记录内容',
                terminateOperationRecord: '',
                resignOperationRecord: '',
                remark: '合同备注信息'
            },
            {
                id: 2,
                contractCode: 'CONTRACT002',
                uniqueId: 'EMP002',
                employeeName: '李四',
                idType: '身份证',
                idNumber: '110101199001021234',
                customerCode: 'CUST002',
                customerName: '客户名称2',
                staffChangeStatus: '增员',
                employmentStatus: '在职',
                formalSalary: '12000',
                needCall: '是',
                signOperator: '王五',
                signStatus: '待签署',
                signDate: '',
                workSystem: '标准工时制',
                contractStartDate: '2023-02-01',
                contractEndDate: '2024-02-01',
                contractVersion: 'V1.0',
                contractType: '劳务派遣协议',
                terminationReason: '',
                dispatchServiceStaff: '赵六',
                dispatchStartDate: '2023-02-01',
                dispatchEndDate: '2024-02-01',
                hasProbation: '否',
                probationStartDate: '',
                probationMonths: '',
                probationEndDate: '',
                probationSalary: '',
                contractSignPlace: '北京',
                contractVersionPlace: '北京',
                contractPrinciple: '平等自愿',
                entryTime: '2023-02-01 10:00:00',
                employmentUnit: '北京鼎捷数智软件有限公司',
                workPlace: '北京市',
                workPosition: '产品经理',
                addOperationRecord: '新增操作记录内容',
                updateOperationRecord: '',
                renewalOperationRecord: '',
                terminateOperationRecord: '',
                resignOperationRecord: '',
                remark: '合同备注信息'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}


// 行双击查看详情
function handleDetail(row) {
    obj.dialogShow = true;
    obj.title = '查看员工合同详情';
    obj.lookShow = true;
    obj.dialogForm = JSON.parse(JSON.stringify(row));
}

// 文件上传成功回调
function uploadedSuccessfully(value) {
    console.log(value);
    proxy.$modal.msgSuccess('文件上传成功');
}
// 数据导出
function handleExport() {
    proxy.$modal.msgSuccess('导出数据成功');
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>