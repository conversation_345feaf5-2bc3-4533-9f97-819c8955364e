import auth from "@/plugins/auth";
import router, { constantRoutes, dynamicRoutes } from "@/router";
import { getRouters } from "@/api/menu";
import Layout from "@/layout/index";
import ParentView from "@/components/ParentView";
import InnerLink from "@/layout/components/InnerLink";

// 匹配views里面所有的.vue文件
const modules = import.meta.glob("./../../views/**/*.vue");

const usePermissionStore = defineStore("permission", {
  state: () => ({
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: [],
  }),
  actions: {
    /**
     * 设置路由
     *
     * 该方法用于动态地设置或更新应用中的路由配置它接受一个路由配置数组作为参数，
     * 将这些新的路由配置添加到现有的路由配置中，以便应用可以使用更新后的路由进行导航和渲染
     *
     * @param {Array} routes - 新的路由配置数组，用于更新应用的路由
     */
    setRoutes(routes) {
      this.addRoutes = routes;
      this.routes = constantRoutes.concat(routes);
    },
    /**
     * 设置默认路由
     *
     * 此方法用于将特定的路由与预定义的常量路由合并，以生成一个完整的默认路由配置
     * 它允许在保持基础路由结构不变的同时，动态地添加新的路由
     *
     * @param {Array} routes - 需要设置为默认路由的数组，包含了路由的配置对象
     */
    setDefaultRoutes(routes) {
      this.defaultRoutes = constantRoutes.concat(routes);
    },
    /**
     * 设置顶部栏路由
     *
     * 此方法用于将一组路由设置到顶部栏组件中它接收一个路由数组作为参数，并将其赋值给组件的topbarRouters属性
     * 这有助于动态地更新顶部栏的导航项
     *
     * @param {Array} routes - 要设置到顶部栏的路由数组
     */
    setTopbarRoutes(routes) {
      this.topbarRouters = routes;
    },
    /**
     * 设置侧边栏路由
     *
     * 此方法用于动态更新侧边栏的路由配置它接受一个路由数组作为参数，
     * 并将其赋值给实例的sidebarRouters属性通过这种方式，可以灵活地
     * 根据不同的需求或条件改变侧边栏的导航项
     *
     * @param {Array} routes - 要设置的侧边栏路由数组
     */
    setSidebarRouters(routes) {
      this.sidebarRouters = routes;
    },
    /**
     * 根据用户角色生成路由
     * 此函数通过后端获取路由数据，然后根据不同的条件过滤和处理这些数据
     * 最后，它将动态路由添加到Vue Router，并设置各种类型的路由用于应用的不同部分
     * @param {Array} roles - 用户角色数组，用于过滤路由
     * @returns {Promise} 返回一个Promise，当路由生成完成时，该Promise将被解决
     */
    generateRoutes(roles) {
      return new Promise((resolve) => {
        // 向后端请求路由数据
        getRouters().then((res) => {
          // 初始化三个变量，分别用于处理不同类型的路由数据
          const sdata = JSON.parse(JSON.stringify(res.data));
          const rdata = JSON.parse(JSON.stringify(res.data));
          const defaultData = JSON.parse(JSON.stringify(res.data));

          // 过滤异步路由数据，生成侧边栏路由
          const sidebarRoutes = filterAsyncRouter(sdata);
          // 过滤异步路由数据，生成重写后的路由，用于实际的路由跳转
          const rewriteRoutes = filterAsyncRouter(rdata, false, true);
          // 过滤异步路由数据，生成默认路由
          const defaultRoutes = filterAsyncRouter(defaultData);
          // 过滤动态路由数据
          const asyncRoutes = filterDynamicRoutes(dynamicRoutes);

          // 将动态路由添加到Vue Router中
          asyncRoutes.forEach((route) => {
            router.addRoute(route);
          });

          // 设置重写后的路由
          this.setRoutes(rewriteRoutes);
          // 设置侧边栏路由，合并常量路由和侧边栏路由
          this.setSidebarRouters(constantRoutes.concat(sidebarRoutes));
          // 设置默认路由
          this.setDefaultRoutes(sidebarRoutes);
          // 设置顶部栏路由
          this.setTopbarRoutes(defaultRoutes);

          // 解决Promise，返回重写后的路由
          resolve(rewriteRoutes);
        });
      });
    },
  },
});

/**
 * 过滤异步路由映射，根据条件处理路由的子路由和组件
 * @param {Array} asyncRouterMap - 异步路由映射，包含多个路由配置对象
 * @param {Boolean} lastRouter - 是否是最后一个路由，用于特定条件下的处理，默认为false
 * @param {Boolean} type - 是否处理子路由，默认为false
 * @returns {Array} - 返回处理后的路由映射
 */
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  // 过滤路由映射，对每个路由进行处理
  return asyncRouterMap.filter((route) => {
    // 如果设置了type且当前路由有子路由，则递归处理子路由
    if (type && route.children) {
      route.children = filterChildren(route.children);
    }
    // 如果路由有组件属性，则根据组件名称替换为相应的组件或加载视图
    if (route.component) {
      if (route.component === "Layout") {
        route.component = Layout;
      } else if (route.component === "ParentView") {
        route.component = ParentView;
      } else if (route.component === "InnerLink") {
        route.component = InnerLink;
      } else {
        route.component = loadView(route.component);
      }
    }
    // 如果路由有子路由且子路由不为空，则递归处理子路由
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type);
    } else {
      // 如果路由没有子路由，则删除children和redirect属性
      delete route["children"];
      delete route["redirect"];
    }
    // 保留当前路由
    return true;
  });
}

/**
 * 根据给定的路由配置映射过滤并生成子路由列表
 *
 * 此函数递归地处理路由配置映射，过滤出所有子路由，并正确设置它们的路径
 * 如果指定了最后一个路由，则会将其路径作为前缀添加到所有子路由的路径中
 *
 * @param {Map} childrenMap - 包含路由配置的映射对象
 * @param {Object} lastRouter - 可选参数，表示最后一个路由配置对象，用于构建子路由路径
 * @returns {Array} 返回一个包含所有子路由配置的数组
 */
function filterChildren(childrenMap, lastRouter = false) {
  // 初始化子路由数组
  var children = [];

  // 遍历路由配置映射
  childrenMap.forEach((el, index) => {
    // 检查当前路由是否有子路由
    if (el.children && el.children.length) {
      // 如果当前路由是父视图且不是最后一个路由，则递归处理其子路由
      if (el.component === "ParentView" && !lastRouter) {
        el.children.forEach((c) => {
          // 更新子路由的路径
          c.path = el.path + "/" + c.path;
          // 如果子路由还有子路由，则递归处理
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c));
            return;
          }
          // 将子路由添加到结果数组中
          children.push(c);
        });
        return;
      }
    }
    // 如果指定了最后一个路由，则更新当前路由的路径
    if (lastRouter) {
      el.path = lastRouter.path + "/" + el.path;
      // 如果当前路由有子路由，则递归处理
      if (el.children && el.children.length) {
        children = children.concat(filterChildren(el.children, el));
        return;
      }
    }
    // 将当前路由添加到结果数组中
    children = children.concat(el);
  });
  // 返回处理后的子路由数组
  return children;
}

// 动态路由遍历，验证是否具备权限
/**
 * 过滤动态路由
 * 该函数根据用户的权限和角色过滤传入的路由数组，只返回用户有权限或角色访问的路由
 * 主要用于控制用户可以访问的路由，从而实现权限控制
 *
 * @param {Array} routes - 需要过滤的路由数组每个路由可以包含权限（permissions）和角色（roles）属性
 * @returns {Array} - 过滤后的路由数组，仅包含用户有权限或角色访问的路由
 */
export function filterDynamicRoutes(routes) {
  // 初始化一个空数组，用于存储过滤后的路由
  const res = [];
  // 遍历每个路由，检查其权限和角色
  routes.forEach((route) => {
    // 如果路由定义了权限，检查用户是否拥有其中一个权限
    if (route.permissions) {
      if (auth.hasPermiOr(route.permissions)) {
        // 如果用户拥有权限，将路由添加到结果数组
        res.push(route);
      }
      // 如果路由定义了角色，检查用户是否拥有其中一个角色
    } else if (route.roles) {
      if (auth.hasRoleOr(route.roles)) {
        // 如果用户拥有角色，将路由添加到结果数组
        res.push(route);
      }
    }
  });
  // 返回过滤后的路由数组
  return res;
}

/**
 * 动态加载视图组件
 *
 * 该函数通过遍历已知的模块路径，寻找匹配的视图组件，并返回一个函数
 * 这个函数当被调用时，会动态导入并返回所请求的视图组件
 *
 * @param {string} view - 需要加载的视图组件名称
 * @returns {Function} - 一个当被调用时会动态加载并返回视图组件的函数
 */
export const loadView = (view) => {
  let res;
  // 遍历所有模块路径，寻找匹配的视图组件
  for (const path in modules) {
    // 提取视图组件的目录路径，去除 'views/' 前缀和 '.vue' 后缀
    const dir = path.split("views/")[1].split(".vue")[0];
    // 当找到匹配的视图组件时
    if (dir === view) {
      // 将 res 设置为一个函数，该函数当被调用时会动态导入并返回对应的视图组件
      res = () => modules[path]();
    }
  }
  // 返回动态加载视图组件的函数
  return res;
};

export default usePermissionStore;
