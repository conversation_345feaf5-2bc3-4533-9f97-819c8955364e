import request from '@/utils/request'

// 查询社保比例维护列表
export function listScale(query) {
  return request({
    url: '/system/scale/list',
    method: 'get',
    params: query
  })
}

// 查询社保比例维护详细
export function getScale(id) {
  return request({
    url: '/system/scale/' + id,
    method: 'get'
  })
}

// 新增社保比例维护
export function addScale(data) {
  return request({
    url: '/system/scale',
    method: 'post',
    data: data
  })
}

// 修改社保比例维护
export function updateScale(data) {
  return request({
    url: '/system/scale',
    method: 'put',
    data: data
  })
}

// 删除社保比例维护
export function delScale(id) {
  return request({
    url: '/system/scale/' + id,
    method: 'delete'
  })
}
