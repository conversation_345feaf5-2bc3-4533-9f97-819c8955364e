<!-- 批量社保基数维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateFrom" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateTo" type="date"
                    placeholder="请选择结束日期" clearable />
            </el-form-item>
            <el-form-item label="导入编号:" prop="importNo">
                <el-input class="width220" v-model="obj.queryParams.importNo" placeholder="请输入导入编号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="导入人:" prop="importBy">
                <el-input class="width220" v-model="obj.queryParams.importBy" placeholder="请输入导入人" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleUpdateTemplate">下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="导入编号" align="center" prop="importNo" />
            <el-table-column label="导入人" align="center" prop="importBy" />
            <el-table-column label="导入时间" align="center" prop="importTime" />
            <el-table-column label="成功记录数" align="center" prop="successCount" />
            <el-table-column label="失败记录数" align="center" prop="failCount" />
            <el-table-column label="导入文件" align="center" prop="importFile">
                <template #default="scope">
                    <el-button link type="primary" @click="handleDownloadFile(scope.row)">下载</el-button>
                </template>
            </el-table-column>
            <el-table-column label="处理状态" align="center" prop="processStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.processStatus === '1' ? 'success' : scope.row.processStatus === '2' ? 'warning' : 'danger'">
                        {{ scope.row.processStatus === '1' ? '处理成功' : scope.row.processStatus === '2' ? '处理中' : '处理失败'
                        }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="历史信息查询" align="center">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 查看详情 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="35%">
            <div class="preview">
                <el-table :data="obj.dialogForm.tableData" border>
                    <el-table-column label="姓名" align="center" prop="name" />
                    <el-table-column label="年龄" align="center" prop="age" />
                    <el-table-column label="社保基数" align="center" prop="base" />
                    <el-table-column label="状态" align="center" prop="status">
                        <template #default="scope">
                            <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
                                {{ scope.row.status }}
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>

<script setup name="BatchSocialSecurityBase_maintenance">

import { listImport, getImport, delImport, addImport, updateImport } from "@/api/reonApi/import";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateFrom: null,
        importDateTo: null,
        importNo: null,
        importBy: null,
    },//查询表单
    total: 0,//总条数

    tableData: [],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}
/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 查看按钮操作 */
function handleDetail(row) {
    obj.dialogForm = {
        tableData: [
            { name: '马六', age: 26, base: '8500', status: '失败' },
            { name: '赵七', age: 28, base: '9500', status: '失败' },
            { name: '钱八', age: 30, base: '10500', status: '失败' }
        ]
    };
    obj.dialogShow = true;
    obj.title = `导入编号 ${row.importNo} 的详细信息`;
}

/** 下载文件 */
function handleDownloadFile(row) {
    proxy.$modal.msgSuccess(`开始下载文件：${row.importFile}`);
    // 模拟下载文件
    const link = document.createElement('a');
    link.href = '#';
    link.setAttribute('download', row.importFile);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 下载模板按钮操作 */
function handleUpdateTemplate() {
    proxy.download('system/import/export', {
        ...obj.queryParams
    }, `社保基数导入模板.xlsx`)
}

getList();
</script>
<style lang="scss" scoped>
.preview {
    width: 100%;
    padding: 12px 6px;
    border: 1px solid rgb(184, 183, 183);
    border-left: 5px solid rgb(184, 183, 183);
    border-radius: 2px;
}
</style>