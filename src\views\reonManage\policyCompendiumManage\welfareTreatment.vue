<!-- 福利待遇主页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="福利待遇类型:" required>
                <el-select class="width220" v-model="obj.queryParams.type" placeholder="请选择">
                    <el-option label="工伤" value="1"></el-option>
                    <el-option label="生育" value="2"></el-option>
                    <el-option label="失业" value="3"></el-option>
                    <el-option label="异地医疗" value="4"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="公司类型:">
                <el-select class="width220" v-model="obj.queryParams.compType" placeholder="请选择">
                    <el-option label="供应商" value="1"></el-option>
                    <el-option label="自有公司" value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="省份:">
                <el-select class="width220" filterable v-model="obj.queryParams.province" placeholder="请选择"
                    @change="handleProvinceChange">
                    <el-option v-for="item in obj.provinceList" :key="item.code" :label="item.name"
                        :value="item.code"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="城市:">
                <el-select class="width220" filterable v-model="obj.queryParams.city" placeholder="请选择"
                    :disabled="!obj.queryParams.province">
                    <el-option v-for="item in obj.cityList" :key="item.code" :label="item.name"
                        :value="item.code"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8"
            v-if="obj.workInjury || obj.fertility || obj.unemployment || obj.remoteMedical">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5" v-if="obj.queryParams.type == '2'">
                <el-button :disabled="obj.single" type="primary" plain icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5" v-if="obj.queryParams.type !== '2'">
                <el-button type="success" plain icon="Edit" @click="handleSave">保存</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">数据导出</el-button>
            </el-col>
        </el-row>

        <!-- 工伤表格 -->
        <el-table v-if="obj.workInjury" v-loading="obj.loading" border style="width: 100%;" :data="obj.tableData">
            <el-table-column label="公司类型" align="center" min-width="160">
                <template #default="scope">
                    <el-select style="width: 100%" v-model="scope.row.compType" placeholder="请选择">
                        <el-option label="供应商" value="1"></el-option>
                        <el-option label="自有公司" value="2"></el-option>
                    </el-select>
                </template>
            </el-table-column>

            <el-table-column label="省" align="center" min-width="160">
                <template #default="scope">
                    <el-select style="width: 100%" filterable v-model="scope.row.province" placeholder="请选择"
                        @change="handleProvinceChange2">
                        <el-option v-for="item in obj.provinceList" :key="item.code" :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="市" align="center" min-width="160">
                <template #default="scope">
                    <el-select style="width: 100%" filterable v-model="scope.row.city" placeholder="请选择">
                        <el-option v-for="item in obj.workInjuryCityList" :key="item.code" :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="人员类型" align="center" min-width="160">
                <template #default="scope">
                    <el-input style="width: 100%" v-model="scope.row.category" placeholder="请输入" clearable></el-input>
                </template>
            </el-table-column>

            <el-table-column label="工伤认定基础信息" align="center">
                <el-table-column label="工伤发生地要求" align="center">
                    <el-table-column label="参保地所在市" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column5" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="参保地所在省" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column6" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="无特殊要求" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column7" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column8" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="工资发放要求" align="center">
                    <el-table-column label="与参保主体一致的银行流水" align="center" min-width="200">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column9" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="客户主体银行发放流水" align="center" min-width="180">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column10" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="我司关联企业银行发放流水" align="center" min-width="200">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column11" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="对银行发放流水主体不限制" align="center" min-width="200">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column12" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="无需提供工资证明" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column13" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="参保单位盖章的工资条即可等" align="center" min-width="200">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column14" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="特殊说明" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column15" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="营业执照要求" align="center">
                    <el-table-column label="实际用工单位营业执照市内" align="center" min-width="200">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column16" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="实际用工单位营业执照省内" align="center" min-width="200">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column17" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="参保单位营业执照" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column18" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="无需提供营业执照" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column19" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column20" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="备案要求" align="center">
                    <el-table-column label="24小时内备案" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column21" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="48小时内备案" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column22" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="无需备案" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column23" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="其他" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column24" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="劳动合同" align="center">
                    <el-table-column label="实际用工单位与员工" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column25" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="参保单位与员工" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column26" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="无需提供" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column27" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="其他" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column28" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="大协议要求" align="center">
                    <el-table-column label="参保单位与实际用工单位" align="center" min-width="180">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column29" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="我司关联企业与实际用工单位" align="center" min-width="200">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column30" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="无需提供" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column31" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table-column>

            <el-table-column label="工伤认定" align="center">
                <el-table-column label="待遇享受时效性要求" align="center" min-width="160">
                    <el-table-column label="增员成功即可" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column32"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="增员成功次日" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column33"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="费用到账后次日" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column34"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="参保次月" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column35"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="其他" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column36"></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="工伤申报时间(发生工伤后，几个工作日内提交申报资料)" align="center" min-width="220">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column37"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="认定主体" align="center">
                    <el-table-column label="参保单位" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column38"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="实际用工单位" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column39"></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="工伤认定材料" align="center">
                    <el-table-column label="客户盖章" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column40"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="参保单位盖章" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column41"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="双方都要盖章" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column42"></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="工伤认定所需资料" align="center">
                    <el-table-column label="工伤办需提供的材料" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column43"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="我参保单位需要提供的资料承诺函、大协议、劳动合同、银行流水等" align="center" min-width="420">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column44"></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="工伤申报结果反馈(资料合格后，XX个工作日)" align="center" min-width="220">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column45"></el-input>
                    </template>
                </el-table-column>
            </el-table-column>

            <el-table-column label="工伤鉴定" align="center">
                <el-table-column label="工伤鉴定前提条件" align="center" min-width="160">
                    <el-table-column label="经治疗伤情相对稳定" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column46"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="存在残疾" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column47"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="影响劳动能力的" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column48"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="其他" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column49"></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="工伤鉴定地点" align="center">
                    <el-table-column label="工伤缴纳地定点医疗机构" align="center" min-width="180">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column50"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="工伤发生地定点医疗机构" align="center" min-width="180">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column51"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="其他(还请具体说明)" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column52"></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="工伤鉴定所需资料" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column53"></el-input>
                    </template>
                </el-table-column>
            </el-table-column>

            <el-table-column label="工伤报销" align="center">
                <el-table-column label="工伤报销所需资料" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column54"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="工伤报销时间要求" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column55"></el-input>
                    </template>
                </el-table-column>
            </el-table-column>

            <el-table-column label="工伤报销、鉴定时间顺序要求" align="center">
                <el-table-column label="先鉴定后报销" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column56"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="先报销后鉴定" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column57"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="鉴定报销无先后顺序" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column58"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="其他" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column59"></el-input>
                    </template>
                </el-table-column>
            </el-table-column>

            <el-table-column label="其他" align="center">
                <el-table-column align="center" min-width="320" label="原单位缴纳了，我司是否可以重复缴纳工伤保险，其他险种不产生费用(如可以，还请标注金额)">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column60"></el-input>
                    </template>
                </el-table-column>
                <el-table-column align="center" min-width="180" label="是否还需他其他特殊资料">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column61"></el-input>
                    </template>
                </el-table-column>
                <el-table-column align="center" min-width="160" label="说明">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column62"></el-input>
                    </template>
                </el-table-column>
            </el-table-column>

            <el-table-column prop="updateTime" align="center" min-width="160" label="更新时间">
                <template #default="scope">
                    <el-input style="width: 100%" v-model="scope.row.column63"></el-input>
                </template>
            </el-table-column>
        </el-table>

        <!-- 生育表格 -->
        <el-table v-if="obj.fertility" v-loading="obj.loading" border height="calc(100vh - 280px)" style="width: 100%;"
            :data="obj.tableData" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="公司类型" align="center" fixed prop="compType"></el-table-column>
            <el-table-column show-overflow-tooltip label="参保地" align="center" fixed min-width="140"
                prop="city"></el-table-column>
            <el-table-column show-overflow-tooltip label="医疗待遇享受生效时间" align="center" min-width="160"
                prop="birthEnjoyTime"></el-table-column>
            <el-table-column show-overflow-tooltip label="无缝衔接是否存在等待期" align="center" min-width="180"
                prop="waitOrNot"></el-table-column>
            <el-table-column show-overflow-tooltip label="无缝衔接等待期时长" align="center" min-width="160"
                prop="waitTime"></el-table-column>
            <el-table-column show-overflow-tooltip label="医保新增后门诊及住院待遇享受注意事项" min-width="260" align="center"
                prop="addNote"></el-table-column>
            <el-table-column show-overflow-tooltip label="办理异地医疗的条件" align="center" min-width="160"
                prop="handleCondition"></el-table-column>
            <el-table-column show-overflow-tooltip label="所需材料" align="center" prop="needFile"></el-table-column>
            <el-table-column show-overflow-tooltip label="办理手续" align="center" prop="checkIn"></el-table-column>
            <el-table-column show-overflow-tooltip label="门诊享受(报销)方式" align="center" min-width="160"
                prop="outEnjoyMethod"></el-table-column>
            <el-table-column show-overflow-tooltip label="门诊报销办理手续及材料" align="center" min-width="180"
                prop="outReimbMaterials"></el-table-column>
            <el-table-column show-overflow-tooltip label="住院享受(报销)方式" align="center" min-width="160"
                prop="hospEnjoyMethod"></el-table-column>
            <el-table-column show-overflow-tooltip label="住院报销办理手续及材料" align="center" min-width="180"
                prop="hospReimbMaterials"></el-table-column>
            <el-table-column show-overflow-tooltip label="异地就医政策备注" align="center" min-width="140"
                prop="offRemark"></el-table-column>
            <el-table-column show-overflow-tooltip label="国家规定" align="center"
                prop="stateRegulations"></el-table-column>
            <el-table-column show-overflow-tooltip label="当地额外奖励假" align="center" min-width="120"
                prop="localAddRewardLeave"></el-table-column>
            <el-table-column show-overflow-tooltip label="国家规定(含产前15天)" align="center" min-width="160"
                prop="stateRegulationsCh"></el-table-column>
            <el-table-column show-overflow-tooltip label="当地额外奖励假" align="center" min-width="120"
                prop="localAddRewardLeaveCh"></el-table-column>
            <el-table-column show-overflow-tooltip label="如果难产，则" align="center" min-width="200"
                prop="dystocia"></el-table-column>
            <el-table-column show-overflow-tooltip label="如果多胞胎，则(每多生1个)" align="center" min-width="200"
                prop="multipleBirth"></el-table-column>
            <el-table-column show-overflow-tooltip label="二胎享受是否同一胎" align="center" min-width="160"
                prop="enjoyFirst"></el-table-column>
            <el-table-column show-overflow-tooltip label="国家规定以为的所有产假类以工作日/日历日计算?" align="center" min-width="200"
                prop="statRegCount"></el-table-column>
            <el-table-column show-overflow-tooltip label="男职工陪产假" align="center" min-width="120"
                prop="manPaternityLeave"></el-table-column>
            <el-table-column show-overflow-tooltip label="异地夫妻男职工陪产假" align="center" min-width="160"
                prop="manPaternityLeaveOff"></el-table-column>
            <el-table-column show-overflow-tooltip label="哺乳假(周岁内)" align="center" min-width="120"
                prop="breastfeedingLeave"></el-table-column>
            <el-table-column show-overflow-tooltip label="流产产假" align="center" prop="maternityLeave"></el-table-column>
            <el-table-column show-overflow-tooltip label="婚假备注" align="center" prop="marRemark"></el-table-column>
            <el-table-column show-overflow-tooltip label="生育报销实际流程" align="center" min-width="140"
                prop="birthSubPro"></el-table-column>
            <el-table-column show-overflow-tooltip label="生育保险享受条件" align="center" min-width="140"
                prop="birthInsureEnjoy"></el-table-column>
            <el-table-column show-overflow-tooltip label="男职工生育享受条件" align="center" min-width="160"
                prop="birthInsureEnjoyMan"></el-table-column>
            <el-table-column show-overflow-tooltip label="生育保险补缴是否计算在内" align="center" min-width="200"
                prop="birthInsurePay"></el-table-column>
            <el-table-column show-overflow-tooltip label="生产医疗待遇" align="center" min-width="140"
                prop="prodTreatment"></el-table-column>
            <el-table-column show-overflow-tooltip label="产前检查费用限额" align="center" min-width="140"
                prop="befBirthFee"></el-table-column>
            <el-table-column show-overflow-tooltip label="产前生育报备时限" align="center" min-width="140"
                prop="befBirthDate"></el-table-column>
            <el-table-column show-overflow-tooltip label="生育津贴核算基数" align="center" min-width="140"
                prop="birthBaseFee"></el-table-column>
            <el-table-column show-overflow-tooltip label="是否由单位履行报销手续" align="center" min-width="180"
                prop="unitPerProce"></el-table-column>
            <el-table-column show-overflow-tooltip label="是否由员工履行报销手续" align="center" min-width="180"
                prop="peoplePerProce"></el-table-column>
            <el-table-column show-overflow-tooltip label="报销费用是否分生育费用和津贴" align="center" min-width="180"
                prop="feeKind"></el-table-column>
            <el-table-column show-overflow-tooltip label="报销款是直接给员工还是给单位" align="center" min-width="180"
                prop="moneyGiven"></el-table-column>
            <el-table-column show-overflow-tooltip label="单位申请还是个人申请" align="center" min-width="160"
                prop="applied"></el-table-column>
            <el-table-column show-overflow-tooltip label="生育津贴是否按员工个人社保缴费基数核算(是/否)" align="center" min-width="180"
                prop="calculate"></el-table-column>
            <el-table-column show-overflow-tooltip label="若否,按什么基数进行核算" align="center" min-width="180"
                prop="baseKind"></el-table-column>
            <el-table-column show-overflow-tooltip label="具体核算方式" align="center" min-width="180"
                prop="calculateMethod"></el-table-column>
            <el-table-column show-overflow-tooltip label="产假天数是否含奖励假" align="center" min-width="180"
                prop="dayType"></el-table-column>
            <el-table-column show-overflow-tooltip label="产后生育津贴申报时限" align="center" min-width="180"
                prop="aftBirthDate"></el-table-column>
            <el-table-column show-overflow-tooltip label="男职工是否有陪产津贴" align="center" min-width="180"
                prop="haveFee"></el-table-column>
        </el-table>

        <!-- 失业表格 -->
        <el-table v-if="obj.unemployment" v-loading="obj.loading" border style="width: 100%" :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <%-- <el-table-column type="selection" min-width="55" align="center"></el-table-column>--%>
                <el-table-column label="公司类型" align="center" min-width="160">
                    <template #default="scope">
                        <el-select style="width: 100%" v-model="scope.row.compType" placeholder="请选择">
                            <el-option label="供应商" value="1"></el-option>
                            <el-option label="自有公司" value="2"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="省" align="center" min-width="160">
                    <template #default="scope">
                        <el-select style="width: 100%" filterable v-model="scope.row.province" placeholder="请选择"
                            @change="handleProvinceChange4">
                            <el-option v-for="item in obj.provinceList" :key="item.code" :label="item.name"
                                :value="item.code"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="市" align="center" min-width="160">
                    <template #default="scope">
                        <el-select style="width: 100%" filterable v-model="scope.row.city" placeholder="请选择">
                            <el-option v-for="item in obj.unemploymentCityList" :key="item.code" :label="item.name"
                                :value="item.code"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="人员类型" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.category" placeholder="请输入"
                            clearable></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="失业金基本要求" align="center" min-width="140">
                    <el-table-column label="失业金缴费年限要求" align="center" min-width="140">
                        <el-table-column label="失业前连续缴满1年" align="center" min-width="140">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column5" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="失业前连续缴满6个月" align="center" min-width="160">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column6" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="失业前累计缴满1年" align="center" min-width="140">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column7" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="其他(还请具体说明)" align="center" min-width="140">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column8" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="实作系统减员原因" align="center">
                        <el-table-column label="非本人意愿离职(辞退、双方协商、合同到期等)" align="center" min-width="320">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column9" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="不受减员原因限制" align="center" min-width="140">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column10" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="备注" align="center" min-width="140">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column11" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="我司盖章材料" align="center">
                        <el-table-column label="劳动合同" align="center">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column12" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="我司抬头的银行发放流水" align="center" min-width="180">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column13" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="解除合同证明" align="center" min-width="120">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column14" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="大协议" align="center">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column15" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="备注" align="center">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column16" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="失业金申领" align="center">
                    <el-table-column label="失业金领取所需材料我司参保≤3个月(广东省≤6个月)" align="center">
                        <el-table-column label="失业金办理方式" align="center">
                            <el-table-column label="线上" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column17" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="线下" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column18" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="无特殊要求" align="center" min-width="120">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column19" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="劳动合同" align="center">
                            <el-table-column label="客户版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column20" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="我司版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column21" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column22" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="员工工资流水" align="center">
                            <el-table-column label="客户发放" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column23" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="我司发放" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column24" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column25" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="员工工资流水最少要求几个月" align="center">
                            <el-table-column label="参保期间所有工资流水" align="center" min-width="160">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column26" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="6个月" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column27" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="3个月" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column28" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column29" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="离职手续(解除协议、解除通知)" align="center">
                            <el-table-column label="客户版本" align="center" min-width="160">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column30" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="我司版本" align="center" min-width="160">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column31" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="情况说明" align="center">
                            <el-table-column label="客户版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column32" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="我司版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column33" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column34" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="客户与我司大协议要求" align="center">
                            <el-table-column label="项目地版本" align="center" min-width="120">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column35" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="接单地版本" align="center" min-width="120">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column36" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column37" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="其它资料" align="center">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column38" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="我司参保>3个月(广东省>6个月)" align="center">
                        <el-table-column label="失业金办理方式" align="center">
                            <el-table-column label="线上" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column39" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="线下" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column40" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="无特殊要求" align="center" min-width="120">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column41" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="劳动合同" align="center">
                            <el-table-column label="客户版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column42" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="我司版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column43" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column44" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="员工工资流水" align="center">
                            <el-table-column label="客户发放" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column45" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="我司发放" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column46" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column47" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="员工工资流水最少要求几个月" align="center">
                            <el-table-column label="参保期间所有工资流水" align="center" min-width="160">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column48" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="6个月" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column49" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="3个月" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column50" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column51" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="离职手续(解除协议、解除通知)" align="center">
                            <el-table-column label="客户版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column52" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="我司版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column53" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="情况说明" align="center">
                            <el-table-column label="客户版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column54" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="我司版本" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column55" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column56" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="客户与我司大协议要求" align="center">
                            <el-table-column label="项目地版本" align="center" min-width="120">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column57" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="接单地版本" align="center" min-width="120">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column58" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="不需要" align="center">
                                <template #default="scope">
                                    <el-input style="width: 100%" v-model="scope.row.column59" placeholder="请输入"
                                        clearable></el-input>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="其它资料" align="center">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column60" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="失业金申领待遇" align="center">
                    <el-table-column label="申领时效(劳动关系解除后,XX天内)" align="center" min-width="240">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column61" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="发放频率" align="center">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column62" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="失业金每月发放标准" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column63" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="失业金申领时长" align="center" min-width="140">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column64" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="异地失业失业可以享受" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column65" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="失业金领取流程" align="center" min-width="140">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column66" placeholder="请输入"
                            clearable></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="更新时间" align="center">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column67" placeholder="请输入"
                            clearable></el-input>
                    </template>
                </el-table-column>
        </el-table>

        <!-- 异地医疗表格 -->
        <el-table v-if="obj.remoteMedical" v-loading="obj.loading" border style="width: 100%" :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <%-- <el-table-column type="selection" min-width="55" align="center"></el-table-column>--%>
                <el-table-column label="公司类型" align="center" min-width="160">
                    <template #default="scope">
                        <el-select style="width: 100%" v-model="scope.row.compType" placeholder="请选择">
                            <el-option label="供应商" value="1"></el-option>
                            <el-option label="自有公司" value="2"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="省" align="center" min-width="160">
                    <template #default="scope">
                        <el-select style="width: 100%" filterable v-model="scope.row.province" placeholder="请选择"
                            @change="handleProvinceChange5">
                            <el-option v-for="item in obj.provinceList" :key="item.code" :label="item.name"
                                :value="item.code"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="市" align="center" min-width="160">
                    <template #default="scope">
                        <el-select style="width: 100%" filterable v-model="scope.row.city" placeholder="请选择">
                            <el-option v-for="item in obj.remoteMedicalCityList" :key="item.code" :label="item.name"
                                :value="item.code"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="人员类型" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.category" placeholder="请输入"
                            clearable></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="异地备案要求" align="center">
                    <el-table-column label="异地参保首月是否必须办理异地备案" align="center" min-width="260">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column5" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="异地备案办理时间要求" align="center">
                        <el-table-column label="参保首月" align="center" min-width="160">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column6" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="就医前即可" align="center" min-width="160">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column7" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="其他(请具体说明)" align="center" min-width="160">
                            <template #default="scope">
                                <el-input style="width: 100%" v-model="scope.row.column8" placeholder="请输入"
                                    clearable></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="异地备案办理方式" align="center">
                    <el-table-column label="员工自行APP办理" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column9" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="单位系统申请" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column10" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="其他(请具体说明)" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column11" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="异地就医政策" align="center">
                    <el-table-column label="办理异地医疗的条件" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column12" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="所需材料" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column13" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="办理手续" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column14" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="工作地可选医院数" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column15" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="社保地可选医院数" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column16" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="门诊享受(报销)方式" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column17" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="门诊报销办理手续及材料" align="center" min-width="180">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column18" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="住院享受(报销)方式" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column19" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="住院报销办理手续及材料" align="center" min-width="180">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column20" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="异地工伤、生育" align="center">
                    <el-table-column label="能否在异地享受工伤" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column21" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="能否在异地享受生育" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column22" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column23" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="大病医疗政策" align="center">
                    <el-table-column label="大病医疗收费标准及金额" align="center" min-width="180">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column24" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="大病医疗收取频率" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column25" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center" min-width="160">
                        <template #default="scope">
                            <el-input style="width: 100%" v-model="scope.row.column26" placeholder="请输入"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="更新时间" align="center" min-width="160">
                    <template #default="scope">
                        <el-input style="width: 100%" v-model="scope.row.column27" placeholder="请输入"
                            clearable></el-input>
                    </template>
                </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination v-if="obj.total" v-model:current-page="obj.queryParams.page"
            v-model:page-size="obj.queryParams.limit" :page-sizes="[50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="obj.total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"></el-pagination>

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="80%" append-to-body>
            <el-form class="formHight" :model="obj.dialogForm" inline label-width="auto">
                <el-row>
                    <el-form-item label="公司类型：" required>
                        <el-select class="width220" :disabled="obj.isEdit" v-model="obj.dialogForm.compType"
                            placeholder="请选择">
                            <el-option label="供应商" value="1"></el-option>
                            <el-option label="自有公司" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="省份：" required>
                        <el-select class="width220" :disabled="obj.isEdit" filterable v-model="obj.dialogForm.province"
                            placeholder="请选择" @change="handleProvinceChange3">
                            <el-option v-for="item in obj.provinceList" :key="item.code" :label="item.name"
                                :value="item.code"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="城市：" required>
                        <el-select class="width220" :disabled="obj.isEdit" filterable v-model="obj.dialogForm.city"
                            placeholder="请选择">
                            <el-option v-for="item in obj.fertilityCityList" :key="item.code" :label="item.name"
                                :value="item.code"></el-option>
                        </el-select>
                    </el-form-item>
                </el-row>
                <el-form-item label="医疗待遇享受生效时间:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.birthEnjoyTime"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="单位扣款成功后预计个人账户显帐时间:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.personPersonTime"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="无缝衔接是否存在等待期:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.waitOrNot"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="无缝衔接等待期时长:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.waitTime"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="医保新增后门诊及住院待遇享受注意事项:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.addNote"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="办理异地医疗的条件:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.handleCondition"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="所需材料:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.needFile"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="办理手续:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.checkIn"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="门诊享受(报销)方式:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.outEnjoyMethod"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="门诊报销办理手段及材料:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.outReimbMaterials"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="住院享受(报销)方式:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.hospEnjoyMethod"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="住院报销办理手续及材料:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.hospReimbMaterials"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="异地就医政策备注:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.offRemark"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="国家规定:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.stateRegulations"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="当地额外奖励假:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.localAddRewardLeave"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="国家规定(含产前15天):" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.stateRegulationsCh"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="当地额外奖励假:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.localAddRewardLeaveCh"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="如果难产,则:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.dystocia"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="如果多胞胎,则(每多生一个):" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.multipleBirth"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="二胎享受是否同一胎:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.enjoyFirst"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="国家规定以为的所有产假类以工作日/日历日计算?">
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.statRegCount"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="男职工陪产假:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.manPaternityLeave"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="异地夫妻男职工陪产假:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.manPaternityLeaveOff"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="哺乳假(周岁内):" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.breastfeedingLeave"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="流产产假:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.maternityLeave"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="婚假备注:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.marRemark"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="生育报销实际流程:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.birthSubPro"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="生育保险享受条件:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.birthInsureEnjoy"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="男职工生育享受条件:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.birthInsureEnjoyMan"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="生育保险补缴是否计算在内:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.birthInsurePay"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="生产医疗待遇:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.prodTreatment"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="产前检查费用限额:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.befBirthFee"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="产前生育报备时限:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.befBirthDate"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="生育津贴核算基数:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.birthBaseFee"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="是否由单位履行报销手续:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.unitPerProce"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="是否由员工履行报销手续:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.peoplePerProce"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="报销费用是否分生育费用和津贴:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.feeKind"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="报销款是直接给员工还是给单位:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.moneyGiven"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="单位申请还是个人申请:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.applied"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="生育津贴是否按员工个人社保缴费基数核算(是/否):" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.calculate"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="若否,按什么基数进行核算:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.baseKind"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="具体核算方式:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.calculateMethod"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="产假天数是否含奖励假:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.dayType"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="产后生育津贴申报时限:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.aftBirthDate"
                        placeholder="请输入"></el-input>
                </el-form-item>
                <el-form-item label="男职工是否有陪产津贴:" required>
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.haveFee"
                        placeholder="请输入"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="WelfareTreatment">

import { ElMessageBox } from 'element-plus'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 响应式数据
const obj = reactive({
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false,

    provinceList: window.top['province'],//省份列表
    cityList: [],// 城市列表
    workInjuryCityList: [],// 工伤城市列表
    fertilityCityList: [],// 生育城市列表
    unemploymentCityList: [],// 失业城市列表
    remoteMedicalCityList: [],// 异地医疗城市列表

    queryParams: {
        page: 1,
        limit: 50,
        type: '', // 福利待遇类型
        compType: '', // 公司类型
        province: '', //省份
        city: '' //城市
    },

    total: 0, // 总条数
    tableData: [], // 列表
    title: '',
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    ids: [], // 选中的id

    workInjury: false,//工伤
    fertility: false,//生育
    unemployment: false,//失业
    remoteMedical: false,//异地医疗

    isEdit: false,//是否编辑
})

/**
 * 获取数据列表
 */
function getList() {
    obj.loading = true
    // 优化：用映射对象统一设置四个布尔值
    const typeMap = {
        '1': { workInjury: true, fertility: false, unemployment: false, remoteMedical: false },
        '2': { workInjury: false, fertility: true, unemployment: false, remoteMedical: false },
        '3': { workInjury: false, fertility: false, unemployment: true, remoteMedical: false },
        '4': { workInjury: false, fertility: false, unemployment: false, remoteMedical: true }
    };
    const typeState = typeMap[obj.queryParams.type] || {
        workInjury: false,
        fertility: false,
        unemployment: false,
        remoteMedical: false
    };
    Object.assign(obj, typeState);
    axios.get(ML.contextPath + '/base/welfare/getAllWelfarePage', { params: obj.queryParams })
        .then(res => {
            obj.loading = false
            obj.tableData = res.data.data
            obj.total = res.data.count

        })
        .catch(err => {
            console.error('获取数据失败:', err);
            ElMessage.error('获取数据失败');
        });
}

/**
 * 查询省份选择
 */
function handleProvinceChange(val) {
    obj.queryParams.city = ''
    obj.cityList = []
    obj.provinceList.forEach((i) => {
        if (i.code === val) {
            obj.cityList = i.children
        }
    });
}

/**
 * 表单重置
 */
function reset() {
    obj.dialogForm = {
        compType: '',
        province: '',
        city: '',
        birthEnjoyTime: '',
        personPersonTime: '',
        waitOrNot: '',
        waitTime: '',
        addNote: '',
        handleCondition: '',
        needFile: '',
        checkIn: '',
        outEnjoyMethod: '',
        outReimbMaterials: '',
        hospEnjoyMethod: '',
        hospReimbMaterials: '',
        offRemark: '',
        stateRegulations: '',
        localAddRewardLeave: '',
        stateRegulationsCh: '',
        localAddRewardLeaveCh: '',
        dystocia: '',
        multipleBirth: '',
        enjoyFirst: '',
        statRegCount: '',
        manPaternityLeave: '',
        manPaternityLeaveOff: '',
        breastfeedingLeave: '',
        maternityLeave: '',
        marRemark: '',
        birthSubPro: '',
        birthInsureEnjoy: '',
        birthInsureEnjoyMan: '',
        birthInsurePay: '',
        prodTreatment: '',
        befBirthFee: '',
        befBirthDate: '',
        birthBaseFee: '',
        unitPerProce: '',
        peoplePerProce: '',
        feeKind: '',
        moneyGiven: '',
        applied: '',
        calculate: '',
        baseKind: '',
        calculateMethod: '',
        dayType: '',
        aftBirthDate: '',
        haveFee: '',
    }
    obj.isEdit = false
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
    obj.tableData = []
    if (!obj.queryParams.type) {
        ElMessage.warning('请选择福利待遇类型!')
        return false;
    }
    obj.queryParams.page = 1;
    getList();
}

/**
 * 重置按钮操作
 */
function resetQuery() {
    obj.queryParams = {
        page: 1,
        limit: 50,
        type: '',
        compType: '',
        province: '',
        city: ''
    };
    obj.workInjury = false
    obj.fertility = false
    obj.unemployment = false
    obj.remoteMedical = false
}

/**
 * 多选框选中数据
 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
    console.log(obj.ids)
}

/**
 * 工伤省份选择
 */
function handleProvinceChange2(val) {
    obj.tableData[obj.tableData.length - 1].city = ''
    obj.workInjuryCityList = []
    obj.provinceList.forEach((i) => {
        if (i.code === val) {
            obj.workInjuryCityList = i.children
        }
    });
}

/**
 * 生育省份选择
 */
function handleProvinceChange3(val) {
    obj.dialogForm.city = ''
    obj.fertilityCityList = []
    obj.provinceList.forEach((i) => {
        if (i.code === val) {
            obj.fertilityCityList = i.children
        }
    });
}

/**
 * 失业省份选择
 */
function handleProvinceChange4(val) {
    obj.tableData[obj.tableData.length - 1].city = ''
    obj.unemploymentCityList = []
    obj.provinceList.forEach((i) => {
        if (i.code === val) {
            obj.unemploymentCityList = i.children
        }
    });
    console.log(obj.unemploymentCityList)
}

/**
 * 异地医疗省份选择
 */
function handleProvinceChange5(val) {
    obj.tableData[obj.tableData.length - 1].city = ''
    obj.remoteMedicalCityList = []
    obj.provinceList.forEach((i) => {
        if (i.code === val) {
            obj.remoteMedicalCityList = i.children
        }
    });
}

/**
 * 新增按钮操作
 */
function handleAdd() {
    reset();
    if (obj.workInjury) {
        if (obj.tableData && obj.tableData.length === 1) {
            ElMessage.warning("一次只能添加一条数据")
            return false
        }
        obj.workInjuryCityList = obj.provinceList[0].children
        obj.tableData.push({
            province: 110000,
            city: 110000
        })
    } else if (obj.fertility) {
        obj.dialogShow = true;
        obj.title = "新增";
    } else if (obj.unemployment) {
        obj.tableData.push({})
    } else if (obj.remoteMedical) {
        obj.tableData.push({})
    }
}

/**
 * 编辑按钮操作
 */
function handleEdit() {
    reset();
    obj.tableData.forEach(item => {
        if (item.id === obj.ids[0]) {
            obj.dialogForm = JSON.parse(JSON.stringify(item))
            obj.dialogShow = true;
            obj.isEdit = true
            obj.title = "编辑";
        }
    });
    obj.provinceList.forEach(i => {
        if (obj.dialogForm.city.includes(i.name)) {
            obj.dialogForm.city = obj.dialogForm.province
            obj.dialogForm.province = i.code
        }
    });
}

/**
 * 保存按钮操作
 */
function handleSave() {
    if (obj.tableData && obj.tableData.length === 0) {
        ElMessage.warning('请添加数据')
        return false
    }
    ElMessageBox.confirm(`确定要保存数据吗？`, '保存确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        let params = {
            paramData: JSON.stringify(obj.tableData),
            type: Number(obj.queryParams.type),
        }
        axios.post(ML.contextPath + '/base/welfare/saveOrUpdateWelfare', params,
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        ).then(res => {
            ElMessage.success('保存成功')
        })
    }).catch(() => {
    });
}

/**
 * 提交表单
 */
function submitForm() {
    if (!obj.dialogForm || Object.keys(obj.dialogForm).length === 0) {
        ElMessage.warning('请填写所有表单项');
        return false;
    }
    for (const key in obj.dialogForm) {
        if (
            obj.dialogForm.hasOwnProperty(key) &&
            (obj.dialogForm[key] === '' || obj.dialogForm[key] === null || obj.dialogForm[key] === undefined)
        ) {
            ElMessage.warning('请填写所有表单项');
            return false;
        }
    }
    obj.dialogForm.province = obj.dialogForm.province.toString().substring(0, 2)
    obj.dialogForm.city = obj.dialogForm.city.toString().substring(2, 7)
    if (obj.isEdit) {
        let params = {
            paramData: JSON.stringify(obj.dialogForm),
            type: 2,
        }
        axios.post(ML.contextPath + '/base/welfare/updateWelfare', params, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
            .then(function (response) {
                if (response.data.code === 0) {
                    ElMessage.success('保存成功')
                    obj.dialogShow = false;
                    getList()
                }

            })
            .catch()
    } else {
        delete obj.dialogForm.compType
        delete obj.dialogForm.province
        delete obj.dialogForm.city
        let params = {
            paramData: JSON.stringify(obj.dialogForm),
            type: 2,
        }
        axios.post(ML.contextPath + '/base/welfare/saveWelfare', params, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
            .then(function (response) {
                if (response.data.code === 0) {
                    ElMessage.success('保存成功')
                    obj.dialogShow = false;
                    getList()
                }

            })
            .catch(function (error) {
                console.log(error);
            });
    }
}

/**
 * 导出按钮操作
 */
function handleExport() {
    ElMessageBox.confirm(`确定要导出数据吗？`, '导出确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        window.location.href = ML.contextPath + '/base/welfare/exportAllWelfarePage';
    })
        .catch(() => {

        });
}

</script>
<style lang="scss" scoped></style>