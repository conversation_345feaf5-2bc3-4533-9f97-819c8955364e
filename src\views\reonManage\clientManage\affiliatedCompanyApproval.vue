<!-- 关联公司审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="typeCode" required>
                <el-select class="width220" filterable v-model="obj.queryParams.province" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="关联公司名称:" prop="typeCode" required>
                <el-select class="width220" filterable v-model="obj.queryParams.province" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleApproval">审批</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="关联公司名称" align="center" prop="affiliateCompanyName" />
            <el-table-column label="所属行业" align="center" prop="industry" />
            <el-table-column label="城市" align="center" prop="city" />
            <el-table-column label="创建人" align="center" prop="creator" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 审批弹窗 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="30%" append-to-body draggable>
            <el-form class="form-container" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="客户名称:" prop="customerName">
                    <el-input class="width220" v-model="obj.dialogForm.customerName" placeholder="请输入客户名称" />
                </el-form-item>
                <el-form-item label="关联公司:" prop="affiliateCompanyName">
                    <el-select class="width220" v-model="obj.dialogForm.affiliateCompanyName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属行业:" prop="industry">
                    <el-select class="width220" v-model="obj.dialogForm.industry" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_industry" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公司性质:" prop="companyNature">
                    <el-select class="width220" v-model="obj.dialogForm.companyNature" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_company_nature" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所在城市:" prop="city">
                    <el-select class="width220" v-model="obj.dialogForm.city" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_city" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="联系人:" prop="contact">
                    <el-input class="width220" v-model="obj.dialogForm.contact" placeholder="请输入联系人" />
                </el-form-item>
                <el-form-item label="联系电话:" prop="contactPhone">
                    <el-input class="width220" v-model="obj.dialogForm.contactPhone" placeholder="请输入联系电话" />
                </el-form-item>
                <el-form-item label="邮箱:" prop="email">
                    <el-input class="width220" v-model="obj.dialogForm.email" placeholder="请输入邮箱" />
                </el-form-item>
                <el-form-item label="联系地址:" prop="contactAddress">
                    <el-input class="width220" v-model="obj.dialogForm.contactAddress" placeholder="请输入联系地址" />
                </el-form-item>
                <el-form-item label="职位:" prop="position">
                    <el-input class="width220" v-model="obj.dialogForm.position" placeholder="请输入职位" />
                </el-form-item>
                <el-form-item label="合同编号:" prop="contractNumber">
                    <el-input class="width220" v-model="obj.dialogForm.contractNumber" placeholder="请输入合同编号" />
                </el-form-item>
                <el-form-item label="合同名称:" prop="contractName">
                    <el-input class="width220" v-model="obj.dialogForm.contractName" placeholder="请输入合同名称" />
                </el-form-item>
                <el-row>
                    <el-form-item label="附件:">
                        <el-upload action="https://jsonplaceholder.typicode.com/posts/" list-type="picture"
                            :on-preview="handlePictureCardPreview" :on-remove="handleRemove">
                            <el-button type="primary">点击上传</el-button>
                        </el-upload>
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-form-item label="驳回意见:" prop="rejectOpinion">
                        <el-input class="width420" type="textarea" :rows="4" v-model="obj.dialogForm.rejectOpinion"
                            placeholder="请输入驳回意见" />
                    </el-form-item>
                </el-row>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup name="AffiliatedCompanyApproval">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    rules: {
        city: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        type: [{ required: true, message: '请选择人员类型', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            customerCode: '123456',
            customerName: '客户名称',
            affiliateCompanyName: '关联公司名称',
            industry: '所属行业',
            city: '城市',
            creator: '创建人',
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 审批 */
function handleApproval() {
    obj.dialogShow = true;
    obj.title = "审批";
}
</script>
<style lang="scss" scoped></style>