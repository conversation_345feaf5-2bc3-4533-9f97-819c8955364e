<!-- 工资支付详情 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="出款公司名称:" prop="companyName">
                <el-input class="width220" readonly v-model="obj.queryParams.companyName" placeholder="请点击"
                    @click="handleSelectCompany" />
            </el-form-item>
            <el-form-item label="收款方:" prop="receiver">
                <el-input class="width220" readonly v-model="obj.queryParams.receiver" placeholder="请点击"
                    @click="handleSelectReceiver" />
            </el-form-item>
            <el-form-item label="支付类型:" prop="paymentType">
                <el-select class="width220" v-model="obj.queryParams.paymentType" placeholder="请选择支付类型" clearable>
                    <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请人:" prop="applicant">
                <el-select class="width220" v-model="obj.queryParams.applicant" placeholder="请选择申请人" clearable>
                    <el-option v-for="item in applicantOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付方式:" prop="paymentMethod">
                <el-select class="width220" v-model="obj.queryParams.paymentMethod" placeholder="请选择支付方式" clearable>
                    <el-option v-for="item in payment_method" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" readonly v-model="obj.queryParams.customerName" placeholder="请点击"
                    @click="handleSelectCustomer" />
            </el-form-item>
            <el-form-item label="申请支付时间>=:" prop="applyTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeStart" type="datetime"
                    placeholder="请选择开始时间" clearable />
            </el-form-item>
            <el-form-item label="申请支付时间<=:" prop="applyTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeEnd" type="datetime"
                    placeholder="请选择结束时间" clearable />
            </el-form-item>
            <el-form-item label="工资支付日期:" prop="paymentDate">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentDate" type="date"
                    placeholder="请选择工资支付日期" clearable />
            </el-form-item>
            <el-form-item label="制单状态:" prop="orderStatus">
                <el-select class="width220" v-model="obj.queryParams.orderStatus" placeholder="请选择制单状态" clearable>
                    <el-option v-for="item in orderStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="审批状态:" prop="approvalStatus">
                <el-select class="width220" v-model="obj.queryParams.approvalStatus" placeholder="请选择审批状态" clearable>
                    <el-option v-for="item in approvalStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否退票重发:" prop="isReissue">
                <el-select class="width220" v-model="obj.queryParams.isReissue" placeholder="请选择是否退票重发" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资复核日期:" prop="reviewDate">
                <el-date-picker class="width220" v-model="obj.queryParams.reviewDate" type="date"
                    placeholder="请选择工资复核日期" clearable />
            </el-form-item>
            <el-form-item label="自有工资复核日期:" prop="ownReviewDate">
                <el-date-picker class="width220" v-model="obj.queryParams.ownReviewDate" type="date"
                    placeholder="请选择自有工资复核日期" clearable />
            </el-form-item>
            <el-form-item label="供应商发薪时间:" prop="supplierPayTime">
                <el-date-picker class="width220" v-model="obj.queryParams.supplierPayTime" type="datetime"
                    placeholder="请选择供应商发薪时间" clearable />
            </el-form-item>
            <el-form-item label="支付关联抬头:" prop="paymentHeader">
                <el-input class="width220" readonly v-model="obj.queryParams.paymentHeader" placeholder="请点击"
                    @click="handleSelectPaymentHeader" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="implement-whitelist-management" />
            <PrintButton print-id="print-table-area" print-title="工资支付详情表格" icon="Printer" />
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column v-if="obj.selectedColumns.includes('customerName')" label="客户名称" align="center" fixed
                min-width="110" sortable prop="customerName" />
            <el-table-column v-if="obj.selectedColumns.includes('receiver')" label="收款方" align="center" fixed
                min-width="110" sortable prop="receiver" />
            <el-table-column v-if="obj.selectedColumns.includes('total')" label="总计" align="center" fixed
                min-width="110" sortable prop="total" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentTotal')" label="支付总计" align="center" fixed
                min-width="110" sortable prop="paymentTotal">
                <template #default="scope">
                    <el-link type="primary" @click="handleViewPaymentTotal(scope.row)">{{
                        scope.row.paymentTotal
                        }}</el-link>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('agencyTotal')" label="代发总计" align="center" fixed
                min-width="110" sortable>
                <template #default="scope">
                    <el-link type="primary" @click="handleViewPaymentTotal(scope.row)">{{
                        scope.row.paymentTotal
                        }}</el-link>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('hasBankEnterpriseNo')" label="是否存在银企直连业务号"
                align="center" fixed min-width="220" sortable prop="hasBankEnterpriseNo" />
            <el-table-column v-if="obj.selectedColumns.includes('verificationAmount')" label="核销金额" align="center" fixed
                min-width="110" sortable>
                <template #default="scope">
                    <el-link type="primary" @click="handleViewPaymentTotal(scope.row)">{{
                        scope.row.paymentTotal
                        }}</el-link>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('matchAmount')" label="匹配金额" align="center" fixed
                min-width="110" sortable>
                <template #default="scope">
                    <el-link type="primary" @click="handleViewPaymentTotal(scope.row)">{{
                        scope.row.paymentTotal
                        }}</el-link>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('createTime')" label="创建时间" align="center"
                min-width="110" sortable prop="createTime" />
            <el-table-column v-if="obj.selectedColumns.includes('businessReferenceNo')" label="业务参考号" align="center"
                min-width="130" sortable prop="businessReferenceNo" />
            <el-table-column v-if="obj.selectedColumns.includes('approvalStatus')" label="审批状态" align="center"
                min-width="110" sortable prop="approvalStatus" />
            <el-table-column v-if="obj.selectedColumns.includes('companyName')" label="出款公司名称" align="center"
                min-width="130" sortable prop="companyName" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentDate')" label="工资支付日期" align="center"
                min-width="130" sortable prop="paymentDate" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentYearMonth')" label="支付所属年月" align="center"
                min-width="130" sortable prop="paymentYearMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('taxTotal')" label="税金合计" align="center" min-width="110"
                sortable prop="taxTotal" />
            <el-table-column v-if="obj.selectedColumns.includes('reviewTime')" label="复核时间" align="center"
                min-width="110" sortable prop="reviewTime" />
            <el-table-column v-if="obj.selectedColumns.includes('ownReviewTime')" label="自有工资复核时间" align="center"
                min-width="160" sortable prop="ownReviewTime" />
            <el-table-column v-if="obj.selectedColumns.includes('applicant')" label="申请人" align="center" min-width="110"
                sortable prop="applicant" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentType')" label="支付类型" align="center"
                min-width="110" sortable prop="paymentType" />
            <el-table-column v-if="obj.selectedColumns.includes('isReissue')" label="是否退票重发" align="center"
                min-width="130" sortable prop="isReissue" />
            <el-table-column v-if="obj.selectedColumns.includes('supplierPayTime')" label="供应商发薪时间" align="center"
                min-width="150" sortable prop="supplierPayTime" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentHeader')" label="支付关联抬头" align="center"
                min-width="130" sortable prop="paymentHeader" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentId')" label="支付ID" align="center"
                min-width="110" sortable prop="paymentId" />
            <el-table-column v-if="obj.selectedColumns.includes('approvalInfo')" label="审批信息" align="center"
                fixed="right" min-width="110" prop="approvalInfo">
                <template #default="scope">
                    <el-button type="primary" link icon="View" @click="handleViewApprovalInfo(scope.row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('staffDetails')" label="查看人员明细" align="center"
                fixed="right" min-width="110" prop="staffDetails">
                <template #default="scope">
                    <el-button type="primary" link icon="View" @click="handleViewStaffDetails(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 打印区域 -->
        <div id="print-table-area" class="print-area">
            <table class="print-table">
                <thead>
                    <tr>
                        <th v-if="obj.selectedColumns.includes('customerName')">客户名称</th>
                        <th v-if="obj.selectedColumns.includes('receiver')">收款方</th>
                        <th v-if="obj.selectedColumns.includes('total')">总计</th>
                        <th v-if="obj.selectedColumns.includes('paymentTotal')">支付总计</th>
                        <th v-if="obj.selectedColumns.includes('agencyTotal')">代发总计</th>
                        <th v-if="obj.selectedColumns.includes('isBankLink')">是否存在银企直连业务号</th>
                        <th v-if="obj.selectedColumns.includes('verificationAmount')">核销金额</th>
                        <th v-if="obj.selectedColumns.includes('matchAmount')">匹配金额</th>
                        <th v-if="obj.selectedColumns.includes('createTime')">创建时间</th>
                        <th v-if="obj.selectedColumns.includes('businessReferenceNo')">业务参考号</th>
                        <th v-if="obj.selectedColumns.includes('approvalStatus')">审批状态</th>
                        <th v-if="obj.selectedColumns.includes('companyName')">出款公司名称</th>
                        <th v-if="obj.selectedColumns.includes('paymentDate')">工资支付日期</th>
                        <th v-if="obj.selectedColumns.includes('paymentYearMonth')">支付所属年月</th>
                        <th v-if="obj.selectedColumns.includes('taxTotal')">税金合计</th>
                        <th v-if="obj.selectedColumns.includes('reviewTime')">复核时间</th>
                        <th v-if="obj.selectedColumns.includes('ownReviewTime')">自有工资复核时间</th>
                        <th v-if="obj.selectedColumns.includes('applicant')">申请人</th>
                        <th v-if="obj.selectedColumns.includes('paymentType')">支付类型</th>
                        <th v-if="obj.selectedColumns.includes('isReissue')">是否退票重发</th>
                        <th v-if="obj.selectedColumns.includes('supplierPayTime')">供应商发薪时间</th>
                        <th v-if="obj.selectedColumns.includes('paymentHeader')">支付关联抬头</th>
                        <th v-if="obj.selectedColumns.includes('paymentId')">支付ID</th>
                        <th v-if="obj.selectedColumns.includes('approvalInfo')">审批信息</th>
                        <th v-if="obj.selectedColumns.includes('staffDetails')">查看人员明细</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in obj.tableData" :key="index">
                        <td v-if="obj.selectedColumns.includes('customerName')">{{ item.customerName }}</td>
                        <td v-if="obj.selectedColumns.includes('receiver')">{{ item.receiver }}</td>
                        <td v-if="obj.selectedColumns.includes('total')">{{ item.total }}</td>
                        <td v-if="obj.selectedColumns.includes('paymentTotal')">{{ item.paymentTotal }}</td>
                        <td v-if="obj.selectedColumns.includes('agencyTotal')">{{ item.agencyTotal }}</td>
                        <td v-if="obj.selectedColumns.includes('isBankLink')">{{ item.isBankLink }}</td>
                        <td v-if="obj.selectedColumns.includes('verificationAmount')">{{ item.verificationAmount }}</td>
                        <td v-if="obj.selectedColumns.includes('matchAmount')">{{ item.matchAmount }}</td>
                        <td v-if="obj.selectedColumns.includes('createTime')">{{ item.createTime }}</td>
                        <td v-if="obj.selectedColumns.includes('businessReferenceNo')">{{ item.businessReferenceNo }}
                        </td>
                        <td v-if="obj.selectedColumns.includes('approvalStatus')">{{ item.approvalStatus }}</td>
                        <td v-if="obj.selectedColumns.includes('companyName')">{{ item.companyName }}</td>
                        <td v-if="obj.selectedColumns.includes('paymentDate')">{{ item.paymentDate }}</td>
                        <td v-if="obj.selectedColumns.includes('paymentYearMonth')">{{ item.paymentYearMonth }}</td>
                        <td v-if="obj.selectedColumns.includes('taxTotal')">{{ item.taxTotal }}</td>
                        <td v-if="obj.selectedColumns.includes('reviewTime')">{{ item.reviewTime }}</td>
                        <td v-if="obj.selectedColumns.includes('ownReviewTime')">{{ item.ownReviewTime }}</td>
                        <td v-if="obj.selectedColumns.includes('applicant')">{{ item.applicant }}</td>
                        <td v-if="obj.selectedColumns.includes('paymentType')">{{ item.paymentType }}</td>
                        <td v-if="obj.selectedColumns.includes('isReissue')">{{ item.isReissue }}</td>
                        <td v-if="obj.selectedColumns.includes('supplierPayTime')">{{ item.supplierPayTime }}</td>
                        <td v-if="obj.selectedColumns.includes('paymentHeader')">{{ item.paymentHeader }}</td>
                        <td v-if="obj.selectedColumns.includes('paymentId')">{{ item.paymentId }}</td>
                        <td v-if="obj.selectedColumns.includes('approvalInfo')">{{ item.approvalInfo }}</td>
                        <td v-if="obj.selectedColumns.includes('staffDetails')">{{ item.staffDetails }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 公司 -->
        <Company v-model:show="obj.companyDialog" @close="closeCompany" @select="selectCompany"
            :selected-value="selectedCompanyValue" />

        <!-- 客户 -->
        <Customer :show="obj.customerDialog" @close="closeCustomer" @select="selectCustomer" />

        <!-- 查看日志 -->
        <el-dialog title="查看日志" v-model="obj.logDialog" width="60%">
            <border-box title="请求体">
                <el-table :data="obj.logTableData" border>
                    <el-table-column label="业务参考号" align="center" prop="businessReferenceNo" />
                    <el-table-column label="转出账号" align="center" prop="outAccount" />
                    <el-table-column label="收方帐号" align="center" prop="inAccount" />
                    <el-table-column label="收方户名" align="center" prop="inAccountName" />
                    <el-table-column label="币种" align="center" prop="currency" />
                    <el-table-column label="交易金额" align="center" prop="amount" />
                    <el-table-column label="用途" align="center" prop="purpose" />
                </el-table>
                <el-divider />
                <el-table :data="obj.logTableData2" border>
                    <el-table-column label="业务参考号" align="center" prop="businessReferenceNo" />
                    <el-table-column label="批次开始标志" align="center" prop="batchStartFlag" />
                    <el-table-column label="批次结束标志" align="center" prop="batchEndFlag" />
                    <el-table-column label="续传次数流程实例号" align="center" prop="processInstanceId" />
                    <el-table-column label="账号" align="center" prop=" account" />
                    <el-table-column label="户名" align="center" prop=" accountName" />
                    <el-table-column label="总金额" align="center" prop="totalAmount" />
                    <el-table-column label="总笔数" align="center" prop="totalNum" />
                    <el-table-column label="本次金额" align="center" prop="currentAmount" />
                    <el-table-column label="本次笔数" align="center" prop="currentNum" />
                    <el-table-column label="币种" align="center" prop="currency" />
                    <el-table-column label="交易类型" align="center" prop="transactionType" />
                    <el-table-column label="用途" align="center" prop="  purpose" />
                    <el-table-column label="期望日期" align="center" prop="expectDate" />
                    <el-table-column label="结算通道" align="center" prop="channel" />
                </el-table>
                <el-divider />
                <el-table :data="obj.logTableData3" border>
                    <el-table-column label="交易序号" align="center" prop="transactionNo" />
                    <el-table-column label="账号" align="center" prop="account" />
                    <el-table-column label="户名" align="center" prop="accountName" />
                    <el-table-column label="交易金额" align="center" prop="  amount" />/>
                    <el-table-column label="他行户口开户行" align="center" prop="otherBankAccount" />
                    <el-table-column label="他行户口开户地" align="center" prop="otherBankAddress" />
                </el-table>
            </border-box>
        </el-dialog>

        <!-- 审批信息 -->
        <PaymentApplication v-model:dialogShow="obj.approvalInfoDialog" title="查看支付详情" :form="obj.approvalInfoForm"
            menuName="wagePaymentDetails" :disabled="true" />

        <!-- 人员明细 -->
        <PersonnelDetails v-model:dialogShow="obj.staffDetailsDialog" title="查看人员明细"
            :tableData="obj.staffDetailsTableData" menuName="wagePaymentDetails" />
    </div>
</template>

<script setup name="WagePaymentDetails">
import { listScale } from "@/api/reonApi/scale";

import PaymentApplication from '@/views/reonManage/components/dialog/paymentApplication.vue'
import PersonnelDetails from '@/views/reonManage/components/dialog/personnelDetails.vue'
import PrintButton from '@/components/PrintButton.vue';
import Company from '@/views/reonManage/components/company.vue';
import Customer from '@/views/reonManage/components/client.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { payment_method, sys_yes_no } = proxy.useDict('payment_method', 'sys_yes_no');

// 支付类型选项
const paymentTypeOptions = [
    { value: '1', label: '工资' },
    { value: '2', label: '自有工资' },
    { value: '3', label: '供应商' },
    { value: '4', label: '异地供应商' }
];

// 制单状态选项
const orderStatusOptions = [
    { value: '1', label: '未制单' },
    { value: '2', label: '已制单' },
    { value: '3', label: '已复核' },
    { value: '4', label: '自由工资已制单' },
    { value: '5', label: '自由工资已复核' }
];

// 审批状态选项
const approvalStatusOptions = [
    { value: '1', label: '驳回' },
    { value: '2', label: '支付完成' },
    { value: '3', label: '终止' },
    { value: '4', label: '草稿' },
    { value: '5', label: '审批中' },
    { value: '6', label: '支付失败' },
    { value: '7', label: '修改中' }
];


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
        receiver: null,
        paymentType: null,
        applicant: null,
        paymentMethod: null,
        customerName: null,
        applyTimeStart: null,
        applyTimeEnd: null,
        paymentDate: null,
        orderStatus: null,
        approvalStatus: null,
        isReissue: null,
        reviewDate: null,
        ownReviewDate: null,
        supplierPayTime: null,
        paymentHeader: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    companyDialog: false,//公司弹窗
    customerDialog: false,//客户弹窗
    logDialog: false,//日志弹窗
    approvalInfoDialog: false,//审批信息弹窗
    approvalInfoForm: {},//审批信息表单
    staffDetailsDialog: false,//人员明细弹窗
    staffDetailsTableData: [
        {
            id: 1,
            name: '张三',
            idNo: '123456789012345678',
            idType: '身份证',
            salaryMonth: '2021-01',
        }
    ],//人员明细表单


    // 列配置
    columnOptions: [
        { label: '客户名称', prop: 'customerName' },
        { label: '收款方', prop: 'receiver' },
        { label: '总计', prop: 'total' },
        { label: '支付总计', prop: 'paymentTotal' },
        { label: '代发总计', prop: 'agencyTotal' },
        { label: '是否存在银企直连业务号', prop: 'hasBankEnterpriseNo' },
        { label: '核销金额', prop: 'verificationAmount' },
        { label: '匹配金额', prop: 'matchAmount' },
        { label: '创建时间', prop: 'createTime' },
        { label: '业务参考号', prop: 'businessReferenceNo' },
        { label: '审批状态', prop: 'approvalStatus' },
        { label: '出款公司名称', prop: 'companyName' },
        { label: '工资支付日期', prop: 'paymentDate' },
        { label: '支付所属年月', prop: 'paymentYearMonth' },
        { label: '税金合计', prop: 'taxTotal' },
        { label: '复核时间', prop: 'reviewTime' },
        { label: '自有工资复核时间', prop: 'ownReviewTime' },
        { label: '申请人', prop: 'applicant' },
        { label: '支付类型', prop: 'paymentType' },
        { label: '是否退票重发', prop: 'isReissue' },
        { label: '供应商发薪时间', prop: 'supplierPayTime' },
        { label: '支付关联抬头', prop: 'paymentHeader' },
        { label: '支付ID', prop: 'paymentId' },
        { label: '审批信息', prop: 'approvalInfo' },
        { label: '查看人员明细', prop: 'staffDetails' },
    ],
    selectedColumns: ['customerName', 'receiver', 'total', 'paymentTotal', 'agencyTotal', 'hasBankEnterpriseNo', 'verificationAmount',
        'matchAmount', 'createTime', 'businessReferenceNo', 'approvalStatus', 'companyName', 'paymentDate', 'paymentYearMonth', 'taxTotal',
        'reviewTime', 'ownReviewTime', 'applicant', 'paymentType', 'isReissue', 'supplierPayTime', 'paymentHeader', 'paymentId', 'approvalInfo',
        'staffDetails'], // 默认显示所有列
})

// 新增：当前选择目标字段
let companySelectTarget = ref('');
// 新增：当前弹窗回显值
const selectedCompanyValue = ref('');

// 打开公司选择弹窗，设置目标字段
function handleSelectCompany() {
    companySelectTarget.value = 'companyName';
    selectedCompanyValue.value = obj.queryParams.companyName;
    obj.companyDialog = true;
}
function handleSelectReceiver() {
    companySelectTarget.value = 'receiver';
    selectedCompanyValue.value = obj.queryParams.receiver;
    obj.companyDialog = true;
}
function handleSelectPaymentHeader() {
    companySelectTarget.value = 'paymentHeader';
    selectedCompanyValue.value = obj.queryParams.paymentHeader;
    obj.companyDialog = true;
}
// 选择公司后写入对应字段
function selectCompany(row) {
    if (companySelectTarget.value) {
        if (row) {
            obj.queryParams[companySelectTarget.value] = row.companyName;
        } else {
            obj.queryParams[companySelectTarget.value] = '';
        }
    }
    obj.companyDialog = false;
    companySelectTarget.value = '';
}
// 关闭弹窗
function closeCompany() {
    obj.companyDialog = false;
    companySelectTarget.value = '';
}

// 打开客户选择弹窗
function handleSelectCustomer() {
    obj.customerDialog = true;
}
// 选择客户后写入对应字段
function selectCustomer(row) {
    obj.queryParams.customerName = row?.name;
    obj.customerDialog = false;
}
// 关闭客户选择弹窗
function closeCustomer() {
    obj.customerDialog = false;
}

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                customerName: '客户A',
                receiver: '收款方A',
                total: 100000.00,
                paymentTotal: 80000.00,
                agencyTotal: 20000.00,
                hasBankEnterpriseNo: '是',
                verificationAmount: 100000.00,
                matchAmount: 100000.00,
                createTime: '2023-05-01 10:30:00',
                businessReferenceNo: 'REF20230501001',
                approvalStatus: '审批通过',
                companyName: '出款公司A',
                paymentDate: '2023-05-15',
                paymentYearMonth: '2023-05',
                taxTotal: 10000.00,
                reviewTime: '2023-05-10 14:20:00',
                ownReviewTime: '2023-05-10 15:30:00',
                applicant: '申请人A',
                paymentType: '工资支付',
                isReissue: '否',
                supplierPayTime: '2023-05-15 09:00:00',
                paymentHeader: '抬头A',
                paymentId: 'PAY20230501001'
            },
            {
                id: 2,
                customerName: '客户B',
                receiver: '收款方B',
                total: 200000.00,
                paymentTotal: 150000.00,
                agencyTotal: 50000.00,
                hasBankEnterpriseNo: '是',
                verificationAmount: 200000.00,
                matchAmount: 200000.00,
                createTime: '2023-05-02 11:45:00',
                businessReferenceNo: 'REF20230502001',
                approvalStatus: '审批通过',
                companyName: '出款公司B',
                paymentDate: '2023-05-16',
                paymentYearMonth: '2023-05',
                taxTotal: 20000.00,
                reviewTime: '2023-05-11 10:15:00',
                ownReviewTime: '2023-05-11 11:30:00',
                applicant: '申请人B',
                paymentType: '工资支付',
                isReissue: '否',
                supplierPayTime: '2023-05-16 09:30:00',
                paymentHeader: '抬头B',
                paymentId: 'PAY20230502001'
            }
        ];
    }, 300);
}

// 查看日志
function handleViewPaymentTotal(row) {
    obj.logDialog = true;
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 查看审批信息
function handleViewApprovalInfo(row) {
    obj.approvalInfoDialog = true;
    obj.approvalInfoForm = row;
}

// 查看人员明细
function handleViewStaffDetails(row) {
    obj.staffDetailsDialog = true;
}
function closeStaffDetails() {
    obj.staffDetailsDialog = false;
}

getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}

// 重写el-link的样式
.el-link.is-underline::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    bottom: 0;
    border-bottom: 1px solid #1890ff;
}
</style>