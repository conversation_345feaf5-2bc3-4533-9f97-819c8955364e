<!-- 社保必填城市列表页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="城市:">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市">
                    <el-option v-for="item in obj.cityList" :key="item.code" :label="item.name"
                        :value="item.code"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple" @click="handleDelete">删除
                </el-button>
            </el-col>
        </el-row>
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" align="center" width="55"></el-table-column>
            <el-table-column label="城市名称" align="center" prop="cityName"></el-table-column>
            <el-table-column label="城市code" align="center" prop="cityCode"></el-table-column>
            <el-table-column label="备注" align="center" prop="remark"></el-table-column>
            <el-table-column label="创建人" align="center" prop="creator"></el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime"></el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" text icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button type="danger" text icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination v-if="obj.total" v-model:current-page="obj.queryParams.page"
            v-model:page-size="obj.queryParams.limit" :page-sizes="[20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="obj.total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"></el-pagination>


        <!-- 新增/修改 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="20%" @close="handleClose">
            <el-form :model="obj.dialogForm" label-width="auto">
                <el-form-item label="城市">
                    <el-select v-model="obj.dialogForm.cityCode" placeholder="请选择城市">
                        <el-option v-for="item in obj.cityList" :key="item.code" :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="备注">
                    <el-input v-model="obj.dialogForm.remark" placeholder="请输入备注"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSubmit">提交</el-button>
                <el-button @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="SocialSecurityRequiredCityList">
import axios from 'axios';
import lodash from 'lodash';
import { getList2 } from '@/api/ceshi';

const { proxy } = getCurrentInstance();

const obj = reactive({
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        page: 1,
        limit: 50,
        cityCode: '',
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogShow: false,//显示新增劳务合同
    dialogForm: {},
    ids: [],//选中的id
    title: "",//标题

    cityList: window.top['area'] || [],

})

/** 列表数据 */
function getList() {
    obj.loading = true;
    let params = {
        page: obj.queryParams.page,
        limit: obj.queryParams.limit,
        paramData: {
            cityCode: obj.queryParams.cityCode,
        }
    };
    params.paramData = JSON.stringify(params.paramData);
    getList2(params).then(res => {
        obj.tableData = res.data.data;
        obj.total = res.data.count;
        obj.loading = false;
    })

}

/** 分页 */
function handleSizeChange(size) {
    obj.queryParams.limit = size
    getList()
}

function handleCurrentChange(page) {
    obj.queryParams.page = page
    getList()
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.page = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    obj.queryParams = {
        page: 1,
        limit: 50,
        cityCode: '',
    };
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 关闭对话框 */
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {};
}

/** 新增 */
function handleAdd() {
    obj.dialogShow = true;
    obj.title = "新增";
}

/** 修改 */
function handleUpdate(row) {
    obj.dialogShow = true;
    obj.title = "修改";
    obj.dialogForm = row.row ? JSON.parse(JSON.stringify(row)) : obj.tableData.find(item => item.id === obj.ids[0]);
    console.log(obj.dialogForm)
}

/** 删除 */
function handleDelete(row) {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        axios.post(ML.contextPath + '/socialSysMandatory/delete', {
            idList: row.id ? [row.id] : obj.ids
        },
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }).then((res) => {
                if (res.data.code === 0) {
                    ElMessage.success('删除成功!');
                    getList();
                }
            })
    })
}

/** 提交 */
function handleSubmit() {
    let paramData = {
        id: obj.dialogForm.id,
        optType: obj.dialogForm.id ? 'edit' : 'add',
        cityCode: obj.dialogForm.cityCode,
        remark: obj.dialogForm.remark,
    }
    paramData = JSON.stringify(paramData);
    axios.post(ML.contextPath + '/socialSysMandatory/saveOrUpdate', {
        paramData: paramData
    },
        {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
        }).then((res) => {
            if (res.data.code === 0) {
                if (obj.dialogForm.id) {
                    ElMessage.success('新增成功!');
                } else {
                    ElMessage.success('修改成功!');
                }
                obj.dialogShow = false;
                getList();
            } else {
                ElMessage.error(res.data.msg);
            }
        })
}

onMounted(() => {
    getList()
})
</script>
<style lang="scss" scoped></style>