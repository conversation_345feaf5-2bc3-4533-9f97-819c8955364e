<!-- 退票录入信息查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="出款公司名称:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="申请人:" prop="applicant">
                <el-input class="width220" v-model="obj.queryParams.applicant" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资支付日期:" prop="paymentDate">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentDate" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="证件号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="姓名:" prop="personName">
                <el-input class="width220" v-model="obj.queryParams.personName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectService">
                <el-select class="width220" v-model="obj.queryParams.projectService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资客服:" prop="salaryService">
                <el-select class="width220" v-model="obj.queryParams.salaryService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="创建人:" prop="creator">
                <el-input class="width220" v-model="obj.queryParams.creator" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="发放状态:" prop="paymentStatus">
                <el-select class="width220" v-model="obj.queryParams.paymentStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格工具栏 -->
        <el-row :gutter="10" class="mb8">
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="refund-ticket-input-information" />
            <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            <el-button type="primary" plain icon="Printer" @click="handlePrint">打印</el-button>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column type="index" align="center" width="55" />
            <el-table-column v-if="obj.selectedColumns.includes('companyName')" label="出款公司名称" align="center"
                prop="companyName">
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('customerName')" label="客户名称" align="center"
                prop="customerName">
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('expenseMonth')" label="费用所属月份" align="center"
                prop="expenseMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentDate')" label="工资支付日期" align="center"
                prop="paymentDate" />
            <el-table-column v-if="obj.selectedColumns.includes('personName')" label="姓名" align="center"
                prop="personName">
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('idNumber')" label="证件号" align="center"
                prop="idNumber" />
            <el-table-column v-if="obj.selectedColumns.includes('bankCardNumber')" label="银行卡号" align="center"
                prop="bankCardNumber" />
            <el-table-column v-if="obj.selectedColumns.includes('actualSalary')" label="实付工资款" align="center"
                prop="actualSalary" />
            <el-table-column v-if="obj.selectedColumns.includes('applicant')" label="申请人" align="center"
                prop="applicant" />
            <el-table-column v-if="obj.selectedColumns.includes('entryStatus')" label="录入" align="center"
                prop="entryStatus" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentId')" label="支付ID" align="center"
                prop="paymentId" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentStatus')" label="薪资发放状态" align="center"
                prop="paymentStatus">
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('projectService')" label="项目客服" align="center"
                prop="projectService" />
            <el-table-column v-if="obj.selectedColumns.includes('salaryService')" label="薪资客服" align="center"
                prop="salaryService" />
            <el-table-column v-if="obj.selectedColumns.includes('creator')" label="创建人" align="center" prop="creator" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="RefundTicketInputInformation_query">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 客服选项
const serviceOptions = [
    { value: '1', label: '客服A' },
    { value: '2', label: '客服B' },
    { value: '3', label: '客服C' }
];

// 状态选项
const statusOptions = [
    { value: '0', label: '未发放' },
    { value: '1', label: '已发放' },
    { value: '2', label: '发放失败' }
];

// 文件列表
const fileList = ref([]);

// 表单验证规则
const rules = {
    companyName: [
        { required: true, message: '请输入出款公司', trigger: 'blur' }
    ],
    customerName: [
        { required: true, message: '请选择客户名称', trigger: 'change' }
    ],
    file: [
        { required: true, message: '请上传文件', trigger: 'change' }
    ]
};

// 列配置选项
const columnOptions = [
    { prop: 'companyName', label: '出款公司名称' },
    { prop: 'customerName', label: '客户名称' },
    { prop: 'expenseMonth', label: '费用所属月份' },
    { prop: 'paymentDate', label: '工资支付日期' },
    { prop: 'personName', label: '姓名' },
    { prop: 'idNumber', label: '证件号' },
    { prop: 'bankCardNumber', label: '银行卡号' },
    { prop: 'actualSalary', label: '实付工资款' },
    { prop: 'applicant', label: '申请人' },
    { prop: 'entryStatus', label: '录入' },
    { prop: 'paymentId', label: '支付ID' },
    { prop: 'paymentStatus', label: '薪资发放状态' },
    { prop: 'projectService', label: '项目客服' },
    { prop: 'salaryService', label: '薪资客服' },
    { prop: 'creator', label: '创建人' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null, // 出款公司名称
        applicant: null, // 申请人
        customerName: null, // 客户名称
        paymentDate: null, // 工资支付日期
        idNumber: null, // 证件号
        personName: null, // 姓名
        projectService: null, // 项目客服
        salaryService: null, // 薪资客服
        creator: null, // 创建人
        paymentStatus: null, // 发放状态
    }, // 查询表单
    filterParams: {
        companyName: '',
        customerName: '',
        personName: '',
        paymentStatus: ''
    }, // 列筛选参数
    columnOptions: columnOptions, // 列配置选项
    selectedColumns: columnOptions.map(col => col.prop), // 选中的列
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {
        companyName: '',
        customerName: '',
        remark: ''
    }, // 导入表单
    dialogShow: false, // 导入弹窗
    exportDialogShow: false, // 导出弹窗
    exportForm: {
        columns: ['companyName', 'customerName', 'personName', 'idNumber', 'actualSalary'],
        fileName: '退票录入信息',
        fileType: 'xlsx'
    }, // 导出表单
    ids: [], // 选中的id
    title: "", // 标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                companyName: '某某科技有限公司',
                customerName: '客户A',
                expenseMonth: '2023-05',
                paymentDate: '2023-05-15',
                personName: '张三',
                idNumber: '110101199001011234',
                bankCardNumber: '****************',
                actualSalary: 8500.00,
                applicant: '李四',
                entryStatus: '已录入',
                paymentId: 'PAY20230515001',
                paymentStatus: '已发放',
                projectService: '王五',
                salaryService: '赵六',
                creator: '管理员'
            },
            {
                id: 2,
                companyName: '某某信息技术有限公司',
                customerName: '客户B',
                expenseMonth: '2023-05',
                paymentDate: '2023-05-16',
                personName: '李四',
                idNumber: '110101199002022345',
                bankCardNumber: '****************',
                actualSalary: 9200.00,
                applicant: '王五',
                entryStatus: '已录入',
                paymentId: 'PAY20230516001',
                paymentStatus: '已发放',
                projectService: '赵六',
                salaryService: '钱七',
                creator: '管理员'
            },
            {
                id: 3,
                companyName: '某某软件有限公司',
                customerName: '客户C',
                expenseMonth: '2023-05',
                paymentDate: '2023-05-17',
                personName: '王五',
                idNumber: '110101199003033456',
                bankCardNumber: '****************',
                actualSalary: 7800.00,
                applicant: '赵六',
                entryStatus: '已录入',
                paymentId: 'PAY20230517001',
                paymentStatus: '未发放',
                projectService: '钱七',
                salaryService: '孙八',
                creator: '管理员'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 文件变化处理 */
function handleFileChange(file) {
    fileList.value = [file];
}

/** 提交导入 */
function submitImport() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (fileList.value.length === 0) {
                proxy.$modal.msgError('请选择要上传的文件');
                return;
            }
            // 实际项目中应该调用API进行文件上传
            proxy.$modal.msgSuccess('文件上传成功');
            obj.dialogShow = false;
        }
    });
}


/** 导出按钮操作 */
function handleExport() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请至少选择一条记录');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 打印按钮操作 */
function handlePrint() {
    proxy.$modal.msgSuccess('打印成功');
}

/** 确认导出 */
function confirmExport() {
    if (obj.exportForm.columns.length === 0) {
        proxy.$modal.msgError('请选择要导出的列');
        return;
    }
    // 实际项目中应该调用API进行导出
    proxy.$modal.msgSuccess('导出成功');
    obj.exportDialogShow = false;

    // 模拟下载文件
    const fileName = obj.exportForm.fileName + '.' + obj.exportForm.fileType;
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>