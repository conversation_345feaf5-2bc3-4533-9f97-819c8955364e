<!-- 薪资单发送 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="工资所属月:" prop="salaryMonth">
                <el-input class="width220" v-model="obj.queryParams.salaryMonth" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资类别编号:" prop="salaryTypeCode">
                <el-select class="width220" v-model="obj.queryParams.salaryTypeCode" placeholder="请选择" clearable>
                    <el-option v-for="item in salaryTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="发放名称:" prop="paymentName">
                <el-input class="width220" v-model="obj.queryParams.paymentName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件编号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="电子邮件:" prop="email">
                <el-input class="width220" v-model="obj.queryParams.email" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row>
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Message" :disabled="obj.multiple"
                    @click="handleSendEmail">发送邮件</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户编号" align="center" prop="customerId" />
            <el-table-column label="薪资类别编号" align="center" prop="salaryTypeCode" />
            <el-table-column label="发放名称" align="center" prop="paymentName" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="工资单格式" align="center" prop="payrollFormat" />
            <el-table-column label="电子邮件" align="center" prop="email" />
            <el-table-column label="工资所属年月" align="center" prop="salaryMonth" />
            <el-table-column label="工资计税年月" align="center" prop="taxMonth" />
            <el-table-column label="客户账单年月" align="center" prop="customerBillMonth" />
            <el-table-column label="是否已发送邮件" align="center" prop="isSent">
                <template #default="scope">
                    <el-tag :type="scope.row.isSent === '1' ? 'success' : 'info'">
                        {{ scope.row.isSent === '1' ? '已发送' : '未发送' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="发送邮件次数" align="center" prop="sentCount" />
            <el-table-column label="收入合计" align="center" prop="totalIncome" />
            <el-table-column label="扣款合计" align="center" prop="totalDeduction" />
            <el-table-column label="实发合计" align="center" prop="actualTotal" />
            <el-table-column label="应税工资" align="center" prop="taxableSalary" />
            <el-table-column label="本次扣税" align="center" prop="currentTax" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="PayrollSent">
import { useAreaStore } from '@/store/modules/area'

// 不再使用areaStore，但保留导入以便将来可能的扩展
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客户选项
const customerOptions = [
    { value: 'C001', label: '北京科技有限公司' },
    { value: 'C002', label: '上海贸易有限公司' },
    { value: 'C003', label: '广州电子有限公司' },
    { value: 'C004', label: '深圳科技有限公司' },
    { value: 'C005', label: '成都信息有限公司' }
];

// 薪资类别选项
const salaryTypeOptions = [
    { value: 'ST001', label: '月薪' },
    { value: 'ST002', label: '年薪' },
    { value: 'ST003', label: '绩效工资' },
    { value: 'ST004', label: '奖金' },
    { value: 'ST005', label: '津贴' }
];


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        salaryMonth: null,
        customerName: null,
        salaryTypeCode: null,
        paymentName: null,
        employeeName: null,
        uniqueId: null,
        idNumber: null,
        email: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        obj.tableData = [
            {
                id: 1,
                customerId: 'C001',
                salaryTypeCode: 'ST001',
                paymentName: '2023年6月工资',
                employeeName: '张三',
                uniqueId: 'UID001',
                payrollFormat: 'PDF',
                email: '<EMAIL>',
                salaryMonth: '2023-06',
                taxMonth: '2023-06',
                customerBillMonth: '2023-06',
                isSent: '1',
                sentCount: 1,
                totalIncome: 15000,
                totalDeduction: 3000,
                actualTotal: 12000,
                taxableSalary: 10000,
                currentTax: 1000
            },
            {
                id: 2,
                customerId: 'C002',
                salaryTypeCode: 'ST001',
                paymentName: '2023年6月工资',
                employeeName: '李四',
                uniqueId: 'UID002',
                payrollFormat: 'PDF',
                email: '<EMAIL>',
                salaryMonth: '2023-06',
                taxMonth: '2023-06',
                customerBillMonth: '2023-06',
                isSent: '0',
                sentCount: 0,
                totalIncome: 18000,
                totalDeduction: 4000,
                actualTotal: 14000,
                taxableSalary: 12000,
                currentTax: 1500
            },
            {
                id: 3,
                customerId: 'C003',
                salaryTypeCode: 'ST002',
                paymentName: '2023年6月工资',
                employeeName: '王五',
                uniqueId: 'UID003',
                payrollFormat: 'PDF',
                email: '<EMAIL>',
                salaryMonth: '2023-06',
                taxMonth: '2023-06',
                customerBillMonth: '2023-06',
                isSent: '1',
                sentCount: 2,
                totalIncome: 20000,
                totalDeduction: 5000,
                actualTotal: 15000,
                taxableSalary: 15000,
                currentTax: 2000
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}





/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 发送邮件
function handleSendEmail() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择需要发送邮件的记录");
        return;
    }

    proxy.$modal.confirm("确认要发送邮件给这些员工吗？").then(() => {
        // 模拟发送邮件操作
        setTimeout(() => {
            // 更新已选择记录的发送状态
            obj.tableData.forEach(item => {
                if (obj.ids.includes(item.id)) {
                    item.isSent = '1';
                    item.sentCount += 1;
                }
            });
            proxy.$modal.msgSuccess("邮件发送成功");
        }, 500);
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped></style>