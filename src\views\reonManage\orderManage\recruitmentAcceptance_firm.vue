<!-- 企业端增员受理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="订单编号:" prop="orderCode">
                <el-input class="width220" v-model="obj.queryParams.orderCode" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="企业端增员状态:" prop="recruitmentStatus">
                <el-select class="width220" v-model="obj.queryParams.recruitmentStatus" placeholder="请选择" clearable>
                    <el-option label="未受理" value="0" />
                    <el-option label="已受理" value="1" />
                    <el-option label="已提交" value="2" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleAccept">受理</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleModify">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleSubmit">确认提交</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="View" @click="handleExport">导出</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail" :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员编号" align="center" prop="employeeCode" />
            <el-table-column label="订单编号" align="center" prop="orderCode" />
            <el-table-column label="增员状态" align="center" prop="recruitmentStatus" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="服务性质" align="center" prop="serviceNature" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="城市" align="center" prop="city" />
            <el-table-column label="是否单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="入职日期" align="center" prop="entryDate" />
            <el-table-column label="申请入职时间" align="center" prop="applyEntryTime" />
            <el-table-column label="企业端增员状态" align="center" prop="enterpriseRecruitmentStatus" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" text icon="View" @click="handleDetail(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 受理弹窗 -->
        <order-contract-reserve-fund type="RecruitmentAcceptance_firm" v-model:dialogShow="obj.dialogShow" title="增员受理"
            :form="obj.dialogForm" :tableData="obj.tableData" :tableData_no="obj.tableData_no" :isDetail="obj.isDetail"
            :isEdit="obj.isEdit" />
    </div>
</template>

<script setup name="RecruitmentAcceptance_firm">

import { listScale } from "@/api/reonApi/scale";
import OrderContractReserveFund from '@/views/reonManage/components/dialog/orderContractReserveFund.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        employeeName: null,
        customerName: null,
        recruitmentStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    tableData_no: [],//未受理列表
    dialogForm: {},//弹窗表单
    dialogShow: false,//弹窗
    isDetail: false,//是否详情
    isEdit: false,//是否编辑
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeCode: 'GY20230001',
                orderCode: 'DD20230001',
                recruitmentStatus: '已受理',
                employeeName: '张三',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                serviceNature: '服务性质1',
                smallContractName: '小合同名称1',
                city: '北京',
                isSingleAccount: '是',
                entryDate: '2023-01-01',
                applyEntryTime: '2023-01-01 10:00:00',
                enterpriseRecruitmentStatus: '已受理'
            },
            {
                id: 2,
                employeeCode: 'GY20230002',
                orderCode: 'DD20230002',
                recruitmentStatus: '未受理',
                employeeName: '李四',
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                serviceNature: '服务性质2',
                smallContractName: '小合同名称2',
                city: '上海',
                isSingleAccount: '否',
                entryDate: '2023-02-01',
                applyEntryTime: '2023-02-01 10:00:00',
                enterpriseRecruitmentStatus: '未受理'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 表格行样式 */
function tableRowClassName({ row }) {

    if (row.enterpriseRecruitmentStatus === '未受理') {
        return 'warning-row';
    }
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 受理按钮操作 */
function handleAccept() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要受理的数据');
        return;
    }
    obj.dialogShow = true;
    obj.isDetail = false;
    obj.isEdit = false;
    obj.title = '增员受理';
}

/** 修改按钮操作 */
function handleModify() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条数据进行修改');
        return;
    }
    obj.dialogShow = true;
    obj.isDetail = false;
    obj.isEdit = true;
    obj.title = '修改增员受理';
}

/** 确认提交按钮操作 */
function handleSubmit() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要提交的数据');
        return;
    }
    proxy.$modal.confirm('确认要提交选中的数据吗？').then(() => {
        proxy.$modal.msgSuccess('提交成功');
        getList();
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 详情按钮操作 */
function handleDetail(row) {
    obj.dialogShow = true;
    obj.isDetail = true;
    obj.isEdit = false;
    obj.title = '增员详情';
    obj.dialogForm = row || {};
}

/** 关闭弹窗 */
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {};
    obj.isDetail = false;
    obj.isEdit = false;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped>
:deep(.warning-row) {
    background-color: #D4E5EF;
}
</style>