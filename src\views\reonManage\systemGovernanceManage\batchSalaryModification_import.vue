<!-- 批量薪资修改导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="typeCode">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateFrom" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="typeCode">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateTo" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="warning" plain icon='Upload' @click="handleExport">导入</el-button>
                <el-button type="primary" plain icon='Download' @click="handleDownloadTemplate">下载模版</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <ExportTable2 :tableData="obj.tableData" :loading="obj.loading" :total="obj.total"
            :page="obj.queryParams.pageNum" :limit="obj.queryParams.pageSize" menuName="batchSalaryModification_import"
            @row-dblclick="handleRowDblClick" @handlePagination="handlePagination" />
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" title="上传导入数据" :dialogForm="obj.dialogForm"
            :type="'batchSalaryModification_import'" />

        <!-- 历史信息查看 -->
        <HistoricalInformation :dialogShow="obj.dialogShow2" :title="obj.title" :tableData="obj.tableData"
            @close="handleHistoryClose" />
    </div>
</template>

<script setup name="BatchSalaryModification_import">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数

    tableData: [],//列表
    historyInfo: [],//历史信息
    dialogShow: false,//导入弹窗
    dialogForm: {},//导入表单
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        obj.tableData = [{
            importNumber: '123',
            importPerson: '张三',
            importTime: '2021-01-01',
            remark: '备注',
            successCount: '10',
            failCount: '1',
            importFile: '123.xlsx',
            status: '成功',
        }];
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
// 双击行
function handleRowDblClick(row) {
    obj.dialogShow2 = true;
    obj.title = '历史信息查看';
}

// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}

// 数据导出
function handleExport() {
    obj.dialogShow = true;
}

// 下载模版
function handleDownloadTemplate() {
    console.log('下载模版');
}

// 关闭导入
function handleClose() {
    obj.dialogShow = false;
}

// 历史信息查看
function handleHistoryInfo(row) {
    console.log(row);
}
/** 历史信息关闭 */
function handleHistoryClose() {
    obj.dialogShow2 = false;
}
getList();
</script>
<style lang="scss" scoped></style>