<!-- 社保比例维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="比例名称:" prop="scaleName">
                <el-input class="width220" v-model="obj.queryParams.scaleName" placeholder="请输入比例名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="比例编号:" prop="scaleNo">
                <el-input class="width220" v-model="obj.queryParams.scaleNo" placeholder="请输入比例编号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="所属城市:" prop="cityCode">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="产品:" prop="productType">
                <el-select class="width220" filterable v-model="obj.queryParams.productType" placeholder="请选择产品"
                    clearable>
                    <el-option v-for="item in productTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="状态:" prop="status">
                <el-select class="width220" filterable v-model="obj.queryParams.status" placeholder="请选择状态" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="View" :disabled="obj.single" @click="handleDetail">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="CircleClose" :disabled="obj.single"
                    @click="handlePause">暂停</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button color="#626aef" plain :disabled="obj.single" @click="handleAdjust">调整订单数据</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column align="center" type="expand">
                <template #default="props">
                    <el-table :data="props.row.family" border>
                        <el-table-column label="企业最低基数" align="center" prop="companyMaxBase" />
                        <el-table-column label="企业最高基数" align="center" prop="companyMinBase" />
                        <el-table-column label="个人最低基数" align="center" prop="personalMaxBase" />
                        <el-table-column label="个人最高基数" align="center" prop="personalMinBase" />
                        <el-table-column label="最近起始月" align="center" prop="startMonth" />
                        <el-table-column label="最近截止月" align="center" prop="endMonth" />
                    </el-table>
                </template>
            </el-table-column>
            <el-table-column type="selection" width="55" />
            <el-table-column label="所属城市" align="center" prop="cityName" />
            <el-table-column label="产品" align="center" prop="productName" />
            <el-table-column label="比例编号" align="center" prop="email" />
            <el-table-column label="比例名称" align="center" prop="name" />
            <el-table-column label="企业比例" align="center" prop="companyScale" />
            <el-table-column label="个人比例" align="center" prop="personalScale" />
            <el-table-column label="企业附加" align="center" prop="companyExtra" />
            <el-table-column label="个人附加" align="center" prop="personalExtra" />
            <el-table-column label="企业精确值" align="center" width="100" prop="companyPrecision" />
            <el-table-column label="个人精确值" align="center" width="100" prop="personalPrecision" />
            <el-table-column label="企业计算方式" align="center" width="120" prop="companyCalcMethod" />
            <el-table-column label="个人计算方式" align="center" width="120" prop="personalCalcMethod" />
            <el-table-column label="收费频率" align="center" prop="chargeFrequency" />
            <el-table-column label="年缴月份" align="center" prop="yearPayMonth" />
            <el-table-column label="操作" align="center" width="220">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="70%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="所属城市:" prop="city">
                    <el-select :disabled="obj.isDetails" class="width220" filterable v-model="obj.dialogForm.city"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="产品:" prop="productType">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.productType" placeholder="请选择" clearable>
                        <el-option v-for="item in productTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="比例名称:" prop="scaleName">
                    <el-input :disabled="obj.isDetails" class="width420" v-model="obj.dialogForm.scaleName"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="企业比例:" prop="companyRatio">
                    <el-input :disabled="obj.isDetails" style="width: calc(220px - 15px);"
                        v-model="obj.dialogForm.companyRatio" placeholder="请输入" />
                    <span class="ml5">%</span>
                </el-form-item>
                <el-form-item label="企业附加:" prop="companyAdditional">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.companyAdditional"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="个人比例:" prop="personalRatio">
                    <el-input :disabled="obj.isDetails" style="width: calc(220px - 15px);"
                        v-model="obj.dialogForm.personalRatio" placeholder="请输入" />
                    <span class="ml5">%</span>
                </el-form-item>
                <el-form-item label="个人附加:" prop="personalAdditional">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.personalAdditional"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="企业精确值:" prop="companyPreciseValue">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.companyPreciseValue" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="企业计算方式:" prop="companyCalculationMethod">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.companyCalculationMethod" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="个人精确值:" prop="personalPreciseValue">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.personalPreciseValue" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="个人计算方式:" prop="personalCalculationMethod">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.personalCalculationMethod" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="收费频率:" prop="chargeFrequency">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.chargeFrequency" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="年缴计算顺序:" prop="annualPaymentOrder">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.annualPaymentOrder" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否按比例:" prop="isRatioBased">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.isRatioBased" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="年缴月份:" prop="annualPaymentMonth">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.annualPaymentMonth"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="年缴每月企业费用:" prop="annualMonthlyCompanyFee">
                    <el-input :disabled="obj.isDetails" class="width220"
                        v-model="obj.dialogForm.annualMonthlyCompanyFee" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="年缴每月个人费用:" prop="annualMonthlyPersonalFee">
                    <el-input :disabled="obj.isDetails" class="width220"
                        v-model="obj.dialogForm.annualMonthlyPersonalFee" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="特殊比例:" prop="specialRatio">
                    <el-select :disabled="obj.isDetails" class="width220" filterable
                        v-model="obj.dialogForm.specialRatio" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-row class="mb10">
                    <el-col :span="24" class="mb8" v-if="!obj.isDetails">
                        <el-button icon="Plus" type="primary" @click="addFormToTable"></el-button>
                        <el-button icon="Delete" type="primary" @click="delFormToTable"></el-button>
                    </el-col>
                    <el-table ref="formTableRef" :data="obj.dialogForm.formTable" border
                        @selection-change="selectFormToTable" @row-click="rowClickFormToTable">
                        <el-table-column type="selection" width="55" />
                        <el-table-column type="index" label="序号" align="center" width="60" />
                        <el-table-column label="起始月" align="center" width="150" prop="startMonth">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.startMonth" type="month"
                                    placeholder="选择日期" />
                            </template>
                        </el-table-column>
                        <el-table-column label="截止月" align="center" width="150" prop="endMonth">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.endMonth" type="month"
                                    placeholder="选择日期" />
                            </template>
                        </el-table-column>
                        <el-table-column label="企业最高基数" align="center" width="120" prop="companyMaxBase">
                            <template #default="scope">
                                <el-input :disabled="obj.isDetails" style="width: 100%;"
                                    v-model="scope.row.companyMaxBase" placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="企业最低基数" align="center" width="120" prop="companyMinBase">
                            <template #default="scope">
                                <el-input :disabled="obj.isDetails" style="width: 100%;"
                                    v-model="scope.row.companyMinBase" placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="个人最高基数" align="center" width="120" prop="personalMaxBase">
                            <template #default="scope">
                                <el-input :disabled="obj.isDetails" style="width: 100%;"
                                    v-model="scope.row.personalMaxBase" placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="个人最低基数" align="center" width="120" prop="personalMinBase">
                            <template #default="scope">
                                <el-input :disabled="obj.isDetails" style="width: 100%;"
                                    v-model="scope.row.personalMinBase" placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="年度平均工资" align="center" width="120" prop="avgBase">
                            <template #default="scope">
                                <el-input :disabled="obj.isDetails" style="width: 100%;" v-model="scope.row.avgBase"
                                    placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="年度最低工资" align="center" width="120" prop="minYearlyWage">
                            <template #default="scope">
                                <el-input :disabled="obj.isDetails" style="width: 100%;"
                                    v-model="scope.row.minYearlyWage" placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="调整类型" align="center" prop="adjustType">
                            <template #default="scope">
                                <el-select :disabled="obj.isDetails" style="width: 100%;" filterable
                                    v-model="scope.row.adjustType" placeholder="请选择" clearable>
                                    <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                        :value="item.code" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="政策链接" align="center" prop="policyLink">
                            <template #default="scope">
                                <el-input :disabled="obj.isDetails" style="width: 100%;" v-model="scope.row.policyLink"
                                    placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="政策文件" align="center" prop="policyFile">
                            <template #default="scope">
                                <FileUpload :isShowTip="false" />
                            </template>
                        </el-table-column>
                        <el-table-column label="调整说明" align="center" prop="adjustmentDescription">
                            <template #default="scope">
                                <el-input :disabled="obj.isDetails" style="width: 100%;"
                                    v-model="scope.row.adjustmentDescription" placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" width="120" v-if="!obj.isDetails">
                            <template #default="scope">
                                <el-button size="small" text type="primary" icon="Plus"
                                    @click="addFormToTable(scope.row)"></el-button>
                                <el-button size="small" text type="primary" icon="Delete"
                                    @click="delFormToTable(scope.row, scope.$index)"></el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="SocialSecurityRatio_maintenance">

import { useAreaStore } from '@/store/modules/area'
import { listScale, getScale, delScale, addScale, updateScale } from "@/api/reonApi/scale";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据

// 产品类型选项
const productTypeOptions = [
    { value: '1', label: '养老保险' },
    { value: '2', label: '医疗保险' },
    { value: '3', label: '失业保险' },
    { value: '4', label: '生育保险' },
    { value: '5', label: '工伤保险' },
    { value: '6', label: '公积金' }
];

// 状态选项
const statusOptions = [
    { value: '1', label: '有效' },
    { value: '2', label: '无效' },
    { value: '3', label: '暂停' }
];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        scaleName: null,
        scaleNo: null,
        cityCode: null,
        productType: null,
        status: null,
    },//查询表单
    rules: {
        city: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        type: [{ required: true, message: '请选择人员类型', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
    isDetails: false,//是否详情
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {
        formTable: []
    };
    obj.isDetails = false
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.title = "新增";
    obj.dialogShow = true;
}
/** 查看按钮操作 */
function handleDetail(row) {
    console.log(row);
    obj.isDetails = true
    obj.title = "查看设备比例";
    obj.dialogShow = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    obj.dialogForm = JSON.parse(JSON.stringify(row));
    obj.title = "修改";
    obj.dialogShow = true;

    return
    const _id = row.id || obj.ids
    getUsers(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改";
    });
}
// 表单-表格选中
const changeFormTable = ref([])
// 表单-表格添加
function addFormToTable() {
    obj.dialogForm.formTable.push({})
}
// 表单-表格删除
function delFormToTable(row, index) {
    proxy.$modal.confirm('确定要删除吗?').then(() => {
        if (changeFormTable.value.length > 0) {
            changeFormTable.value.forEach(row => {
                const index = obj.dialogForm.formTable.indexOf(row)
                if (index > -1) {
                    obj.dialogForm.formTable.splice(index, 1)
                }
            })
        } else {
            obj.dialogForm.formTable.splice(index, 1)
        }
        proxy.$modal.msgSuccess("删除成功!");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}
const formTableRef = ref(null)
// 表单-表格选中
function selectFormToTable(selection) {
    changeFormTable.value = selection
}
// 表单-表格行点击
function rowClickFormToTable(row, column) {
    if (column.label == '序号') {
        formTableRef.value.toggleRowSelection(row)
    }
}


/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateScale(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false
                    getList();
                });
            } else {
                addScale(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false
                    getList();
                });
            }
        }
    });
}
/** 暂停按钮操作 */
function handlePause() {
    const _ids = obj.ids;
    proxy.$modal.confirm('确定要暂停吗？').then(() => {
        console.log(obj.dialogForm);
    }).catch(() => { });
}

/** 调整按钮操作 */
function handleAdjust() {
    const _ids = obj.ids;
    proxy.$modal.confirm('确定要调整吗？').then(() => {
        console.log(obj.dialogForm);
    }).catch(() => { });
}
/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.download('system/scale/export', {
        ...obj.queryParams
    }, `scale_${new Date().getTime()}.xlsx`)
}

getList();
</script>
<style lang="scss" scoped></style>