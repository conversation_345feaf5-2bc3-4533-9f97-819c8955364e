<!-- 供应商一次性展示页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-select class="width220" v-model="obj.queryParams.supplierName" placeholder="请选择供应商名称" clearable>
                    <el-option v-for="item in supplierNameOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" />
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractNo">
                <el-input class="width220" v-model="obj.queryParams.contractNo" placeholder="请输入合同编号" />
            </el-form-item>
            <el-form-item label="账单类型:" prop="billType">
                <el-select class="width220" v-model="obj.queryParams.billType" placeholder="请选择账单类型" clearable>
                    <el-option v-for="item in billTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleImport">同步到供应商列表</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商名称" align="center" fixed prop="supplierName" />
            <el-table-column label="提交分公司" align="center" fixed prop="submitBranch" />
            <el-table-column label="提交人" align="center" fixed prop="submitter" />
            <el-table-column label="账单类型" align="center" fixed prop="billType" />
            <el-table-column label="客户名称" align="center" fixed prop="customerName" />
            <el-table-column label="客户帐套" align="center" prop="customerAccount" />
            <el-table-column label="大合同编号" align="center" prop="contractNo" />
            <el-table-column label="大合同名称" align="center" prop="contractName" />
            <el-table-column label="账单年月" align="center" prop="billMonth" />
            <el-table-column label="财务应收年月" align="center" prop="financeMonth" />
            <el-table-column label="产品类型" align="center" prop="productType" />
            <el-table-column label="产品分类" align="center" prop="productCategory" />
            <el-table-column label="总人次" align="center" prop="totalCount" />
            <el-table-column label="供应商人次" align="center" prop="supplierCount" />
            <el-table-column label="供应商成本" align="center" prop="supplierCost" />
            <el-table-column label="一次性支持人员" align="center" prop="onceSupportStaff" />
            <el-table-column label="核销状态" align="center" prop="verificationStatus" />
            <el-table-column label="原因备注" align="center" prop="remark" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 同步到供应商列表 -->
        <el-dialog v-model="obj.dialogShow" title="同步到供应商列表" width="20%">
            <el-form class="mt20" :model="obj.dialogForm" ref="dialogRef" inline label-width="auto">
                <el-form-item label="进入报表月:" prop="reportMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.reportMonth" type="month"
                        placeholder="请选择月份" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSyncToSupplier">保存</el-button>
                <el-button @click="closeDialog">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup name="SupplierOnceShow">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 账单类型选项
const billTypeOptions = [
    { value: '1', label: '社保' },
    { value: '2', label: '商保' },
    { value: '3', label: '工资' },
    { value: '4', label: '一次性服务账单' },
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        customerName: null,
        contractNo: null,
        billType: null,
    },//查询表单
    total: 10,//总条数
    tableData: [],//列表
    dialogForm: {
        reportMonth: null // 进入报表月
    },//同步表单
    dialogShow: false,//同步弹窗
    ids: [],//选中的id
})


/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                supplierName: '供应商A',
                submitBranch: '北京分公司',
                submitter: '张三',
                billType: '一次性费用',
                customerName: '客户A',
                customerAccount: 'CA001',
                contractNo: 'CN20230501',
                contractName: '合同1',
                billMonth: '2023-05',
                financeMonth: '2023-06',
                productType: '社保',
                productCategory: '基础类',
                totalCount: 100,
                supplierCount: 80,
                supplierCost: 8000,
                onceSupportStaff: '李四',
                verificationStatus: '已核销',
                remark: '特殊情况处理'
            },
            {
                id: 2,
                supplierName: '供应商B',
                submitBranch: '上海分公司',
                submitter: '王五',
                billType: '常规费用',
                customerName: '客户B',
                customerAccount: 'CB001',
                contractNo: 'CN20230502',
                contractName: '合同2',
                billMonth: '2023-05',
                financeMonth: '2023-06',
                productType: '公积金',
                productCategory: '基础类',
                totalCount: 150,
                supplierCount: 120,
                supplierCost: 12000,
                onceSupportStaff: '赵六',
                verificationStatus: '未核销',
                remark: ''
            },
            {
                id: 3,
                supplierName: '供应商C',
                submitBranch: '广州分公司',
                submitter: '刘七',
                billType: '其他费用',
                customerName: '客户C',
                customerAccount: 'CC001',
                contractNo: 'CN20230503',
                contractName: '合同3',
                billMonth: '2023-06',
                financeMonth: '2023-07',
                productType: '医保',
                productCategory: '附加类',
                totalCount: 80,
                supplierCount: 60,
                supplierCost: 6000,
                onceSupportStaff: '孙八',
                verificationStatus: '已核销',
                remark: '特殊情况处理'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 同步到供应商列表
function handleImport() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要同步的数据");
        return;
    }
    obj.dialogShow = true;
}

// 关闭弹窗
function closeDialog() {
    obj.dialogShow = false;
    obj.dialogForm.reportMonth = null;
}

// 保存同步
function handleSyncToSupplier() {
    if (!obj.dialogForm.reportMonth) {
        proxy.$modal.msgError("请选择进入报表月");
        return;
    }

    proxy.$modal.msgSuccess("同步成功");
    closeDialog();
    getList();
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

getList();
</script>
<style lang="scss" scoped></style>