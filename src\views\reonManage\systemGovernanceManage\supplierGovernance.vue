<!-- 供应商实做治理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="queryParams" inline label-width="auto">
            <el-row class="mb8">
                <el-form-item>
                    手动生成供应商实做往期数据
                </el-form-item>
                <el-form-item label="订单编号:">
                    <el-select class="width220" v-model="queryParams.orderNumber" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    手动生成供应商实做
                </el-form-item>
                <el-form-item label="订单编号:">
                    <el-input class="width220" v-model="queryParams.orderNumber2" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleGenerate">生成数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    根据前道同步任务Id进行数据回滚
                </el-form-item>
                <el-form-item label="同步任务Id:">
                    <el-input class="width220" v-model="queryParams.syncTaskId" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleRollback">回滚数据</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    根据同步数据Id将社保订单中最新得数据复制到变更之后
                </el-form-item>
                <el-form-item label="小合同号:">
                    <el-input class="width220" v-model="queryParams.contract_small" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleExecute">执行</el-button>
                </el-form-item>
            </el-row>
            <el-row class="mb8">
                <el-form-item>
                    根据订单号添加供应商实做产品
                </el-form-item>
                <el-form-item>
                    <FileUpload :fileList="fileList" @upload="handleUpload" :isShowTip="false" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleUpload">开始上传</el-button>
                </el-form-item>
            </el-row>
        </el-form>

    </div>
</template>


<script setup name="SupplierGovernance">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const queryParams = ref({
    orderNumber: '',//订单编号
    orderNumber2: '',//订单编号
    syncTaskId: '',//同步任务Id
    contract_small: '',//小合同号
})
const fileList = ref([])

/** 生成 */
function handleGenerate() {
    console.log(111);
}
/** 回滚 */
function handleRollback() {
    console.log(111);
}
/** 执行 */
function handleExecute() {
    console.log(111);
}
/** 文件上传 */
function handleUpload() {
    console.log(fileList.value)
}

</script>
<style lang="scss" scoped>
.upload-file {
    display: flex;
}
</style>