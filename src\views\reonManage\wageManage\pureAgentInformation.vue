<!-- 纯代发人员信息导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateStart" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateEnd" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="导入人:" prop="importBy">
                <el-select class="width220" v-model="obj.queryParams.importBy" placeholder="请选择" clearable>
                    <el-option v-for="item in importByOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入编号:" prop="importId">
                <el-input class="width220" v-model="obj.queryParams.importId" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="6">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="downloadTemplate">下载模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="importPureAgentInfo">人员信息导入</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <ExportTable2 :tableData="obj.tableData" :loading="obj.loading" :total="obj.total"
            :page="obj.queryParams.pageNum" :limit="obj.queryParams.pageSize" menuName="batchPayrollImport"
            @row-dblclick="handleRowDblClick" @handlePagination="handlePagination" />

        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" title="上传导入数据" :dialogForm="obj.dialogForm"
            type="pureAgentImport" :rules="importRules" />
        <!-- 历史信息查看 -->
        <HistoricalInformation :dialogShow="obj.dialogShow2" :title="obj.title" :tableData="obj.tableData"
            @close="handleHistoryClose" />
    </div>
</template>

<script setup name="PureAgentInformation">




const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 导入人选项
const importByOptions = [
    { value: 'admin', label: '管理员' },
    { value: 'user1', label: '用户一' },
    { value: 'user2', label: '用户二' },
    { value: 'user3', label: '用户三' }
];

// 文件列表
const fileList = ref([]);

// 上传URL
const uploadUrl = ref('');

// 表单验证规则
const rules = {
    file: [
        { required: true, message: '请上传文件', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    detailLoading: false, // 详情加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateStart: null, // 导入日期开始
        importDateEnd: null, // 导入日期结束
        importBy: null, // 导入人
        importId: null, // 导入编号
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    detailData: [], // 详情数据
    dialogForm: { // 表单
        remark: '',
        file: null
    },
    dialogShow: false, // 显示对话框
    dialogShow2: false, // 显示详情对话框
    title: '', // 对话框标题
    ids: [], // 选中的id
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        obj.tableData = [
            {
                id: 1,
                importCode: 'IMP20230701001',
                importer: '1',
                importTime: '2023-06-01 10:00:00',
                remark: '测试导入',
                successCount: 10,
                failCount: 0,
                fileName: '纯代发人员信息20230601.xlsx',
                status: '1',
                createBy: 'admin',
                createTime: '2023-06-01 10:00:00',
                updateBy: '',
                updateTime: ''
            },
            {
                id: 2,
                importCode: 'IMP20230602',
                importer: 'user1',
                importTime: '2023-06-02 11:00:00',
                remark: '测试导入',
                successCount: 8,
                failCount: 2,
                fileName: '纯代发人员信息20230602.xlsx',
                status: '1',
                createBy: 'user1',
                createTime: '2023-06-02 11:00:00',
                updateBy: 'admin',
                updateTime: '2023-06-02 11:05:00'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
// 双击行
function handleRowDblClick(row) {
    obj.dialogShow2 = true;
    obj.title = '历史信息查看';
}

// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}
/** 人员信息导入按钮操作 */
function importPureAgentInfo() {
    obj.dialogForm = {
        remark: '',
        file: null
    };
    fileList.value = [];
    obj.dialogShow = true;
    obj.title = '上传导入数据';
}

/** 下载模板按钮操作 */
function downloadTemplate() {
    // 模拟下载操作
    proxy.$modal.loading("正在下载模板，请稍候...");
    setTimeout(() => {
        proxy.$modal.closeLoading();
        proxy.$modal.msgSuccess("模板下载成功");
    }, 1000);
}

/** 导入关闭 */
function handleImportClose() {
    obj.dialogShow = false;
}
/** 历史信息关闭 */
function handleHistoryClose() {
    obj.dialogShow2 = false;
}
getList();
</script>
<style lang="scss" scoped></style>