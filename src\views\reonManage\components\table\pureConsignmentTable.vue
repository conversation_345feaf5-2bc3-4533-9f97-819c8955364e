<!-- 纯代发 -->
<template>
    <div>
        <el-table v-loading="loading" border :data="tableData" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件类型" align="center" prop="idType">
                <template #default="scope">
                    <el-tag>{{ scope.row.idType == '1' ? '身份证' : '护照' }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="客户编号" align="center" prop="customerId" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="派单客服" align="center" prop="dispatchService" />
            <el-table-column label="薪资客服" align="center" prop="salaryService" />
            <el-table-column label="接单客服" align="center" prop="receiveService" />
            <el-table-column label="是否竞业员工" align="center" prop="isCompetitor">
                <template #default="scope">
                    <el-tag>{{ scope.row.isCompetitor == '1' ? '是' : '否' }}</el-tag>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" v-model:page="currentPage" v-model:limit="pageSize"
            @pagination="onPaginationChange" />
    </div>
</template>
<script setup>
const props = defineProps({
    // 表格数据
    tableData: {
        type: Array,
        required: true,
        default: () => []
    },
    // 加载状态
    loading: {
        type: Boolean,
        default: false
    },
    // 总数
    total: {
        type: Number,
        default: 0
    },
    // 当前页
    page: {
        type: Number,
        default: 1
    },
    // 每页数量
    limit: {
        type: Number,
        default: 10
    },
    // 菜单名称
    menuName: {
        type: String,
        default: ''
    }
})

const loading = ref(props.loading);
const tableData = ref(props.tableData);
const currentPage = ref(props.page);
const pageSize = ref(props.limit);

// 监听props变化，更新内部状态
watch(() => props.tableData, (newValue) => {
    tableData.value = newValue;
});
watch(() => props.loading, (newValue) => {
    loading.value = newValue;
});
watch(() => props.page, (newValue) => {
    currentPage.value = newValue;
});

watch(() => props.limit, (newValue) => {
    pageSize.value = newValue;
});
const emit = defineEmits([
    'selection-change',
    'handlePagination'
]);
function handleSelectionChange(selection) {
    emit('selection-change', selection);
}

// 处理分页变化
function onPaginationChange(pagination) {
    // 更新内部状态
    currentPage.value = pagination.page;
    pageSize.value = pagination.limit;

    // 向父组件发送事件
    emit('handlePagination', {
        page: pagination.page,
        limit: pagination.limit
    });
}

</script>