<!-- 供应商小合同处理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-select class="width220" v-model="obj.queryParams.supplierName" placeholder="请选择供应商" clearable>
                    <el-option label="供应商A" value="供应商A" />
                    <el-option label="供应商B" value="供应商B" />
                    <el-option label="供应商C" value="供应商C" />
                </el-select>
            </el-form-item>
            <el-form-item label="参保城市:" prop="insuredCity">
                <el-select class="width220" v-model="obj.queryParams.insuredCity" placeholder="请选择城市" clearable>
                    <el-option label="北京" value="北京" />
                    <el-option label="上海" value="上海" />
                    <el-option label="广州" value="广州" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractCode">
                <el-input class="width220" v-model="obj.queryParams.contractCode" placeholder="请输入合同编号" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户编号:" prop="customerCode">
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入客户编号" clearable />
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-input class="width220" v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称"
                    clearable />
            </el-form-item>
            <el-form-item label="小合同编号:" prop="smallContractCode">
                <el-input class="width220" v-model="obj.queryParams.smallContractCode" placeholder="请输入小合同编号"
                    clearable />
            </el-form-item>
            <el-form-item label="是否绑定供应商模版:" prop="isBindTemplate">
                <el-select class="width220" v-model="obj.queryParams.isBindTemplate" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否绑定供应商报价单:" prop="isBindPriceSheet">
                <el-select class="width220" v-model="obj.queryParams.isBindPriceSheet" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单模版:" prop="billTemplate">
                <el-select class="width220" v-model="obj.queryParams.billTemplate" placeholder="请选择账单模版" clearable>
                    <el-option label="模版A" value="模版A" />
                    <el-option label="模版B" value="模版B" />
                    <el-option label="模版C" value="模版C" />
                </el-select>
            </el-form-item>
            <el-form-item label="收费模版:" prop="chargeTemplate">
                <el-select class="width220" v-model="obj.queryParams.chargeTemplate" placeholder="请选择收费模版" clearable>
                    <el-option label="模版A" value="模版A" />
                    <el-option label="模版B" value="模版B" />
                    <el-option label="模版C" value="模版C" />
                </el-select>
            </el-form-item>
            <el-form-item label="报单价:" prop="quotedPrice">
                <el-input class="width220" v-model="obj.queryParams.quotedPrice" placeholder="请输入报单价" clearable />
            </el-form-item>

            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handlesupplier('template')">绑定供应商账单模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handlesupplier('priceSheet')">绑定供应商报价单</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handlesupplier('alter_template')">变更供应商账单模板</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handlesupplier('alter_priceSheet')">变更供应商报价单</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="小合同编号" align="center" prop="smallContractCode" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="供应商账单模板" align="center" prop="supplierBillTemplate" />
            <el-table-column label="供应商收费模板" align="center" prop="supplierChargeTemplate" />
            <el-table-column label="城市" align="center" prop="city" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" plain @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 绑定供应商账单模版/报价单 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="55%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">

                <el-form-item label="供应商名称" prop="supplierName">
                    <el-select class="width220" v-model="obj.dialogForm.supplierName" placeholder="请选择供应商" clearable>
                        <el-option label="供应商A" value="供应商A" />
                        <el-option label="供应商B" value="供应商B" />
                        <el-option label="供应商C" value="供应商C" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="obj.istemplate" label="账单模板" prop="billTemplate">
                    <el-select class="width220" v-model="obj.dialogForm.billTemplate" placeholder="请选择账单模板" clearable>
                        <el-option label="模版A" value="模版A" />
                        <el-option label="模版B" value="模版B" />
                        <el-option label="模版C" value="模版C" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="obj.istemplate" label="收费模板" prop="chargeTemplate">
                    <el-select class="width220" v-model="obj.dialogForm.chargeTemplate" placeholder="请选择收费模板" clearable>
                        <el-option label="模版A" value="模版A" />
                        <el-option label="模版B" value="模版B" />
                        <el-option label="模版C" value="模版C" />
                    </el-select>
                </el-form-item>


                <el-form-item v-if="obj.ispriceSheet" label="供应商报价单" prop="supplierPriceSheet">
                    <el-select class="width220" v-model="obj.dialogForm.supplierPriceSheet" placeholder="请选择报价单"
                        clearable>
                        <el-option label="报价单A" value="报价单A" />
                        <el-option label="报价单B" value="报价单B" />
                        <el-option label="报价单C" value="报价单C" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="obj.ispriceSheet" label="报价单编号" prop="priceSheetCode">
                    <el-input class="width220" v-model="obj.dialogForm.priceSheetCode" placeholder="请输入报价单编号" />
                </el-form-item>
                <el-form-item v-if="obj.ispriceSheet" label="供应商报价" prop="supplierPrice">
                    <el-input class="width220" v-model="obj.dialogForm.supplierPrice" placeholder="请输入供应商报价" />
                </el-form-item>
                <el-form-item v-if="obj.ispriceSheet" label="报价类型" prop="priceType">
                    <el-select class="width220" v-model="obj.dialogForm.priceType" placeholder="请选择报价类型" clearable>
                        <el-option label="类型A" value="类型A" />
                        <el-option label="类型B" value="类型B" />
                        <el-option label="类型C" value="类型C" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="obj.ispriceSheet" label="报价单指定城市" prop="priceSheetCity">
                    <el-select class="width220" v-model="obj.dialogForm.priceSheetCity" placeholder="请选择城市" clearable>
                        <el-option label="北京" value="北京" />
                        <el-option label="上海" value="上海" />
                        <el-option label="广州" value="广州" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="obj.ispriceSheet" label="选中小合同城市" prop="selectedContractCity">
                    <el-input class="width220" v-model="obj.dialogForm.selectedContractCity" placeholder="请输入小合同城市"
                        disabled />
                </el-form-item>

                <el-form-item v-if="obj.isalter_template" label="原账单模板" prop="originalBillTemplate">
                    <el-input class="width220" v-model="obj.dialogForm.originalBillTemplate" placeholder="请输入原账单模板"
                        disabled />
                </el-form-item>
                <el-form-item v-if="obj.isalter_template" label="原收费模板" prop="originalChargeTemplate">
                    <el-input class="width220" v-model="obj.dialogForm.originalChargeTemplate" placeholder="请输入原收费模板"
                        disabled />
                </el-form-item>
                <el-form-item v-if="obj.isalter_template" label="新账单模板" prop="newBillTemplate">
                    <el-select class="width220" v-model="obj.dialogForm.newBillTemplate" placeholder="请选择新账单模板"
                        clearable>
                        <el-option label="模版A" value="模版A" />
                        <el-option label="模版B" value="模版B" />
                        <el-option label="模版C" value="模版C" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="obj.isalter_template" label="新收费模板" prop="newChargeTemplate">
                    <el-select class="width220" v-model="obj.dialogForm.newChargeTemplate" placeholder="请选择新收费模板"
                        clearable>
                        <el-option label="模版A" value="模版A" />
                        <el-option label="模版B" value="模版B" />
                        <el-option label="模版C" value="模版C" />
                    </el-select>
                </el-form-item>


                <el-form-item v-if="obj.isalter_priceSheet" label="原供应商报价单" prop="originalSupplierPriceSheet">
                    <el-input class="width220" v-model="obj.dialogForm.originalSupplierPriceSheet"
                        placeholder="请输入原供应商报价单" disabled />
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="原报价单编号" prop="originalPriceSheetCode">
                    <el-input class="width220" v-model="obj.dialogForm.originalPriceSheetCode" placeholder="请输入原报价单编号"
                        disabled />
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="原供应商报价" prop="originalSupplierPrice">
                    <el-input class="width220" v-model="obj.dialogForm.originalSupplierPrice" placeholder="请输入原供应商报价"
                        disabled />
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="原报价类型" prop="originalPriceType">
                    <el-input class="width220" v-model="obj.dialogForm.originalPriceType" placeholder="请输入原报价类型"
                        disabled />
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="原报价单指定城市" prop="originalPriceSheetCity">
                    <el-input class="width220" v-model="obj.dialogForm.originalPriceSheetCity" placeholder="请输入原报价单指定城市"
                        disabled />
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="选中小合同城市" prop="selectedContractCity2">
                    <el-input class="width220" v-model="obj.dialogForm.selectedContractCity2" placeholder="请输入选中小合同城市"
                        disabled />
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="新报价单起始月(默认为当月)" prop="newPriceSheetStartMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.newPriceSheetStartMonth" type="month"
                        placeholder="请选择新报价单起始月" />
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="新供应商报价单" prop="newSupplierPriceSheet">
                    <el-select class="width220" v-model="obj.dialogForm.newSupplierPriceSheet" placeholder="请选择新供应商报价单"
                        clearable>
                        <el-option label="报价单A" value="报价单A" />
                        <el-option label="报价单B" value="报价单B" />
                        <el-option label="报价单C" value="报价单C" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="新报价单编号" prop="newPriceSheetCode">
                    <el-input class="width220" v-model="obj.dialogForm.newPriceSheetCode" placeholder="请输入新报价单编号" />
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="新供应商报价" prop="newSupplierPrice">
                    <el-input class="width220" v-model="obj.dialogForm.newSupplierPrice" placeholder="请输入新供应商报价" />
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="新报价类型" prop="newPriceType">
                    <el-select class="width220" v-model="obj.dialogForm.newPriceType" placeholder="请选择新报价类型" clearable>
                        <el-option label="类型A" value="类型A" />
                        <el-option label="类型B" value="类型B" />
                        <el-option label="类型C" value="类型C" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="obj.isalter_priceSheet" label="新报价单指定城市" prop="newPriceSheetCity">
                    <el-select class="width220" v-model="obj.dialogForm.newPriceSheetCity" placeholder="请选择新报价单指定城市"
                        clearable>
                        <el-option label="北京" value="北京" />
                        <el-option label="上海" value="上海" />
                        <el-option label="广州" value="广州" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="obj.dialogShow = false">取消</el-button>
                <el-button type="primary" @click="handleSave">确定</el-button>
            </template>
        </el-dialog>

        <!-- 详情 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="60%" append-to-body draggable>
            <div class="flex-between-center">
                <el-card style="width: 49.5%;">
                    <template #header>账单模板日志</template>
                    <el-table :data="obj.tableData2" border>
                        <el-table-column label="小合同编号" align="center" prop="smallContractCode" />
                        <el-table-column label="小合同名称" align="center" prop="smallContractName" />
                        <el-table-column label="操作类型" align="center" prop="operationType" />
                        <el-table-column label="账单模板名称" align="center" prop="billTemplateName" />
                        <el-table-column label="收费频率名称" align="center" prop="chargeFrequencyName" />
                        <el-table-column label="原账单模板名称" align="center" prop="originalBillTemplateName" />
                        <el-table-column label="原收费频率名称" align="center" prop="originalChargeFrequencyName" />
                        <el-table-column label="创建人" align="center" prop="createBy" />
                        <el-table-column label="创建时间" align="center" prop="createTime" />
                    </el-table>
                </el-card>
                <el-card style="width: 49.5%;">
                    <template #header>报价单日志</template>
                    <el-table :data="obj.tableData2" border>
                        <el-table-column label="小合同编号" align="center" prop="smallContractCode" />
                        <el-table-column label="小合同名称" align="center" prop="smallContractName" />
                        <el-table-column label="操作类型" align="center" prop="operationType" />
                        <el-table-column label="报价单名称" align="center" prop="priceSheetName" />
                        <el-table-column label="报价类型" align="center" prop="priceType" />
                        <el-table-column label="原报价单名称" align="center" prop="originalPriceSheetName" />
                        <el-table-column label="原报价类型" align="center" prop="originalPriceType" />
                        <el-table-column label="创建人" align="center" prop="createBy" />
                        <el-table-column label="创建时间" align="center" prop="createTime" />
                    </el-table>
                </el-card>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    supplierName: [
        { required: true, message: '供应商名称不能为空', trigger: 'change' }
    ],
    billTemplate: [
        { required: true, message: '账单模板不能为空', trigger: 'change' }
    ],
    chargeTemplate: [
        { required: true, message: '收费模板不能为空', trigger: 'change' }
    ],
    supplierPriceSheet: [
        { required: true, message: '供应商报价单不能为空', trigger: 'change' }
    ],
    priceSheetCode: [
        { required: true, message: '报价单编号不能为空', trigger: 'blur' }
    ],
    supplierPrice: [
        { required: true, message: '供应商报价不能为空', trigger: 'blur' }
    ],
    priceType: [
        { required: true, message: '报价类型不能为空', trigger: 'change' }
    ],
    priceSheetCity: [
        { required: true, message: '报价单指定城市不能为空', trigger: 'change' }
    ],
    newBillTemplate: [
        { required: true, message: '新账单模板不能为空', trigger: 'change' }
    ],
    newChargeTemplate: [
        { required: true, message: '新收费模板不能为空', trigger: 'change' }
    ],
    newPriceSheetStartMonth: [
        { required: true, message: '新报价单起始月不能为空', trigger: 'change' }
    ],
    newSupplierPriceSheet: [
        { required: true, message: '新供应商报价单不能为空', trigger: 'change' }
    ],
    newPriceSheetCode: [
        { required: true, message: '新报价单编号不能为空', trigger: 'blur' }
    ],
    newSupplierPrice: [
        { required: true, message: '新供应商报价不能为空', trigger: 'blur' }
    ],
    newPriceType: [
        { required: true, message: '新报价类型不能为空', trigger: 'change' }
    ],
    newPriceSheetCity: [
        { required: true, message: '新报价单指定城市不能为空', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        insuredCity: null,
        contractCode: null,
        customerName: null,
        customerCode: null,
        smallContractName: null,
        smallContractCode: null,
        isBindTemplate: null,
        isBindPriceSheet: null,
        billTemplate: null,
        chargeTemplate: null,
        quotedPrice: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    tableData2: [],//详情日志列表
    dialogForm: {},//表单
    dialogShow: false,//弹窗
    dialogShow2: false,//详情弹窗
    bindSave: false,//绑定保存
    ids: [],//选中的id
    title: "",//标题
    isDetail: false,//是否详情
    istemplate: false,//是否绑定供应商账单模版
    ispriceSheet: false,//是否绑定供应商报价单
    isalter_template: false,//是否变更供应商账单模板
    isalter_priceSheet: false,//是否变更供应商报价单
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                smallContractCode: 'XHT20230001',
                smallContractName: '小合同名称1',
                contractCode: 'HT20230001',
                contractName: '合同名称1',
                supplierBillTemplate: '模版A',
                supplierChargeTemplate: '模版A',
                city: '北京'
            },
            {
                id: 2,
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                smallContractCode: 'X**********',
                smallContractName: '小合同名称2',
                contractCode: '**********',
                contractName: '合同名称2',
                supplierBillTemplate: '模版B',
                supplierChargeTemplate: '模版B',
                city: '上海'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 绑定供应商账单模版
function handlesupplier(type) {
    // 重置所有状态
    obj.istemplate = false;
    obj.ispriceSheet = false;
    obj.isalter_template = false;
    obj.isalter_priceSheet = false;

    if (type == 'template') {
        obj.title = '绑定账单模板'
        obj.istemplate = true
        obj.dialogForm = {
            supplierName: '',
            billTemplate: '',
            chargeTemplate: ''
        }
    } else if (type == 'priceSheet') {
        obj.title = '绑定报价单'
        obj.ispriceSheet = true
        obj.dialogForm = {
            supplierName: '',
            supplierPriceSheet: '',
            priceSheetCode: '',
            supplierPrice: '',
            priceType: '',
            priceSheetCity: '',
            selectedContractCity: '北京'
        }
    } else if (type == 'alter_template') {
        obj.title = '变更账单模板'
        obj.isalter_template = true
        obj.dialogForm = {
            supplierName: '',
            originalBillTemplate: '模版A',
            originalChargeTemplate: '模版A',
            newBillTemplate: '',
            newChargeTemplate: ''
        }
    } else if (type == 'alter_priceSheet') {
        obj.title = '变更报价单'
        obj.isalter_priceSheet = true
        obj.dialogForm = {
            supplierName: '',
            originalSupplierPriceSheet: '报价单A',
            originalPriceSheetCode: 'PRC20230001',
            originalSupplierPrice: '100',
            originalPriceType: '类型A',
            originalPriceSheetCity: '北京',
            selectedContractCity2: '北京',
            newPriceSheetStartMonth: '',
            newSupplierPriceSheet: '',
            newPriceSheetCode: '',
            newSupplierPrice: '',
            newPriceType: '',
            newPriceSheetCity: ''
        }
    }
    obj.dialogShow = true
}

// 详情按钮操作
function handleDetail() {
    obj.dialogShow2 = true
    obj.title = '供应商小合同处理详情'

    // 模拟账单模板日志数据
    obj.tableData2 = [
        {
            smallContractCode: 'XHT20230001',
            smallContractName: '小合同名称1',
            operationType: '绑定',
            billTemplateName: '模版A',
            chargeFrequencyName: '月度',
            originalBillTemplateName: '',
            originalChargeFrequencyName: '',
            createBy: '创建人1',
            createTime: '2023-01-01 10:00:00'
        },
        {
            smallContractCode: 'XHT20230001',
            smallContractName: '小合同名称1',
            operationType: '变更',
            billTemplateName: '模版B',
            chargeFrequencyName: '月度',
            originalBillTemplateName: '模版A',
            originalChargeFrequencyName: '月度',
            createBy: '创建人2',
            createTime: '2023-02-01 10:00:00'
        }
    ];
}

// 保存按钮操作
function handleSave() {
    proxy.$refs.formRef.validate(valid => {
        if (valid) {
            proxy.$modal.msgSuccess('保存成功');
            obj.dialogShow = false;
            // 重置所有状态
            obj.istemplate = false;
            obj.ispriceSheet = false;
            obj.isalter_template = false;
            obj.isalter_priceSheet = false;
        }
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>