<!-- 坏账处理List页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customer">
                <el-input class="width220" v-model="obj.queryParams.customer" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="合同:" prop="contract">
                <el-input class="width220" v-model="obj.queryParams.contract" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="客户帐套" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单年月:" prop="billYearMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billYearMonth" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Check" @click="handleBadDebt">处理坏账</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="账单模板名称" align="center" prop="billTemplateName" />
            <el-table-column label="账单月" align="center" prop="billYearMonth" />
            <el-table-column label="账单金额" align="center" prop="billAmount" />
            <el-table-column label="坏账金额" align="center" prop="badDebtAmount" />
            <el-table-column label="处理人" align="center" prop="handlePerson" />
            <el-table-column label="处理时间" align="center" prop="handleTime" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="文件" align="center" prop="file" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 弹窗 -->
        <el-dialog v-model="obj.dialogShow" title="处理坏账" width="50%">
            <el-form :model="obj.dialogForm" inline label-width="auto">
                <el-form-item label="合同名称" prop="contractName">
                    <el-input class="width220" v-model="obj.dialogForm.contractName" />
                </el-form-item>
                <el-form-item label="客户帐套" prop="customerAccount">
                    <el-select class="width220" v-model="obj.dialogForm.customerAccount" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账单年月" prop="billYearMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.billYearMonth" type="date"
                        placeholder="请选择日期" clearable />
                </el-form-item>
                <el-form-item label="账单金额" prop="billAmount">
                    <el-input class="width220" v-model="obj.dialogForm.billAmount" />
                </el-form-item>
                <el-form-item label="已核销金额" prop="writtenOffAmount">
                    <el-input class="width220" v-model="obj.dialogForm.writtenOffAmount" />
                </el-form-item>
                <el-form-item label="未核销金额" prop="unwrittenOffAmount">
                    <el-input class="width220" v-model="obj.dialogForm.unwrittenOffAmount" />
                </el-form-item>
                <el-divider content-position="left">备注</el-divider>
                <el-input style="width: 70%;" type="textarea" rows="4" v-model="obj.dialogForm.remark" />
                <el-divider content-position="left">文件上传</el-divider>
                <file-upload v-model="obj.dialogForm.file" />
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button type="info" @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="BadDebtTreatment">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数
    ids: [],//选中id

    tableData: [],//列表
    dialogShow: false,//弹窗
    dialogForm: {},//弹窗表单
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 处理坏账 */
function handleBadDebt() {
    obj.dialogShow = true;
}

/** 保存 */
function handleSave() {
    console.log(obj.dialogForm);
}



getList();
</script>
<style lang="scss" scoped></style>