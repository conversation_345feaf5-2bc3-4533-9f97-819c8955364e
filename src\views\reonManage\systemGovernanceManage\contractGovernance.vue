<!-- 合同治理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户编号:" prop="customerCode">
                <el-select class="width220" v-model="obj.queryParams.customerCode" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="销售:" prop="sales">
                <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <div class="content" :class="{ 'expanded': obj.showMore }">
                <el-form-item label="销售所属公司:" prop="salesCompany">
                    <el-date-picker v-model="obj.queryParams.salesCompany" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="人员分布:" prop="staffDistribution">
                    <el-input class="width220" readonly @click="obj.sendOrdersShow = true"
                        v-model="obj.queryParams.staffDistribution" placeholder="请输入唯一号" />
                </el-form-item>
                <el-form-item label="合同类型:" prop="contractType">
                    <el-select class="width220" v-model="obj.queryParams.contractType" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同状态:" prop="contractStatus">
                    <el-input class="width220" v-model="obj.queryParams.contractStatus" placeholder="请输入唯一号" />
                </el-form-item>
                <el-form-item label="负责客服:" prop="serviceManager">
                    <el-date-picker v-model="obj.queryParams.serviceManager" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="销售所属城市:" prop="salesCity">
                    <el-date-picker v-model="obj.queryParams.salesCity" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="审批通过日期(开始):" prop="approvalStartDate">
                    <el-date-picker v-model="obj.queryParams.approvalStartDate" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="审批通过日期(结束):" prop="approvalEndDate">
                    <el-date-picker v-model="obj.queryParams.approvalEndDate" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="终止服务日期(开始):" prop="terminationStartDate">
                    <el-date-picker v-model="obj.queryParams.terminationStartDate" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="终止服务日期(结束):" prop="terminationEndDate">
                    <el-date-picker v-model="obj.queryParams.terminationEndDate" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="是否上传会议纪要:" prop="hasUploadMeetingMinutes">
                    <el-select class="width220" v-model="obj.queryParams.hasUploadMeetingMinutes" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同起始日>=:" prop="contractStartDateFrom">
                    <el-select class="width220" v-model="obj.queryParams.contractStartDateFrom" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同起始日<=:" prop="contractStartDateTo">
                    <el-select class="width220" v-model="obj.queryParams.contractStartDateTo" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </div>

            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" icon="Expand" plain @click="showMore">显示更多</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon='View' @click="handleDetail">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" @click="handleDelete">删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.contractList"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="流程类型" align="center" prop="processType" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="报价编号" align="center" prop="quoteCode" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="大集团" align="center" prop="largeGroup" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户规模" align="center" prop="customerScale" />
            <el-table-column label="销售所在公司" align="center" prop="salesCompany" />
            <el-table-column label="派单分公司" align="center" prop="dispatchCompany" />
            <el-table-column label="签约方抬头" align="center" prop="signingParty" />
            <el-table-column label="销售" align="center" prop="sales" />
            <el-table-column label="负责客服" align="center" prop="serviceManager" />
            <el-table-column label="是否上传会议纪要" align="center" prop="hasUploadMeetingMinutes" />
            <el-table-column label="新增/存量标识" align="center" prop="stockFlag" />
            <el-table-column label="新增/存量标识补充" align="center" prop="stockFlagSupplement" />
            <el-table-column label="首版账单年月" align="center" prop="firstBillDate" />
            <el-table-column label="在职人数" align="center" prop="employeeCount" />
            <el-table-column label="日期" align="center" prop="date" />
            <el-table-column label="合同终止日" align="center" prop="contractEndDate" />
            <el-table-column label="合同终止原因" align="center" prop="terminationReason" />
            <el-table-column label="合同状态" align="center" prop="contractStatus" />
            <el-table-column label="审批状态" align="center" prop="approvalStatus" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="自动顺延操作日志" align="center" prop="autoExtensionLog" />
            <el-table-column label="项目客服备注" align="center" prop="serviceRemark" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 详情 -->
        <el-dialog v-model="obj.dialogShow" title="查看" width="70%">
            <el-tabs type="border-card">
                <el-tab-pane label="基本信息">
                    <el-form :model="obj.dialogForm" label-suffix=":" inline label-width="auto" append-to-body
                        draggable>
                        <el-form-item label="客户名称" prop="customerName">
                            <el-input class="width220" v-model="obj.dialogForm.customerName" />
                        </el-form-item>
                        <el-form-item label="合同名称" prop="contractName">
                            <el-input class="width420" v-model="obj.dialogForm.contractName" />
                        </el-form-item>
                        <el-form-item label="合同类型" prop="contractType">
                            <el-input class="width220" v-model="obj.dialogForm.contractType" />
                        </el-form-item>
                        <el-form-item label="二级合同分类" prop="contractSubType">
                            <el-input class="width220" v-model="obj.dialogForm.contractSubType" />
                        </el-form-item>
                        <el-form-item label="到款日" prop="paymentDate">
                            <el-input class="width220" v-model="obj.dialogForm.paymentDate" />
                        </el-form-item>
                        <el-form-item label="合同起始日" prop="contractStartDate">
                            <el-input class="width220" v-model="obj.dialogForm.contractStartDate" />
                        </el-form-item>
                        <el-form-item label="合同终止日" prop="contractEndDate">
                            <el-input class="width220" v-model="obj.dialogForm.contractEndDate" />
                        </el-form-item>
                        <el-form-item label="报价单编号" prop="quoteCode">
                            <el-input class="width220" v-model="obj.dialogForm.quoteCode" />
                        </el-form-item>
                        <el-form-item label="销售所在公司" prop="salesCompany">
                            <el-input class="width220" v-model="obj.dialogForm.salesCompany" />
                        </el-form-item>
                        <el-form-item label="签单地" prop="signingPlace">
                            <el-input class="width220" v-model="obj.dialogForm.signingPlace" />
                        </el-form-item>
                        <el-form-item label="派单分公司" prop="dispatchCompany">
                            <el-input class="width220" v-model="obj.dialogForm.dispatchCompany" />
                        </el-form-item>
                        <el-form-item label="派单地" prop="dispatchPlace">
                            <el-input class="width220" v-model="obj.dialogForm.dispatchPlace" />
                        </el-form-item>
                        <el-form-item label="责任客服" prop="serviceManager">
                            <el-select class="width220" v-model="obj.dialogForm.serviceManager" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="签约方抬头" prop="signingParty">
                            <el-select class="width220" v-model="obj.dialogForm.signingParty" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="合同标准类型" prop="contractStandardType">
                            <el-select class="width220" v-model="obj.dialogForm.contractStandardType" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账单日" prop="billDate">
                            <el-input class="width220" v-model="obj.dialogForm.billDate" />
                        </el-form-item>
                        <el-form-item label="工资发放日" prop="salaryPayDate">
                            <el-input class="width220" v-model="obj.dialogForm.salaryPayDate" />
                        </el-form-item>
                        <el-form-item label="工资到款日" prop="salaryArrivalDate">
                            <el-input class="width220" v-model="obj.dialogForm.salaryArrivalDate" />
                        </el-form-item>
                        <el-form-item label="开具发票方式" prop="invoiceMethod">
                            <el-select class="width220" v-model="obj.dialogForm.invoiceMethod" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="回款频率" prop="paymentFrequency">
                            <el-select class="width220" v-model="obj.dialogForm.paymentFrequency" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="增减员截止点" prop="staffChangeDeadline">
                            <el-input class="width220" v-model="obj.dialogForm.staffChangeDeadline" />
                        </el-form-item>
                        <el-form-item label="是否自动延顺" prop="autoExtension">
                            <el-select class="width220" v-model="obj.dialogForm.autoExtension" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="是否有特殊审批项" prop="hasSpecialApproval">
                            <el-select class="width220" v-model="obj.dialogForm.hasSpecialApproval" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item style="width :100%" label="报价单">
                            <el-table :data="obj.dialogForm.tableData" border>
                                <el-table-column label="序号" type="index" align="center" width="80" />
                                <el-table-column label="报价编号" align="center" prop="quoteCode" />
                                <el-table-column label="报价单名称" align="center" prop="quoteName" />
                                <el-table-column label="客户名称" align="center" prop="customerName" />
                                <el-table-column label="报价单类型" align="center" prop="quoteType" />
                                <el-table-column label="产品类型" align="center" prop="productType" />
                                <el-table-column label="区域" align="center" prop="area" />
                                <el-table-column label="销售" align="center" prop="sales" />
                                <el-table-column label="日期" align="center" prop="date" />
                            </el-table>
                        </el-form-item>
                        <el-form-item style="width: 100%" label="备注" prop="remark">
                            <el-input type="textarea" :rows="3" placeholder="请输入" clearable
                                v-model="obj.dialogForm.remark" />
                        </el-form-item>
                        <el-divider content-position="left">文件上传</el-divider>
                        <file-upload v-model="obj.dialogForm.files" />
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="会议纪要">
                    <MeetingMinutes :tableData="obj.dialogForm.tableData" />
                </el-tab-pane>
                <el-tab-pane label="特殊审批记录">
                    <el-divider content-position="left">特殊审批时间线</el-divider>
                </el-tab-pane>
                <el-tab-pane label="流程信息">
                    <ProcessInformation :tableData="obj.dialogForm.tableData" />
                </el-tab-pane>
            </el-tabs>
            <template #footer>
                <el-button @click="obj.dialogShow = false">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ContractGovernance">
import MeetingMinutes from '@/views/reonManage/components/meetingMinutes.vue';
import ProcessInformation from '@/views/reonManage/components/processInformation.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 字典数据
const sys_yes_no = ref([]);

// 数据对象
const obj = reactive({
    // 遮罩层
    loading: false,
    // 选中数组
    ids: [],
    // 非单个禁用
    single: true,
    // 非多个禁用
    multiple: true,
    // 显示搜索条件
    showSearch: true,
    // 显示更多
    showMore: false,
    // 总条数
    total: 0,
    // 合同列表
    contractList: [],
    // 弹出层标题
    title: "",
    // 是否显示弹出层
    dialogShow: false,
    // 查询参数
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerCode: undefined,
        customerName: undefined,
        sales: undefined,
        contractType: undefined
    },
    // 表单参数
    dialogForm: {
        customerName: undefined,
        contractName: undefined,
        contractType: undefined,
        contractSubType: undefined,
        paymentDate: undefined,
        contractStartDate: undefined,
        contractEndDate: undefined,
        quoteCode: undefined,
        salesCompany: undefined,
        signingPlace: undefined,
        dispatchCompany: undefined,
        dispatchPlace: undefined,
        serviceManager: undefined,
        signingParty: undefined,
        contractStandardType: undefined,
        billDate: undefined,
        salaryPayDate: undefined,
        salaryArrivalDate: undefined,
        invoiceMethod: undefined,
        paymentFrequency: undefined,
        staffChangeDeadline: undefined,
        autoExtension: undefined,
        hasSpecialApproval: undefined,
        remark: undefined,
        files: [],
        tableData: []
    }
});

/** 查询合同列表 */
function getList() {
    obj.loading = true;
    // 模拟异步请求
    setTimeout(() => {
        obj.contractList = [
            {
                id: 1,
                contractCode: '**********',
                contractName: '人力资源外包服务合同',
                processType: '新签',
                contractType: '人力资源服务',
                quoteCode: '**********',
                customerCode: 'KH001',
                largeGroup: '否',
                customerName: '北京科技有限公司',
                customerScale: '中型',
                salesCompany: '北京分公司',
                dispatchCompany: '北京分公司',
                signingParty: '北京科技有限公司',
                sales: '张三',
                serviceManager: '李四',
                hasUploadMeetingMinutes: '是',
                stockFlag: '新增',
                stockFlagSupplement: '',
                firstBillDate: '2023-01',
                employeeCount: 50,
                date: '2023-01-01',
                contractEndDate: '2024-01-01',
                terminationReason: '',
                contractStatus: '生效中',
                approvalStatus: '已通过',
                remark: '无',
                autoExtensionLog: '',
                serviceRemark: ''
            }
        ];
        obj.total = obj.contractList.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 显示更多搜索条件 */
function showMore() {
    obj.showMore = !obj.showMore;
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 查看详情
function handleDetail(row) {
    obj.dialogShow = true;
    if (row.id) {
        obj.dialogForm = JSON.parse(JSON.stringify(row));
    } else {
        // 如果是通过按钮点击，检查是否有选中的行
        if (obj.ids.length !== 1) {
            proxy.$modal.msgError("请选择一条记录进行查看");
            obj.dialogShow = false;
            return;
        }
        const selectedRow = obj.contractList.find(item => item.id === obj.ids[0]);
        obj.dialogForm = JSON.parse(JSON.stringify(selectedRow));
    }
    obj.title = '查看合同';
}

// 删除
function handleDelete() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要删除的数据");
        return;
    }
    proxy.$modal.confirm('是否确认删除所选数据？').then(function () {
        proxy.$modal.msgSuccess("删除成功");
        getList();
    }).catch(() => { });
}

// 关闭弹窗
function handleClose() {
    obj.dialogShow = false;
}
// 初始化
getList();
</script>

<style lang="scss" scoped>
.content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.expanded {
    max-height: 1000px;
    transition: max-height 0.5s ease-in;
}
</style>