<!-- 工资支付退票录入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收款人名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卡号:" prop="bankCardNo">
                <el-input class="width220" v-model="obj.queryParams.bankCardNo" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工资支付日期:" prop="salaryPaymentDate">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryPaymentDate" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="setPaymentResult">设置发放结果</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="员工编号" align="center" fixed sortable width="120" prop="employeeCode" />
            <el-table-column label="姓名" align="center" fixed sortable prop="employeeName" />
            <el-table-column label="证件号码" align="center" fixed sortable width="120" prop="idNumber" />
            <el-table-column label="证件类型" align="center" sortable width="120" prop="idType">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.idType === '1' ? 'primary' : scope.row.idType === '2' ? 'success' : 'info'">
                        {{ getIdTypeName(scope.row.idType) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="工资支付日期" align="center" sortable width="150" prop="salaryPaymentDate" />
            <el-table-column label="客户编号" align="center" sortable width="120" prop="customerCode" />
            <el-table-column label="客户名称" align="center" sortable width="120" prop="customerName" />
            <el-table-column label="银行卡号" align="center" sortable width="120" prop="bankCardNo" />
            <el-table-column label="开户行" align="center" sortable width="90" prop="bankName" />
            <el-table-column label="实发合计" align="center" sortable width="120" prop="actualTotal">
                <template #default="scope">
                    <span class="text-blue">{{ scope.row.actualTotal }}</span>
                </template>
            </el-table-column>
            <el-table-column label="个人所得税" align="center" sortable width="120" prop="personalIncomeTax" />
            <el-table-column label="供应商工资服务费" align="center" sortable width="180" prop="supplierSalaryServiceFee" />
            <el-table-column label="供应商残障金" align="center" sortable width="150" prop="supplierDisabilityFund" />
            <el-table-column label="供应商跨行手续费" align="center" sortable width="180" prop="supplierCrossBankFee" />
            <el-table-column label="供应商工会费" align="center" sortable width="150" prop="supplierUnionFee" />
            <el-table-column label="供应商税金合计" align="center" sortable width="150" prop="supplierTaxTotal" />
            <el-table-column label="经济补偿金实发金额" align="center" sortable width="180" prop="compensationActualAmount" />
            <el-table-column label="经济补偿金个税" align="center" sortable width="150" prop="compensationTax" />
            <el-table-column label="状态" align="center" sortable prop="status">
                <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                        {{ getStatusName(scope.row.status) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="支付ID" align="center" sortable width="120" prop="paymentId" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 对话框 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="25%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" label-width="auto">
                <el-form-item label="发放状态" prop="paymentStatus">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.paymentStatus" placeholder="请选择发放状态">
                        <el-option v-for="item in paymentStatusOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="发放日期" prop="paymentDate">
                    <el-date-picker style="width: 100%;;" v-model="obj.dialogForm.paymentDate" type="date"
                        placeholder="请选择日期" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="失败原因" prop="failReason">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.failReason" type="textarea"
                        placeholder="请输入失败原因" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">提交</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="WagePaymentRefundEntry">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 证件类型选项
const idTypeOptions = [
    { value: '1', label: '身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '其他' }
];

// 发放状态选项
const paymentStatusOptions = [
    { value: '1', label: '成功' },
    { value: '2', label: '失败' },
    { value: '3', label: '退票' }
];

// 表单验证规则
const rules = {
    paymentStatus: [
        { required: true, message: '请选择发放状态', trigger: 'change' }
    ],
    paymentDate: [
        { required: true, message: '请选择发放日期', trigger: 'change' }
    ],
    failReason: [
        { required: false, message: '请输入失败原因', trigger: 'blur' }
    ]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null, // 客户名称
        employeeName: null, // 收款人名
        idNumber: null, // 证件号
        bankCardNo: null, // 卡号
        salaryPaymentDate: null, // 工资支付日期
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {
        paymentStatus: '',
        paymentDate: '',
        failReason: ''
    }, // 表单
    dialogShow: false, // 显示对话框
    title: '', // 对话框标题
    ids: [], // 选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                employeeCode: 'EMP001',
                employeeName: '张三',
                idNumber: '110101199001011234',
                idType: '1',
                salaryPaymentDate: '2023-05-15',
                customerCode: 'CUS001',
                customerName: '某某科技有限公司',
                bankCardNo: '****************',
                bankName: '工商银行',
                actualTotal: 8000.00,
                personalIncomeTax: 500.00,
                supplierSalaryServiceFee: 200.00,
                supplierDisabilityFund: 100.00,
                supplierCrossBankFee: 50.00,
                supplierUnionFee: 50.00,
                supplierTaxTotal: 900.00,
                compensationActualAmount: 0.00,
                compensationTax: 0.00,
                status: '1',
                paymentId: 'PAY20230515001'
            },
            {
                id: 2,
                employeeCode: 'EMP002',
                employeeName: '李四',
                idNumber: '110101199002022345',
                idType: '1',
                salaryPaymentDate: '2023-05-15',
                customerCode: 'CUS002',
                customerName: '某某信息技术有限公司',
                bankCardNo: '****************',
                bankName: '建设银行',
                actualTotal: 10000.00,
                personalIncomeTax: 800.00,
                supplierSalaryServiceFee: 300.00,
                supplierDisabilityFund: 150.00,
                supplierCrossBankFee: 50.00,
                supplierUnionFee: 50.00,
                supplierTaxTotal: 1350.00,
                compensationActualAmount: 0.00,
                compensationTax: 0.00,
                status: '2',
                paymentId: 'PAY20230515002'
            },
            {
                id: 3,
                employeeCode: 'EMP003',
                employeeName: '王五',
                idNumber: '110101199003033456',
                idType: '2',
                salaryPaymentDate: '2023-05-15',
                customerCode: 'CUS003',
                customerName: '某某软件有限公司',
                bankCardNo: '****************',
                bankName: '农业银行',
                actualTotal: 12000.00,
                personalIncomeTax: 1000.00,
                supplierSalaryServiceFee: 400.00,
                supplierDisabilityFund: 200.00,
                supplierCrossBankFee: 50.00,
                supplierUnionFee: 50.00,
                supplierTaxTotal: 1700.00,
                compensationActualAmount: 5000.00,
                compensationTax: 500.00,
                status: '3',
                paymentId: 'PAY20230515003'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}



// 表单重置
function resetForm() {
    obj.dialogForm = {
        paymentStatus: '',
        paymentDate: '',
        failReason: ''
    };
    proxy.resetForm("formRef");
}

// 获取证件类型名称
function getIdTypeName(idType) {
    const idTypeMap = {
        '1': '身份证',
        '2': '护照',
        '3': '其他'
    };
    return idTypeMap[idType] || '未知类型';
}

// 获取状态类型
function getStatusType(status) {
    switch (status) {
        case '1': return 'success';
        case '2': return 'danger';
        case '3': return 'warning';
        default: return 'info';
    }
}

// 获取状态名称
function getStatusName(status) {
    const statusMap = {
        '1': '成功',
        '2': '失败',
        '3': '退票'
    };
    return statusMap[status] || '未知状态';
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

// 设置发放结果
function setPaymentResult() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要设置发放状态的记录');
        return;
    }

    obj.dialogShow = true;
    obj.title = '设置发放状态';
    resetForm();
}

// 提交表单
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}



getList();
</script>
<style lang="scss" scoped>
.text-blue {
    color: #409EFF;
    font-weight: bold;
}
</style>