<!-- 个人订单查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" />
            </el-form-item>
            <el-form-item label="客户编号:" prop="customerCode">
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入客户编号" />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderCode">
                <el-input class="width220" v-model="obj.queryParams.orderCode" placeholder="请输入订单编号" />
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-input class="width220" v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称" />
            </el-form-item>
            <el-form-item label="雇员状态:" prop="employeeStatus">
                <el-select class="width220" v-model="obj.queryParams.employeeStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in employeeStatusList" :key="item.code" :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" />
            </el-form-item>
            <div class="content" :class="{ 'expanded': obj.showMore }">
                <el-form-item label="订单创建时间(起):" prop="createTimeStart">
                    <el-date-picker v-model="obj.queryParams.createTimeStart" type="date" placeholder="请选择日期" />
                </el-form-item>

                <el-form-item label="派单方:" prop="orderSender">
                    <el-select class="width220" v-model="obj.queryParams.orderSender" placeholder="请选择" clearable>
                        <el-option v-for="item in senderList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>

                <el-form-item label="派单客服:" prop="senderService">
                    <el-select class="width220" v-model="obj.queryParams.senderService" placeholder="请选择" clearable>
                        <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>

                <el-form-item label="派单类型:" prop="orderType">
                    <el-select class="width220" v-model="obj.queryParams.orderType" placeholder="请选择" clearable>
                        <el-option v-for="item in orderTypeList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>

                <el-form-item label="订单创建时间(止):" prop="createTimeEnd">
                    <el-date-picker v-model="obj.queryParams.createTimeEnd" type="date" placeholder="请选择日期" />
                </el-form-item>

                <el-form-item label="入职时间起:" prop="entryDateStart">
                    <el-date-picker v-model="obj.queryParams.entryDateStart" type="date" placeholder="请选择日期" />
                </el-form-item>

                <el-form-item label="入职时间止:" prop="entryDateEnd">
                    <el-date-picker v-model="obj.queryParams.entryDateEnd" type="date" placeholder="请选择日期" />
                </el-form-item>

                <el-form-item label="离职时间起:" prop="leaveDateStart">
                    <el-date-picker v-model="obj.queryParams.leaveDateStart" type="date" placeholder="请选择日期" />
                </el-form-item>

                <el-form-item label="申报离职日期起:" prop="applyLeaveDateStart">
                    <el-date-picker v-model="obj.queryParams.applyLeaveDateStart" type="date" placeholder="请选择日期" />
                </el-form-item>

                <el-form-item label="变更状态:" prop="changeStatus">
                    <el-select class="width220" v-model="obj.queryParams.changeStatus" placeholder="请选择" clearable>
                        <el-option v-for="item in changeStatusList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>

                <el-form-item label="入离职状态:" prop="employmentStatus">
                    <el-select class="width220" v-model="obj.queryParams.employmentStatus" placeholder="请选择" clearable>
                        <el-option v-for="item in employmentStatusList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>

                <el-form-item label="离职时间止:" prop="leaveDateEnd">
                    <el-date-picker v-model="obj.queryParams.leaveDateEnd" type="date" placeholder="请选择日期" />
                </el-form-item>

                <el-form-item label="申报离职日期止:" prop="applyLeaveDateEnd">
                    <el-date-picker v-model="obj.queryParams.applyLeaveDateEnd" type="date" placeholder="请选择日期" />
                </el-form-item>

                <el-form-item label="城市:" prop="city">
                    <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择" clearable>
                        <el-option v-for="item in cityList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>

                <el-form-item label="接单方:" prop="orderReceiver">
                    <el-select class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请选择" clearable>
                        <el-option v-for="item in receiverList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>

                <el-form-item label="接单客服:" prop="receiverService">
                    <el-select class="width220" v-model="obj.queryParams.receiverService" placeholder="请选择" clearable>
                        <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="人员分布:" prop="personnelDistribution">
                    <el-select class="width220" v-model="obj.queryParams.personnelDistribution" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in distributionList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="减员原因:" prop="reductionReason">
                    <el-select class="width220" v-model="obj.queryParams.reductionReason" placeholder="请选择" clearable>
                        <el-option v-for="item in reductionReasonList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同类型:" prop="contractType">
                    <el-select class="width220" v-model="obj.queryParams.contractType" placeholder="请选择" clearable>
                        <el-option v-for="item in contractTypeList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否单立户:" prop="isSingleAccount">
                    <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </div>

            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" icon="Expand" plain @click="showMore">显示更多</el-button>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleDetail">订单详情</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleModifyOrderEntryAndDepartureInfo">修改订单入离职信息</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleModifyEmployeeNameOrPhoneNumber">修改雇员姓名或者手机号</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleModifyCertificateNumber">修改证件编号</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleModifyOrderBasicInfo">修改订单基本信息</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleDownloadTemplate">下载模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleDataExport">数据导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleDataImport">数据导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleDataImportHistory">查询导入历史</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleUploadIdentityInfo">上传身份信息</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleUploadProxyEmployeeResignationInfo">代理员工离职资料上传</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="订单编号" align="center" prop="orderCode">
                <template #default="scope">
                    <div class="order-number" @click="showOrderMenu($event, scope.row)">
                        {{ scope.row.orderCode }}
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件类型" align="center" prop="idType" />
            <el-table-column label="证件编号" align="center" prop="idNumber" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="年龄" align="center" prop="age" />
            <el-table-column label="入离职状态" align="center" prop="employmentStatus" />
            <el-table-column label="性别" align="center" prop="gender" />
            <el-table-column label="退休日期" align="center" prop="retirementDate" />
            <el-table-column label="户口性质" align="center" prop="householdType" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="劳动合同是否上传" align="center" prop="isLaborContractUploaded" />
            <el-table-column label="代理员工离职资料是否上传" align="center" prop="isProxyResignationUploaded" />
            <el-table-column label="离职资料是吆上传" align="center" prop="isResignationUploaded" />
            <el-table-column label="身份证上传标识" align="center" prop="isIdCardUploaded" />
            <el-table-column label="人员分布" align="center" prop="personnelDistribution" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="派单类型" align="center" prop="orderType" />
            <el-table-column label="手机" align="center" prop="mobile" />
            <el-table-column label="联系电话" align="center" prop="telephone" />
            <el-table-column label="小合同编号" align="center" prop="smallContractCode" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="派单方" align="center" prop="orderSender" />
            <el-table-column label="接单方" align="center" prop="orderReceiver" />
            <el-table-column label="自有/供应商" align="center" prop="supplierType" />
            <el-table-column label="派单方客服" align="center" prop="senderService" />
            <el-table-column label="接单方客服" align="center" prop="receiverService" />
            <el-table-column label="入职日期" align="center" prop="entryDate" />
            <el-table-column label="申请入职日期" align="center" prop="applyEntryDate" />
            <el-table-column label="企业端申请入职日期" align="center" prop="companyApplyEntryDate" />
            <el-table-column label="雇员状态" align="center" prop="employeeStatus" />
            <el-table-column label="入职备注" align="center" prop="entryRemark" />
            <el-table-column label="变更状态" align="center" prop="changeStatus" />
            <el-table-column label="企业端申报离职日期" align="center" prop="companyApplyLeaveDate" />
            <el-table-column label="离职日期" align="center" prop="leaveDate" />
            <el-table-column label="申报离职人" align="center" prop="applyLeavePerson" />
            <el-table-column label="是否外呼" align="center" prop="isOutCall" />
            <el-table-column label="是否单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="是否单工伤" align="center" prop="isSingleWorkInjury" />
            <el-table-column label="离职原因" align="center" prop="leaveReason" />
            <el-table-column label="减员原因" align="center" prop="reductionReason" />
            <el-table-column label="增员确认过程" align="center" prop="addConfirmProcess" />
            <el-table-column label="减员确认过程" align="center" prop="reduceConfirmProcess" />
            <el-table-column label="变更确认过程" align="center" prop="changeConfirmProcess" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="是否连续转移标识" align="center" prop="isContinuousTransfer" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 订单详情 -->
        <order-details v-model:dialogShow="obj.dialogShow" :dialogForm="obj.dialogForm" :title="obj.title"
            isDetail="true" />

        <!-- 导入历史 -->
        <el-dialog v-model="obj.importHistoryShow" title="查询导入历史" width="50%">
            <el-form :model="obj.dialogForm" ref="importHistoryRef" inline label-width="auto">
                <el-form-item label="导入日期从" prop="importTime">
                    <el-date-picker v-model="obj.dialogForm.importTime" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="导入日期到" prop="importTime">
                    <el-date-picker v-model="obj.dialogForm.importTime" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="导入人" prop="importPerson">
                    <el-input v-model="obj.dialogForm.importPerson" placeholder="请输入导入人" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleImportHistoryQuery">查询</el-button>
                    <el-button type="primary" @click="handleImportHistoryReset">重置</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="obj.importHistoryData" border>
                <el-table-column label="导入编号" align="center" prop="importCode" />
                <el-table-column label="导入人" align="center" prop="importPerson" />
                <el-table-column label="导入时间" align="center" prop="importTime" />
                <el-table-column label="备注" align="center" prop="remark" />
                <el-table-column label="成功记录数" align="center" prop="successCount" />
                <el-table-column label="失败记录数" align="center" prop="failCount" />
                <el-table-column label="导入文件" align="center" prop="importFile" />
                <el-table-column label="处理状态" align="center" prop="processStatus" />
                <el-table-column label="创建人" align="center" prop="createBy" />
                <el-table-column label="创建时间" align="center" prop="createTime" />
            </el-table>
        </el-dialog>

        <!-- 订单操作菜单 -->
        <div v-if="showMenu" class="order-menu"
            :style="{ left: menuPosition.left + 'px', top: menuPosition.top + 'px' }" @mouseleave="hideMenu">
            <div class="menu-item">增员过程</div>
            <div class="menu-item contract-number">
                合同编号
                <span>{{ currentOrder ? currentOrder.contractNumber : '' }}</span>
            </div>
            <div class="menu-item create-info" v-if="currentOrder && currentOrder.creatorInfo">
                派单与生成个人订单： {{ currentOrder.creatorInfo }}
            </div>
            <div class="menu-item">减员过程</div>
            <div class="menu-item">变更过程</div>
            <div class="menu-item">变更备注</div>
            <div class="menu-item">离职备注</div>
        </div>
    </div>
</template>


<script setup>

import { listScale } from "@/api/reonApi/scale";
import orderDetails from '../components/dialog/orderDetails.vue';

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 雇员状态列表
const employeeStatusList = ref([]);
// 入离职状态列表
const employmentStatusList = ref([]);
// 变更状态列表
const changeStatusList = ref([]);
// 客服列表
const serviceList = ref([]);
// 派单方列表
const senderList = ref([]);
// 接单方列表
const receiverList = ref([]);
// 城市列表
const cityList = ref([]);
// 人员分布列表
const distributionList = ref([]);
// 减员原因列表
const reductionReasonList = ref([]);
// 合同类型列表
const contractTypeList = ref([]);
// 派单类型列表
const orderTypeList = ref([]);

// 表单验证规则
const rules = {
    // 可以根据需要添加验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    showMore: false,//显示更多
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null,
        employeeName: null,
        idNumber: null,
        customerCode: null,
        orderCode: null,
        smallContractName: null,
        employeeStatus: null,
        customerName: null,
        createTimeStart: null,
        orderSender: null,
        senderService: null,
        orderType: null,
        createTimeEnd: null,
        entryDateStart: null,
        entryDateEnd: null,
        leaveDateStart: null,
        applyLeaveDateStart: null,
        changeStatus: null,
        employmentStatus: null,
        leaveDateEnd: null,
        applyLeaveDateEnd: null,
        city: null,
        orderReceiver: null,
        receiverService: null,
        personnelDistribution: null,
        reductionReason: null,
        contractType: null,
        isSingleAccount: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    importHistoryData: [],//导入历史数据
    dialogForm: {},//导入表单
    dialogShow: false,//导入弹窗
    dialogShow2: false,//导入弹窗
    importHistoryShow: false,//导入历史弹窗
    sendOrdersShow: false,//派单方弹窗
    bindSave: false,//绑定保存
    ids: [],//选中的id
    title: "",//标题
    sendOrdersList: [],//派单方列表
})

/** 获取雇员状态列表 */
function getEmployeeStatusList() {
    // 这里可以调用API获取雇员状态列表
    employeeStatusList.value = [
        { code: '1', name: '正常' },
        { code: '2', name: '待审核' },
        { code: '3', name: '已挂起' },
        { code: '4', name: '已驳回' }
    ];
}

/** 获取入离职状态列表 */
function getEmploymentStatusList() {
    // 这里可以调用API获取入离职状态列表
    employmentStatusList.value = [
        { code: '1', name: '在职' },
        { code: '2', name: '离职' },
        { code: '3', name: '待入职' }
    ];
}

/** 获取变更状态列表 */
function getChangeStatusList() {
    // 这里可以调用API获取变更状态列表
    changeStatusList.value = [
        { code: '1', name: '未变更' },
        { code: '2', name: '已变更' },
        { code: '3', name: '变更中' }
    ];
}

/** 获取客服列表 */
function getServiceList() {
    // 这里可以调用API获取客服列表
    serviceList.value = [
        { code: '1', name: '客服1' },
        { code: '2', name: '客服2' },
        { code: '3', name: '客服3' }
    ];
}

/** 获取派单方列表 */
function getSenderList() {
    // 这里可以调用API获取派单方列表
    senderList.value = [
        { code: '1', name: '派单方1' },
        { code: '2', name: '派单方2' },
        { code: '3', name: '派单方3' }
    ];
    // 同时更新obj.sendOrdersList
    obj.sendOrdersList = [
        { code: '1', name: '派单方1' },
        { code: '2', name: '派单方2' },
        { code: '3', name: '派单方3' }
    ];
}

/** 获取接单方列表 */
function getReceiverList() {
    // 这里可以调用API获取接单方列表
    receiverList.value = [
        { code: '1', name: '接单方1' },
        { code: '2', name: '接单方2' },
        { code: '3', name: '接单方3' }
    ];
}

/** 获取城市列表 */
function getCityList() {
    // 这里可以调用API获取城市列表
    cityList.value = [
        { code: '1', name: '北京' },
        { code: '2', name: '上海' },
        { code: '3', name: '广州' }
    ];
}

/** 获取人员分布列表 */
function getDistributionList() {
    // 这里可以调用API获取人员分布列表
    distributionList.value = [
        { code: '1', name: '分布1' },
        { code: '2', name: '分布2' },
        { code: '3', name: '分布3' }
    ];
}

/** 获取减员原因列表 */
function getReductionReasonList() {
    // 这里可以调用API获取减员原因列表
    reductionReasonList.value = [
        { code: '1', name: '原因1' },
        { code: '2', name: '原因2' },
        { code: '3', name: '原因3' }
    ];
}

/** 获取合同类型列表 */
function getContractTypeList() {
    // 这里可以调用API获取合同类型列表
    contractTypeList.value = [
        { code: '1', name: '类型1' },
        { code: '2', name: '类型2' },
        { code: '3', name: '类型3' }
    ];
}

/** 获取派单类型列表 */
function getOrderTypeList() {
    // 这里可以调用API获取派单类型列表
    orderTypeList.value = [
        { code: '1', name: '类型1' },
        { code: '2', name: '类型2' },
        { code: '3', name: '类型3' }
    ];
}
/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                orderCode: 'DD20230001',
                employeeName: '雇员姓名1',
                idType: '身份证',
                idNumber: '110101199001011234',
                contractCode: 'HT20230001',
                age: 30,
                employmentStatus: '在职',
                gender: '男',
                retirementDate: '2050-01-01',
                householdType: '城镇户口',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                isLaborContractUploaded: '是',
                isProxyResignationUploaded: '否',
                isResignationUploaded: '否',
                isIdCardUploaded: '是',
                personnelDistribution: '分布1',
                contractType: '类型1',
                orderType: '类型1',
                mobile: '13800138000',
                telephone: '010-12345678',
                smallContractCode: 'XHT20230001',
                smallContractName: '小合同名称1',
                orderSender: '派单方1',
                orderReceiver: '接单方1',
                supplierType: '自有',
                senderService: '客服1',
                receiverService: '客服2',
                entryDate: '2023-01-01',
                applyEntryDate: '2022-12-15',
                companyApplyEntryDate: '2022-12-10',
                employeeStatus: '正常',
                entryRemark: '入职备注1',
                changeStatus: '未变更',
                companyApplyLeaveDate: '',
                leaveDate: '',
                applyLeavePerson: '',
                isOutCall: '否',
                isSingleAccount: '否',
                isSingleWorkInjury: '否',
                leaveReason: '',
                reductionReason: '',
                addConfirmProcess: '增员确认过程1',
                reduceConfirmProcess: '',
                changeConfirmProcess: '',
                uniqueId: 'WY20230001',
                isContinuousTransfer: '否'
            },
            {
                id: 2,
                orderCode: 'DD20230002',
                employeeName: '雇员姓名2',
                idType: '身份证',
                idNumber: '110101199001011235',
                contractCode: 'HT20230002',
                age: 28,
                employmentStatus: '在职',
                gender: '女',
                retirementDate: '2050-01-01',
                householdType: '城镇户口',
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                isLaborContractUploaded: '是',
                isProxyResignationUploaded: '否',
                isResignationUploaded: '否',
                isIdCardUploaded: '是',
                personnelDistribution: '分布2',
                contractType: '类型2',
                orderType: '类型2',
                mobile: '13800138001',
                telephone: '010-12345679',
                smallContractCode: 'XHT20230002',
                smallContractName: '小合同名称2',
                orderSender: '派单方2',
                orderReceiver: '接单方2',
                supplierType: '自有',
                senderService: '客服2',
                receiverService: '客服3',
                entryDate: '2023-02-01',
                applyEntryDate: '2023-01-15',
                companyApplyEntryDate: '2023-01-10',
                employeeStatus: '正常',
                entryRemark: '入职备注2',
                changeStatus: '未变更',
                companyApplyLeaveDate: '',
                leaveDate: '',
                applyLeavePerson: '',
                isOutCall: '否',
                isSingleAccount: '否',
                isSingleWorkInjury: '否',
                leaveReason: '',
                reductionReason: '',
                addConfirmProcess: '增员确认过程2',
                reduceConfirmProcess: '',
                changeConfirmProcess: '',
                uniqueId: 'WY20230002',
                isContinuousTransfer: '否'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 获取导入历史数据 */
function getImportHistoryData() {
    // 这里可以调用API获取导入历史数据
    obj.importHistoryData = [
        {
            importCode: 'DR20230001',
            importPerson: '导入人1',
            importTime: '2023-01-01 10:00:00',
            remark: '备注1',
            successCount: 10,
            failCount: 0,
            importFile: '文件1.xlsx',
            processStatus: '处理成功',
            createBy: '创建人1',
            createTime: '2023-01-01 10:00:00'
        },
        {
            importCode: '**********',
            importPerson: '导入人2',
            importTime: '2023-02-01 10:00:00',
            remark: '备注2',
            successCount: 8,
            failCount: 2,
            importFile: '文件2.xlsx',
            processStatus: '处理成功',
            createBy: '创建人2',
            createTime: '2023-02-01 10:00:00'
        }
    ];
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 显示更多 */
function showMore() {
    obj.showMore = !obj.showMore;
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 订单详情
function handleDetail(row) {
    obj.dialogShow = true;
    obj.title = '订单详情';
    if (row) {
        // 如果是从表格行点击进入，可以加载该行的数据
        obj.dialogForm = { ...row };
    }
}

// 关闭订单详情
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {};
}

// 修改订单入离职信息
function handleModifyOrderEntryAndDepartureInfo() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要修改的订单');
        return;
    }
    obj.dialogShow2 = true;
    obj.title = '修改订单入离职信息';
    // 加载选中行的数据
    const selectedRow = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedRow) {
        obj.dialogForm = { ...selectedRow };
    }
}

// 修改雇员姓名或者手机号
function handleModifyEmployeeNameOrPhoneNumber() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要修改的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 修改证件编号
function handleModifyCertificateNumber() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要修改的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 修改订单基本信息
function handleModifyOrderBasicInfo() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要修改的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 下载模版
function handleDownloadTemplate() {
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 数据导出
function handleDataExport() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要导出的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 数据导入
function handleDataImport() {
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 查询导入历史
function handleDataImportHistory() {
    obj.importHistoryShow = true;
    getImportHistoryData();
}

// 导入历史查询
function handleImportHistoryQuery() {
    getImportHistoryData();
}

// 导入历史重置
function handleImportHistoryReset() {
    obj.dialogForm = {};
    getImportHistoryData();
}

// 上传身份信息
function handleUploadIdentityInfo() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要上传身份信息的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 代理员工离职资料上传
function handleUploadProxyEmployeeResignationInfo() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要上传离职资料的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 菜单状态
const showMenu = ref(false)
const menuPosition = reactive({ left: 0, top: 0 })
const currentOrder = ref(null)

// 显示菜单
const showOrderMenu = (event, row) => {
    // 阻止事件冒泡
    event.stopPropagation()

    // 设置菜单位置
    const rect = event.target.getBoundingClientRect()
    menuPosition.left = rect.left
    menuPosition.top = rect.bottom + window.scrollY

    // 存储当前订单并显示菜单
    currentOrder.value = row
    showMenu.value = true

    // 点击页面其他位置关闭菜单
    document.addEventListener('click', hideMenuOnClickOutside)
}

// 隐藏菜单
const hideMenu = () => {
    showMenu.value = false
    document.removeEventListener('click', hideMenuOnClickOutside)
}

// 点击外部关闭菜单
const hideMenuOnClickOutside = () => {
    hideMenu()
}

// 初始化数据
getEmployeeStatusList();
getEmploymentStatusList();
getChangeStatusList();
getServiceList();
getSenderList();
getReceiverList();
getCityList();
getDistributionList();
getReductionReasonList();
getContractTypeList();
getOrderTypeList();
getList();
</script>
<style lang="scss" scoped>
.content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out;
}

.content.expanded {
    max-height: 1000px;
    /* 设置一个足够大的值 */
}

:deep(.el-table__body tr.current-row>td.el-table__cell) {
    background-color: aqua;
}

.order-number {
    color: #1890ff;
    cursor: pointer;
    text-decoration: underline;
}

.order-menu {
    position: absolute;
    z-index: 1000;
    background: rgba(70, 70, 70, 0.9);
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    color: white;
    width: 300px;
}
</style>