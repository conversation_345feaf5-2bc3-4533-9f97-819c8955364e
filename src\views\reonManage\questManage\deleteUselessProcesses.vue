<!-- 删除无用流程实例 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="审批数据类型:" prop="dataType">
                <el-select class="width220" v-model="obj.queryParams.dataType" placeholder="请选择" clearable>
                    <el-option v-for="item in dataTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractNo">
                <el-input class="width220" v-model="obj.queryParams.contractNo" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="报价单编号:" prop="quotationNo">
                <el-input class="width220" v-model="obj.queryParams.quotationNo" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="delete-useless-processes" />
            <PrintButton print-id="print-table-area" print-title="删除无用流程实例表格" icon="Printer" />
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <div>
            <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column v-if="obj.selectedColumns.includes('customerName')" label="客户名称" align="center"
                    prop="customerName" />
                <el-table-column v-if="obj.selectedColumns.includes('contractNo')" label="合同编号" align="center"
                    prop="contractNo" />
                <el-table-column v-if="obj.selectedColumns.includes('contractName')" label="合同名称" align="center"
                    prop="contractName" />
                <el-table-column v-if="obj.selectedColumns.includes('processType')" label="流程审批类型" align="center"
                    prop="processType" />
                <el-table-column v-if="obj.selectedColumns.includes('processStatus')" label="流程审批状态" align="center"
                    prop="processStatus" />
                <el-table-column v-if="obj.selectedColumns.includes('submitter')" label="提交人" align="center"
                    prop="submitter" />
                <el-table-column label="操作" align="center" width="180">
                    <template #default="scope">
                        <el-button type="primary" link @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 打印区域 -->
        <div id="print-table-area" class="print-area">
            <table class="print-table">
                <thead>
                    <tr>
                        <th v-if="obj.selectedColumns.includes('customerName')">客户名称</th>
                        <th v-if="obj.selectedColumns.includes('contractNo')">合同编号</th>
                        <th v-if="obj.selectedColumns.includes('contractName')">合同名称</th>
                        <th v-if="obj.selectedColumns.includes('processType')">流程审批类型</th>
                        <th v-if="obj.selectedColumns.includes('processStatus')">流程审批状态</th>
                        <th v-if="obj.selectedColumns.includes('submitter')">提交人</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in obj.tableData" :key="index">
                        <td v-if="obj.selectedColumns.includes('customerName')">{{ item.customerName }}</td>
                        <td v-if="obj.selectedColumns.includes('contractNo')">{{ item.contractNo }}</td>
                        <td v-if="obj.selectedColumns.includes('contractName')">{{ item.contractName }}</td>
                        <td v-if="obj.selectedColumns.includes('processType')">{{ item.processType }}</td>
                        <td v-if="obj.selectedColumns.includes('processStatus')">{{ item.processStatus }}</td>
                        <td v-if="obj.selectedColumns.includes('submitter')">{{ item.submitter }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="DeleteUselessProcesses">
import { listInstance, delInstance } from '@/api/reonApi/instance';

import PrintButton from '@/components/PrintButton.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 审批数据类型选项
const dataTypeOptions = [
    { value: '1', label: '合同审批' },
    { value: '2', label: '报价单审批' },
    { value: '3', label: '付款审批' },
    { value: '4', label: '其他审批' }
];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        dataType: null,
        contractNo: null,
        quotationNo: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题

    selectedColumns: ['customerName', 'contractNo', 'contractName', 'processType', 'processStatus', 'submitter'],//默认显示所有列
    columnOptions: [
        { label: '客户名称', prop: 'customerName' },
        { label: '合同编号', prop: 'contractNo' },
        { label: '合同名称', prop: 'contractName' },
        { label: '流程审批类型', prop: 'processType' },
        { label: '流程审批状态', prop: 'processStatus' },
        { label: '提交人', prop: 'submitter' }
    ],//列配置选项
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    listInstance(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        let arr = []
        for (let i = 0; i < 30; i++) {
            arr.push({
                customerName: '客户名称' + i,
                contractNo: '合同编号' + i,
                contractName: '合同名称' + i,
                processType: '流程审批类型' + i,
                processStatus: '流程审批状态' + i,
                submitter: '提交人' + i
            });
        }
        obj.tableData = arr;
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.multiple = !selection.length;
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row?.id || obj.ids;
    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delInstance(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/instance/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>
