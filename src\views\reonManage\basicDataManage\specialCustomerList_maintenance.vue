<!-- 特殊客户名单维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input v-model="obj.queryParams.customerName" placeholder="请输入客户名称" @focus="handleCustomer" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" prop="id" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="类型" align="center" prop="type">
                <template #default="scope">
                    <span>{{ scope.row.type === '1' ? '禁止自动开票' : '其他' }}</span>
                </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" prop="createBy" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 新增 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="20%" append-to-body draggable>
            <el-form :model="obj.dialogForm" ref="formRef" :rules="obj.rules" label-width="auto">
                <el-form-item label="客户名称" prop="name">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.name" placeholder="请输入客户名称" />
                </el-form-item>
                <el-form-item label="用途" prop="type">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.type" placeholder="请选择" clearable>
                        <el-option label="禁止自动开票" value="1" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <client :width="'35%'" v-model:show="clientShow" :isShow="false" @select="handleSelect" />
    </div>
</template>

<script setup name="SpecialCustomerList_maintenance">

import client from '@/views/reonManage/components/client.vue'
import { listImport } from "@/api/reonApi/import";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const clientShow = ref(false);

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
    },//查询表单
    rules: {
        name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择用途', trigger: 'change' }],
    },//新增表单验证规则
    dialogForm: {},//新增表单
    dialogShow: false,//新增弹出框
    total: 0,//总条数
    tableData: [
        {
            id: 1,
            customerName: '北京海天科技有限公司',
            type: '1',
            createBy: 'admin'
        },
        {
            id: 2,
            customerName: '上海星辰科技有限公司',
            type: '1',
            createBy: 'admin'
        },
        {
            id: 3,
            customerName: '广州天河科技有限公司',
            type: '1',
            createBy: 'user1'
        }
    ],//列表
    ids: [],//选中的id
    title: "",//标题
})
/** 选择 */
function handleSelect(row) {
    if (row) {
        obj.queryParams.customerName = row.name;
    } else {
        obj.queryParams.customerName = null;
    }
}
/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 客户信息
function handleCustomer() {
    clientShow.value = true;
}
/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.title = "新增";
    obj.dialogShow = true;
}
/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = JSON.parse(JSON.stringify(row || obj.tableData.find(item => obj.ids.includes(item.id))));
    obj.title = "修改";
    obj.dialogShow = true;
}
/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}
/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

getList();
</script>
<style lang="scss" scoped></style>