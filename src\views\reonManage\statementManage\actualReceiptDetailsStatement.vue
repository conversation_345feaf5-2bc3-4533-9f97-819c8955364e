<!-- 实收明细报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="客户帐套:" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户帐套" clearable>
                    <el-option v-for="item in customerAccountOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单年月(起):" prop="billMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonthStart" type="month"
                    placeholder="请选择起始月份" clearable />
            </el-form-item>
            <el-form-item label="账单年月(止):" prop="billMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonthEnd" type="month"
                    placeholder="请选择结束月份" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="出账单分公司:" prop="billingCompany">
                <el-select class="width220" v-model="obj.queryParams.billingCompany" placeholder="请选择分公司" clearable>
                    <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="锁定状态:" prop="lockStatus">
                <el-select class="width220" v-model="obj.queryParams.lockStatus" placeholder="请选择锁定状态" clearable>
                    <el-option v-for="item in lockStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="核销状态:" prop="verificationStatus">
                <el-select class="width220" v-model="obj.queryParams.verificationStatus" placeholder="请选择核销状态"
                    clearable>
                    <el-option v-for="item in verificationStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="开票状态:" prop="invoiceStatus">
                <el-select class="width220" v-model="obj.queryParams.invoiceStatus" placeholder="请选择开票状态" clearable>
                    <el-option v-for="item in invoiceStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="责任客服:" prop="customerService">
                <el-select class="width220" v-model="obj.queryParams.customerService" placeholder="请选择责任客服" clearable>
                    <el-option v-for="item in customerServiceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="财务应收年月(起):" prop="financeReceivableMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.financeReceivableMonthStart" type="month"
                    placeholder="请选择起始月份" clearable />
            </el-form-item>
            <el-form-item label="财务应收年月(止):" prop="financeReceivableMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.financeReceivableMonthEnd" type="month"
                    placeholder="请选择结束月份" clearable />
            </el-form-item>
            <el-form-item label="开票时间(起):" prop="invoiceTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceTimeStart" type="date"
                    placeholder="请选择开票起始日期" clearable />
            </el-form-item>
            <el-form-item label="开票时间(止):" prop="invoiceTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceTimeEnd" type="date"
                    placeholder="请选择开票结束日期" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb20">
                <el-col :span="24">
                    <el-button type="primary" icon="Refresh" @click="handleSearch">查询</el-button>
                    <el-button icon="Refresh" @click="handleReset">重置</el-button>
                    <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                    <div style="display: inline-block;" class="ml20">
                        <span>总数:</span>
                        <span>100</span>
                    </div>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" plain icon="Download" @click="handleExport">账单打印</el-button>
            </el-col>
        </el-row>
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" border style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="customerName" label="客户名称" align="center" />
            <el-table-column prop="groupName" label="集团名称" align="center" />
            <el-table-column prop="customerAccount" label="客户帐套" align="center" />
            <el-table-column prop="billMonth" label="账单年月" align="center" />
            <el-table-column prop="financeReceivableMonth" label="财务应收年月" align="center" />
            <el-table-column prop="billPersonCount" label="账单人数" align="center" />
            <el-table-column prop="receivableAmount" label="应收金额" align="center">
                <template #default="scope">
                    {{ formatAmount(scope.row.receivableAmount) }}
                </template>
            </el-table-column>
            <el-table-column prop="billVersion" label="账单版本" align="center" />
            <el-table-column prop="generateStatus" label="生成状态" align="center">
                <template #default="scope">
                    <dict-tag :options="generateStatusOptions" :value="scope.row.generateStatus" />
                </template>
            </el-table-column>
            <el-table-column prop="billStatus" label="账单状态" align="center">
                <template #default="scope">
                    <dict-tag :options="billStatusOptions" :value="scope.row.billStatus" />
                </template>
            </el-table-column>
            <el-table-column prop="firstGenerateTime" label="首次生成时间" align="center" />
            <el-table-column prop="customerService" label="责任客服" align="center" />
            <el-table-column prop="sales" label="销售" align="center" />
            <el-table-column prop="salesDepartment" label="销售所在部门" align="center" />
            <el-table-column prop="verificationStatus" label="核销状态" align="center">
                <template #default="scope">
                    <dict-tag :options="verificationStatusOptions" :value="scope.row.verificationStatus" />
                </template>
            </el-table-column>
            <el-table-column prop="invoiceStatus" label="开票状态" align="center">
                <template #default="scope">
                    <dict-tag :options="invoiceStatusOptions" :value="scope.row.invoiceStatus" />
                </template>
            </el-table-column>
            <el-table-column prop="unverifiedAmount" label="未核销金额" align="center">
                <template #default="scope">
                    {{ formatAmount(scope.row.unverifiedAmount) }}
                </template>
            </el-table-column>
            <el-table-column prop="verifiedAmount" label="已核销金额" align="center">
                <template #default="scope">
                    {{ formatAmount(scope.row.verifiedAmount) }}
                </template>
            </el-table-column>
            <el-table-column prop="uninvoicedAmount" label="未开票金额" align="center">
                <template #default="scope">
                    {{ formatAmount(scope.row.uninvoicedAmount) }}
                </template>
            </el-table-column>
            <el-table-column prop="invoicedAmount" label="已开票金额" align="center">
                <template #default="scope">
                    {{ formatAmount(scope.row.invoicedAmount) }}
                </template>
            </el-table-column>
            <el-table-column prop="signCity" label="签单地城市" align="center" />
            <el-table-column prop="serviceFee" label="服务费" align="center">
                <template #default="scope">
                    {{ formatAmount(scope.row.serviceFee) }}
                </template>
            </el-table-column>
            <el-table-column prop="agreedBillingDate" label="约定出账日" align="center" />
            <el-table-column prop="agreedLockDate" label="约定锁定日" align="center" />
            <el-table-column prop="agreedPaymentDate" label="约定到款日" align="center" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="ActualReceiptDetailsStatement">

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客户帐套选项
const customerAccountOptions = ref([
    { value: '1', label: '帐套A' },
    { value: '2', label: '帐套B' },
    { value: '3', label: '帐套C' }
]);

// 公司选项
const companyOptions = ref([
    { value: '1', label: '分公司A' },
    { value: '2', label: '分公司B' },
    { value: '3', label: '分公司C' }
]);

// 锁定状态选项
const lockStatusOptions = ref([
    { value: '0', label: '未锁定' },
    { value: '1', label: '已锁定' }
]);

// 核销状态选项
const verificationStatusOptions = ref([
    { value: '0', label: '未核销' },
    { value: '1', label: '部分核销' },
    { value: '2', label: '全部核销' }
]);

// 开票状态选项
const invoiceStatusOptions = ref([
    { value: '0', label: '未开票' },
    { value: '1', label: '部分开票' },
    { value: '2', label: '全部开票' }
]);

// 客服选项
const customerServiceOptions = ref([
    { value: '1', label: '客服1' },
    { value: '2', label: '客服2' },
    { value: '3', label: '客服3' }
]);

// 生成状态选项
const generateStatusOptions = ref([
    { value: '0', label: '未生成' },
    { value: '1', label: '已生成' }
]);

// 账单状态选项
const billStatusOptions = ref([
    { value: '0', label: '草稿' },
    { value: '1', label: '已确认' },
    { value: '2', label: '已锁定' }
]);

const obj = reactive({
    // 加载状态
    loading: false,
    // 选中数据
    ids: [],
    // 单选
    single: true,
    // 多选
    multiple: true,
    // 总条数
    total: 0,
    // 表格数据
    tableData: [],
    // 查询参数
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractName: null,
        customerAccount: null,
        billMonthStart: null,
        billMonthEnd: null,
        customerName: null,
        billingCompany: null,
        lockStatus: null,
        verificationStatus: null,
        invoiceStatus: null,
        customerService: null,
        financeReceivableMonthStart: null,
        financeReceivableMonthEnd: null,
        invoiceTimeStart: null,
        invoiceTimeEnd: null
    }
});

/** 获取实收明细列表 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerName: '客户A',
                groupName: '集团A',
                customerAccount: '帐套A',
                billMonth: '2023-05',
                financeReceivableMonth: '2023-06',
                billPersonCount: 100,
                receivableAmount: 50000.00,
                billVersion: 'V1.0',
                generateStatus: '1',
                billStatus: '1',
                firstGenerateTime: '2023-05-15 10:00:00',
                customerService: '客服1',
                sales: '销售1',
                salesDepartment: '销售部门A',
                verificationStatus: '1',
                invoiceStatus: '1',
                unverifiedAmount: 20000.00,
                verifiedAmount: 30000.00,
                uninvoicedAmount: 15000.00,
                invoicedAmount: 35000.00,
                signCity: '北京',
                serviceFee: 5000.00,
                agreedBillingDate: '5日',
                agreedLockDate: '10日',
                agreedPaymentDate: '15日'
            },
            {
                id: 2,
                customerName: '客户B',
                groupName: '集团B',
                customerAccount: '帐套B',
                billMonth: '2023-06',
                financeReceivableMonth: '2023-07',
                billPersonCount: 150,
                receivableAmount: 75000.00,
                billVersion: 'V1.0',
                generateStatus: '1',
                billStatus: '2',
                firstGenerateTime: '2023-06-15 10:00:00',
                customerService: '客服2',
                sales: '销售2',
                salesDepartment: '销售部门B',
                verificationStatus: '2',
                invoiceStatus: '2',
                unverifiedAmount: 0.00,
                verifiedAmount: 75000.00,
                uninvoicedAmount: 0.00,
                invoicedAmount: 75000.00,
                signCity: '上海',
                serviceFee: 7500.00,
                agreedBillingDate: '5日',
                agreedLockDate: '10日',
                agreedPaymentDate: '15日'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>