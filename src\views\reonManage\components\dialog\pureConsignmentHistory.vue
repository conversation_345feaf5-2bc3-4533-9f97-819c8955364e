<template>
    <div>
        <el-dialog v-model="dialogShow" :title="props.title" width="50%" append-to-body draggable @close="close">
            <el-table :data="props.tableData" border>
                <el-table-column label="唯一号" align="center" prop="uniqueId" />
                <el-table-column label="雇员姓名" align="center" prop="employeeName" />
                <el-table-column label="证件类型" align="center" prop="idType">
                    <template #default="scope">
                        <el-tag>{{ scope.row.idType == '1' ? '身份证' : '护照' }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="证件号码" align="center" prop="idNumber" />
                <el-table-column label="手机号码" align="center" prop="phoneNumber" />
                <el-table-column label="备份时间" align="center" prop="backupTime" />
                <el-table-column label="操作人" align="center" prop="operator" />
            </el-table>
            <pagination v-show="props.total > 0" :total="props.total" v-model:page="props.currentPage"
                v-model:limit="props.pageSize" @pagination="handlePagination" />
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="close">关闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>

const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false,
    },
    tableData: {
        type: Array,
        default: () => []
    },
    title: {
        type: String,
        default: ''
    },
    currentPage: {
        type: Number,
        default: 1
    },
    pageSize: {
        type: Number,
        default: 10
    },
    total: {
        type: Number,
        default: 0
    },
})
// 使用本地变量来跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.show的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});

const emit = defineEmits(['historyClose', 'handlePagination', 'update:dialogShow']);

function close() {
    dialogShow.value = false;
    emit('historyClose');
}

function handlePagination() {
    emit('handlePagination');
}
</script>
