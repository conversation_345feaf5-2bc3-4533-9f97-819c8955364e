<!-- 社保套餐维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="所属城市:" prop="cityCode">
                <el-select class="width180" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否单立户:" prop="isStandalone">
                <el-select class="width180" v-model="obj.queryParams.isStandalone" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="分公司/供应商/单立户:" prop="companyType">
                <el-select class="width180" v-model="obj.queryParams.companyType" placeholder="请选择" clearable>
                    <el-option v-for="item in companyTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="社保套餐名称:" prop="setMealName">
                <el-input class="width180" v-model="obj.queryParams.setMealName" placeholder="请输入社保套餐名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="人员类型名称:" prop="personnelType">
                <el-select class="width180" v-model="obj.queryParams.personnelType" placeholder="请选择人员类型" clearable>
                    <el-option v-for="item in personnelTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否社保必缴费:" prop="isSocialRequired">
                <el-select class="width180" v-model="obj.queryParams.isSocialRequired" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否公积金必缴:" prop="isHousingRequired">
                <el-select class="width180" v-model="obj.queryParams.isHousingRequired" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否有补充公积金:" prop="hasSupplementHousing">
                <el-select class="width180" v-model="obj.queryParams.hasSupplementHousing" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否标准套餐:" prop="isStandardSetMeal">
                <el-select class="width180" v-model="obj.queryParams.isStandardSetMeal" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="View" :disabled="obj.single" @click="handleDetail">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain :disabled="obj.single" @click="handleMaintenance">维护社保公积金产品</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain :disabled="obj.single" @click="handleOpen">生效</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain :disabled="obj.single" @click="handlePause">暂停</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="社保套餐名称" align="center" prop="setMealName" />
            <el-table-column label="所属城市" align="center" prop="cityName" />
            <el-table-column label="人员类型" align="center" prop="personnelTypeName" />
            <el-table-column label="分公司/供应商" align="center" prop="companyName" />
            <el-table-column label="是否单立户" align="center" prop="isStandalone">
                <template #default="scope">
                    <el-tag :type="scope.row.isStandalone === '1' ? 'success' : 'info'">
                        {{ scope.row.isStandalone === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="是否社保必缴" align="center" prop="isSocialRequired">
                <template #default="scope">
                    <el-tag :type="scope.row.isSocialRequired === '1' ? 'success' : 'info'">
                        {{ scope.row.isSocialRequired === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="是否公积金必缴" align="center" prop="isHousingRequired">
                <template #default="scope">
                    <el-tag :type="scope.row.isHousingRequired === '1' ? 'success' : 'info'">
                        {{ scope.row.isHousingRequired === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.status === '1' ? 'success' : scope.row.status === '2' ? 'warning' : 'danger'">
                        {{ scope.row.status === '1' ? '有效' : scope.row.status === '2' ? '暂停' : '无效' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="历史信息查询" align="center">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"></el-button>
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
                    <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"></el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!--新增/修改/查看/维护社保公积金产品-->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="65%">
            <el-form :model="obj.dialogForm" ref="dialogRef" inline label-width="auto">
                <el-form-item label="社保套餐名称:" prop="setMealName">
                    <el-input :disabled="obj.isDetails" class="width180" v-model="obj.dialogForm.setMealName"
                        placeholder="请输入社保套餐名称" clearable />
                </el-form-item>
                <el-form-item label="是否社保必缴费:" prop="isSocialRequired">
                    <el-select :disabled="obj.isDetails" class="width180" v-model="obj.dialogForm.isSocialRequired"
                        placeholder="请选择" clearable>
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="社保减员截止日:" prop="socialReduceDeadline">
                    <el-date-picker :disabled="obj.isDetails" class="width180"
                        v-model="obj.dialogForm.socialReduceDeadline" type="date" placeholder="请选择日期" clearable />
                </el-form-item>
                <el-form-item label="所属城市:" prop="cityCode">
                    <el-select :disabled="obj.isDetails" class="width180" v-model="obj.dialogForm.cityCode"
                        placeholder="请选择城市" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否代理员工公积金必缴:" prop="isHousingRequired">
                    <el-select :disabled="obj.isDetails" class="width180" v-model="obj.dialogForm.isHousingRequired"
                        placeholder="请选择" clearable>
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="社保增员截止日:" prop="socialAddDeadline">
                    <el-date-picker :disabled="obj.isDetails" class="width180"
                        v-model="obj.dialogForm.socialAddDeadline" type="date" placeholder="请选择日期" clearable />
                </el-form-item>
                <el-form-item label="人员类型名称:" prop="personnelType">
                    <el-select :disabled="obj.isDetails" class="width180" v-model="obj.dialogForm.personnelType"
                        placeholder="请选择人员类型" clearable>
                        <el-option v-for="item in personnelTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否有补充公积金:" prop="hasSupplementHousing">
                    <el-select :disabled="obj.isDetails" class="width180" v-model="obj.dialogForm.hasSupplementHousing"
                        placeholder="请选择" clearable>
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公积金减员截止日:" prop="housingReduceDeadline">
                    <el-date-picker :disabled="obj.isDetails" class="width180"
                        v-model="obj.dialogForm.housingReduceDeadline" type="date" placeholder="请选择日期" clearable />
                </el-form-item>
                <el-form-item label="分公司/供应商:" prop="companyType">
                    <el-select :disabled="obj.isDetails" class="width180" v-model="obj.dialogForm.companyType"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in companyTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="标准套餐:" prop="isStandardSetMeal">
                    <el-select :disabled="obj.isDetails" class="width180" v-model="obj.dialogForm.isStandardSetMeal"
                        placeholder="请选择" clearable>
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否单立户:" prop="isStandalone">
                    <el-select :disabled="obj.isDetails" class="width180" v-model="obj.dialogForm.isStandalone"
                        placeholder="请选择" clearable>
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公积金增员截止日:" prop="housingAddDeadline">
                    <el-date-picker :disabled="obj.isDetails" class="width180"
                        v-model="obj.dialogForm.housingAddDeadline" type="date" placeholder="请选择日期" clearable />
                </el-form-item>

                <el-row :gutter="10" class="mb8" v-if="obj.isMaintenance">
                    <el-col :span="6">
                        <div class="title">产品:</div>
                        <div class="mt10 mb10" v-if="!obj.isDetails">
                            <el-button size="small" type="primary" plain icon="Plus" @click="addProduct"></el-button>
                            <el-button size="small" type="danger" plain icon="Delete"
                                @click="deleteProduct"></el-button>
                        </div>
                        <el-table :data="obj.productList" border highlight-current-row
                            @current-change="handleCurrentChange">
                            <el-table-column align="center" width="60">
                                <template #default="scope">
                                    <el-radio v-model="radio" :value="scope.row.id"
                                        @change="handleCurrentChange(scope.row)"></el-radio>
                                </template>
                            </el-table-column>
                            <el-table-column prop="name" label="产品名称" align="center">
                                <template #default="scope">
                                    <el-select style="width: 100%;" v-model="scope.row.name" placeholder="请选择产品"
                                        clearable>
                                        <el-option v-for="item in personnelTypeOptions" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column align="center" />
                        </el-table>
                    </el-col>
                    <el-col :span="18">
                        <div class="title">生效时间段:</div>
                        <div class="mt10 mb10" v-if="!obj.isDetails">
                            <el-button size="small" type="primary" plain icon="Plus"
                                @click="maintenanceAddTime"></el-button>
                            <el-button size="small" type="danger" plain icon="Delete"
                                @click="maintenanceDeleteTime"></el-button>
                        </div>
                        <el-table :data="obj.dialogForm.tableData || []" border
                            @selection-change="maintenanceSelectionChange">
                            <el-table-column type="selection" width="55" />
                            <el-table-column label="生效日期" align="center" prop="effectiveDate" />
                            <el-table-column label="失效日期" align="center" prop="expiryDate" />
                            <el-table-column label="是否有效" align="center" prop="isValid">
                                <template #default="scope">
                                    <el-tag :type="scope.row.isValid === '1' ? 'success' : 'danger'">
                                        {{ scope.row.isValid === '1' ? '有效' : '无效' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="社保/公积金组" width="140" align="center" prop="groupName" />
                            <el-table-column label="产品" align="center" prop="productName" />
                            <el-table-column label="产品比例编号" width="140" align="center" prop="scaleNo" />
                            <el-table-column label="产品比例名称" width="140" align="center" prop="scaleName" />
                            <el-table-column label="企业比例" align="center" prop="companyScale" />
                            <el-table-column label="个人比例" align="center" prop="personalScale" />
                        </el-table>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <el-button v-if="obj.isMaintenance && !obj.isDetails" type="primary"
                    @click="submitMaintenance">保存并维护社保产品</el-button>
                <el-button type="primary" @click="submit">保存</el-button>
                <el-button type="info" @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="SocialSecurity_setMeal">

import { useAreaStore } from '@/store/modules/area'
import { listImport } from "@/api/reonApi/import";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const radio = ref(null);
const provinces = areaStore.provinces // 获取省份数据

// 分公司/供应商类型选项
const companyTypeOptions = [
    { value: '1', label: '分公司' },
    { value: '2', label: '供应商' },
    { value: '3', label: '单立户' }
];

// 人员类型选项
const personnelTypeOptions = [
    { value: '1', label: '全日制员工' },
    { value: '2', label: '非全日制员工' },
    { value: '3', label: '实习生' },
    { value: '4', label: '退休人员' }
];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cityCode: null,
        isStandalone: null,
        companyType: null,
        setMealName: null,
        personnelType: null,
        isSocialRequired: null,
        isHousingRequired: null,
        hasSupplementHousing: null,
        isStandardSetMeal: null,
    },//查询表单
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            setMealName: '北京全日制员工社保套餐',
            cityName: '北京市',
            cityCode: '110000',
            personnelTypeName: '全日制员工',
            personnelType: '1',
            companyName: '北京分公司',
            companyType: '1',
            isStandalone: '0',
            isSocialRequired: '1',
            isHousingRequired: '1',
            hasSupplementHousing: '0',
            isStandardSetMeal: '1',
            status: '1',
            socialReduceDeadline: '2023-05-15',
            socialAddDeadline: '2023-05-05',
            housingReduceDeadline: '2023-05-15',
            housingAddDeadline: '2023-05-05'
        },
        {
            id: 2,
            setMealName: '上海非全日制员工社保套餐',
            cityName: '上海市',
            cityCode: '310000',
            personnelTypeName: '非全日制员工',
            personnelType: '2',
            companyName: '上海分公司',
            companyType: '1',
            isStandalone: '0',
            isSocialRequired: '1',
            isHousingRequired: '0',
            hasSupplementHousing: '0',
            isStandardSetMeal: '1',
            status: '1',
            socialReduceDeadline: '2023-05-15',
            socialAddDeadline: '2023-05-05',
            housingReduceDeadline: '2023-05-15',
            housingAddDeadline: '2023-05-05'
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    title: "",//标题
    isDetails: false,//是否详情
    isMaintenance: false,//是否维护

    productList: [
        {
            id: 1,
            name: '1',
            code: 'YL001'
        },
        {
            id: 2,
            name: '6',
            code: 'GJJ001'
        }
    ],//产品列表
})

const selectedProduct = ref(null);

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}
// 表单重置
function reset() {
    obj.dialogForm = {};
    obj.isDetails = false;
    obj.isMaintenance = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框操作 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset()
    obj.dialogShow = true;
    obj.title = "新增社保套餐";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset()
    obj.title = "修改社保套餐";
    obj.dialogForm = JSON.parse(JSON.stringify(row));
    obj.dialogShow = true;
}

/** 查看按钮操作 */
function handleDetail(row) {
    reset()
    obj.isDetails = true;
    obj.isMaintenance = true;
    obj.title = "查看社保套餐";
    obj.dialogForm = JSON.parse(JSON.stringify(row));
    // 模拟生效时间段数据
    obj.dialogForm.tableData = [
        {
            id: 1,
            effectiveDate: '2023-01-01',
            expiryDate: '2023-12-31',
            isValid: '1',
            groupName: '养老保险组',
            productName: '养老保险',
            scaleNo: 'BL001',
            scaleName: '北京养老保险比例',
            companyScale: '20%',
            personalScale: '8%'
        },
        {
            id: 2,
            effectiveDate: '2023-01-01',
            expiryDate: '2023-12-31',
            isValid: '1',
            groupName: '公积金组',
            productName: '公积金',
            scaleNo: 'GJJ001',
            scaleName: '北京公积金比例',
            companyScale: '12%',
            personalScale: '12%'
        }
    ];
    obj.dialogShow = true;
}

/** 维护社保公积金产品 */
function handleMaintenance() {
    reset()
    obj.isMaintenance = true;
    obj.title = "维护社保套餐";
    // 获取选中行的数据
    const selectedRow = obj.tableData.find(item => obj.ids && obj.ids.includes(item.id));
    if (selectedRow) {
        obj.dialogForm = JSON.parse(JSON.stringify(selectedRow));
        // 模拟生效时间段数据
        obj.dialogForm.tableData = [
            {
                id: 1,
                effectiveDate: '2023-01-01',
                expiryDate: '2023-12-31',
                isValid: '1',
                groupName: '养老保险组',
                productName: '养老保险',
                scaleNo: 'BL001',
                scaleName: '北京养老保险比例',
                companyScale: '20%',
                personalScale: '8%'
            }
        ];
    }
    obj.dialogShow = true;
}

/** 生效 */
function handleOpen() {
    console.log("生效");
}

/** 暂停 */
function handlePause() {
    console.log("暂停");
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row?.id || obj.ids;
    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 保存并维护社保产品 */
function submitMaintenance() {
    proxy.$modal.msgSuccess("保存并维护社保产品成功");
    obj.dialogShow = false;
}

/** 保存 */
function submit() {
    proxy.$modal.msgSuccess(obj.dialogForm.id ? "修改成功" : "新增成功");
    obj.dialogShow = false;
    getList();
}

/** 新增产品 */
function addProduct() {
    obj.productList.push({
        id: obj.productList.length + 1,
        name: null,
        code: null
    });
}

/** 删除产品 */
function deleteProduct() {
    if (selectedProduct.value) {
        const index = obj.productList.findIndex(item => item.id === selectedProduct.value.id);
        if (index !== -1) {
            obj.productList.splice(index, 1);
            selectedProduct.value = null;
        }
    } else {
        proxy.$modal.msgWarning("请选择要删除的产品");
    }
}


/** 处理当前行变化 */
function handleCurrentChange(row) {
    if (row) {
        radio.value = row.id;
        console.log("已选产品:", row);
    }
}

/** 新增时间段 */
function maintenanceAddTime() {
    if (!obj.dialogForm.tableData) {
        obj.dialogForm.tableData = [];
    }

    obj.dialogForm.tableData.push({
        id: obj.dialogForm.tableData.length + 1,
        effectiveDate: '2023-01-01',
        expiryDate: '2023-12-31',
        isValid: '1',
        groupName: '新增产品组',
        productName: '新增产品',
        scaleNo: 'NEW001',
        scaleName: '新增产品比例',
        companyScale: '0%',
        personalScale: '0%'
    });
}

/** 删除时间段 */
function maintenanceDeleteTime() {
    if (obj.selectedTimeRows && obj.selectedTimeRows.length > 0) {
        const ids = obj.selectedTimeRows.map(row => row.id);
        obj.dialogForm.tableData = obj.dialogForm.tableData.filter(item => !ids.includes(item.id));
    } else {
        proxy.$modal.msgWarning("请选择要删除的时间段");
    }
}

/** 时间段选择变化 */
function maintenanceSelectionChange(selection) {
    obj.selectedTimeRows = selection;
}

getList();
</script>
<style lang="scss" scoped>
// 日期选择器宽度
:deep(.width180, .el-date-editor) {
    width: 180px;
}

// 选中行背景色
:deep(.el-table__body tr.current-row>td.el-table__cell) {
    background-color: rgb(134, 231, 231);
}
</style>