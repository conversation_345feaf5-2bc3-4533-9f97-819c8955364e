<!-- 社保公积金单个城市 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="所属城市:">
                <el-select class="width220" filterable v-model="obj.queryParams.city" placeholder="请选择"
                    @change="handleCityChange">
                    <el-option v-for="item in obj.cityOptions" :key="item.cityCode" :label="item.cityName"
                        :value="item.cityCode + '-' + item.cityName"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="人员类别:">
                <el-select class="width220" filterable v-model="obj.queryParams.personType" placeholder="请选择"
                    @change="handlePersonTypeChange">
                    <el-option v-for="item in obj.personTypeOptions" :key="item.code" :label="item.name"
                        :value="item.code"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="服务网点:">
                <el-select style="width: 260px" filterable v-model="obj.queryParams.servicePoint" placeholder="请选择"
                    @change="handleServicePointChange">
                    <el-option v-for="item in obj.servicePointOptions" :key="item.value" :label="item.serviceSiteName"
                        :value="item.categoryCode + '-' + item.cityCode + '-' + item.serviceSiteCode"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="success" icon="Download" @click="handleExport">导出</el-button>
            </el-form-item>
        </el-form>
        <el-tabs v-model="obj.activeTab" type="border-card">
            <el-tab-pane label="基础信息" name="basic">
                <el-form :model="obj.basicForm" class="basic-info-form" label-width="auto">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="使用状态:">
                                <el-select disabled class="width-full" filterable v-model="obj.basicForm.useStatusType"
                                    placeholder="">
                                    <el-option value="1" label="正常使用"></el-option>
                                    <el-option value="2" label="停止使用"></el-option>
                                    <el-option value="3" label="特殊使用"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="使用状态说明:">
                                <el-input readonly class="width-full" v-model="obj.basicForm.useState"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="社保增员截点:">
                                <el-input readonly class="width-full" v-model="obj.basicForm.insurAddDay"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="社保减员截点:">
                                <el-input readonly class="width-full" v-model="obj.basicForm.insurSubDay"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="公积金增员截点:">
                                <el-input readonly class="width-full" v-model="obj.basicForm.crfAddDay"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="公积金减员截点:">
                                <el-input readonly class="width-full" v-model="obj.basicForm.crfSubDay"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="社保申报频率:">
                                <el-select disabled class="width-full" filterable
                                    v-model="obj.basicForm.applyInsurFreqName" placeholder="">
                                    <el-option value="1" label="当月增当月,当月减当月"></el-option>
                                    <el-option value="2" label="当月增次月，当月减次月"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="公积金申报频率:">
                                <el-select disabled class="width-full" filterable
                                    v-model="obj.basicForm.applyFundFreqName" placeholder="">
                                    <el-option value="1" label="当月增当月,当月减当月"></el-option>
                                    <el-option value="2" label="当月增次月，当月减次月"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="账单收费规则:">
                                <el-input readonly class="width-full"
                                    v-model="obj.basicForm.billFeeRuleName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="大病申报频率:">
                                <el-input readonly class="width-full"
                                    v-model="obj.basicForm.strApplyIllnessFreq"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="大户所在区:">
                                <el-input readonly class="width-full" v-model="obj.basicForm.bigAccountArea"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="当地单立户可操作性区县:">
                                <el-input readonly class="width-full"
                                    v-model="obj.basicForm.singleAccountCounty"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="增员材料:">
                                <el-input readonly class="width-full" v-model="obj.basicForm.addInfo"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="是否必须落地发薪:">
                                <el-select disabled class="width-full" filterable v-model="obj.basicForm.mustPayment"
                                    placeholder="">
                                    <el-option value="0" label="是"></el-option>
                                    <el-option value="1" label="否"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="减员材料:">
                                <el-input readonly type="textarea" class="width-full" v-model="obj.basicForm.downInfo"
                                    :rows="4"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="补缴材料:">
                                <el-input readonly type="textarea" class="width-full" v-model="obj.basicForm.appendInfo"
                                    :rows="4"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="特殊注意事项:">
                                <el-input readonly type="textarea" class="width-full"
                                    v-model="obj.basicForm.specialConsiderations" :rows="4"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="是否离职补差:">
                                <el-input readonly class="width-full"
                                    v-model="obj.basicForm.additionFlagName"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="离职补差起始月:">
                                <el-input readonly class="width-full"
                                    v-model="obj.basicForm.additionStartMonth"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="基数比例一览" name="ratio">
                <!-- 基数比例表格 -->
                <div class="table-container">
                    <el-table :data="obj.tableData" border style="width: 100%;">
                        <el-table-column prop="productName" label="产品名称" min-width="120"></el-table-column>
                        <el-table-column prop="lowBaseCom" label="单位基数下限" min-width="140" sortable></el-table-column>
                        <el-table-column prop="highBaseCom" label="单位基数上限" min-width="120"></el-table-column>
                        <el-table-column prop="comRatio" label="单位比例" min-width="100"></el-table-column>
                        <el-table-column prop="comAdd" label="单位定值" min-width="120" sortable></el-table-column>
                        <el-table-column prop="lowBaseInd" label="个人基数下限" min-width="140" sortable></el-table-column>
                        <el-table-column prop="highBaseInd" label="个人基数上限" min-width="120"></el-table-column>
                        <el-table-column prop="indRatio" label="个人比例" min-width="120" sortable></el-table-column>
                        <el-table-column prop="indlAdd" label="个人定值" min-width="120" sortable></el-table-column>
                        <el-table-column prop="lowBaseComRatio" label="单位下限金额" min-width="140"
                            sortable></el-table-column>
                        <el-table-column prop="lowBaseIndAdd" label="个人下限金额" min-width="140" sortable></el-table-column>
                        <el-table-column prop="totalLow" label="单位+个人下限金额总计" min-width="200" sortable></el-table-column>
                        <el-table-column prop="highBaseComRatio" label="单位上限金额" min-width="140"
                            sortable></el-table-column>
                        <el-table-column prop="highBaseIndAdd" label="个人上限金额" min-width="140"
                            sortable></el-table-column>
                        <el-table-column prop="totalHigh" label="单位+个人上限金额总计" min-width="200"
                            sortable></el-table-column>
                        <el-table-column prop="complementMo" label="补缴月份" min-width="120" sortable></el-table-column>
                        <el-table-column prop="spanYearElag" label="是否可跨年补缴" min-width="140" sortable>
                            <template #default="scope">
                                <div> {{ scope.row.spanYearElagName }}</div>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <el-pagination v-model:current-page="obj.queryParams.pageNum"
                        v-model:page-size="obj.queryParams.pageSize" :page-sizes="[50, 100]"
                        layout="total, sizes, prev, pager, next, jumper" :total="obj.total"
                        @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
                </div>

                <!-- 最低工资表格 -->
                <div class="table-container mt20">
                    <el-table :data="obj.wageData" border style="width: 100%;">
                        <el-table-column prop="cityName" label="城市名称"></el-table-column>
                        <el-table-column prop="serviceSiteName" label="服务网点"></el-table-column>
                        <el-table-column prop="startTime" label="开始时间"></el-table-column>
                        <el-table-column prop="endTime" label="结束时间"></el-table-column>
                        <el-table-column prop="minWage" label="最低工资"></el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <el-pagination v-model:current-page="obj.queryParams.pageNum"
                        v-model:page-size="obj.queryParams.pageSize" :page-sizes="[50, 100]"
                        layout="total, sizes, prev, pager, next, jumper" :total="obj.wageTotal"
                        @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup>


// 初始化常量和对象
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 响应式数据
const obj = reactive({
    // 页面状态
    activeTab: 'basic',

    // 查询参数
    queryParams: {
        pageNum: 1,
        pageSize: 50,
    },
    cityOptions: [],// 城市下拉框数据
    personTypeOptions: [],// 人员类型下拉框数据
    waitData: [],
    servicePointOptions: [],// 服务网点下拉框数据

    cityChange: '',// 城市选中数据
    personTypeChange: '',// 人员类型选中数据
    servicePointChange: '',// 服务网点选中数据

    paramsList: [],
    // 基础信息表单
    basicForm: {},

    // 基数比例相关
    tableData: [],
    total: 0,

    // 最低工资相关
    wageTotal: 0,
    wageData: [],

})

function getCodeName(type, val) {
    let arr = window.top['dictCachePool'][type];
    if (!arr) return "";
    for (let i in arr) {
        let obj = arr[i];
        if (val == obj.code) {
            return obj.name;
        }
    }
    return "";
}

//获取城市列表
function cityList() {
    SocialSecurityCityConfigAPI.getCityList().then(res => {
        if (res.data.code === 0) {
            obj.cityOptions = res.data.data
        }
    })
}

//城市选择
function handleCityChange(val) {
    obj.cityChange = val
    if (val) {
        let arr = val.split('-')
        SocialSecurityCityConfigAPI.getServiceSiteList({
            cityCode: arr[0],
        }).then(res => {
            if (res.data.code === 0) {
                obj.personTypeOptions = []
                obj.waitData = []
                obj.servicePointOptions = []
                obj.paramsList = []
                obj.queryParams.personType = ''
                obj.queryParams.servicePoint = ''
                obj.personTypeChange = ''
                obj.servicePointChange = ''

                obj.waitData = res.data.data
                if (obj.waitData && obj.waitData.length > 0) {
                    obj.waitData.forEach(item => {
                        item.serviceSiteName = item.serviceSiteName + '(' + (item.useStatusType == 1 ? '正常使用' : item.useStatusType == 3 ? '特殊使用' : '') + ')'
                    })
                    obj.waitData.forEach(i => {
                        obj.personTypeOptions.push({
                            code: arr[0],
                            name: arr[1] + '-' + getCodeName('PEOPLE_IND_TYPE', i.indTypeCode),
                            categoryCode: i.categoryCode,
                        })
                    });
                    obj.personTypeOptions = obj.personTypeOptions.filter((item, index, self) => {
                        return self.findIndex(t => t.name === item.name) === index
                    })
                }
            }
        })
    }
}

//服务人员类型 选择
function handlePersonTypeChange(val) {
    obj.personTypeChange = val
    obj.servicePointOptions = []
    if (val) {
        obj.waitData.forEach(i => {
            if (i.categoryCode == val) {
                obj.servicePointOptions.push(i)
            }
        });
    }
}

//服务网点 选择
function handleServicePointChange(val) {
    obj.servicePointChange = val
    if (val) {
        obj.paramsList = val.split('-')
    }
}

//查询
function handleQuery() {
    if (!obj.cityChange) {
        ElMessage.warning('请选择所属城市!')
        return false
    }
    if (!obj.personTypeChange) {
        ElMessage.warning('请选择人员类别!')
        return false
    }
    if (!obj.servicePointChange) {
        ElMessage.warning('请选择服务网点!')
        return false
    }
    getBasicInfo()
    getFundRatio()
    getFundRatio2()
}

// 重置
function resetQuery() {
    obj.personTypeOptions = []
    obj.waitData = []
    obj.servicePointOptions = []
    obj.paramsList = []
    obj.queryParams.personType = ''
    obj.queryParams.servicePoint = ''
    obj.queryParams.city = ''
    obj.queryParams.pageNum = 1
    obj.queryParams.pageSize = 50

    obj.cityChange = ''
    obj.personTypeChange = ''
    obj.servicePointChange = ''
}

// 导出
function handleExport() {
    if (!obj.cityChange) {
        ElMessage.warning('请选择所属城市!')
        return false
    }
    if (!obj.personTypeChange) {
        ElMessage.warning('请选择人员类别!')
        return false
    }
    if (!obj.servicePointChange) {
        ElMessage.warning('请选择服务网点!')
        return false
    }
    let params = {
        cityCode: obj.paramsList[1],
        indTypeCode: obj.paramsList[0],
        serviceSiteCode: obj.paramsList[2]
    }
    let vot = JSON.stringify(params);
    window.location.href = ML.contextPath + '/serviceSiteCfg/socialOneCity/exportFile?vot=' + vot;
}

//获取基础信息
function getBasicInfo() {
    let params = {
        cityCode: obj.paramsList[1],
        indTypeCode: obj.paramsList[0],
        serviceSiteCode: obj.paramsList[2]
    }
    SocialSecurityCityConfigAPI.getBaseInfo(params).then(res => {
        if (res.data.code === 0) {
            obj.basicForm = res.data.data
            if (obj.basicForm.useStatusType) {
                obj.basicForm.useStatusType = obj.basicForm.useStatusType.toString()
            }
            if (obj.basicForm.mustPayment) {
                obj.basicForm.mustPayment = obj.basicForm.mustPayment.toString()
            } else {
                obj.basicForm.mustPayment = "0"
            }
        }
    })
}

//获取基金比例
function getFundRatio() {
    let params = {
        page: obj.queryParams.pageNum,
        limit: obj.queryParams.pageSize,
        cityCode: obj.paramsList[1],
        indTypeCode: obj.paramsList[0],
        serviceSiteCode: obj.paramsList[2],
        currentTimeTip: true
    }
    SocialSecurityCityConfigAPI.getBaseRadio(params).then(res => {
        if (res.data.code === 0) {
            obj.tableData = res.data.data
            obj.total = res.data.count
        }
    })
}

//获取基金比例2
function getFundRatio2() {
    let params = {
        page: obj.queryParams.pageNum,
        limit: obj.queryParams.pageSize,
        cityCode: obj.paramsList[1],
        indTypeCode: obj.paramsList[0],
        serviceSiteCode: obj.paramsList[2],
        currentTimeTip: true
    }
    SocialSecurityCityConfigAPI.getBaseRadio2(params).then(res => {
        if (res.data.code === 0) {
            obj.wageData = res.data.data
            obj.wageTotal = res.data.count
        }
    })
}

//分页
function handleSizeChange(size) {
    obj.queryParams.pageSize = size
    getFundRatio()
    getFundRatio2()
}

function handleCurrentChange(page) {
    obj.queryParams.pageNum = page
    getFundRatio()
    getFundRatio2()
}

onMounted(() => {
    cityList()
})

</script>
<style lang="scss" scoped>
.width-full {
    width: 100%;
}


.basic-info-form {
    padding: 20px;

    .el-row {
        margin-bottom: 20px;
    }
}

.text-center {
    text-align: center;
}

.table-container {
    padding: 15px;

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .table-actions {
            display: flex;
            gap: 10px;
        }
    }
}
</style>