import Cookies from "js-cookie";

const useAppStore = defineStore("app", {
  state: () => ({
    sidebar: {
      opened: Cookies.get("sidebarStatus")
        ? !!+Cookies.get("sidebarStatus")
        : true,
      withoutAnimation: false,
      hide: false,
    },
    device: "desktop",
    size: Cookies.get("size") || "default",
  }),
  actions: {
    /**
     * 切换侧边栏的显示状态。
     * @param {boolean} withoutAnimation - 是否禁用动画效果。
     */
    toggleSideBar(withoutAnimation) {
      // 如果侧边栏处于隐藏状态，则直接返回，不进行任何操作。
      if (this.sidebar.hide) {
        return false;
      }
      // 切换侧边栏的打开状态。
      this.sidebar.opened = !this.sidebar.opened;
      // 设置是否禁用动画效果。
      this.sidebar.withoutAnimation = withoutAnimation;
      // 根据侧边栏的打开状态，设置Cookie中的sidebarStatus值。
      if (this.sidebar.opened) {
        Cookies.set("sidebarStatus", 1);
      } else {
        Cookies.set("sidebarStatus", 0);
      }
    },

    /**
     * 关闭侧边栏。
     * @param {Object} options - 包含是否禁用动画效果的选项对象。
     * @param {boolean} options.withoutAnimation - 是否禁用动画效果。
     */
    closeSideBar({ withoutAnimation }) {
      // 设置Cookie中的sidebarStatus值为0，表示侧边栏关闭。
      Cookies.set("sidebarStatus", 0);
      // 将侧边栏的打开状态设置为false。
      this.sidebar.opened = false;
      // 设置是否禁用动画效果。
      this.sidebar.withoutAnimation = withoutAnimation;
    },

    /**
     * 切换设备类型。
     * @param {string} device - 设备类型，如'desktop'或'mobile'。
     */
    toggleDevice(device) {
      // 将当前设备类型设置为传入的设备类型。
      this.device = device;
    },

    /**
     * 设置尺寸。
     * @param {string} size - 尺寸，如'default'或'large'。
     */
    setSize(size) {
      // 将当前尺寸设置为传入的尺寸。
      this.size = size;
      // 将尺寸存储到Cookie中。
      Cookies.set("size", size);
    },

    /**
     * 切换侧边栏的隐藏状态。
     * @param {boolean} status - 侧边栏的隐藏状态，true表示隐藏，false表示显示。
     */
    toggleSideBarHide(status) {
      // 设置侧边栏的隐藏状态为传入的状态值。
      this.sidebar.hide = status;
    },
  },
});

export default useAppStore;
