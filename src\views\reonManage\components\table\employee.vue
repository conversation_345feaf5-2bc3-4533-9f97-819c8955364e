<template>
    <el-table v-loading="props.loading" border :data="props.tableData" @selection-change="handleSelectionChange"
        @row-dblclick="handleRowDblClick">
        <el-table-column type="selection" width="55" />
        <el-table-column label="雇员姓名" align="center" prop="employeeName" />
        <el-table-column label="订单编号" align="center" prop="orderNo" width="160" />
        <el-table-column label="证件类型" align="center" prop="idType">
            <template #default="scope">
                {{idTypeOptions.find(item => item.value === scope.row.idType)?.label || '未知类型'}}
            </template>
        </el-table-column>
        <el-table-column label="证件号码" align="center" prop="idNumber" width="180" />
        <el-table-column label="入离职状态" align="center" prop="employmentStatus" width="100">
            <template #default="scope">
                {{employmentStatusOptions.find(item => item.value === scope.row.employmentStatus)?.label || '未知状态'
                }}
            </template>
        </el-table-column>
        <el-table-column label="客户编号" align="center" prop="customerNo" />
        <el-table-column label="客户名称" align="center" prop="customerName" />
        <el-table-column label="合同类型" align="center" prop="contractType" width="100">
            <template #default="scope">
                {{contractTypeOptions.find(item => item.value === scope.row.contractType)?.label || '未知类型'}}
            </template>
        </el-table-column>
        <el-table-column label="小合同编号" align="center" prop="smallContractNo" width="100" />
        <el-table-column label="小合同名称" align="center" prop="smallContractName" width="100" />
        <el-table-column label="接单方" align="center" prop="orderReceiver">
            <template #default="scope">
                {{orderReceiverOptions.find(item => item.value === scope.row.orderReceiver)?.label || '未知接单方'}}
            </template>
        </el-table-column>
        <el-table-column label="城市" align="center" prop="city">
            <template #default="scope">
                {{cityOptions.find(item => item.value === scope.row.city)?.label || '未知城市'}}
            </template>
        </el-table-column>
        <el-table-column label="派单方客服" align="center" prop="dispatcherService" width="100" />
        <el-table-column label="接单方客服" align="center" prop="receiverService" width="100" />
        <el-table-column label="业务大类" align="center" prop="businessCategory">
            <template #default="scope">
                {{businessCategoryOptions.find(item => item.value === scope.row.businessCategory)?.label || '未知大类'
                }}
            </template>
        </el-table-column>
        <el-table-column label="业务小类" align="center" prop="businessSubcategory">
            <template #default="scope">
                {{businessSubcategoryOptions.find(item => item.value === scope.row.businessSubcategory)?.label ||
                    '未知小类'}}
            </template>
        </el-table-column>
        <el-table-column label="具体业务" align="center" prop="specificBusiness">
            <template #default="scope">
                {{specificBusinessOptions.find(item => item.value === scope.row.specificBusiness)?.label || '未知业务'
                }}
            </template>
        </el-table-column>
        <el-table-column label="业务发生日期" align="center" prop="businessDate" width="140" />
        <el-table-column label="业务进度" align="center" prop="businessProgress">
            <template #default="scope">
                <el-tag
                    :type="scope.row.businessProgress === '4' ? 'success' : (scope.row.businessProgress === '5' ? 'danger' : 'warning')">
                    {{businessProgressOptions.find(item => item.value === scope.row.businessProgress)?.label ||
                        '未知进度'}}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column label="办理结果" align="center" prop="handleResult">
            <template #default="scope">
                <el-tag v-if="scope.row.handleResult" :type="scope.row.handleResult === '1' ? 'success' : 'danger'">
                    {{handleResultOptions.find(item => item.value === scope.row.handleResult)?.label || '未知结果'}}
                </el-tag>
                <span v-else>-</span>
            </template>
        </el-table-column>
        <el-table-column label="报销金额" align="center" prop="reimbursementAmount" />
        <el-table-column label="票据金额" align="center" prop="invoiceAmount" />
        <el-table-column v-if="props.menuName !== 'employeeBusinessFiling'" label="是否需要支付" align="center"
            prop="needPayment">
            <template #default="scope">
                {{needPaymentOptions.find(item => item.value === scope.row.needPayment)?.label || '未知'}}
            </template>
        </el-table-column>
    </el-table>
</template>
<script setup>
const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },// 是否加载
    tableData: {
        type: Array,
        default: () => []
    },// 表格数据
    menuName: {
        type: String,
        default: ''
    },// 菜单名称
})

const emit = defineEmits(['selectionChange', 'rowDblClick'])
// 选择变化
const handleSelectionChange = (selection) => {
    emit('selectionChange', selection)
}
// 行双击
const handleRowDblClick = (row) => {
    emit('rowDblClick', row)
}


// 证件类型选项
const idTypeOptions = [
    { value: '1', label: '身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '军官证' }
];

// 入离职状态选项
const employmentStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '停薪' }
];

// 合同类型选项
const contractTypeOptions = [
    { value: '1', label: '标准合同' },
    { value: '2', label: '非标准合同' }
];

// 接单方选项
const orderReceiverOptions = [
    { value: '1', label: '分公司A' },
    { value: '2', label: '分公司B' },
    { value: '3', label: '分公司C' }
];

// 城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' }
];

// 业务大类选项
const businessCategoryOptions = [
    { value: '1', label: '社保类' },
    { value: '2', label: '公积金类' },
    { value: '3', label: '人事类' }
];

// 业务小类选项
const businessSubcategoryOptions = [
    { value: '1', label: '养老保险' },
    { value: '2', label: '医疗保险' },
    { value: '3', label: '失业保险' },
    { value: '4', label: '公积金' },
    { value: '5', label: '入职办理' },
    { value: '6', label: '离职办理' }
];

// 具体业务选项
const specificBusinessOptions = [
    { value: '1', label: '养老保险变更' },
    { value: '2', label: '医疗保险变更' },
    { value: '3', label: '失业保险变更' },
    { value: '4', label: '公积金变更' },
    { value: '5', label: '入职办理' },
    { value: '6', label: '离职办理' }
];

// 业务进度选项
const businessProgressOptions = [
    { value: '1', label: '待提交' },
    { value: '2', label: '待审核' },
    { value: '3', label: '已审核' },
    { value: '4', label: '已办理' },
    { value: '5', label: '办理失败' },
    { value: '6', label: '已取消' }
];

// 办理结果选项
const handleResultOptions = [
    { value: '1', label: '成功' },
    { value: '2', label: '失败' }
];

// 是否需要支付选项
const needPaymentOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' }
];

</script>
<style lang="scss" scoped></style>
