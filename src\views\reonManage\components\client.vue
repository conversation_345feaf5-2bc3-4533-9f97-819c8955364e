<!-- 客户 -->
<template>
    <div>
        <el-dialog v-model="dialogShow" :width="props.width" append-to-body draggable @close="close">
            <div class="mt20 mb20 flex-between">
                <div>
                    <el-input class="width320" v-model="obj.queryParams.clientName" placeholder="客户名称/编号/合同名称/编号"
                        clearable>
                        <template #append>
                            <el-button @click="handleCustomerQuery" icon="Search" />
                        </template>
                    </el-input>
                </div>
                <div>
                    <el-button @click="clear">清空</el-button>
                    <el-button type="primary" @click="select">选择</el-button>
                </div>
            </div>
            <el-table :data="obj.tableData" style="width: 100%" @current-change="handleCurrentChange" border>
                <el-table-column align="center" width="80">
                    <template #default="scope">
                        <el-radio v-model="radio" :value="scope.row.id" @change="handleCurrentChange(scope.row)" />
                    </template>
                </el-table-column>
                <el-table-column label="客户ID" align="center" prop="id" />
                <el-table-column label="客户编号" align="center" prop="code" />
                <el-table-column label="客户名称" align="center" prop="name" />
                <el-table-column v-if="props.isShow" label="合同编号" align="center" prop="contractId" />
                <el-table-column v-if="props.isShow" label="合同名称" align="center" prop="contractName" />
            </el-table>
            <!-- 分页 -->
            <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        </el-dialog>
    </div>
</template>

<script setup name="ClientComponent">
import { listScale } from "@/api/reonApi/scale";

const props = defineProps({
    width: {
        type: String,
        default: '45%'
    },
    show: {
        type: Boolean,
        default: false
    },
    isShow: {
        type: Boolean,
        default: true
    }
});

// 使用本地变量来跟踪对话框状态
const dialogShow = ref(props.show);

// 监听props.show的变化，更新本地状态
watch(() => props.show, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:show', newVal);
});

const obj = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        clientName: null,
    },//查询表单
    tableData: [
        {
            id: 1,
            name: '客户1',
            code: '123456',
            contractId: '123456',
            contractName: '合同1'
        },
        {
            id: 2,
            name: '客户2',
            code: '123456',
            contractId: '123456',
            contractName: '合同2'
        },
        {
            id: 3,
            name: '客户3',
            code: '123456',
            contractId: '123456',
            contractName: '合同3'
        }
    ],
    total: 0,
    currentRow: null,
})


/** 查询 */
function handleCustomerQuery() {
    obj.queryParams.pageNum = 1;
    obj.queryParams.pageSize = 10;
    getList();
}

const radio = ref(null);
/** 选择 */
function handleCurrentChange(row) {
    radio.value = row.id;
    obj.currentRow = row;
}


/** 清空 */
function clear() {
    obj.queryParams = {
        pageNum: 1,
        pageSize: 10,
        clientName: null,
    };
    obj.currentRow = null
    radio.value = null;
    emit('select', obj.currentRow);
    dialogShow.value = false;
}
const emit = defineEmits(['select', 'close', 'update:show']);
/** 选择 */
function select() {
    emit('select', obj.currentRow);
    dialogShow.value = false;
}
function close() {
    dialogShow.value = false;
    emit('close');
}

/** 列表 */
function getList() {
    listScale(obj.queryParams).then(res => {
        // obj.tableData = res.rows;
        // obj.total = res.total;
    });
}
getList();
</script>
<style lang="scss" scoped></style>