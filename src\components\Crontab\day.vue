<template>
    <el-form>
        <el-form-item>
            <el-radio v-model='radioValue' :value="1">
                日，允许的通配符[, - * ? / L W]
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="2">
                不指定
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="3">
                周期从
                <el-input-number v-model='cycle01' :min="1" :max="30" /> -
                <el-input-number v-model='cycle02' :min="cycle01 + 1" :max="31" /> 日
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="4">
                从
                <el-input-number v-model='average01' :min="1" :max="30" /> 号开始，每
                <el-input-number v-model='average02' :min="1" :max="31 - average01" /> 日执行一次
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="5">
                每月
                <el-input-number v-model='workday' :min="1" :max="31" /> 号最近的那个工作日
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="6">
                本月最后一天
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="7">
                指定
                <el-select clearable v-model="checkboxList" placeholder="可多选" multiple :multiple-limit="10">
                    <el-option v-for="item in 31" :key="item" :label="item" :value="item" />
                </el-select>
            </el-radio>
        </el-form-item>
    </el-form>
</template>
<script setup>
const emit = defineEmits(['update'])
const props = defineProps({
    cron: {
        type: Object,
        default: {
            second: "*",
            min: "*",
            hour: "*",
            day: "*",
            month: "*",
            week: "?",
            year: "",
        }
    },
    check: {
        type: Function,
        default: () => {
        }
    }
})
const radioValue = ref(1)
const cycle01 = ref(1)
const cycle02 = ref(2)
const average01 = ref(1)
const average02 = ref(1)
const workday = ref(1)
const checkboxList = ref([])
const checkCopy = ref([1])

const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, 1, 30)
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, 31)
    return cycle01.value + '-' + cycle02.value
})
const averageTotal = computed(() => {
    average01.value = props.check(average01.value, 1, 30)
    average02.value = props.check(average02.value, 1, 31 - average01.value)
    return average01.value + '/' + average02.value
})
const workdayTotal = computed(() => {
    workday.value = props.check(workday.value, 1, 31)
    return workday.value + 'W'
})
const checkboxString = computed(() => {
    return checkboxList.value.join(',')
})
watch(() => props.cron.day, value => changeRadioValue(value))
watch([radioValue, cycleTotal, averageTotal, workdayTotal, checkboxString], () => onRadioChange())
/**
 * 根据不同的输入值更新radioValue的值
 * 此函数用于解析和更新基于不同时间格式的单选框值
 * @param {String} value - 输入的时间格式字符串
 */
function changeRadioValue(value) {
    // 当输入值为"*"时，设置radioValue为1
    if (value === "*") {
        radioValue.value = 1
    }
    // 当输入值为"?"时，设置radioValue为2
    else if (value === "?") {
        radioValue.value = 2
    }
    // 当输入值包含"-"时，表示一个范围，将其分割并设置cycle01和cycle02的值，并将radioValue设置为3
    else if (value.indexOf("-") > -1) {
        const indexArr = value.split('-')
        cycle01.value = Number(indexArr[0])
        cycle02.value = Number(indexArr[1])
        radioValue.value = 3
    }
    // 当输入值包含"/"时，表示一个步长，将其分割并设置average01和average02的值，并将radioValue设置为4
    else if (value.indexOf("/") > -1) {
        const indexArr = value.split('/')
        average01.value = Number(indexArr[0])
        average02.value = Number(indexArr[1])
        radioValue.value = 4
    }
    // 当输入值包含"W"时，表示最接近的工作日，分割并设置workday的值，并将radioValue设置为5
    else if (value.indexOf("W") > -1) {
        const indexArr = value.split("W")
        workday.value = Number(indexArr[0])
        radioValue.value = 5
    }
    // 当输入值为"L"时，表示最后一个，将radioValue设置为6
    else if (value === "L") {
        radioValue.value = 6
    }
    // 对于其他情况，将输入值按逗号分割，转换为唯一数字数组，并设置checkboxList的值，将radioValue设置为7
    else {
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))]
        radioValue.value = 7
    }
}
// 单选按钮值变化时
function onRadioChange() {
    // 当单选按钮值为2且周字段为'?'时，更新周字段为'*'，日字段为'day'
    if (radioValue.value === 2 && props.cron.week === '?') {
        emit('update', 'week', '*', 'day')
    }
    // 当单选按钮值不为2且周字段不为'?'时，更新周字段为'?'，日字段为'day'
    if (radioValue.value !== 2 && props.cron.week !== '?') {
        emit('update', 'week', '?', 'day')
    }
    // 根据单选按钮值更新日字段
    switch (radioValue.value) {
        case 1:
            // 更新日字段为'*'
            emit('update', 'day', '*', 'day')
            break
        case 2:
            // 更新日字段为'?'
            emit('update', 'day', '?', 'day')
            break
        case 3:
            // 更新日字段为cycleTotal的值
            emit('update', 'day', cycleTotal.value, 'day')
            break
        case 4:
            // 更新日字段为averageTotal的值
            emit('update', 'day', averageTotal.value, 'day')
            break
        case 5:
            // 更新日字段为workdayTotal的值
            emit('update', 'day', workdayTotal.value, 'day')
            break
        case 6:
            // 更新日字段为'L'
            emit('update', 'day', 'L', 'day')
            break
        case 7:
            // 处理checkbox列表，确保至少有一个选项被选中，并更新日字段为选中项的字符串表示
            if (checkboxList.value.length === 0) {
                checkboxList.value.push(checkCopy.value[0])
            } else {
                checkCopy.value = checkboxList.value
            }
            emit('update', 'day', checkboxString.value, 'day')
            break
    }
}
</script>

<style lang="scss" scoped>
.el-input-number--small,
.el-select,
.el-select--small {
    margin: 0 0.2rem;
}

.el-select,
.el-select--small {
    width: 18.8rem;
}
</style>