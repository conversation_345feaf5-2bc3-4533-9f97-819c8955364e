import request from '@/utils/request'

// 查询合同补充/关联协议审批列表
export function listSupplement(query) {
  return request({
    url: '/system/supplement/list',
    method: 'get',
    params: query
  })
}

// 查询合同补充/关联协议审批详细
export function getSupplement(id) {
  return request({
    url: '/system/supplement/' + id,
    method: 'get'
  })
}

// 新增合同补充/关联协议审批
export function addSupplement(data) {
  return request({
    url: '/system/supplement',
    method: 'post',
    data: data
  })
}

// 修改合同补充/关联协议审批
export function updateSupplement(data) {
  return request({
    url: '/system/supplement',
    method: 'put',
    data: data
  })
}

// 删除合同补充/关联协议审批
export function delSupplement(id) {
  return request({
    url: '/system/supplement/' + id,
    method: 'delete'
  })
}
