<template>
    <el-popover placement="bottom" :width="280" trigger="click" :show-arrow="false">
        <template #reference>
            <el-button type="primary" class="btn-setting">
                <el-icon class="setting-icon">
                    <Setting />
                </el-icon>
                <span class="setting-text">列设置</span>
            </el-button>
        </template>
        <div class="column-setting">
            <div class="column-setting__header">
                <div class="header-title">
                    <el-icon class="title-icon">
                        <Grid />
                    </el-icon>
                    <span class="title-text">列展示</span>
                </div>
                <div class="column-setting__btns">
                    <el-button link type="primary" size="small" class="action-btn select-all-btn"
                        @click="selectAllColumns">
                        <el-icon>
                            <Check />
                        </el-icon>
                        全选
                    </el-button>
                    <el-divider direction="vertical" class="btn-divider" />
                    <el-button link type="danger" size="small" class="action-btn unselect-all-btn"
                        @click="unselectAllColumns">
                        <el-icon>
                            <Close />
                        </el-icon>
                        清空
                    </el-button>
                </div>
            </div>
            <el-divider class="column-setting__divider" />
            <div class="column-setting__content">
                <el-checkbox-group v-model="selectedColumns" class="checkbox-group">
                    <div v-for="col in columnOptions" :key="col.prop" class="column-setting__item">
                        <el-checkbox :value="col.prop" class="column-checkbox">
                            <span class="checkbox-label">{{ col.label }}</span>
                        </el-checkbox>
                    </div>
                </el-checkbox-group>
            </div>
            <div class="column-setting__footer">
                <div class="selected-count">
                    已选择 <span class="count-number">{{ selectedColumns.length }}</span> / {{ columnOptions.length }} 列
                </div>
            </div>
        </div>
    </el-popover>
</template>

<script setup>

const props = defineProps({
    columnOptions: {
        type: Array,
        required: true
    },
    modelValue: {
        type: Array,
        required: true
    },
    // 添加缓存标识符
    cacheKey: {
        type: String,
        required: true
    }
})

const emit = defineEmits(['update:modelValue'])

const selectedColumns = ref(props.modelValue)

// 监听选中列变化并保存到本地缓存
watch(selectedColumns, (newVal) => {
    emit('update:modelValue', newVal)
    localStorage.setItem(`table-columns-${props.cacheKey}`, JSON.stringify(newVal))
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
    selectedColumns.value = newVal
}, { deep: true })

// 全选方法
const selectAllColumns = () => {
    selectedColumns.value = props.columnOptions.map(col => col.prop)
}

// 取消全选方法
const unselectAllColumns = () => {
    selectedColumns.value = []
}
// 从本地缓存加载列设置
onMounted(() => {
    const cachedColumns = localStorage.getItem(`table-columns-${props.cacheKey}`)
    if (cachedColumns) {
        const parsedColumns = JSON.parse(cachedColumns)
        // 验证缓存的列是否都存在于当前的columnOptions中
        const validColumns = parsedColumns.filter(col =>
            props.columnOptions.some(option => option.prop === col)
        )
        if (validColumns.length > 0) {
            selectedColumns.value = validColumns
            emit('update:modelValue', validColumns)
        }
    }
})
</script>

<style lang="scss" scoped>
// 设置按钮样式
.btn-setting {
    margin-left: auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .setting-icon {
        margin-right: 6px;
        font-size: 16px;
    }

    .setting-text {
        font-size: 14px;
    }
}

// 弹出框内容样式
.column-setting {
    padding: 0;

    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px 8px 0 0;

        .header-title {
            display: flex;
            align-items: center;
            gap: 8px;

            .title-icon {
                font-size: 18px;
                color: #4facfe;
            }

            .title-text {
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
            }
        }
    }

    &__btns {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 8px;
            border-radius: 6px;
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-1px);
            }

            &.select-all-btn {
                color: #27ae60;

                &:hover {
                    background: rgba(39, 174, 96, 0.1);
                }
            }

            &.unselect-all-btn {
                color: #e74c3c;

                &:hover {
                    background: rgba(231, 76, 60, 0.1);
                }
            }
        }

        .btn-divider {
            height: 16px;
            margin: 0 4px;
            border-color: #ddd;
        }
    }

    &__divider {
        margin: 12px 0;
        border-color: #e9ecef;
    }

    &__content {
        padding: 8px 20px 16px;
        max-height: 320px;
        overflow-y: auto;

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &::-webkit-scrollbar-track {
            border-radius: 3px;
            background-color: #f8f9fa;
        }
    }

    &__item {
        margin: 0;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
            background: #f0f9ff;
            transform: translateX(2px);
        }

        .column-checkbox {
            width: 100%;
            margin: 0;

            :deep(.el-checkbox__input) {
                .el-checkbox__inner {
                    border-radius: 4px;
                    border-color: #4facfe;
                    transition: all 0.3s ease;

                    &:hover {
                        border-color: #4facfe;
                        transform: scale(1.1);
                    }
                }

                &.is-checked {
                    .el-checkbox__inner {
                        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                        border-color: #4facfe;
                    }
                }
            }

            :deep(.el-checkbox__label) {
                font-size: 14px;
                font-weight: 500;
                color: #2c3e50;
                padding-left: 8px;
                transition: color 0.3s ease;
            }

            &:hover {
                :deep(.el-checkbox__label) {
                    color: #4facfe;
                }
            }
        }

        .checkbox-label {
            user-select: none;
        }
    }

    &__footer {
        padding: 12px 20px 16px;
        background: #f8f9fa;
        border-radius: 0 0 8px 8px;
        margin-top: 12px;
        border-top: 1px solid #e9ecef;

        .selected-count {
            font-size: 13px;
            color: #7f8c8d;
            text-align: center;
            font-weight: 500;

            .count-number {
                color: #4facfe;
                font-weight: 600;
                font-size: 14px;
            }
        }
    }
}



// 响应式设计
@media (max-width: 768px) {
    .btn-setting {
        padding: 6px 12px;

        .setting-text {
            display: none;
        }

        .setting-icon {
            margin-right: 0;
        }
    }

    .column-setting {
        &__header {
            padding: 12px 16px 8px;

            .header-title {
                .title-text {
                    font-size: 14px;
                }
            }
        }

        &__content {
            padding: 8px 16px 12px;
            max-height: 250px;
        }

        &__footer {
            padding: 8px 16px 12px;
        }
    }
}
</style>