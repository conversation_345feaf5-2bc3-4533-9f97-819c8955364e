import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import path from "path";

/**
 * 创建一个用于处理SVG图标的Vite插件
 * @param {Boolean} isBuild - 是否为构建模式
 * @returns {Object} 返回一个Vite插件对象
 */
export default function createSvgIcon(isBuild) {
  // 使用vite-plugin-svg-icons插件创建一个SVG图标插件
  return createSvgIconsPlugin({
    // 指定SVG图标文件所在的目录
    iconDirs: [path.resolve(process.cwd(), "src/assets/icons/svg")],
    // 定义SVG图标的ID格式
    symbolId: "icon-[dir]-[name]",
    // 如果是构建模式，传递svgoOptions配置项
    svgoOptions: isBuild,
  });
}
