<!-- 社保收费频率报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择城市" clearable>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractNumber">
                <el-input class="width220" v-model="obj.queryParams.contractNumber" placeholder="请输入合同编号" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="接单方:" prop="receiver">
                <el-select class="width220" v-model="obj.queryParams.receiver" placeholder="请选择接单方" clearable>
                    <el-option v-for="item in receiverOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="入离职状态:" prop="employmentStatus">
                <el-select class="width220" v-model="obj.queryParams.employmentStatus" placeholder="请选择状态" clearable>
                    <el-option v-for="item in employmentStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="单立户:" prop="isSingleAccount">
                <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row>
                <el-form-item>
                    <el-button icon="Refresh" @click="handleReset">重置</el-button>
                    <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
                    <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                </el-form-item>
            </el-row>
        </el-form>
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" style="width: 100%" border
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column typee="index" label="序号" width="60" align="center" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="集团名称" align="center" prop="groupName" />
            <el-table-column label="合同编号" align="center" prop="contractNumber" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="合同二级类型" align="center" width="120" prop="contractSubtype" />
            <el-table-column label="派单地" align="center" prop="dispatchLocation" />
            <el-table-column label="是否单立户" align="center" width="90" prop="isSingleAccount">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isSingleAccount" />
                </template>
            </el-table-column>
            <el-table-column label="派单客服" align="center" prop="dispatchService" />
            <el-table-column label="员工姓名" align="center" prop="employeeName" />
            <el-table-column label="身份证号" align="center" prop="idNumber" />
            <el-table-column label="参保城市" align="center" prop="insuredCity" />
            <el-table-column label="接单方" align="center" prop="receiver" />
            <el-table-column label="自有城市or供应商" align="center" width="140" prop="companyType">
                <template #default="scope">
                    <el-tag :type="scope.row.companyType === 'own' ? 'primary' : 'success'">
                        {{ scope.row.companyType === 'own' ? '自有城市' : '供应商' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="账单收付规" align="center" width="90" prop="billingRule" />
            <el-table-column label="社保险种" align="center" prop="socialSecurityType" />
            <el-table-column label="收费频率" align="center" prop="chargeFrequency">
                <template #default="scope">
                    <el-tag :type="getFrequencyTagType(scope.row.chargeFrequency)">
                        {{ getFrequencyLabel(scope.row.chargeFrequency) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="提前几月收" align="center" width="90" prop="advanceMonths">
                <template #default="scope">
                    <span>{{ scope.row.advanceMonths }} 月</span>
                </template>
            </el-table-column>
            <el-table-column label="备案内容" align="center" prop="filingContent" />
            <el-table-column label="备案附件" align="center" prop="filingAttachment">
                <template #default="scope">
                    <el-button v-if="scope.row.filingAttachment" type="primary" link
                        @click="handleViewAttachment(scope.row)">
                        查看
                    </el-button>
                    <span v-else>无</span>
                </template>
            </el-table-column>
        </el-table>
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
];

// 接单方选项
const receiverOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '个人' }
];

// 入离职状态选项
const employmentStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '待入职' }
];

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    ids: [], // 选中id
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        city: null,
        contractNumber: null,
        customerName: null,
        receiver: null,
        employmentStatus: null,
        isSingleAccount: null
    }, // 查询表单
    tableData: [] // 表格数据
});

/** 获取收费频率标签类型 */
function getFrequencyTagType(frequency) {
    switch (frequency) {
        case 'monthly':
            return 'primary';
        case 'quarterly':
            return 'success';
        case 'semiannually':
            return 'warning';
        case 'annually':
            return 'danger';
        default:
            return 'info';
    }
}

/** 获取收费频率标签文本 */
function getFrequencyLabel(frequency) {
    switch (frequency) {
        case 'monthly':
            return '月缴';
        case 'quarterly':
            return '季缴';
        case 'semiannually':
            return '半年缴';
        case 'annually':
            return '年缴';
        default:
            return frequency;
    }
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerCode: 'KH20230001',
                customerName: '客户A',
                groupName: '集团A',
                contractNumber: 'HT20230001',
                contractName: '合同1',
                contractType: '劳务派遣',
                contractSubtype: '社保代缴',
                dispatchLocation: '北京',
                isSingleAccount: 'Y',
                dispatchService: '客服1',
                employeeName: '张三',
                idNumber: '110101199001011234',
                insuredCity: '北京',
                receiver: '公司A',
                companyType: 'own',
                billingRule: '先收后付',
                socialSecurityType: '养老保险',
                chargeFrequency: 'monthly',
                advanceMonths: 1,
                filingContent: '已完成备案',
                filingAttachment: '附件1.pdf'
            },
            {
                id: 2,
                customerCode: 'KH20230002',
                customerName: '客户B',
                groupName: '集团B',
                contractNumber: 'HT20230002',
                contractName: '合同2',
                contractType: '劳务外包',
                contractSubtype: '商保代缴',
                dispatchLocation: '上海',
                isSingleAccount: 'N',
                dispatchService: '客服2',
                employeeName: '李四',
                idNumber: '310101199002022345',
                insuredCity: '上海',
                receiver: '公司B',
                companyType: 'supplier',
                billingRule: '先付后收',
                socialSecurityType: '医疗保险',
                chargeFrequency: 'quarterly',
                advanceMonths: 3,
                filingContent: '备案中',
                filingAttachment: null
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {

    });
}



/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 查看附件操作 */
function handleViewAttachment(row) {
    proxy.$modal.msgInfo('查看附件：' + row.filingAttachment);
    // 实际开发时可以调用文件预览接口
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

// 初始化数据
getList();
</script>

<style lang="scss" scoped>
.width220 {
    width: 220px;
}
</style>