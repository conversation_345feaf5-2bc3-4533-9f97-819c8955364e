<!-- 配置模板 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="配置项键:" prop="configKey">
                <el-input class="width220" v-model="obj.queryParams.configKey" placeholder="请输入配置项键" clearable />
            </el-form-item>
            <el-form-item label="配置项描述:" prop="configDescription">
                <el-input class="width220" v-model="obj.queryParams.configDescription" placeholder="请输入配置项描述"
                    clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="20" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 表格 -->
        <el-table style="width: 100%;" v-loading="obj.loading" border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="配置项键" align="center" prop="configKey" />
            <el-table-column label="配置项值" align="center" prop="configValue" />
            <el-table-column label="配置项种类" align="center" prop="configType" />
            <el-table-column label="配置项描述" align="center" prop="configDescription" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="40%">
            <el-form :model="obj.dialogForm" ref="formRef" label-width="auto">
                <el-form-item label="配置项键" prop="configKey"
                    :rules="[{ required: true, message: '请输入配置项键', trigger: 'blur' }]">
                    <el-input class="width220" v-model="obj.dialogForm.configKey" placeholder="请输入配置项键" clearable />
                </el-form-item>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="配置项值" prop="configValue"
                            :rules="[{ required: true, message: '请输入配置项值', trigger: 'blur' }]">
                            <el-input class="width220" v-model="obj.dialogForm.configValue" placeholder="请输入配置项值"
                                clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <FileUpload v-model="obj.dialogForm.fileList" :limit="1" :fileSize="10"
                            :fileType="['jpg', 'jpeg', 'png', 'pdf']" />
                    </el-col>
                </el-row>
                <el-form-item label="配置项描述" prop="configDescription">
                    <el-input class="width220" v-model="obj.dialogForm.configDescription" placeholder="请输入配置项描述"
                        clearable />
                </el-form-item>
                <el-form-item label="配置项种类" prop="configType">
                    <el-select class="width220" v-model="obj.dialogForm.configType" placeholder="请选择配置项种类" clearable>
                        <el-option v-for="item in configTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSubmit">保存</el-button>
                <el-button @click="reset">重置</el-button>
                <el-button @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";
import FileUpload from '@/components/FileUpload'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 配置项种类选项
const configTypeOptions = [
    { value: 'system', label: '系统配置' },
    { value: 'business', label: '业务配置' },
    { value: 'security', label: '安全配置' },
    { value: 'other', label: '其他配置' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        configKey: null,
        configDescription: null
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    ids: [], // 选中的id
    title: "新增配置项" // 标题
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                configKey: 'system.name',
                configValue: '系统名称',
                configType: 'system',
                configDescription: '系统名称配置'
            },
            {
                id: 2,
                configKey: 'system.logo',
                configValue: '/logo.png',
                configType: 'system',
                configDescription: '系统标志配置'
            },
            {
                id: 3,
                configKey: 'business.timeout',
                configValue: '30',
                configType: 'business',
                configDescription: '业务超时时间（分钟）'
            }
        ];
        obj.total = 3;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 表单重置 */
function reset() {
    obj.dialogForm = {
        configKey: '',
        configValue: '',
        configType: '',
        configDescription: '',
        fileList: []
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.title = "新增配置项";
    obj.dialogShow = true;
}

/** 修改按钮操作 */
function handleEdit(row) {
    reset();
    obj.title = "修改配置项";
    // 实际开发时可以调用接口获取数据
    // 这里模拟数据
    if (row) {
        obj.dialogForm = {
            id: row.id,
            configKey: row.configKey,
            configValue: row.configValue,
            configType: row.configType,
            configDescription: row.configDescription,
            fileList: []
        };
    }
    obj.dialogShow = true;
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 提交按钮操作 */
function handleSubmit() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            // 处理文件上传
            if (obj.dialogForm.fileList && obj.dialogForm.fileList.length > 0) {
                // 如果有文件，可以将文件路径设置到configValue中
                obj.dialogForm.configValue = obj.dialogForm.fileList[0].url || obj.dialogForm.configValue;
            }

            if (obj.dialogForm.id) {
                // 调用修改接口
                proxy.$modal.msgSuccess('修改成功');
            } else {
                // 调用新增接口
                proxy.$modal.msgSuccess('新增成功');
            }
            obj.dialogShow = false;
            getList();
        }
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>