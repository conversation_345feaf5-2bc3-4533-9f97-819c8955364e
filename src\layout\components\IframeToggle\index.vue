<template>
  <inner-link v-for="(item, index) in tagsViewStore.iframeViews" :key="item.path" :iframeId="'iframe' + index"
    v-show="route.path === item.path" :src="iframeUrl(item.meta.link, item.query)"></inner-link>
</template>

<script setup>
import InnerLink from "../InnerLink/index";
import useTagsViewStore from "@/store/modules/tagsView";

const route = useRoute();
const tagsViewStore = useTagsViewStore();

/**
 * 生成带有查询参数的iframe URL
 * 
 * 根据提供的基础URL和查询参数对象，动态构造一个带有查询参数的URL
 * 如果查询参数对象为空，则直接返回基础URL
 * 
 * @param {string} url - 基础URL地址
 * @param {Object} query - 查询参数对象，键值对形式
 * @returns {string} - 带有查询参数的URL地址
 */
function iframeUrl(url, query) {
  // 检查查询参数对象是否为空
  if (Object.keys(query).length > 0) {
    // 将查询参数对象转换为URL参数字符串
    let params = Object.keys(query).map((key) => key + "=" + query[key]).join("&");
    // 返回带有查询参数的URL
    return url + "?" + params;
  }
  // 如果查询参数对象为空，直接返回基础URL
  return url;
}
</script>
