<template>
    <div>
        <el-dialog v-model="dialogShow" :title="props.title" width="50%" @close="handleClose">
            <el-form :model="props.dialogForm" class="formHight" ref="formRef" :rules="props.rules" inline
                label-width="auto">
                <el-form-item label="唯一号" prop="uniqueId">
                    <el-input class="width220" v-model="props.dialogForm.uniqueId" placeholder="请输入" disabled />
                </el-form-item>
                <el-form-item label="雇员姓名" prop="employeeName">
                    <el-input class="width220" v-model="props.dialogForm.employeeName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="证件类型" prop="idType" required>
                    <el-select class="width220" v-model="props.dialogForm.idType" placeholder="请选择" clearable>
                        <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="证件号码" prop="idNumber" required>
                    <el-input class="width220" v-model="props.dialogForm.idNumber" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="手机号码" prop="phoneNumber">
                    <el-input class="width220" v-model="props.dialogForm.phoneNumber" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                    <el-input class="width220" v-model="props.dialogForm.email" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="是否竞业员工" prop="isCompetitor"
                    v-if="props.menuName === 'pureAgent' || props.menuName === 'non_pureAgent'">
                    <el-select class="width220" v-model="props.dialogForm.isCompetitor" placeholder="请选择" clearable>
                        <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工资证件类型" prop="salaryIdType">
                    <el-select class="width220" v-model="props.dialogForm.salaryIdType" placeholder="请选择" clearable>
                        <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工资其他证件类型" prop="salaryOtherIdType">
                    <el-input class="width220" v-model="props.dialogForm.salaryOtherIdType" placeholder="请输入"
                        :disabled="props.dialogForm.salaryIdType !== 'other'" />
                </el-form-item>
                <el-form-item label="国籍（地区）" prop="nationality">
                    <el-select class="width220" v-model="props.dialogForm.nationality" placeholder="请选择" clearable>
                        <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="性别" prop="gender">
                    <el-select class="width220" v-model="props.dialogForm.gender" placeholder="请选择" clearable>
                        <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="出生日期" prop="birthDate">
                    <el-date-picker class="width220" v-model="props.dialogForm.birthDate" type="date"
                        placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="涉税事由" prop="taxReason">
                    <el-select class="width220" v-model="props.dialogForm.taxReason" placeholder="请选择" clearable>
                        <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="出生国家（地区）" prop="birthCountry">
                    <el-select class="width220" v-model="props.dialogForm.birthCountry" placeholder="请选择" clearable>
                        <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="首次入境时间" prop="firstEntryTime">
                    <el-date-picker class="width220" v-model="props.dialogForm.firstEntryTime" type="date"
                        placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="预计离境时间" prop="expectedDepartureTime">
                    <el-date-picker class="width220" v-model="props.dialogForm.expectedDepartureTime" type="date"
                        placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="其他证件号码" prop="otherIdNumber">
                    <el-input class="width220" v-model="props.dialogForm.otherIdNumber" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="其他姓名" prop="otherName">
                    <el-input class="width220" v-model="props.dialogForm.otherName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="任职受雇从业类型" prop="employmentType">
                    <el-select class="width220" v-model="props.dialogForm.employmentType" placeholder="请选择" clearable>
                        <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="submit">提交</el-button>
                <el-button @click="props.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { id_type } = proxy.useDict("id_type");

const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false,
    },
    dialogForm: {
        type: Object,
    },
    title: {
        type: String,
        default: ''
    },
    rules: {
        type: Object,
        default: () => ({})
    },
    menuName: {
        type: String,
        default: ''
    }
});
// 使用本地变量来跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.show的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});
const emit = defineEmits([
    'submit',
    'handleClose',
    'update:dialogShow'
]);

// 提交
function submit() {
    emit('submit');
}

// 关闭
function handleClose() {
    emit('handleClose');
}

</script>