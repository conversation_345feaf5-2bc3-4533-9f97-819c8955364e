<!-- 个税预申报数据导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入编号:" prop="importId">
                <el-input class="width220" v-model="obj.queryParams.importId" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="计税月:" prop="taxMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.taxMonth" type="month" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="withholdingAgent">
                <el-select class="width220" v-model="obj.queryParams.withholdingAgent" placeholder="请选择" clearable>
                    <el-option v-for="item in withholdingAgentOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="所得项目:" prop="incomeType">
                <el-select class="width220" v-model="obj.queryParams.incomeType" placeholder="请选择" clearable>
                    <el-option v-for="item in incomeTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="个税申报类型:" prop="taxDeclarationType">
                <el-select class="width220" v-model="obj.queryParams.taxDeclarationType" placeholder="请选择" clearable>
                    <el-option v-for="item in taxDeclarationTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleImport">导入个税申报数据</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="View" @click="handleViewImportRecord">查看导入记录</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" @click="handleExport">导出数据</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="导入编号" align="center" width="90" prop="importId" />
            <el-table-column label="导入时间" align="center" width="90" prop="importTime" />
            <el-table-column label="工号" align="center" width="90" prop="employeeId" />
            <el-table-column label="姓名" align="center" width="90" prop="employeeName" />
            <el-table-column label="证件类型" align="center" width="90" prop="idType" />
            <el-table-column label="证件号码" align="center" width="90" prop="idNumber" />
            <el-table-column label="扣缴义务人编号" align="center" width="120" prop="withholdingAgentId" />
            <el-table-column label="扣缴义务人名称" align="center" width="120" prop="withholdingAgentName" />
            <el-table-column label="计税月" align="center" width="90" prop="taxMonth" />
            <el-table-column label="所得项目" align="center" width="90" prop="incomeType" />
            <el-table-column label="个税申报类型" align="center" width="110" prop="taxDeclarationType" />
            <el-table-column label="本期收入" align="center" width="90" prop="currentIncome" />
            <el-table-column label="本期费用" align="center" width="90" prop="currentExpense" />
            <el-table-column label="本期免税收入" align="center" width="110" prop="currentTaxFreeIncome" />
            <el-table-column label="本期基本养老保险" align="center" width="130" prop="currentPensionInsurance" />
            <el-table-column label="本期基本医疗保险费" align="center" width="150" prop="currentMedicalInsurance" />
            <el-table-column label="本期失业失业保险费" align="center" width="150" prop="currentUnemploymentInsurance" />
            <el-table-column label="本期住房公积金" align="center" width="120" prop="currentHousingFund" />
            <el-table-column label="本期企业(职业)年金" align="center" width="150" prop="currentEnterpriseAnnuity" />
            <el-table-column label="本期商业健康保险费" align="center" width="150" prop="currentCommercialHealthInsurance" />
            <el-table-column label="本期税延养老保险" align="center" width="130" prop="currentDeferredPensionInsurance" />
            <el-table-column label="本期其他扣除(其他)" align="center" width="140" prop="currentOtherDeductions" />
            <el-table-column label="累计收入额" align="center" width="90" prop="cumulativeIncome" />
            <el-table-column label="累计免税收入" align="center" width="110" prop="cumulativeTaxFreeIncome" />
            <el-table-column label="累计减除费用" align="center" width="110" prop="cumulativeDeductibleExpenses" />
            <el-table-column label="累计专项扣除" align="center" width="110" prop="cumulativeSpecialDeductions" />
            <el-table-column label="累计子女教育支出扣除" align="center" width="160" prop="cumulativeChildEducationDeduction" />
            <el-table-column label="累计继续教育支出扣除" align="center" width="160"
                prop="cumulativeContinuingEducationDeduction" />
            <el-table-column label="累计住房贷款利息支出扣除" align="center" width="180"
                prop="cumulativeHousingLoanInterestDeduction" />
            <el-table-column label="累计住房租金支出扣除" align="center" width="150" prop="cumulativeHousingRentDeduction" />
            <el-table-column label="累计赡养老人支出扣除" align="center" width="160" prop="cumulativeElderlySupport" />
            <el-table-column label="累计其他扣除" align="center" width="110" prop="cumulativeOtherDeductions" />
            <el-table-column label="累计准予扣除的捐赠" align="center" width="150" prop="cumulativeCharitableDonations" />
            <el-table-column label="累计应纳税所得额" align="center" width="130" prop="cumulativeTaxableIncome" />
            <el-table-column label="税率" align="center" width="90" prop="taxRate" />
            <el-table-column label="速算扣除数" align="center" width="90" prop="quickCalculationDeduction" />
            <el-table-column label="累计应纳税额" align="center" width="120" prop="cumulativeTaxPayable" />
            <el-table-column label="累计减免税额" align="center" width="120" prop="cumulativeTaxReduction" />
            <el-table-column label="累计应扣缴税额" align="center" width="120" prop="cumulativeWithholdingTax" />
            <el-table-column label="已缴税额" align="center" width="90" prop="paidTax" />
            <el-table-column label="应补(退)税额" align="center" width="130" prop="taxRefundOrPayment" />
            <el-table-column label="备注" align="center" width="90" prop="remark" />
            <el-table-column label="创建人" align="center" width="90" prop="createBy" />
            <el-table-column label="创建时间" align="center" width="90" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" title="上传导入数据" type="personalIncomeTaxDeclaration_data"
            :dialogForm="obj.dialogForm" :rules="rules" />

        <!-- 查看导入记录 -->
        <ImportRecord v-model:dialogShow="obj.dialogShow2" title="查看导入记录" type="personalIncomeTaxDeclaration_data" />
    </div>
</template>

<script setup name="PersonalTaxPreDeclarationData_import">
import ImportRecord from '@/views/reonManage/components/dialog/importRecord.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 扣缴义务人选项
const withholdingAgentOptions = [
    { value: 'W001', label: '北京科技有限公司' },
    { value: 'W002', label: '上海贸易有限公司' },
    { value: 'W003', label: '广州电子有限公司' },
    { value: 'W004', label: '深圳科技有限公司' },
    { value: 'W005', label: '成都信息有限公司' }
];

// 所得项目选项
const incomeTypeOptions = [
    { value: 'I001', label: '工资薪金' },
    { value: 'I002', label: '劳务报销' },
    { value: 'I003', label: '税前奖金' },
    { value: 'I004', label: '年终奖' },
    { value: 'I005', label: '劳务报酬' }
];

// 个税申报类型选项
const taxDeclarationTypeOptions = [
    { value: 'T001', label: '工资薪金所得' },
    { value: 'T002', label: '个体工商户生产经营所得' },
    { value: 'T003', label: '利息股息红利所得' },
    { value: 'T004', label: '财产租赁所得' },
    { value: 'T005', label: '特许权使用费所得' }
];

// 表单验证规则
const rules = {
    withholdingAgent: [
        { required: true, message: '请选择扣缴义务人', trigger: 'change' }
    ],
    file: [
        { required: true, message: '请上传文件', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importId: null, // 导入编号
        employeeName: null, // 姓名
        idNumber: null, // 证件号
        taxMonth: null, // 计税月
        withholdingAgent: null, // 扣缴义务人
        incomeType: null, // 所得项目
        taxDeclarationType: null, // 个税申报类型
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {}, // 导入表单
    dialogShow: false, // 导入对话框
    dialogShow2: false, // 导入记录对话框
    recordQueryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateStart: null, // 导入日期开始
        importDateEnd: null, // 导入日期结束
        importBy: null, // 导入人
        importId: null, // 导入编号
    }, // 导入记录查询参数
    importRecords: [], // 导入记录列表
    recordTotal: 0, // 导入记录总数
    ids: [], // 选中的id
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        obj.tableData = [
            {
                id: 1,
                importId: 'IMP20230601001',
                importTime: '2023-06-01 10:00:00',
                employeeId: 'EMP001',
                employeeName: '张三',
                idType: '身份证',
                idNumber: '110101199001011234',
                withholdingAgentId: 'W001',
                withholdingAgentName: '北京科技有限公司',
                taxMonth: '2023-06',
                incomeType: '工资薪金',
                taxDeclarationType: '工资薪金所得',
                currentIncome: 15000,
                currentExpense: 2000,
                currentTaxFreeIncome: 1000,
                currentPensionInsurance: 800,
                currentMedicalInsurance: 400,
                currentUnemploymentInsurance: 200,
                currentHousingFund: 1200,
                currentEnterpriseAnnuity: 500,
                currentCommercialHealthInsurance: 300,
                currentDeferredPensionInsurance: 200,
                currentOtherDeductions: 100,
                cumulativeIncome: 90000,
                cumulativeTaxFreeIncome: 6000,
                cumulativeDeductibleExpenses: 12000,
                cumulativeSpecialDeductions: 8000,
                cumulativeChildEducationDeduction: 3000,
                cumulativeContinuingEducationDeduction: 1000,
                cumulativeHousingLoanInterestDeduction: 5000,
                cumulativeHousingRentDeduction: 4000,
                cumulativeElderlySupport: 2000,
                cumulativeOtherDeductions: 1000,
                cumulativeCharitableDonations: 500,
                cumulativeTaxableIncome: 55500,
                taxRate: 0.1,
                quickCalculationDeduction: 2520,
                cumulativeTaxPayable: 3030,
                cumulativeTaxReduction: 0,
                cumulativeWithholdingTax: 3030,
                paidTax: 3000,
                taxRefundOrPayment: 30,
                remark: '',
                createBy: 'admin',
                createTime: '2023-06-01 10:00:00'
            },
            {
                id: 2,
                importId: 'IMP20230601002',
                importTime: '2023-06-01 10:05:00',
                employeeId: 'EMP002',
                employeeName: '李四',
                idType: '身份证',
                idNumber: '310101199002022345',
                withholdingAgentId: 'W002',
                withholdingAgentName: '上海贸易有限公司',
                taxMonth: '2023-06',
                incomeType: '工资薪金',
                taxDeclarationType: '工资薪金所得',
                currentIncome: 20000,
                currentExpense: 3000,
                currentTaxFreeIncome: 1500,
                currentPensionInsurance: 1000,
                currentMedicalInsurance: 500,
                currentUnemploymentInsurance: 250,
                currentHousingFund: 1500,
                currentEnterpriseAnnuity: 600,
                currentCommercialHealthInsurance: 400,
                currentDeferredPensionInsurance: 300,
                currentOtherDeductions: 150,
                cumulativeIncome: 120000,
                cumulativeTaxFreeIncome: 9000,
                cumulativeDeductibleExpenses: 18000,
                cumulativeSpecialDeductions: 10000,
                cumulativeChildEducationDeduction: 6000,
                cumulativeContinuingEducationDeduction: 2000,
                cumulativeHousingLoanInterestDeduction: 7000,
                cumulativeHousingRentDeduction: 0,
                cumulativeElderlySupport: 4000,
                cumulativeOtherDeductions: 1500,
                cumulativeCharitableDonations: 1000,
                cumulativeTaxableIncome: 71500,
                taxRate: 0.2,
                quickCalculationDeduction: 4920,
                cumulativeTaxPayable: 9380,
                cumulativeTaxReduction: 500,
                cumulativeWithholdingTax: 8880,
                paidTax: 8000,
                taxRefundOrPayment: 880,
                remark: '',
                createBy: 'admin',
                createTime: '2023-06-01 10:05:00'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 导入个税申报数据 */
function handleImport() {
    obj.dialogForm = {
        withholdingAgent: null,
        remark: '',
        file: null
    };
    obj.dialogShow = true;
}

/** 关闭导入弹窗 */
function handleClose() {
    obj.dialogShow = false;
}

/** 查看导入记录按钮操作 */
function handleViewImportRecord() {
    obj.dialogShow2 = true;
}

/** 关闭查看导入记录弹窗 */
function handleClose2() {
    obj.dialogShow2 = false;
}


/** 导出数据 */
function handleExport() {
    // 检查是否有选中记录
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请至少选择一条记录进行导出");
        return;
    }

    // 模拟导出操作
    proxy.$modal.loading("正在导出数据，请稍候...");
    setTimeout(() => {
        proxy.$modal.closeLoading();
        proxy.$modal.msgSuccess("导出成功，共导出 " + obj.ids.length + " 条记录");
    }, 1000);
}


getList();
</script>
<style lang="scss" scoped></style>