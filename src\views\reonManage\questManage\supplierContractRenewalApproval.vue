<!-- 供应商合同续签审批流程 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="供应商:" prop="supplierName">
                <el-select class="width220" v-model="obj.queryParams.supplierName" placeholder="请选择" clearable>
                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="合同编号" align="center" prop="contractNo" />
            <el-table-column label="供应商名称" align="center" prop="supplierName" />
            <el-table-column label="合同开始时间" align="center" prop="contractStartTime" />
            <el-table-column label="合同结束时间" align="center" prop="contractEndTime" />
            <el-table-column label="节点名称" align="center" prop="nodeName" />
            <el-table-column label="提交人" align="center" prop="submitter" />
            <el-table-column label="待处理人" align="center" prop="processor" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="操作" align="center" width="180">
                <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="SupplierContractRenewalApproval">
import { listApproval } from '@/api/reonApi/approval';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 供应商选项
const supplierOptions = [
    { value: '1', label: '供应商A' },
    { value: '2', label: '供应商B' },
    { value: '3', label: '供应商C' },
    { value: '4', label: '供应商D' },
    { value: '5', label: '供应商E' }
];

const obj = reactive({
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        contractNo: null,
        startTime: null,
        endTime: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})
/** 获取列表数据 */
function getList() {
    obj.loading = true;
    listApproval(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 查看详情 */
function handleDetail(row) {
    proxy.$modal.msgInfo(`查看ID为${row.id}的详细信息`);
}


getList();
</script>
<style lang="scss" scoped></style>