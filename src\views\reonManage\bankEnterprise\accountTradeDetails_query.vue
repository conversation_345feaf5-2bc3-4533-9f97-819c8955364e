<!-- 查询账户交易明细 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="户口号:" prop="accountNo">
                <el-input class="width220" v-model="obj.queryParams.accountNo" placeholder="请输入户口号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="开始日期:" prop="startDate">
                <el-date-picker class="width220" v-model="obj.queryParams.startDate" type="date" placeholder="请选择开始日期"
                    clearable />
            </el-form-item>
            <el-form-item label="结束日期:" prop="endDate">
                <el-date-picker class="width220" v-model="obj.queryParams.endDate" type="date" placeholder="请选择结束日期"
                    clearable />
            </el-form-item>
            <el-form-item label="借贷码:" prop="debitCreditCode">
                <el-select class="width220" v-model="obj.queryParams.debitCreditCode" placeholder="请选择借贷码" clearable>
                    <el-option v-for="item in debitCreditOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="交易日" align="center" prop="transactionDate" />
            <el-table-column label="流水号" align="center" prop="serialNo" />
            <el-table-column label="交易时间" align="center" prop="transactionTime" />
            <el-table-column label="起息日" align="center" prop="valueDate" />
            <el-table-column label="借贷码" align="center" prop="debitCreditCode" />
            <el-table-column label="交易金额" align="center" prop="amount" />
            <el-table-column label="币种" align="center" prop="currency" />
            <el-table-column label="交易类型" align="center" prop="transactionType" />
            <el-table-column label="票据号" align="center" prop="voucherNo" />
            <el-table-column label="你方摘要" align="center" prop="yourSummary" />
            <el-table-column label="冲帐标志" align="center" prop="offsetFlag" />
            <el-table-column label="余额" align="center" prop="balance" />
            <el-table-column label="扩展摘要" align="center" prop="extendedSummary" />
            <el-table-column label="收付方帐号" align="center" prop="payeeAccountNo" />
            <el-table-column label="收付方名称" align="center" prop="payeeName" />
            <el-table-column label="收付方开户行行名" align="center" prop="payeeBankName" />
            <el-table-column label="收付方开户行地址" align="center" prop="payeeBankAddress" />
            <el-table-column label="母子公司帐号" align="center" prop="parentAccountNo" />
            <el-table-column label="母子公司名称" align="center" prop="parentName" />
            <el-table-column label="母子公司开户行行名" align="center" prop="parentBankName" width="150" />
            <el-table-column label="母子公司开户行地址" align="center" prop="parentBankAddress" width="150" />
            <el-table-column label="信息标志" align="center" prop="infoFlag" />
            <el-table-column label="业务名称" align="center" prop="businessName" />
            <el-table-column label="网银业务摘要" align="center" prop="onlineBankingSummary" />
            <el-table-column label="网银流程实例号" align="center" prop="onlineBankingInstanceNo" />
            <el-table-column label="网银业务参考号" align="center" prop="onlineBankingReferenceNo" />
            <el-table-column label="虚拟户编号" align="center" prop="virtualAccountNo" />
            <el-table-column label="商务支付订单号" align="center" prop="businessPaymentOrderNo" />
            <el-table-column label="保留字" align="center" prop="reservedField" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="AccountTradeDetails_query">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 借贷码选项
const debitCreditOptions = [
    { value: 'D', label: '借' },
    { value: 'C', label: '贷' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountNo: null,
        startDate: null,
        endDate: null,
        debitCreditCode: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                transactionDate: '2023-05-15',
                serialNo: 'TX20230515001',
                transactionTime: '10:30:45',
                valueDate: '2023-05-15',
                debitCreditCode: 'D',
                amount: 5000.00,
                currency: 'CNY',
                transactionType: '转账',
                voucherNo: 'VC001',
                yourSummary: '工资发放',
                offsetFlag: '否',
                balance: 15000.00,
                extendedSummary: '5月工资',
                payeeAccountNo: '****************',
                payeeName: '张三',
                payeeBankName: '中国银行北京分行',
                payeeBankAddress: '北京市海淀区中关村大街',
                parentAccountNo: '',
                parentName: '',
                parentBankName: '',
                parentBankAddress: '',
                infoFlag: '正常',
                businessName: '工资发放',
                onlineBankingSummary: '工资发放',
                onlineBankingInstanceNo: 'OB001',
                onlineBankingReferenceNo: 'REF001',
                virtualAccountNo: '',
                businessPaymentOrderNo: '',
                reservedField: ''
            },
            {
                id: 2,
                transactionDate: '2023-05-16',
                serialNo: 'TX20230516001',
                transactionTime: '14:20:30',
                valueDate: '2023-05-16',
                debitCreditCode: 'C',
                amount: 10000.00,
                currency: 'CNY',
                transactionType: '存款',
                voucherNo: 'VC002',
                yourSummary: '客户存款',
                offsetFlag: '否',
                balance: 25000.00,
                extendedSummary: '客户存款',
                payeeAccountNo: '****************',
                payeeName: '李四',
                payeeBankName: '中国建设银行上海分行',
                payeeBankAddress: '上海市浦东新区陆家嘴路',
                parentAccountNo: '',
                parentName: '',
                parentBankName: '',
                parentBankAddress: '',
                infoFlag: '正常',
                businessName: '存款',
                onlineBankingSummary: '客户存款',
                onlineBankingInstanceNo: 'OB002',
                onlineBankingReferenceNo: 'REF002',
                virtualAccountNo: '',
                businessPaymentOrderNo: '',
                reservedField: ''
            },
            {
                id: 3,
                transactionDate: '2023-05-17',
                serialNo: 'TX20230517001',
                transactionTime: '09:15:20',
                valueDate: '2023-05-17',
                debitCreditCode: 'D',
                amount: 3000.00,
                currency: 'CNY',
                transactionType: '转账',
                voucherNo: 'VC003',
                yourSummary: '供应商付款',
                offsetFlag: '否',
                balance: 22000.00,
                extendedSummary: '供应商付款',
                payeeAccountNo: '****************',
                payeeName: '王五',
                payeeBankName: '中国农业银行广州分行',
                payeeBankAddress: '广州市天河区体育西路',
                parentAccountNo: '',
                parentName: '',
                parentBankName: '',
                parentBankAddress: '',
                infoFlag: '正常',
                businessName: '供应商付款',
                onlineBankingSummary: '供应商付款',
                onlineBankingInstanceNo: 'OB003',
                onlineBankingReferenceNo: 'REF003',
                virtualAccountNo: '',
                businessPaymentOrderNo: '',
                reservedField: ''
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}





/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}


getList();
</script>
<style lang="scss" scoped></style>