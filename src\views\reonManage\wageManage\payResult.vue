<!-- 薪资结果查询页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户编号:" prop="customerCode">
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractCode">
                <el-input class="width220" v-model="obj.queryParams.contractCode" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="客户账单月起:" prop="billMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonthStart" type="month"
                    placeholder="请选择月份" clearable />
            </el-form-item>
            <el-form-item label="客户账单月止:" prop="billMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonthEnd" type="month" placeholder="请选择月份"
                    clearable />
            </el-form-item>
            <el-form-item label="工资计税月起:" prop="taxMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.taxMonthStart" type="month"
                    placeholder="请选择月份" clearable />
            </el-form-item>
            <el-form-item label="工资计税月止:" prop="taxMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.taxMonthEnd" type="month" placeholder="请选择月份"
                    clearable />
            </el-form-item>
            <el-form-item label="工资所属月起:" prop="salaryMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryMonthStart" type="month"
                    placeholder="请选择月份" clearable />
            </el-form-item>
            <el-form-item label="工资所属月止:" prop="salaryMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryMonthEnd" type="month"
                    placeholder="请选择月份" clearable />
            </el-form-item>
            <el-form-item label="派单方:" prop="dispatchParty">
                <el-select class="width220" v-model="obj.queryParams.dispatchParty" placeholder="请选择" clearable>
                    <el-option v-for="item in dispatchPartyOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单客服:" prop="dispatchService">
                <el-select class="width220" v-model="obj.queryParams.dispatchService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="withholdingAgent">
                <el-input class="width220" v-model="obj.queryParams.withholdingAgent" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectService">
                <el-input class="width220" v-model="obj.queryParams.projectService" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="薪资客服:" prop="salaryService">
                <el-input class="width220" v-model="obj.queryParams.salaryService" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="创建人:" prop="creator">
                <el-select class="width220" v-model="obj.queryParams.creator" placeholder="请选择" clearable>
                    <el-option v-for="item in creatorOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资发放状态:" prop="paymentStatus">
                <el-select class="width220" v-model="obj.queryParams.paymentStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in paymentStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资支付日期起:" prop="payDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.payDateStart" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="工资支付日期止:" prop="payDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.payDateEnd" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Printer" @click="handlePrint">打印</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions" cache-key="pay-result" />
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column v-if="obj.selectedColumns.includes('uniqueId')" label="唯一号" align="center"
                prop="uniqueId" />
            <el-table-column v-if="obj.selectedColumns.includes('name')" label="姓名" align="center" prop="name" />
            <el-table-column v-if="obj.selectedColumns.includes('idNumber')" label="证件号码" align="center"
                prop="idNumber" />
            <el-table-column v-if="obj.selectedColumns.includes('idType')" label="证件类型" align="center" prop="idType" />
            <el-table-column v-if="obj.selectedColumns.includes('phone')" label="手机号码" align="center" prop="phone" />
            <el-table-column v-if="obj.selectedColumns.includes('withholdingAgent')" label="扣缴义务人" align="center"
                prop="withholdingAgent" />
            <el-table-column v-if="obj.selectedColumns.includes('withholdingAgentType')" label="扣缴义务人类型" align="center"
                prop="withholdingAgentType" />
            <el-table-column v-if="obj.selectedColumns.includes('customerCode')" label="客户编号" align="center"
                prop="customerCode" />
            <el-table-column v-if="obj.selectedColumns.includes('contractName')" label="合同名称" align="center"
                prop="contractName" />
            <el-table-column v-if="obj.selectedColumns.includes('contractCode')" label="合同编号" align="center"
                prop="contractCode" />
            <el-table-column v-if="obj.selectedColumns.includes('contractType')" label="合同类型" align="center"
                prop="contractType" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentStatus')" label="薪资发放状态" align="center"
                prop="paymentStatus" />
            <el-table-column v-if="obj.selectedColumns.includes('taxStatus')" label="报税状态" align="center"
                prop="taxStatus" />
            <el-table-column v-if="obj.selectedColumns.includes('salaryTypeName')" label="薪资类别名称" align="center"
                prop="salaryTypeName" />
            <el-table-column v-if="obj.selectedColumns.includes('billTemplateCode')" label="帐单模板编号" align="center"
                prop="billTemplateCode" />
            <el-table-column v-if="obj.selectedColumns.includes('billTemplateName')" label="帐单模板名称" align="center"
                prop="billTemplateName" />
            <el-table-column v-if="obj.selectedColumns.includes('bankCardNumber')" label="银行卡号" align="center"
                prop="bankCardNumber" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentName')" label="发放名称" align="center"
                prop="paymentName" />
            <el-table-column v-if="obj.selectedColumns.includes('salaryMonth')" label="工资所属月" align="center"
                prop="salaryMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('taxMonth')" label="工资计税月" align="center"
                prop="taxMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('billMonth')" label="客户账单月" align="center"
                prop="billMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('totalIncome')" label="收入合计" align="center"
                prop="totalIncome" />
            <el-table-column v-if="obj.selectedColumns.includes('personalPension')" label="养老个人" align="center"
                prop="personalPension" />
            <el-table-column v-if="obj.selectedColumns.includes('personalMedical')" label="医疗个人" align="center"
                prop="personalMedical" />
            <el-table-column v-if="obj.selectedColumns.includes('personalLongTermCare')" label="个人长护险" align="center"
                prop="personalLongTermCare" />
            <el-table-column v-if="obj.selectedColumns.includes('majorMedicalSubsidy')" label="大额医疗补助" align="center"
                prop="majorMedicalSubsidy" />
            <el-table-column v-if="obj.selectedColumns.includes('supplementaryMedicalInsurance')" label="补充医疗保险"
                align="center" prop="supplementaryMedicalInsurance" />
            <el-table-column v-if="obj.selectedColumns.includes('outpatientMedical')" label="门诊医疗(社保)" align="center"
                prop="outpatientMedical" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup>



const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



// 下拉选项数据
const dispatchPartyOptions = ref([
    { value: '1', label: '派单方A' },
    { value: '2', label: '派单方B' },
    { value: '3', label: '派单方C' }
]);

const serviceOptions = ref([
    { value: '1', label: '客服A' },
    { value: '2', label: '客服B' },
    { value: '3', label: '客服C' }
]);

const creatorOptions = ref([
    { value: '1', label: '创建人A' },
    { value: '2', label: '创建人B' },
    { value: '3', label: '创建人C' }
]);

const paymentStatusOptions = ref([
    { value: '1', label: '已发放' },
    { value: '2', label: '未发放' },
    { value: '3', label: '发放中' }
]);

// 列配置选项
const columnOptions = [
    { prop: 'uniqueId', label: '唯一号' },
    { prop: 'name', label: '姓名' },
    { prop: 'idNumber', label: '证件号码' },
    { prop: 'idType', label: '证件类型' },
    { prop: 'phone', label: '手机号码' },
    { prop: 'withholdingAgent', label: '扣缴义务人' },
    { prop: 'withholdingAgentType', label: '扣缴义务人类型' },
    { prop: 'customerCode', label: '客户编号' },
    { prop: 'contractName', label: '合同名称' },
    { prop: 'contractCode', label: '合同编号' },
    { prop: 'contractType', label: '合同类型' },
    { prop: 'paymentStatus', label: '薪资发放状态' },
    { prop: 'taxStatus', label: '报税状态' },
    { prop: 'salaryTypeName', label: '薪资类别名称' },
    { prop: 'billTemplateCode', label: '帐单模板编号' },
    { prop: 'billTemplateName', label: '帐单模板名称' },
    { prop: 'bankCardNumber', label: '银行卡号' },
    { prop: 'paymentName', label: '发放名称' },
    { prop: 'salaryMonth', label: '工资所属月' },
    { prop: 'taxMonth', label: '工资计税月' },
    { prop: 'billMonth', label: '客户账单月' },
    { prop: 'totalIncome', label: '收入合计' },
    { prop: 'personalPension', label: '养老个人' },
    { prop: 'personalMedical', label: '医疗个人' },
    { prop: 'personalLongTermCare', label: '个人长护险' },
    { prop: 'majorMedicalSubsidy', label: '大额医疗补助' },
    { prop: 'supplementaryMedicalInsurance', label: '补充医疗保险' },
    { prop: 'outpatientMedical', label: '门诊医疗(社保)' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerCode: null, // 客户编号
        customerName: null, // 客户名称
        contractCode: null, // 合同编号
        contractName: null, // 合同名称
        employeeName: null, // 雇员姓名
        idNumber: null, // 证件号码
        billMonthStart: null, // 客户账单月起
        billMonthEnd: null, // 客户账单月止
        taxMonthStart: null, // 工资计税月起
        taxMonthEnd: null, // 工资计税月止
        salaryMonthStart: null, // 工资所属月起
        salaryMonthEnd: null, // 工资所属月止
        dispatchParty: null, // 派单方
        dispatchService: null, // 派单客服
        withholdingAgent: null, // 扣缴义务人
        projectService: null, // 项目客服
        salaryService: null, // 薪资客服
        creator: null, // 创建人
        paymentStatus: null, // 薪资发放状态
        payDateStart: null, // 工资支付日期起
        payDateEnd: null, // 工资支付日期止
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    ids: [], // 选中的id
    title: "", // 标题
    columnOptions: columnOptions, // 列配置选项
    selectedColumns: columnOptions.map(item => item.prop) // 默认选中所有列
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                uniqueId: '001',
                name: '张三',
                idNumber: '110101199001011234',
                idType: '身份证',
                phone: '13800138000',
                withholdingAgent: '某某公司',
                withholdingAgentType: '企业',
                customerCode: 'CUS001',
                contractName: '劳务外包合同',
                contractCode: 'CON001',
                contractType: '劳务外包',
                paymentStatus: '已发放',
                taxStatus: '已申报',
                salaryTypeName: '月薪',
                billTemplateCode: 'TPL001',
                billTemplateName: '标准模板',
                bankCardNumber: '6222021234567890123',
                paymentName: '工资',
                salaryMonth: '2023-06',
                taxMonth: '2023-06',
                billMonth: '2023-06',
                totalIncome: 10000,
                personalPension: 800,
                personalMedical: 300,
                personalLongTermCare: 100,
                majorMedicalSubsidy: 50,
                supplementaryMedicalInsurance: 200,
                outpatientMedical: 150
            },
            {
                uniqueId: '002',
                name: '李四',
                idNumber: '310101199102023456',
                idType: '身份证',
                phone: '***********',
                withholdingAgent: '某某公司',
                withholdingAgentType: '企业',
                customerCode: 'CUS002',
                contractName: '劳务外包合同',
                contractCode: 'CON002',
                contractType: '劳务外包',
                paymentStatus: '已发放',
                taxStatus: '已申报',
                salaryTypeName: '月薪',
                billTemplateCode: 'TPL001',
                billTemplateName: '标准模板',
                bankCardNumber: '6222021234567890456',
                paymentName: '工资',
                salaryMonth: '2023-06',
                taxMonth: '2023-06',
                billMonth: '2023-06',
                totalIncome: 12000,
                personalPension: 960,
                personalMedical: 360,
                personalLongTermCare: 120,
                majorMedicalSubsidy: 60,
                supplementaryMedicalInsurance: 240,
                outpatientMedical: 180
            },
            {
                uniqueId: '003',
                name: '王五',
                idNumber: '******************',
                idType: '身份证',
                phone: '***********',
                withholdingAgent: '某某公司',
                withholdingAgentType: '企业',
                customerCode: 'CUS003',
                contractName: '劳务外包合同',
                contractCode: 'CON003',
                contractType: '劳务外包',
                paymentStatus: '未发放',
                taxStatus: '未申报',
                salaryTypeName: '月薪',
                billTemplateCode: 'TPL002',
                billTemplateName: '高级模板',
                bankCardNumber: '6222021234567890789',
                paymentName: '工资',
                salaryMonth: '2023-06',
                taxMonth: '2023-06',
                billMonth: '2023-06',
                totalIncome: 15000,
                personalPension: 1200,
                personalMedical: 450,
                personalLongTermCare: 150,
                majorMedicalSubsidy: 75,
                supplementaryMedicalInsurance: 300,
                outpatientMedical: 225
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 500);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

// 导出数据
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 打印功能
function handlePrint() {
    if (obj.tableData.length === 0) {
        proxy.$modal.msgError('没有数据可打印');
        return;
    }

    proxy.$modal.msgSuccess('准备打印');
    // 实际项目中应该调用打印API或使用打印插件

    // 简单的打印当前页面
    setTimeout(() => {
        window.print();
    }, 300);
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.uniqueId);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>