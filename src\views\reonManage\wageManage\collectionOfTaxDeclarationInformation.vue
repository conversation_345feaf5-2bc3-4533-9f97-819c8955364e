<!-- 个税申报信息采集 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="申报个税公司名称:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="扣缴义务人类型:" prop="withholdingType">
                <el-select class="width220" v-model="obj.queryParams.withholdingType" placeholder="请选择" clearable>
                    <el-option v-for="item in withholdingTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资计税月:" prop="taxMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.taxMonth" type="month" format="YYYY-MM"
                    value-format="YYYY-MM" placeholder="请选择月份" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="个税类型:" prop="taxType">
                <el-select class="width220" v-model="obj.queryParams.taxType" placeholder="请选择" clearable>
                    <el-option v-for="item in taxTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="新增申请人:" prop="addApplicant">
                <el-select class="width220" v-model="obj.queryParams.addApplicant" placeholder="请选择" clearable>
                    <el-option v-for="item in addApplicantOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="collection-of-tax-declaration-information" />
            <el-button type="primary" plain icon="Printer" @click="handlePrint">打印</el-button>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column v-if="obj.selectedColumns.includes('companyName')" label="申报个税公司名称" align="center"
                prop="companyName" min-width="150" />
            <el-table-column v-if="obj.selectedColumns.includes('withholdingType')" label="扣缴义务人类型" align="center"
                width="120">
                <template #default="scope">
                    {{ getWithholdingTypeName(scope.row.withholdingType) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('employeeId')" label="工号" align="center"
                prop="employeeId" width="100" />
            <el-table-column v-if="obj.selectedColumns.includes('employeeName')" label="姓名" align="center"
                prop="employeeName" width="100" />
            <el-table-column v-if="obj.selectedColumns.includes('idNumber')" label="证件号码" align="center" prop="idNumber"
                width="180" />
            <el-table-column v-if="obj.selectedColumns.includes('idType')" label="证件类型" align="center" width="150">
                <template #default="scope">
                    {{ getIdTypeName(scope.row.idType) }}
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('customerName')" label="客户名称" align="center"
                prop="customerName" min-width="150" />
            <el-table-column v-if="obj.selectedColumns.includes('taxMonth')" label="工资计税月" align="center"
                prop="taxMonth" width="100" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="CollectionOfTaxDeclarationInformation">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 扣缴义务人类型选项
const withholdingTypeOptions = [
    { value: '1', label: '企业' },
    { value: '2', label: '个体工商户' },
    { value: '3', label: '事业单位' },
    { value: '4', label: '社会团体' },
    { value: '5', label: '其他' }
];

// 证件类型选项
const idTypeOptions = [
    { value: '1', label: '居民身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '港澳居民来往内地通行证' },
    { value: '4', label: '台湾同胞来往内地通行证' },
    { value: '5', label: '外国人永久居留证' },
    { value: '6', label: '其他' }
];

// 个税类型选项
const taxTypeOptions = [
    { value: '1', label: '工资薪金所得' },
    { value: '2', label: '年终奖金' },
    { value: '3', label: '劳务报酒' },
    { value: '4', label: '税前工资薪金' },
    { value: '5', label: '其他' }
];

// 个税申报信息采集数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: '', // 申报个税公司名称
        withholdingType: '', // 扣缴义务人类型
        taxMonth: '', // 工资计税月
        customerName: '', // 客户名称
        taxType: '', // 个税类型
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            companyName: '北京科技有限公司',
            withholdingType: '1',
            employeeId: 'EMP001',
            employeeName: '张三',
            idNumber: '110101199001011234',
            idType: '1',
            customerName: '北京科技有限公司',
            taxMonth: '2023-09',
        },
        {
            id: 2,
            companyName: '上海贸易有限公司',
            withholdingType: '1',
            employeeId: 'EMP002',
            employeeName: '李四',
            idNumber: '310101199002022345',
            idType: '1',
            customerName: '上海贸易有限公司',
            taxMonth: '2023-09',
        },
    ],
    ids: [], // 选中的id

    selectedColumns: ['companyName', 'withholdingType', 'employeeId', 'employeeName', 'idNumber', 'idType', 'customerName',
        'taxMonth'],
    columnOptions: [
        { label: '申报个税公司名称', prop: 'companyName' },
        { label: '扣缴义务人类型', prop: 'withholdingType' },
        { label: '工号', prop: 'employeeId' },
        { label: '姓名', prop: 'employeeName' },
        { label: '证件号码', prop: 'idNumber' },
        { label: '证件类型', prop: 'idType' },
        { label: '客户名称', prop: 'customerName' },
        { label: '工资计税月', prop: 'taxMonth' },
    ],
});

/**
 * 获取扣缴义务人类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getWithholdingTypeName(typeId) {
    if (!typeId) return '-';

    const type = withholdingTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}

/**
 * 获取证件类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getIdTypeName(typeId) {
    if (!typeId) return '-';

    const type = idTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}


/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.companyName) {
            filteredData = filteredData.filter(item =>
                item.companyName.includes(obj.queryParams.companyName)
            );
        }

        if (obj.queryParams.withholdingType) {
            filteredData = filteredData.filter(item =>
                item.withholdingType === obj.queryParams.withholdingType
            );
        }

        if (obj.queryParams.taxMonth) {
            filteredData = filteredData.filter(item =>
                item.taxMonth === obj.queryParams.taxMonth
            );
        }

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item =>
                item.customerName.includes(obj.queryParams.customerName)
            );
        }

        if (obj.queryParams.taxType) {
            filteredData = filteredData.filter(item =>
                item.taxType === obj.queryParams.taxType
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 导出数据 */
function handleExport() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgInfo('请选择要导出的数据');
        return;
    }

    proxy.$modal.msgSuccess('数据导出成功');
}

/** 打印数据 */
function handlePrint() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgInfo('请选择要打印的数据');
        return;
    }

    proxy.$modal.msgSuccess('数据打印成功');
}

// 初始化加载数据
onMounted(() => {
    getList();
});
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>