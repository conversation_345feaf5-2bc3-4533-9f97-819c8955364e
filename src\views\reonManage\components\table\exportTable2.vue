<template>
    <!-- 表格 -->
    <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange"
        @row-dblclick="handleRowDblClick">
        <el-table-column type="selection" align="center" />
        <el-table-column label="导入编号" align="center" prop="importNo" />
        <el-table-column label="导入人" align="center" prop="creator" />
        <el-table-column label="导入时间" align="center" prop="createTime" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="成功记录数" align="center" prop="successCount" />
        <el-table-column label="失败记录数" align="center" prop="failCount" />
        <el-table-column label="提醒记录数" v-if="props.menuName === 'batchSalaryIntroduction'" align="center"
            prop="remindCount" />
        <el-table-column label="导入文件" align="center" width="240">
            <template #default="scope">
                <el-link type="primary" @click="handleDownloadFile(scope.row)">
                    {{ scope.row.importFile }}
                </el-link>
            </template>
        </el-table-column>
        <el-table-column label="处理状态" align="center" prop="processStatus">
            <template #default="scope">
                <el-tag
                    :type="scope.row.processStatus === '1' ? 'success' : (scope.row.processStatus === '2' ? 'danger' : 'warning')">
                    {{ scope.row.processStatus === '1' ? '处理完成' : (scope.row.processStatus === '2' ? '处理失败' : '处理中') }}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="creator" />
        <el-table-column label="创建时间" align="center" prop="createTime" />
        <el-table-column label="修改人" align="center" prop="updateUser" />
        <el-table-column label="修改时间" align="center" prop="updateTime" />
        <el-table-column :label="props.menuName === 'customerIntroduction' ? '增员政策提醒及失败原因' : '历史信息查询'" align="center"
            width="140">
            <template #default="scope">
                <el-button type="primary" text icon="Search" plain @click="handleRowDblClick(scope.row)">查看</el-button>
            </template>
        </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination v-show="props.total > 0" :total="props.total" v-model:page="currentPage" v-model:limit="pageSize"
        @pagination="onPaginationChange" />
</template>

<script setup>

const props = defineProps({
    // 表格数据
    tableData: {
        type: Array,
        required: true,
        default: () => []
    },
    // 加载状态
    loading: {
        type: Boolean,
        default: false
    },
    // 总数
    total: {
        type: Number,
        default: 0
    },
    // 当前页
    page: {
        type: Number,
        default: 1
    },
    // 每页数量
    limit: {
        type: Number,
        default: 10
    },
    // 菜单名称
    menuName: {
        type: String,
        default: ''
    }
});

const emit = defineEmits([
    'selection-change',
    'row-dblclick',
    'handlePagination'
]);

// 内部状态变量
const currentPage = ref(props.page);
const pageSize = ref(props.limit);
const tableData = ref(props.tableData);
const loading = ref(props.loading);

// 监听props变化，更新内部状态
watch(() => props.tableData, (newValue) => {
    tableData.value = newValue;
});
watch(() => props.loading, (newValue) => {
    loading.value = newValue;
});
watch(() => props.page, (newValue) => {
    currentPage.value = newValue;
});

watch(() => props.limit, (newValue) => {
    pageSize.value = newValue;
});

// 处理行双击
function handleRowDblClick(row, column, event) {
    emit('row-dblclick', row, column, event);
}

// 处理分页变化
function onPaginationChange(pagination) {
    // 更新内部状态
    currentPage.value = pagination.page;
    pageSize.value = pagination.limit;

    // 向父组件发送事件
    emit('handlePagination', {
        page: pagination.page,
        limit: pagination.limit
    });
}

// 文件下载处理
function handleDownloadFile(row) {
    window.open(row.importFile, '_blank');
}
</script>

<style lang="scss" scoped>
.el-link.is-underline::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    bottom: 0;
    border-bottom: 1px solid #1890ff;
}
</style>