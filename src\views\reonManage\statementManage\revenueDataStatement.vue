<!-- 收入数据报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="报表年月:" prop="reportMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.reportMonth" type="month" placeholder="请选择年月"
                    clearable />
            </el-form-item>
            <el-form-item label="所属公司:" prop="company">
                <el-select class="width220" v-model="obj.queryParams.company" placeholder="请选择公司" clearable>
                    <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否打印供应商详细数据:" prop="printSupplierDetail">
                <el-select class="width220" v-model="obj.queryParams.printSupplierDetail" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row>
                <el-form-item>
                    <el-button icon="Refresh" @click="handleReset">重置</el-button>
                    <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
                    <el-button type="primary" icon="Download" @click="handleExport">数据导出</el-button>
                </el-form-item>
            </el-row>
        </el-form>
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" border style="width: 100%">
            <el-table-column prop="company" label="所属公司" align="center" />
            <el-table-column prop="billId" label="账单ID" align="center" />
            <el-table-column prop="customer" label="客户" align="center" />
            <el-table-column prop="paymentDate" label="到款日期" align="center" />
            <el-table-column prop="paymentAmount" label="到账金额" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.paymentAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="financialSummary" label="财务摘要" align="center" />
            <el-table-column prop="billAmount" label="账单金额" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.billAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="verificationAmount" label="核销金额" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.verificationAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="taxIncludedServiceFee" label="含税服务费" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.taxIncludedServiceFee) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="productType" label="产品类型" align="center" />
            <el-table-column prop="productPlan" label="产品方案" align="center" />
            <el-table-column prop="orderType" label="本地单/全国单" align="center" />
            <el-table-column prop="isSingleAccount" label="是否单立户" align="center">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isSingleAccount" />
                </template>
            </el-table-column>
            <el-table-column prop="executionPeriod" label="执行期" align="center" />
            <el-table-column prop="contractNumber" label="合同编号" align="center" />
            <el-table-column prop="contractPrice" label="合同报价(单价)" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.contractPrice) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="serviceCount" label="服务人次" align="center">
                <template #default="scope">
                    <span>{{ scope.row.serviceCount }} 人次</span>
                </template>
            </el-table-column>
            <el-table-column prop="supplierServiceCount" label="供应商服务人次" align="center">
                <template #default="scope">
                    <span>{{ scope.row.supplierServiceCount }} 人次</span>
                </template>
            </el-table-column>
            <el-table-column prop="supplierCost" label="供应商成本" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.supplierCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="channelCost" label="渠道成本" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.channelCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="serviceCost" label="服务成本" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.serviceCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="customerServiceCost" label="客服成本" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.customerServiceCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="taxCost" label="税点成本" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.taxCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="otherCost" label="其他" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.otherCost) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="customerService" label="客服" align="center" />
            <el-table-column prop="sales" label="销售" align="center" />
        </el-table>

        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="RevenueDataStatement">


import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 公司选项
const companyOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportMonth: null,
        company: null,
        printSupplierDetail: null
    }, // 查询表单
    tableData: [] // 表格数据
})



/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                company: '公司A',
                billId: 'ZD20230001',
                customer: '客户A',
                paymentDate: '2023-05-10',
                paymentAmount: 50000.00,
                financialSummary: '服务费收入',
                billAmount: 50000.00,
                verificationAmount: 50000.00,
                taxIncludedServiceFee: 5000.00,
                productType: '社保',
                productPlan: '基础方案',
                orderType: '本地单',
                isSingleAccount: 'Y',
                executionPeriod: '2023-05 ~ 2023-12',
                contractNumber: 'HT20230001',
                contractPrice: 500.00,
                serviceCount: 100,
                supplierServiceCount: 80,
                supplierCost: 30000.00,
                channelCost: 2000.00,
                serviceCost: 5000.00,
                customerServiceCost: 3000.00,
                taxCost: 2000.00,
                otherCost: 1000.00,
                customerService: '客服1',
                sales: '销售1'
            },
            {
                id: 2,
                company: '公司B',
                billId: 'ZD20230002',
                customer: '客户B',
                paymentDate: '2023-06-15',
                paymentAmount: 75000.00,
                financialSummary: '服务费收入',
                billAmount: 75000.00,
                verificationAmount: 75000.00,
                taxIncludedServiceFee: 7500.00,
                productType: '商保',
                productPlan: '高级方案',
                orderType: '全国单',
                isSingleAccount: 'N',
                executionPeriod: '2023-06 ~ 2024-01',
                contractNumber: 'HT20230002',
                contractPrice: 750.00,
                serviceCount: 100,
                supplierServiceCount: 90,
                supplierCost: 45000.00,
                channelCost: 3000.00,
                serviceCost: 7500.00,
                customerServiceCost: 4500.00,
                taxCost: 3000.00,
                otherCost: 1500.00,
                customerService: '客服2',
                sales: '销售2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {

    });
}

/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 初始化数据
getList();
</script>

<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>