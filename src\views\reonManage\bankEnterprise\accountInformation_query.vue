<!-- 查询账户信息 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form @submit.prevent :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="账号:" prop="accountNo">
                <el-input class="width220" v-model="obj.queryParams.accountNo" placeholder="请输入账号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="币种" align="center" prop="currency" />
            <el-table-column label="科目" align="center" prop="subject" />
            <el-table-column label="分行号" align="center" prop="branchNo" />
            <el-table-column label="帐号" align="center" prop="accountNo" />
            <el-table-column label="注解" align="center" prop="remark" />
            <el-table-column label="上日余额" align="center" prop="previousDayBalance" />
            <el-table-column label="联机余额" align="center" prop="onlineBalance" />
            <el-table-column label="冻结余额" align="center" prop="frozenBalance" />
            <el-table-column label="可用余额" align="center" prop="availableBalance" />
            <el-table-column label="透支额度" align="center" prop="overdraftLimit" />
            <el-table-column label="状态" align="center" prop="status" />
            <el-table-column label="利息码" align="center" prop="interestCode" />
            <el-table-column label="年利率" align="center" prop="annualInterestRate" />
            <el-table-column label="开户日" align="center" prop="openDate" />
            <el-table-column label="到期日" align="center" prop="maturityDate" />
            <el-table-column label="利率类型" align="center" prop="interestRateType" />
            <el-table-column label="存期" align="center" prop="depositTerm" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="AccountInformation_query">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountNo: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                currency: 'CNY',
                subject: '1001',
                branchNo: '0001',
                accountNo: '****************',
                remark: '基本户',
                previousDayBalance: 100000.00,
                onlineBalance: 98500.00,
                frozenBalance: 0.00,
                availableBalance: 98500.00,
                overdraftLimit: 0.00,
                status: '正常',
                interestCode: 'A001',
                annualInterestRate: '0.35%',
                openDate: '2020-01-15',
                maturityDate: '',
                interestRateType: '活期',
                depositTerm: '活期'
            },
            {
                id: 2,
                currency: 'CNY',
                subject: '1002',
                branchNo: '0001',
                accountNo: '****************',
                remark: '定期户',
                previousDayBalance: 200000.00,
                onlineBalance: 200000.00,
                frozenBalance: 0.00,
                availableBalance: 200000.00,
                overdraftLimit: 0.00,
                status: '正常',
                interestCode: 'B001',
                annualInterestRate: '2.10%',
                openDate: '2022-05-20',
                maturityDate: '2023-05-20',
                interestRateType: '定期',
                depositTerm: '1年'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;

    }, 300);
}





/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}


getList();
</script>
<style lang="scss" scoped></style>