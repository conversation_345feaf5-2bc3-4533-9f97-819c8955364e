import defaultSettings from "@/settings";
import { useDynamicTitle } from "@/utils/dynamicTitle";

const {
  sideTheme,
  showSettings,
  topNav,
  tagsView,
  fixedHeader,
  sidebarLogo,
  dynamicTitle,
} = defaultSettings;

const storageSetting = JSON.parse(localStorage.getItem("layout-setting")) || "";

const useSettingsStore = defineStore("settings", {
  state: () => ({
    title: "",
    theme: storageSetting.theme || "#409EFF",
    sideTheme: storageSetting.sideTheme || sideTheme,
    showSettings: showSettings,
    topNav:
      storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
    tagsView:
      storageSetting.tagsView === undefined
        ? tagsView
        : storageSetting.tagsView,
    fixedHeader:
      storageSetting.fixedHeader === undefined
        ? fixedHeader
        : storageSetting.fixedHeader,
    sidebarLogo:
      storageSetting.sidebarLogo === undefined
        ? sidebarLogo
        : storageSetting.sidebarLogo,
    dynamicTitle:
      storageSetting.dynamicTitle === undefined
        ? dynamicTitle
        : storageSetting.dynamicTitle,
  }),
  actions: {
    /**
     * 修改布局设置
     * 该方法用于更新当前对象的属性，以修改布局的设置
     * @param {Object} data - 包含要修改的设置键和值的对象
     * @param {string} data.key - 要修改的设置属性的键
     * @param {any} data.value - 要修改的设置属性的新值
     */
    changeSetting(data) {
      // 解构data对象，获取key和value属性
      const { key, value } = data;
      // 检查当前对象是否具有指定的key属性
      if (this.hasOwnProperty(key)) {
        // 如果属性存在，则更新该属性的值
        this[key] = value;
      }
    },
    /**
     * 设置网页标题
     * 该方法用于动态更新网页的标题
     * @param {string} title - 要设置的网页标题
     */
    setTitle(title) {
      // 更新当前实例的title属性为传入的title值
      this.title = title;
      // 调用动态标题功能，具体实现未在代码中给出
      useDynamicTitle();
    },
  },
});

export default useSettingsStore;
