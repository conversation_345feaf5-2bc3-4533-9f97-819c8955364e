<!-- 公司相关信息 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="公司名称:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入"
                    @focus="handleCompany" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="Download" :disabled="obj.single" @click="handleDownload">下载</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" type="index" width="80" align="center" />
            <el-table-column label="公司名称" align="center" prop="companyName" />
            <el-table-column label="公司电话" align="center" prop="companyPhone" />
            <el-table-column label="公司地址" align="center" prop="companyAddress" />
            <el-table-column label="纳税人识别号" align="center" prop="taxpayerId" />
            <el-table-column label="邮编" align="center" prop="postCode" />
            <el-table-column label="传真" align="center" prop="fax" />
            <el-table-column label="法人" align="center" prop="" />
            <el-table-column label="公司邮箱" align="center" prop="companyEmail" />
            <el-table-column label="公司客服" align="center" width="180">
                <template #default="scope">
                    <el-button type="primary" link icon="View" @click="handleView(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 查看 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="40%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <el-form-item label="员工姓名" prop="employeeName">
                    <el-input class="width220" v-model="obj.dialogForm.employeeName" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleEmployeeQuery">查询</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="obj.employeeData" border>
                <el-table-column label="姓名" align="center" prop="employeeName" />
                <el-table-column label="电话" align="center" prop="employeePhone" />
                <el-table-column label="邮箱" align="center" prop="employeeEmail" />
                <el-table-column label="所属公司" align="center" prop="companyName" />
            </el-table>
        </el-dialog>
        <Company :show="companyShow" @select="handleSelect" @close="handleClose" />
    </div>
</template>

<script setup name="CompanyRelatedInformation">
import Company from '@/views/reonManage/components/company.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
        companyPhone: null,
        companyAddress: null
    },//查询表单
    total: 0,//总条数
    tableData: [
        {
            id: 1,
            companyName: '北京科技有限公司',
            companyPhone: '010-12345678',
            companyAddress: '北京市海淀区中关村软件园',
        },
        {
            id: 2,
            companyName: '上海信息技术有限公司',
            companyPhone: '021-87654321',
            companyAddress: '上海市浦东新区张江高科技园区'
        },
    ],//列表
    dialogForm: {},//表单
    dialogShow: false,//查看弹窗
    title: '',//弹窗标题
    ids: [],//选中id

    // 员工数据
    employeeData: [
        {
            id: 1,
            employeeName: '张三',
            employeePhone: '13800138001',
            employeeEmail: '<EMAIL>',
            companyName: '北京科技有限公司',
        },
        {
            id: 2,
            employeeName: '李四',
            employeePhone: '13800138002',
            employeeEmail: '<EMAIL>',
            companyName: '北京科技有限公司'
        }
    ]
})

// 公司选择显示
const companyShow = ref(false);


// 表单校验规则
const rules = {
    employeeName: [
        { required: false, message: '请输入员工姓名', trigger: 'blur' }
    ]
};

/** 公司选择 */
function handleCompany() {
    companyShow.value = true;
}

/** 选择公司 */
function handleSelect(row) {
    if (row) {
        obj.queryParams.companyName = row.companyName;
    } else {
        obj.queryParams.companyName = null;
    }
    companyShow.value = false;
}

/** 关闭公司选择对话框 */
function handleClose() {
    companyShow.value = false;
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // listScale(obj.queryParams).then(response => {
    //     obj.tableData = response.rows;
    //     obj.total = response.total;
    //     obj.loading = false;
    // });
    obj.loading = false;
    obj.total = obj.tableData.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 员工搜索按钮操作 */
function handleEmployeeQuery() {
    // 实际项目中应该调用API获取员工数据
    proxy.$modal.msgSuccess("查询成功");
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 查看客服 */
function handleView(row) {
    obj.dialogShow = true;
    obj.title = "查看公司客服";
    obj.dialogForm.companyId = row.id;
    // 实际项目中应该调用API获取员工数据
}

/** 下载模板 */
function handleDownload() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 导出 */
function handleExport() {
    console.log('导出');
}

getList();
</script>
<style lang="scss" scoped></style>