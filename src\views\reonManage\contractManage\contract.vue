<!-- 合同管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="140px">
            <el-form-item label="合同编号:" prop="contractCode">
                <el-input class="width220" v-model="obj.queryParams.contractCode" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="销售:" prop="salesPerson">
                <el-select class="width220" v-model="obj.queryParams.salesPerson" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <div v-show="obj.showMore">
                <el-form-item label="销售所属公司:" prop="salesCompany">
                    <el-input class="width220" v-model="obj.queryParams.salesCompany" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="人员分布:" prop="staffDistribution">
                    <el-input class="width220" v-model="obj.queryParams.staffDistribution" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="合同类型:" prop="contractType">
                    <el-input class="width220" v-model="obj.queryParams.contractType" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="合同状态:" prop="contractStatus">
                    <el-select class="width220" v-model="obj.queryParams.contractStatus" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="负责客服:" prop="serviceManager">
                    <el-input class="width220" v-model="obj.queryParams.serviceManager" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="审批状态:" prop="approvalStatus">
                    <el-input class="width220" v-model="obj.queryParams.approvalStatus" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="审批通过日期(开始):" prop="approvalDateStart">
                    <el-input class="width220" v-model="obj.queryParams.approvalDateStart" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="审批通过日期(结束):" prop="approvalDateEnd">
                    <el-select class="width220" v-model="obj.queryParams.approvalDateEnd" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="大集团:" prop="groupName">
                    <el-input class="width220" v-model="obj.queryParams.groupName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="终止服务日期(开始):" prop="terminationDateStart">
                    <el-input class="width220" v-model="obj.queryParams.terminationDateStart" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="终止服务日期(结束):" prop="terminationDateEnd">
                    <el-input class="width220" v-model="obj.queryParams.terminationDateEnd" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="是否上传会议纪要:" prop="hasUploadMeetingMinutes">
                    <el-select class="width220" v-model="obj.queryParams.hasUploadMeetingMinutes" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同起始日>=:" prop="contractStartDateMin">
                    <el-input class="width220" v-model="obj.queryParams.contractStartDateMin" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="合同起始日<=:" prop="contractStartDateMax">
                    <el-input class="width220" v-model="obj.queryParams.contractStartDateMax" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="销售所属城市:" prop="salesCity">
                    <el-input class="width220" v-model="obj.queryParams.salesCity" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="是否上传会议纪要:" prop="hasUploadMeetingMinutes">
                    <el-select class="width220" v-model="obj.queryParams.hasUploadMeetingMinutes" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </div>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" @click="handleShowMore">显示更多...</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24" class="button-group">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                <el-button type="success" plain :disabled="obj.single" icon="Edit" @click="handleUpdate">修改</el-button>
                <el-button type="danger" plain :disabled="obj.single" icon="Delete" @click="handleDelete">删除</el-button>
                <el-button type="primary" plain :disabled="obj.single" @click="handleComplement">补充/关联协议审批</el-button>
                <el-button type="success" plain :disabled="obj.single" icon="View" @click="handleView">查看</el-button>
                <el-button type="primary" plain :disabled="obj.single" @click="handleProjectRecord">项目启动会议记录</el-button>
                <el-button type="primary" plain :disabled="obj.single" @click="handleCustomerInfo">维护客户信息</el-button>
                <el-button type="danger" plain :disabled="obj.single" @click="handleTerminate">终止</el-button>
                <el-button type="success" plain :disabled="obj.single" @click="handleRenewal">续签</el-button>
                <el-button type="primary" plain :disabled="obj.single" @click="handleAutoExtension">自动延顺</el-button>
                <el-button type="success" plain icon="Download" @click="handleExport">导出</el-button>
                <el-button type="primary" plain :disabled="obj.single" @click="handleTransfer">转出</el-button>
                <el-button type="primary" plain :disabled="obj.single" @click="handleBindQuote">绑定报价单</el-button>
                <el-button type="primary" plain :disabled="obj.single"
                    @click="handleUpdateFinalVersion">更新最终版合同</el-button>
                <el-button type="success" plain :disabled="obj.single" icon="Edit"
                    @click="handleUpdateProjectRemark">修改项目客服备注</el-button>
                <el-button type="primary" plain :disabled="obj.single" icon="Document"
                    @click="handleGenerateBlankStandardContract">生成空白标准合同</el-button>
                <el-button type="success" plain :disabled="obj.single" icon="Edit"
                    @click="handleChangeDefaultQuote">更换默认报价单</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="合同编号" type="index" width="80" align="center" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="流程类型" align="center" prop="processType" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="是否归档" align="center" prop="isArchived">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isArchived" />
                </template>
            </el-table-column>
            <el-table-column label="报价编号" align="center" prop="quoteCode" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="大集团" align="center" prop="groupName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户规模" align="center" prop="customerSize" />
            <el-table-column label="销售所在公司" align="center" width="120" prop="salesCompany" />
            <el-table-column label="派单分公司" align="center" width="120" prop="dispatchCompany" />
            <el-table-column label="签约方抬头" align="center" width="120" prop="signCompany" />
            <el-table-column label="销售" align="center" prop="salesPerson" />
            <el-table-column label="负责客服" align="center" prop="serviceManager" />
            <el-table-column label="薪资客服" align="center" prop="salaryManager" />
            <el-table-column label="是否上传会议纪要" align="center" width="140" prop="hasUploadMeetingMinutes">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.hasUploadMeetingMinutes" />
                </template>
            </el-table-column>
            <el-table-column label="新增/存量标识" align="center" width="120" prop="stockFlag" />
            <el-table-column label="新增/存量标识补充" align="center" width="140" prop="stockFlagSupplement" />
            <el-table-column label="首版账单年月" align="center" width="120" prop="firstBillDate" />
            <el-table-column label="在职人数" align="center" prop="employeeCount" />
            <el-table-column label="日期" align="center" prop="date" />
            <el-table-column label="合同终止日" align="center" width="120" prop="contractEndDate" />
            <el-table-column label="合同终止原因" align="center" width="120" prop="terminationReason" />
            <el-table-column label="合同状态" align="center" prop="contractStatus" />
            <el-table-column label="审批状态" align="center" prop="approvalStatus" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="自动顺延操作日志" align="center" width="160" prop="autoExtensionLog" />
            <el-table-column label="项目客服备注" align="center" width="120" prop="projectServiceRemark" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="65%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-tabs v-model="obj.activeTab" type="border-card">
                    <el-tab-pane label="基本信息" name="1">
                        <el-form-item v-if="!obj.isComplement" label="客户" prop="customerName">
                            <el-select class="width220" v-model="obj.dialogForm.customerName" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="obj.isComplement" label="客户名称" prop="customerName">
                            <el-input class="width220" v-model="obj.dialogForm.customerName" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="合同名称" prop="contractName">
                            <el-input class="width220" v-model="obj.dialogForm.contractName" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="合同类型" prop="contractType">
                            <el-select class="width220" v-model="obj.dialogForm.contractType" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="obj.isComplement" label="关联合同编号" prop="relatedContractCode">
                            <el-input class="width220" v-model="obj.dialogForm.relatedContractCode" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item v-if="!obj.isComplement" label="二级合同分类" prop="contractSubType">
                            <el-select class="width220" v-model="obj.dialogForm.contractSubType" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="到款日" prop="paymentDate">
                            <el-date-picker class="width220" v-model="obj.dialogForm.paymentDate" placeholder="请选择"
                                clearable />
                        </el-form-item>
                        <el-form-item label="合同起始日" prop="contractStartDate">
                            <el-date-picker class="width220" v-model="obj.dialogForm.contractStartDate"
                                placeholder="请选择" clearable />
                        </el-form-item>
                        <el-form-item label="合同终止日" prop="contractEndDate">
                            <el-date-picker class="width220" v-model="obj.dialogForm.contractEndDate" placeholder="请选择"
                                clearable />
                        </el-form-item>
                        <el-form-item label="报价单编号" prop="quoteCode">
                            <el-input class="width220" v-model="obj.dialogForm.quoteCode" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="销售所在公司" prop="salesCompany">
                            <el-select class="width220" v-model="obj.dialogForm.salesCompany" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="签单地" prop="signLocation">
                            <el-input class="width220" v-model="obj.dialogForm.signLocation" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="派单分公司" prop="dispatchCompany">
                            <el-select class="width220" v-model="obj.dialogForm.dispatchCompany" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="派单地" prop="dispatchLocation">
                            <el-input class="width220" v-model="obj.dialogForm.dispatchLocation" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="责任客服" prop="serviceManager">
                            <el-select class="width220" v-model="obj.dialogForm.serviceManager" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="签约方抬头" prop="signCompany">
                            <el-select class="width220" v-model="obj.dialogForm.signCompany" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="!obj.isComplement" label="合同标准类型" prop="contractStandardType">
                            <el-select class="width220" v-model="obj.dialogForm.contractStandardType" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账单日" prop="billDay">
                            <el-date-picker class="width220" v-model="obj.dialogForm.billDay" placeholder="请选择"
                                clearable />
                        </el-form-item>
                        <el-form-item label="工资发放日" prop="salaryDay">
                            <el-date-picker class="width220" v-model="obj.dialogForm.salaryDay" placeholder="请选择"
                                clearable />
                        </el-form-item>
                        <el-form-item label="工资到款日" prop="salaryPaymentDay">
                            <el-input class="width220" v-model="obj.dialogForm.salaryPaymentDay" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item v-if="!obj.isComplement" label="开具发票方式" prop="invoiceMethod">
                            <el-select class="width220" v-model="obj.dialogForm.invoiceMethod" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="回款频率" prop="paymentFrequency">
                            <el-select class="width220" v-model="obj.dialogForm.paymentFrequency" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="!obj.isComplement" label="增减员截止点" prop="staffChangeDeadline">
                            <el-date-picker class="width220" v-model="obj.dialogForm.staffChangeDeadline"
                                placeholder="请选择" clearable />
                            <div style="font-size: 8px;position: absolute;top: 25px;color: red;">
                                如果是单立户到款日>20日，次月到款不会自动走持殊审批
                            </div>
                        </el-form-item>
                        <el-form-item label="是否自动顺延" prop="isAutoExtension">
                            <el-select class="width220" v-model="obj.dialogForm.isAutoExtension" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="!obj.isComplement" label="是否有特殊审批项" prop="hasSpecialApproval">
                            <el-select class="width220" v-model="obj.dialogForm.hasSpecialApproval" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-row :gutter="10" class="mb8">
                            <el-form-item label="备注" prop="remark">
                                <el-input class="width420" type="textarea" v-model="obj.dialogForm.remark"
                                    placeholder="请输入" />
                            </el-form-item>
                        </el-row>
                        <div v-if="obj.isComplement">
                            <el-divider content-position="left">文件上传</el-divider>
                            <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
                        </div>
                    </el-tab-pane>
                    <el-tab-pane v-if="obj.isDetail" label="会议纪要" name="2">
                        <MeetingMinutes :tableData="obj.tableData" />
                    </el-tab-pane>
                    <el-tab-pane v-if="obj.isDetail" label="特殊审批记录" name="3">
                        <el-divider content-position="left">特殊审批时间线</el-divider>
                    </el-tab-pane>
                    <el-tab-pane v-if="obj.isComplement || obj.isDetail" label="流程信息" name="4">
                        <ProcessInformation :tableData="obj.tableData" />
                    </el-tab-pane>
                </el-tabs>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 项目启动会议记录 -->
        <el-dialog title="项目启动会议记录" v-model="obj.projectRecordDialogShow" width="30%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.projectRecordDialogForm" :rules="obj.rules"
                label-width="auto">
                <el-form-item label="会议时间" prop="meetingTime">
                    <el-date-picker class="width220" v-model="obj.projectRecordDialogForm.meetingTime" placeholder="请选择"
                        clearable />
                </el-form-item>
                <el-form-item label="会议主题" prop="meetingTheme">
                    <el-input class="width220" v-model="obj.projectRecordDialogForm.meetingTheme" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input class="width220" type="textarea" v-model="obj.projectRecordDialogForm.remark"
                        placeholder="请输入" />
                </el-form-item>
                <el-divider content-position="left">文件上传</el-divider>
                <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
            </el-form>
        </el-dialog>

        <!-- 维护客户信息 -->
        <el-dialog title="维护客户信息" v-model="obj.customerInfoDialogShow" width="25%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.customerInfoDialogForm" :rules="obj.rules"
                label-width="auto">
                <el-form-item label="客户名称" prop="customerName">
                    <el-input style="width: 100%;" v-model="obj.customerInfoDialogForm.customerName"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="联系人" prop="contactPerson">
                    <el-input style="width: 100%;" v-model="obj.customerInfoDialogForm.contactPerson"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="联系电话" prop="contactNumber">
                    <el-input style="width: 100%;" v-model="obj.customerInfoDialogForm.contactNumber"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                    <el-input style="width: 100%;" v-model="obj.customerInfoDialogForm.email" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="联系地址" prop="contactAddress">
                    <el-input style="width: 100%;" v-model="obj.customerInfoDialogForm.contactAddress"
                        placeholder="请输入" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 终止 -->
        <el-dialog title="终止原因" v-model="obj.terminateDialogShow" width="30%" append-to-body draggable>
            <el-input type="textarea" :rows="4" v-model="obj.terminateDialogForm.reason" placeholder="请输入" />
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 续签 -->
        <el-dialog title="编辑续签" v-model="obj.renewDialogShow" width="40%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.renewDialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="合同起始日期" prop="contractStartDate">
                    <el-date-picker class="width220" v-model="obj.renewDialogForm.contractStartDate" placeholder="请选择"
                        clearable />
                </el-form-item>
                <el-form-item label="合同截止日期" prop="contractEndDate">
                    <el-date-picker class="width220" v-model="obj.renewDialogForm.contractEndDate" placeholder="请选择"
                        clearable />
                </el-form-item>
                <el-form-item label="报价单编号" prop="quoteNumber">
                    <el-input class="width220" v-model="obj.renewDialogForm.quoteNumber" placeholder="请输入" />
                </el-form-item>
                <el-divider content-position="left">文件上传</el-divider>
                <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 转出 -->
        <el-dialog title="销售专员转出" v-model="obj.transferDialogShow" width="25%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.transferDialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="销售专员" prop="salesSpecialistId">
                    <el-select class="width220" v-model="obj.transferDialogForm.salesSpecialistId" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="销售服务起始月" prop="serviceStartMonth">
                    <el-date-picker class="width220" v-model="obj.transferDialogForm.serviceStartMonth"
                        placeholder="请选择" clearable />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 绑定项目 -->
        <el-dialog title="绑定" v-model="obj.bindProjectDialogShow" width="40%" append-to-body draggable>
            <el-button class="mb10" type="primary" @click="obj.bindProjectDialogShow = true">绑定</el-button>
            <el-table :data="obj.bindProjectDialogTableData" style="width: 100%">
                <el-table-column label="报价编号" prop="quoteNumber" />
                <el-table-column label="报价单名称" prop="quoteName" />
                <el-table-column label="客户名称" prop="customerName" />
                <el-table-column label="报价单类型" prop="quoteType" />
                <el-table-column label="产品类型" prop="productType" />
                <el-table-column label="区域" prop="region" />
                <el-table-column label="销售" prop="salesSpecialist" />
                <el-table-column label="日期" prop="date" />
            </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 更新最终版合同 -->
        <el-dialog title="上传最终版合同" v-model="obj.updateFinalContractDialogShow" width="40%" append-to-body draggable>
            <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 修改存量 -->
        <el-dialog title="修改存量" v-model="obj.modifyStockDialogShow" width="40%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.modifyStockDialogForm" :rules="obj.rules"
                label-width="auto">
                <el-form-item label="存量标志" prop="stockFlag">
                    <el-select class="width220" v-model="obj.modifyStockDialogForm.stockFlag" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="存量备注" prop="stockRemark">
                    <el-input type="textarea" v-model="obj.modifyStockDialogForm.stockRemark" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" v-model="obj.modifyStockDialogForm.remark" placeholder="请输入" />
                </el-form-item>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button type="primary" @click="submitForm">确 定</el-button>
                        <el-button @click="obj.dialogShow = false">取 消</el-button>
                    </div>
                </template>
            </el-form>
        </el-dialog>

        <!-- 修改项目客服备注 -->
        <el-dialog title="项目客服备注" v-model="obj.projectRemarkDialogShow" width="30%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.projectRemarkDialogForm" :rules="obj.rules"
                label-width="auto">
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" :rows="4" v-model="obj.projectRemarkDialogForm.remark"
                        placeholder="请输入" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 生成空白标准合同 -->
        <el-dialog title="下载空白标准合同" v-model="obj.blankContractDialogShow" width="55%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.blankContractDialogForm" inline :rules="obj.rules"
                label-width="auto">
                <el-form-item label="合同类型" prop="contractType">
                    <el-radio-group v-model="obj.blankContractDialogForm.contractType">
                        <el-radio value="1">单项人事代理</el-radio>
                        <el-radio value="2">派遣</el-radio>
                        <el-radio value="3">外包1</el-radio>
                        <el-radio value="4">外包2</el-radio>
                        <el-radio value="5">代发工资</el-radio>
                        <el-radio value="6">全代理</el-radio>
                        <el-radio value="7">全代理单立户</el-radio>
                        <el-radio value="8">同行单项人事代理</el-radio>
                        <el-radio value="9">单项人事代理(单立户)</el-radio>
                        <el-radio value="10">人事代理合同(个体)</el-radio>
                        <el-radio value="11">雇主责任险</el-radio>
                        <el-radio value="12">补充医疗保险</el-radio>
                        <el-radio value="13">意外保险</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-row>
                    <el-form-item label="是否5人以下取消季度单删除合同条款" prop="cancelQuarterlyClause">
                        <el-select class="width220" v-model="obj.blankContractDialogForm.cancelQuarterlyClause"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否删除自动延期条款" prop="removeAutoExtensionClause">
                        <el-select class="width220" v-model="obj.blankContractDialogForm.removeAutoExtensionClause"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所在地法院诉讼" prop="courtLocation">
                        <el-select class="width220" v-model="obj.blankContractDialogForm.courtLocation"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否包含招聘-劳动关系" prop="includeRecruitmentLaborRelation">
                        <el-select class="width220"
                            v-model="obj.blankContractDialogForm.includeRecruitmentLaborRelation" placeholder="请选择"
                            clearable>
                            <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                </el-row>
                <el-divider content-position="left">生成合同</el-divider>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 更换默认报价单 -->
        <el-dialog title="更换默认报价单" v-model="obj.changeDefaultQuoteDialogShow" width="25%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.changeDefaultQuoteDialogForm" :rules="obj.rules"
                label-width="auto">
                <el-form-item label="原默认报价单" prop="originalDefaultQuote">
                    <el-input style="width: 100%;" v-model="obj.changeDefaultQuoteDialogForm.originalDefaultQuote"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="新默认报价单" prop="newDefaultQuote">
                    <el-input style="width: 100%;" v-model="obj.changeDefaultQuoteDialogForm.newDefaultQuote"
                        placeholder="请输入" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Contract">
import ProcessInformation from '@/views/reonManage/components/processInformation.vue';
import MeetingMinutes from '@/views/reonManage/components/meetingMinutes.vue';
import { useAreaStore } from '@/store/modules/area'
// import { listScale } from "@/api/reonApi/scale";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractCode: null,
        contractName: null,
        customerName: null,
        salesPerson: null,
        salesCompany: null,
        staffDistribution: null,
        contractType: null,
        contractStatus: null,
        serviceManager: null,
        approvalStatus: null,
        approvalDateStart: null,
        approvalDateEnd: null,
        groupName: null,
        terminationDateStart: null,
        terminationDateEnd: null,
        hasUploadMeetingMinutes: null,
        contractStartDateMin: null,
        contractStartDateMax: null,
        salesCity: null
    },//查询表单
    rules: {
    },
    total: 0,//总条数


    tableData: [
        {
            id: 1,
            contractCode: 'HT20230001',
            contractName: '合同名称1',
            processType: '流程类型1',
            contractType: '合同类型1',
            isArchived: 'Y',
            quoteCode: 'BJ20230001',
            customerCode: 'KH20230001',
            groupName: '大集团1',
            customerName: '客户名称1',
            customerSize: '大型',
            salesCompany: '销售公司1',
            dispatchCompany: '分公司1',
            signCompany: '签约公司1',
            salesPerson: '销售1',
            serviceManager: '客服1',
            salaryManager: '薪资客服1',
            hasUploadMeetingMinutes: 'Y',
            stockFlag: '新增',
            stockFlagSupplement: '补充信息1',
            firstBillDate: '2023-01',
            employeeCount: 100,
            date: '2023-01-01',
            contractEndDate: '2023-12-31',
            terminationReason: '',
            contractStatus: '正常',
            approvalStatus: '已审批',
            remark: '备注信息1',
            autoExtensionLog: '',
            projectServiceRemark: '项目备注1'
        },
        {
            id: 2,
            contractCode: 'HT20230002',
            contractName: '合同名称2',
            processType: '流程类型2',
            contractType: '合同类型2',
            isArchived: 'N',
            quoteCode: 'BJ20230002',
            customerCode: 'KH20230002',
            groupName: '大集团2',
            customerName: '客户名称2',
            customerSize: '中型',
            salesCompany: '销售公司2',
            dispatchCompany: '分公司2',
            signCompany: '签约公司2',
            salesPerson: '销售2',
            serviceManager: '客服2',
            salaryManager: '薪资客服2',
            hasUploadMeetingMinutes: 'N',
            stockFlag: '存量',
            stockFlagSupplement: '补充信息2',
            firstBillDate: '2023-02',
            employeeCount: 50,
            date: '2023-02-01',
            contractEndDate: '2023-12-31',
            terminationReason: '',
            contractStatus: '正常',
            approvalStatus: '已审批',
            remark: '备注信息2',
            autoExtensionLog: '',
            projectServiceRemark: '项目备注2'
        }
    ],//列表
    dialogForm: {}, //表单
    projectRecordDialogForm: {},//项目启动会议记录表单
    projectRemarkDialogForm: {},//项目客服备注表单
    blankContractDialogForm: {},//生成空白标准合同表单
    customerInfoDialogForm: {},//维护客户信息表单
    transferDialogForm: {},//销售专员转出表单
    renewDialogForm: {},//编辑续签表单
    changeDefaultQuoteDialogForm: {},//更换默认报价单表单
    dialogShow: false, //弹出框
    projectRecordDialogShow: false,//项目启动会议记录弹出框
    projectRemarkDialogShow: false,//项目客服备注弹出框
    blankContractDialogShow: false,//生成空白标准合同弹出框
    changeDefaultQuoteDialogShow: false,//更换默认报价单弹出框
    transferDialogShow: false,//销售专员转出弹出框
    customerInfoDialogShow: false,//维护客户信息弹出框
    terminateDialogShow: false,//终止弹出框
    renewDialogShow: false,//续签弹出框
    bindProjectDialogShow: false,//绑定项目弹出框
    updateFinalContractDialogShow: false,//更新最终版合同弹出框
    updateStockDialogShow: false,//修改存量弹出框
    changeDefaultQuoteDialogShow: false,//更换默认报价单弹出框
    ids: [],//选中的id
    title: "",//标题
    showMore: false,//是否显示更多
    activeTab: '1',//当前选中的tab

    isComplement: false,//是否补充
    isDetail: false,//是否详情
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取合同列表
    // listScale(obj.queryParams).then(response => {
    //     obj.tableData = response.rows;
    //     obj.total = response.total;
    //     obj.loading = false;
    // });

    // 模拟数据，实际开发时可以删除
    setTimeout(() => {
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}

// 表单重置
function reset() {
    obj.dialogForm = {};
    obj.activeTab = '1';
    obj.isComplement = false;
    obj.isDetail = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

// 显示更多
function handleShowMore() {
    obj.showMore = !obj.showMore;
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}
/** 查看按钮操作 */
function handleDetail(row) {
    console.log(row);
    obj.dialogShow = true;
    obj.title = "详情";
    obj.isDetail = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || obj.ids
    getUsers(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改";
    });
}

/** 提交按钮 */
function submitForm() {
    console.log(obj.dialogForm);
    return
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

// 补充/关联协议审批
function handleComplement() {
    reset();
    obj.isComplement = true;
    obj.dialogShow = true;
    obj.title = "上传关联协议";
}

// 查看
function handleView() {
    reset();
    obj.isDetail = true;
    obj.dialogShow = true;
}

// 项目启动会议记录
function handleProjectRecord() {
    obj.projectRecordDialogShow = true;
}

// 维护客户信息
function handleCustomerInfo() {
    obj.customerInfoDialogShow = true;
}

// 终止
function handleTerminate() {
    obj.terminateDialogShow = true;
}

// 续签
function handleRenewal() {
    obj.renewDialogShow = true;
}

// 自动延顺
function handleAutoExtension() {
    console.log('自动延顺');
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 转出
function handleTransfer() {
    obj.transferDialogShow = true;
}

// 绑定报价单
function handleBindQuote() {
    obj.bindProjectDialogShow = true;
}

// 更新最终版合同
function handleUpdateFinalVersion() {
    obj.updateFinalContractDialogShow = true;
}

// 修改存量
function handleUpdateStock() {
    obj.updateStockDialogShow = true;
}

// 修改项目客服备注
function handleUpdateProjectRemark() {
    obj.projectRemarkDialogShow = true;
}

// 生成空白标准合同
function handleGenerateBlankStandardContract() {
    obj.blankContractDialogShow = true;
}

// 更换默认报价单
function handleChangeDefaultQuote() {
    obj.changeDefaultQuoteDialogShow = true;
}



getList();
</script>
<style lang="scss" scoped>
.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    /* 这里控制按钮之间的间距 */
}

.button-group .el-button {
    margin: 5px 0;
    /* 这里控制按钮上下的间距 */
    margin-right: 0;
    /* 覆盖Element UI的默认右边距 */
}
</style>