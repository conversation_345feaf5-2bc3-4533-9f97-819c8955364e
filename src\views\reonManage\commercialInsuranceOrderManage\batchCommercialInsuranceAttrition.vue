<!-- 商保批量减员 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateFrom" type="date"
                    placeholder="请选择开始日期" value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateTo" type="date"
                    placeholder="请选择结束日期" value-format="YYYY-MM-DD" clearable />
            </el-form-item>

            <el-form-item label="导入人:" prop="importUser">
                <el-select class="width220" v-model="obj.queryParams.importUser" placeholder="请选择" clearable>
                    <el-option v-for="item in importUserOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入编号:" prop="importId">
                <el-input class="width220" v-model="obj.queryParams.importId" placeholder="请输入导入编号" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Upload" @click="handleImport">数据导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" @click="handleDownloadTemplate">下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <ExportTable2 :tableData="obj.tableData" :loading="obj.loading" :total="obj.total"
            :page="obj.queryParams.pageNum" :limit="obj.queryParams.pageSize"
            menuName="batchCommercialInsuranceAttrition" @row-dblclick="handleRowDblClick"
            @handlePagination="handlePagination" />
        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" title="上传导入数据" :dialogForm="obj.dialogForm"
            :type="'batchCommercialInsuranceAttrition'" :rules="importRules" />
        <!-- 历史信息查看 -->
        <HistoricalInformation :dialogShow="obj.dialogShow2" :title="obj.title" :tableData="obj.tableData"
            @close="handleHistoryClose" />
    </div>
</template>

<script setup name="BatchCommercialInsuranceAttrition">



const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 导入人选项
const importUserOptions = ref([
    { value: '1', label: '管理员' },
    { value: '2', label: '张三' },
    { value: '3', label: '李四' },
    { value: '4', label: '王五' },
    { value: '5', label: '赵六' }
])

// 响应式数据
const obj = reactive({
    showSearch: true, // 显示搜索
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateFrom: '',
        importDateTo: '',
        importUser: '',
        importId: '',
        status: ''
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表

    // 导入对话框
    importDialogShow: false,
    importForm: {
        remark: '',
        fileList: []
    },
    importRules: {
        file: [
            { required: true, message: '请选择要上传的文件', trigger: 'change' }
        ]
    },

    // 详情对话框
    detailDialogShow: false,
    currentImport: null,
    errorData: []
})


/**
 * 获取导入列表
 */
function getList() {
    obj.loading = true;

    // 模拟数据
    setTimeout(() => {
        // 生成模拟数据
        const mockData = [];
        for (let i = 0; i < 10; i++) {
            const status = String((i % 4) + 1);
            const successCount = status === '3' ? 0 : Math.floor(Math.random() * 100);
            const failCount = status === '2' ? 0 : Math.floor(Math.random() * 20);

            mockData.push({
                id: i + 1,
                importId: `IMP${String(1000 + i).padStart(4, '0')}`,
                importUser: importUserOptions.value[i % importUserOptions.value.length].label,
                importTime: `2023-05-${String(10 + i).padStart(2, '0')} 14:30:00`,
                remark: i % 2 === 0 ? `导入备注${i + 1}` : '',
                fileName: `商业保险数据${i + 1}.xlsx`,
                successCount: successCount,
                failCount: failCount,
                totalCount: successCount + failCount,
                status: status,
                creator: importUserOptions.value[i % importUserOptions.value.length].label,
                createTime: `2023-05-${String(10 + i).padStart(2, '0')} 14:30:00`
            });
        }

        // 应用查询条件过滤
        let filteredData = [...mockData];
        const params = obj.queryParams;

        if (params.importId) {
            filteredData = filteredData.filter(item => item.importId.includes(params.importId));
        }

        if (params.importUser) {
            const importUser = importUserOptions.value.find(item => item.value === params.importUser)?.label;
            if (importUser) {
                filteredData = filteredData.filter(item => item.importUser === importUser);
            }
        }

        if (params.status) {
            filteredData = filteredData.filter(item => item.status === params.status);
        }

        if (params.importDateFrom || params.importDateTo) {
            // 实际项目中需要进行日期范围过滤
            // 这里简化处理，不做实际过滤
            // 如果有开始日期，过滤大于等于开始日期的记录
            // 如果有结束日期，过滤小于等于结束日期的记录
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 500);
}



/**
 * 搜索按钮操作
 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/**
 * 重置按钮操作
 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    obj.queryParams = {
        pageNum: 1,
        pageSize: 10,
        importDateFrom: '',
        importDateTo: '',
        importUser: '',
        importId: '',
        status: ''
    };
    handleQuery();
}
// 双击行
function handleRowDblClick(row) {
    obj.dialogShow2 = true;
    obj.title = '历史信息查看';
}

// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}
/**
 * 数据导入
 */
function handleImport() {
    obj.dialogShow = true;
}


/**
 * 下载模板
 */
function handleDownloadTemplate() {
    // 显示加载中提示
    const loading = ElMessage({
        message: '正在准备下载，请稍候...',
        type: 'info',
        duration: 0
    });

    // 模拟下载过程
    setTimeout(() => {
        // 关闭加载中提示
        loading.close();

        // 显示成功提示
        proxy.$modal.msgSuccess('模板下载成功');
    }, 1000);
}
/** 历史信息关闭 */
function handleHistoryClose() {
    obj.dialogShow2 = false;
}
// 初始化加载数据
getList();
</script>
<style lang="scss" scoped></style>