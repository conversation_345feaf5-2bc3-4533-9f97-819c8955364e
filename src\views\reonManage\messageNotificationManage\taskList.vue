<!-- 查看任务列表页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="任务编号:" prop="taskId">
                <el-input class="width220" v-model="obj.queryParams.taskId" placeholder="请输入任务编号" clearable />
            </el-form-item>
            <el-form-item label="任务类型:" prop="taskType">
                <el-select class="width220" v-model="obj.queryParams.taskType" placeholder="请选择" clearable>
                    <el-option v-for="item in taskTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="任务状态:" prop="taskStatus">
                <el-select class="width220" v-model="obj.queryParams.taskStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in taskStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="创建时间起:" prop="dateRangeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.dateRangeStart" type="date" placeholder="请选择"
                    value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="创建时间止:" prop="dateRangeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.dateRangeEnd" type="date" placeholder="请选择"
                    value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-row>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData"
            :row-class-name="tableRowClassName">
            <el-table-column label="消息编号" align="center" prop="taskId" width="180" />
            <el-table-column label="任务类型" align="center" prop="taskType" width="180" />
            <el-table-column label="任务状态" align="center" prop="taskStatus" width="100" />
            <el-table-column label="文件名称" align="center" prop="fileName" min-width="220" />
            <el-table-column label="创建人" align="center" prop="creator" width="120" />
            <el-table-column label="任务开始时间" align="center" prop="startTime" width="160" />
            <el-table-column label="失败原因" align="center" prop="failReason" width="160" />
            <el-table-column label="操作" align="center" width="200">
                <template #default="scope">
                    <el-button type="primary" link icon="Download" @click="handleDownload(scope.row)">下载文件</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');

/** 表格行样式 */
function tableRowClassName({ row, rowIndex }) {
    if (rowIndex % 2 === 1) { // Element Plus 的 rowIndex 从 0 开始，所以偶数行是 index 为 1, 3, 5...
        return 'even-row';
    }
    return '';
}

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    rules: {
        city: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        type: [{ required: true, message: '请选择人员类型', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [
        {
            taskId: 'TN-20250417001440',
            taskType: '任务类型',
            taskStatus: '任务状态',
            fileName: '订单与实做差异报表导出20250415111232.xls',
            creator: '创建人',
            startTime: '任务开始时间',
            failReason: '失败原因',
        },
        {
            taskId: 'TN-20250417001441',
            taskType: '任务类型',
            taskStatus: '任务状态',
            fileName: '订单与实做差异报表导出20250415111232.xls',
            creator: '创建人',
            startTime: '任务开始时间',
            failReason: '失败原因',
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 下载文件 */
function handleDownload(row) {
    proxy.$message.success("下载成功");
}

getList();
</script>
<style lang="scss" scoped>
.el-table :deep(.even-row) {
    background-color: #f5f7fa;
    /* 你可以根据需要调整颜色 */
}
</style>