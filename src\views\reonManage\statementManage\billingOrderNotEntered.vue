<!-- 未进入账单订单查询页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="报表年月:" prop="reportMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.reportMonth" type="month" placeholder="请选择年月"
                    clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="success" icon="ChatSquare" @click="handleImport">导入备注</el-button>
                <el-button type="danger" icon="Delete" @click="handleDelete">删除</el-button>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="订单编号" align="center" type="index" width="80" />
            <el-table-column label="生成月" align="center" prop="generateMonth" />
            <el-table-column label="服务月" align="center" prop="serviceMonth" />
            <el-table-column label="备注" align="center" width="220">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.remark" />
                </template>
            </el-table-column>
            <el-table-column label="未进账单原因" align="center" width="220">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.reason" />
                </template>
            </el-table-column>
            <el-table-column label="员工编号" align="center" prop="employeeCode" />
            <el-table-column label="员工名称" align="center" prop="employeeName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="项目客服" align="center" prop="projectService" />
            <el-table-column label="接单客服" align="center" prop="receiveService" />
            <el-table-column label="入离职状态" align="center" prop="employmentStatus">
                <template #default="scope">
                    <dict-tag :options="employmentStatusOptions" :value="scope.row.employmentStatus" />
                </template>
            </el-table-column>
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 导入备注 -->
        <el-dialog v-model="obj.importDialogVisible" :title="obj.title" width="60%">
            <el-form :model="obj.importParams" ref="importRef" inline label-width="auto">
                <el-row class="mb8">
                    <el-form-item label="导入日期从:" prop="importDateFrom">
                        <el-date-picker class="width220" v-model="obj.importParams.importDateFrom" type="month"
                            placeholder="请选择年月" clearable />
                    </el-form-item>
                    <el-form-item label="导入日期到:" prop="importDateTo">
                        <el-date-picker class="width220" v-model="obj.importParams.importDateTo" type="month"
                            placeholder="请选择年月" clearable />
                    </el-form-item>
                    <el-form-item label="导入编号:" prop="importCode">
                        <el-input class="width220" v-model="obj.importParams.importCode" placeholder="请输入导入编号"
                            clearable />
                    </el-form-item>
                    <el-form-item label="导入人:" prop="importUser">
                        <el-input class="width220" v-model="obj.importParams.importUser" placeholder="请输入导入人"
                            clearable />
                    </el-form-item>
                </el-row>
                <el-row :gutter="10" class="mb8">
                    <el-button type="primary" icon="Search" @click="handleImportQuery">查询</el-button>
                    <el-button icon="Refresh" @click="resetImportQuery">重置</el-button>
                    <el-button type="warning" icon="Upload" @click="handleImportUpload">导入</el-button>
                    <el-button type="success" icon="Download" @click="handleDownload">下载模版</el-button>
                </el-row>
            </el-form>
            <!-- 表格 -->
            <el-table v-loading="obj.importLoading" border :data="obj.importTableData"
                @selection-change="handleImportSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="导入编号" align="center" prop="importCode" />
                <el-table-column label="导入人" align="center" prop="importUser" />
                <el-table-column label="导入时间" align="center" prop="importTime" />
                <el-table-column label="成功记录数" align="center" prop="successCount" />
                <el-table-column label="失败记录数" align="center" prop="failCount" />
                <el-table-column label="导入文件" align="center" prop="importFile">
                    <template #default="scope">
                        <el-button text @click="handleDownloadFile(scope.row)">
                            {{ scope.row.importFile }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column label="处理状态" align="center" prop="processStatus">
                    <template #default="scope">
                        <dict-tag :options="processStatusOptions" :value="scope.row.processStatus" />
                    </template>
                </el-table-column>
                <el-table-column label="历史信息查询" align="center">
                    <template #default="scope">
                        <el-button text @click="handleViewHistory(scope.row)">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination v-show="obj.importTotal > 0" :total="obj.importTotal" v-model:page="obj.importParams.pageNum"
                v-model:limit="obj.importParams.pageSize" @pagination="getImportList" />
        </el-dialog>
    </div>
</template>

<script setup name="BillingOrderNotEntered">

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 字典数据

// 入离职状态选项
const employmentStatusOptions = ref([
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '待入职' },
    { value: '4', label: '未入职' }
]);

// 处理状态选项
const processStatusOptions = ref([
    { value: '0', label: '待处理' },
    { value: '1', label: '处理中' },
    { value: '2', label: '处理完成' },
    { value: '3', label: '处理失败' }
]);

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    ids: [], // 选中的ID数组
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportMonth: null,
        customerName: null,
        employeeName: null,
        idNumber: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    title: '',
    importDialogVisible: false,//导入备注弹窗
    importLoading: false, // 导入列表加载中
    importTableData: [], // 导入列表数据
    importTotal: 0, // 导入列表总条数
    importIds: [], // 导入列表选中的ID数组
    importParams: { // 导入查询参数
        pageNum: 1,
        pageSize: 10,
        reportMonth: null,
        customerName: null,
        employeeName: null,
        idNumber: null
    }
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                generateMonth: '2023-05',
                serviceMonth: '2023-04',
                remark: '测试备注',
                employeeCode: 'YG20230001',
                employeeName: '张三',
                customerCode: 'KH20230001',
                customerName: '客户A',
                projectService: '客服1',
                receiveService: '客服2',
                employmentStatus: '1',
                contractCode: 'HT20230001',
                contractName: '合同1'
            },
            {
                id: 2,
                generateMonth: '2023-06',
                serviceMonth: '2023-05',
                remark: '测试备注2',
                employeeCode: 'YG20230002',
                employeeName: '李四',
                customerCode: 'KH20230002',
                customerName: '客户B',
                projectService: '客服3',
                receiveService: '客服4',
                employmentStatus: '2',
                contractCode: 'HT20230002',
                contractName: '合同2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 获取导入列表数据 */
function getImportList() {
    obj.importLoading = true;
    // 调用API获取导入列表数据
    listScale(obj.importParams).then(response => {
        obj.importTableData = response.rows;
        obj.importTotal = response.total;
        obj.importLoading = false;

        // 模拟数据，实际开发时可以删除
        obj.importTableData = [
            {
                id: 1,
                importCode: 'DR20230001',
                importUser: '管理员',
                importTime: '2023-05-15 10:00:00',
                successCount: 100,
                failCount: 0,
                importFile: '备注导入.xlsx',
                processStatus: '2'
            },
            {
                id: 2,
                importCode: '**********',
                importUser: '管理员',
                importTime: '2023-06-15 10:00:00',
                successCount: 95,
                failCount: 5,
                importFile: '备注导入.xlsx',
                processStatus: '3'
            }
        ];
        obj.importTotal = 2;
        obj.importLoading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 导入备注对话框搜索按钮操作 */
function handleImportQuery() {
    obj.importParams.pageNum = 1;
    getImportList();
}

/** 导入备注对话框重置按钮操作 */
function resetImportQuery() {
    proxy.resetForm("importRef");
    handleImportQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 导入列表多选框选中数据 */
function handleImportSelectionChange(selection) {
    obj.importIds = selection.map(item => item.id);
}

/** 导入备注 */
function handleImport() {
    obj.importDialogVisible = true;
    obj.title = '批量导入';
    getImportList();
}

/** 导入上传 */
function handleImportUpload() {
    proxy.$modal.msgSuccess("文件上传成功");
    getImportList();
}

/** 下载模版 */
function handleDownload() {
    proxy.$modal.msgSuccess("模版下载成功");
}

/** 下载文件 */
function handleDownloadFile(row) {
    proxy.$modal.msgSuccess("文件下载成功：" + row.importFile);
}

/** 查看历史信息 */
function handleViewHistory(row) {
    proxy.$modal.msgInfo("查看历史信息：" + row.importCode);
}

/** 删除 */
function handleDelete(row) {
    const _ids = row.id || obj.ids;
    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 导出 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        // 调用导出接口
        proxy.$modal.msgSuccess('导出成功');
    }).catch(() => { });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>