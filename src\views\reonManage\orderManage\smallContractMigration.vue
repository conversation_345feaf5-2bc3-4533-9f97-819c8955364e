<!-- 小合同迁移 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="分公司:" prop="branchCompany">
                <el-input class="width220" v-model="obj.queryParams.branchCompany" placeholder="请输入分公司" />
            </el-form-item>
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" />
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-input class="width220" v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称" />
            </el-form-item>

            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" @click="handleAdd">分配</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" @click="handleDetail">分配历史日志</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" type="index" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="派单分公司" align="center" prop="branchCompany" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="客服类型" align="center" prop="serviceType" />
            <el-table-column label="接单客服经理" align="center" prop="receiverManager" />
            <el-table-column label="派单客服经理" align="center" prop="senderManager" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" plain @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    // 可以根据需要添加验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        branchCompany: null,
        customerName: null,
        contractName: null,
        smallContractName: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogShow: false,//分配弹窗
    dialogShow2: false,//分配历史日志弹窗
    dialogForm: {},//分配表单
    rules: {},//表单验证规则
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerName: '客户名称1',
                contractName: '合同名称1',
                branchCompany: '北京分公司',
                smallContractName: '小合同名称1',
                serviceType: '客服类型1',
                receiverManager: '接单客服经理1',
                senderManager: '派单客服经理1'
            },
            {
                id: 2,
                customerName: '客户名称2',
                contractName: '合同名称2',
                branchCompany: '上海分公司',
                smallContractName: '小合同名称2',
                serviceType: '客服类型2',
                receiverManager: '接单客服经理2',
                senderManager: '派单客服经理2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 分配按钮操作 */
function handleAdd() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要分配的数据');
        return;
    }
    obj.dialogShow = true;
    obj.title = "小合同分配";
    obj.dialogForm = {
        // 可以根据需要设置表单默认值
    };
}

/** 分配历史日志按钮操作 */
function handleDetail(row) {
    obj.dialogShow2 = true;
    obj.title = "分配历史日志";
    // 模拟数据，实际开发时可以从表格中获取
    if (row) {
        obj.dialogForm = { ...row };
    } else if (obj.ids.length === 1) {
        const selectedRow = obj.tableData.find(item => item.id === obj.ids[0]);
        if (selectedRow) {
            obj.dialogForm = { ...selectedRow };
        }
    }
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>