<!-- 实做办理反馈查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="接单方:" prop="orderReceiver">
                <el-select class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请选择" clearable>
                    <el-option v-for="item in orderReceiverOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单客服:" prop="orderReceiverService">
                <el-input class="width220" v-model="obj.queryParams.orderReceiverService" placeholder="请输入接单客服"
                    clearable />
            </el-form-item>
            <el-form-item label="派单方:" prop="orderDispatcher">
                <el-select class="width220" v-model="obj.queryParams.orderDispatcher" placeholder="请选择" clearable>
                    <el-option v-for="item in orderDispatcherOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="雇员状态:" prop="employeeStatus">
                <el-select class="width220" v-model="obj.queryParams.employeeStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in employeeStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="类型:" prop="type">
                <el-select class="width220" v-model="obj.queryParams.type" placeholder="请选择" clearable>
                    <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入日期从:" prop="importDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateStart" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateEnd" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="险种:" prop="insuranceType">
                <el-select class="width220" v-model="obj.queryParams.insuranceType" placeholder="请选择" clearable>
                    <el-option v-for="item in insuranceTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="申报结果:" prop="declarationResult">
                <el-select class="width220" v-model="obj.queryParams.declarationResult" placeholder="请选择" clearable>
                    <el-option v-for="item in declarationResultOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-row class="mb8 text-danger">
            申报结果选择成功，则展示订单最新的对应成功数据；申报结果选择失败，则展示订单最新的对应失败数据，如申报结果不进行选择，则展示所有的导入数据
        </el-row>
        <el-row class="mb8 text-danger">
            点击对应的数据即可查看失败截图,如果点击后没有反应则代表没有上传失败截图
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column align="center" type="index" />
            <el-table-column label="类型" align="center" prop="type">
                <template #default="scope">
                    {{typeOptions.find(item => item.value === scope.row.type)?.label || '未知类型'}}
                </template>
            </el-table-column>
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="雇员状态" align="center" prop="employeeStatus">
                <template #default="scope">
                    {{employeeStatusOptions.find(item => item.value === scope.row.employeeStatus)?.label || '未知状态'}}
                </template>
            </el-table-column>
            <el-table-column label="报入职时间" align="center" prop="entryReportTime" />
            <el-table-column label="员工姓名" align="center" prop="employeeName" />
            <el-table-column label="身份证号" align="center" prop="idNumber" />
            <el-table-column label="接单分公司" align="center" prop="orderReceiver">
                <template #default="scope">
                    {{orderReceiverOptions.find(item => item.value === scope.row.orderReceiver)?.label || '未知分公司'}}
                </template>
            </el-table-column>
            <el-table-column label="接单客服" align="center" prop="orderReceiverService" />
            <el-table-column label="导入时间" align="center" prop="importTime" />
            <el-table-column label="养老" align="center">
                <el-table-column label="收费起始月" align="center" prop="pension.chargeStartMonth" />
                <el-table-column label="申报结果" align="center" prop="pension.declarationResult">
                    <template #default="scope">
                        <el-tag :type="scope.row.pension?.declarationResult === '1' ? 'success' : 'danger'">
                            {{ scope.row.pension?.declarationResult === '1' ? '成功' : '失败' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="原因" align="center" prop="pension.reason" />
                <el-table-column label="失败截图" align="center">
                    <template #default="scope">
                        <el-button v-if="scope.row.pension?.failureScreenshot" type="primary" link
                            @click="handleViewScreenshot(scope.row, '1')">查看</el-button>
                        <span v-else>无</span>
                    </template>
                </el-table-column>
                <el-table-column label="次月最晚处理时间(当月已截止)" align="center" prop="pension.nextMonthDeadline" width="220" />
            </el-table-column>
            <el-table-column label="医疗" align="center">
                <el-table-column label="收费起始月" align="center" prop="medical.chargeStartMonth" />
                <el-table-column label="申报结果" align="center" prop="medical.declarationResult">
                    <template #default="scope">
                        <el-tag :type="scope.row.medical?.declarationResult === '1' ? 'success' : 'danger'">
                            {{ scope.row.medical?.declarationResult === '1' ? '成功' : '失败' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="原因" align="center" prop="medical.reason" />
                <el-table-column label="失败截图" align="center">
                    <template #default="scope">
                        <el-button v-if="scope.row.medical?.failureScreenshot" type="primary" link
                            @click="handleViewScreenshot(scope.row, '2')">查看</el-button>
                        <span v-else>无</span>
                    </template>
                </el-table-column>
                <el-table-column label="次月最晚处理时间(当月已截止)" align="center" prop="medical.nextMonthDeadline" width="220" />
            </el-table-column>
            <el-table-column label="失业" align="center">
                <el-table-column label="收费起始月" align="center" prop="unemployment.chargeStartMonth" />
                <el-table-column label="申报结果" align="center" prop="unemployment.declarationResult">
                    <template #default="scope">
                        <el-tag :type="scope.row.unemployment?.declarationResult === '1' ? 'success' : 'danger'">
                            {{ scope.row.unemployment?.declarationResult === '1' ? '成功' : '失败' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="原因" align="center" prop="unemployment.reason" />
                <el-table-column label="失败截图" align="center">
                    <template #default="scope">
                        <el-button v-if="scope.row.unemployment?.failureScreenshot" type="primary" link
                            @click="handleViewScreenshot(scope.row, '3')">查看</el-button>
                        <span v-else>无</span>
                    </template>
                </el-table-column>
                <el-table-column label="次月最晚处理时间(当月已截止)" align="center" prop="unemployment.nextMonthDeadline"
                    width="220" />
            </el-table-column>
            <el-table-column label="工伤" align="center">
                <el-table-column label="收费起始月" align="center" prop="workInjury.chargeStartMonth" />
                <el-table-column label="申报结果" align="center" prop="workInjury.declarationResult">
                    <template #default="scope">
                        <el-tag :type="scope.row.workInjury?.declarationResult === '1' ? 'success' : 'danger'">
                            {{ scope.row.workInjury?.declarationResult === '1' ? '成功' : '失败' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="原因" align="center" prop="workInjury.reason" />
                <el-table-column label="失败截图" align="center">
                    <template #default="scope">
                        <el-button v-if="scope.row.workInjury?.failureScreenshot" type="primary" link
                            @click="handleViewScreenshot(scope.row, '4')">查看</el-button>
                        <span v-else>无</span>
                    </template>
                </el-table-column>
                <el-table-column label="次月最晚处理时间(当月已截止)" align="center" prop="workInjury.nextMonthDeadline"
                    width="220" />
            </el-table-column>
            <el-table-column label="大病" align="center">
                <el-table-column label="收费起始月" align="center" prop="seriousIllness.chargeStartMonth" />
                <el-table-column label="申报结果" align="center" prop="seriousIllness.declarationResult">
                    <template #default="scope">
                        <el-tag :type="scope.row.seriousIllness?.declarationResult === '1' ? 'success' : 'danger'">
                            {{ scope.row.seriousIllness?.declarationResult === '1' ? '成功' : '失败' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="原因" align="center" prop="seriousIllness.reason" />
                <el-table-column label="失败截图" align="center">
                    <template #default="scope">
                        <el-button v-if="scope.row.seriousIllness?.failureScreenshot" type="primary" link
                            @click="handleViewScreenshot(scope.row, '6')">查看</el-button>
                        <span v-else>无</span>
                    </template>
                </el-table-column>
                <el-table-column label="次月最晚处理时间(当月已截止)" align="center" prop="seriousIllness.nextMonthDeadline"
                    width="220" />
            </el-table-column>
            <el-table-column label="公积金" align="center">
                <el-table-column label="收费起始月" align="center" prop="housingFund.chargeStartMonth" />
                <el-table-column label="申报结果" align="center" prop="housingFund.declarationResult">
                    <template #default="scope">
                        <el-tag :type="scope.row.housingFund?.declarationResult === '1' ? 'success' : 'danger'">
                            {{ scope.row.housingFund?.declarationResult === '1' ? '成功' : '失败' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="原因" align="center" prop="housingFund.reason" />
                <el-table-column label="失败截图" align="center">
                    <template #default="scope">
                        <el-button v-if="scope.row.housingFund?.failureScreenshot" type="primary" link
                            @click="handleViewScreenshot(scope.row, '10')">查看</el-button>
                        <span v-else>无</span>
                    </template>
                </el-table-column>
                <el-table-column label="次月最晚处理时间(当月已截止)" align="center" prop="housingFund.nextMonthDeadline"
                    width="220" />
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="接单是否外呼" align="center" prop="isOutboundCall" />
            <el-table-column label="社保增员截点" align="center" prop="socialSecurityAddDeadline" />
            <el-table-column label="社保减员截点" align="center" prop="socialSecurityRemoveDeadline" />
            <el-table-column label="公积金增员截点" align="center" prop="housingFundAddDeadline" />
            <el-table-column label="公积金减员截点" align="center" prop="housingFundRemoveDeadline" />
            <el-table-column label="订单编号" align="center" prop="orderNo" />
            <el-table-column label="签约方抬头" align="center" prop="contractHeader" />
            <el-table-column label="性别" align="center" prop="gender" />
            <el-table-column label="年龄" align="center" prop="age" />
            <el-table-column label="退休时间" align="center" prop="retirementTime" />
            <el-table-column label="人员分类" align="center" prop="personnelCategory" />
            <el-table-column label="派单分公司" align="center" prop="orderDispatcher">
                <template #default="scope">
                    {{orderDispatcherOptions.find(item => item.value === scope.row.orderDispatcher)?.label || '未知分公司'
                    }}
                </template>
            </el-table-column>
            <el-table-column label="派单客服" align="center" prop="orderDispatcherService" />
            <el-table-column label="自有公司/供应商公司" align="center" prop="companyType" width="150" />
            <el-table-column label="报离职时间" align="center" prop="leaveReportTime" />
            <el-table-column label="社保工资" align="center" prop="socialSecuritySalary" />
            <el-table-column label="公积金工资" align="center" prop="housingFundSalary" />
            <el-table-column label="户口性质" align="center" prop="householdType" />
            <el-table-column label="社保收费起始月" align="center" prop="socialSecurityChargeStartMonth" />
            <el-table-column label="公积金收费起始月" align="center" prop="housingFundChargeStartMonth" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm"
            :type="'practiceHandlingFeedback_search'" :rules="importRules" />
        <!-- 导入失败截图 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="50%" append-to-body>
            <div v-if="obj.currentScreenshot" class="screenshot-container">
                <img :src="obj.currentScreenshot" alt="失败截图" style="max-width: 100%;" />
            </div>
            <div v-else>
                <el-empty description="没有失败截图" />
            </div>
            <template #footer>
                <el-button @click="obj.dialogShow2 = false">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup name="PracticeHandlingFeedback_query">
import { listScale } from "@/api/reonApi/scale";



const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 接单方选项
const orderReceiverOptions = [
    { value: '1', label: '分公司A' },
    { value: '2', label: '分公司B' },
    { value: '3', label: '分公司C' }
];

// 派单方选项
const orderDispatcherOptions = [
    { value: '1', label: '总公司' },
    { value: '2', label: '分公司A' },
    { value: '3', label: '分公司B' }
];

// 雇员状态选项
const employeeStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '停薪' }
];

// 类型选项
const typeOptions = [
    { value: '1', label: '新增' },
    { value: '2', label: '变更' },
    { value: '3', label: '停办' }
];

// 险种选项
const insuranceTypeOptions = [
    { value: '1', label: '养老' },
    { value: '2', label: '医疗' },
    { value: '3', label: '失业' },
    { value: '4', label: '工伤' },
    { value: '6', label: '大病' },
    { value: '10', label: '公积金' }
];

// 申报结果选项
const declarationResultOptions = [
    { value: '1', label: '成功' },
    { value: '2', label: '失败' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        type: '1',
        customerName: '客户A',
        contractType: '标准合同',
        employeeStatus: '1',
        entryReportTime: '2023-01-01',
        employeeName: '张三',
        idNumber: '110101199001011234',
        orderReceiver: '1',
        orderReceiverService: '王五',
        importTime: '2023-01-15 10:30:00',
        // 养老保险信息
        pension: {
            chargeStartMonth: '2023-01',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-02-15'
        },
        // 医疗保险信息
        medical: {
            chargeStartMonth: '2023-01',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-02-15'
        },
        // 失业保险信息
        unemployment: {
            chargeStartMonth: '2023-01',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-02-15'
        },
        // 工伤保险信息
        workInjury: {
            chargeStartMonth: '2023-01',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-02-15'
        },
        // 大病保险信息
        seriousIllness: {
            chargeStartMonth: '2023-01',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-02-15'
        },
        // 公积金信息
        housingFund: {
            chargeStartMonth: '2023-01',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-02-15'
        },
        remark: '无特殊情况',
        isOutboundCall: '是',
        socialSecurityAddDeadline: '2023-01-10',
        socialSecurityRemoveDeadline: '2023-01-20',
        housingFundAddDeadline: '2023-01-10',
        housingFundRemoveDeadline: '2023-01-20',
        orderNo: 'ORD20230001',
        contractHeader: '公司A',
        gender: '男',
        age: 30,
        retirementTime: '2053-01-01',
        personnelCategory: '普通员工',
        orderDispatcher: '1',
        orderDispatcherService: '李四',
        companyType: '自有公司',
        leaveReportTime: '',
        socialSecuritySalary: 8000,
        housingFundSalary: 8000,
        householdType: '城镇户口',
        socialSecurityChargeStartMonth: '2023-01',
        housingFundChargeStartMonth: '2023-01'
    },
    {
        id: 2,
        type: '2',
        customerName: '客户B',
        contractType: '特殊合同',
        employeeStatus: '1',
        entryReportTime: '2023-02-01',
        employeeName: '李四',
        idNumber: '110101199001021234',
        orderReceiver: '2',
        orderReceiverService: '赵六',
        importTime: '2023-02-15 09:15:00',
        // 养老保险信息
        pension: {
            chargeStartMonth: '2023-02',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-03-15'
        },
        // 医疗保险信息
        medical: {
            chargeStartMonth: '2023-02',
            declarationResult: '2',
            reason: '资料不全',
            failureScreenshot: 'medical_failure_2.jpg',
            nextMonthDeadline: '2023-03-15'
        },
        // 失业保险信息
        unemployment: {
            chargeStartMonth: '2023-02',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-03-15'
        },
        // 工伤保险信息
        workInjury: {
            chargeStartMonth: '2023-02',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-03-15'
        },
        // 大病保险信息
        seriousIllness: {
            chargeStartMonth: '2023-02',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-03-15'
        },
        // 公积金信息
        housingFund: {
            chargeStartMonth: '2023-02',
            declarationResult: '1',
            reason: '',
            failureScreenshot: '',
            nextMonthDeadline: '2023-03-15'
        },
        remark: '医疗保险申报失败',
        isOutboundCall: '否',
        socialSecurityAddDeadline: '2023-02-10',
        socialSecurityRemoveDeadline: '2023-02-20',
        housingFundAddDeadline: '2023-02-10',
        housingFundRemoveDeadline: '2023-02-20',
        orderNo: 'ORD20230002',
        contractHeader: '公司B',
        gender: '男',
        age: 35,
        retirementTime: '2048-02-01',
        personnelCategory: '管理员工',
        orderDispatcher: '2',
        orderDispatcherService: '王五',
        companyType: '供应商公司',
        leaveReportTime: '',
        socialSecuritySalary: 10000,
        housingFundSalary: 10000,
        householdType: '农村户口',
        socialSecurityChargeStartMonth: '2023-02',
        housingFundChargeStartMonth: '2023-02'
    }
];

// 导入表单验证规则
const importRules = {
    remark: [
        { required: true, message: '请输入备注', trigger: 'blur' }
    ],
    file: [
        { required: true, message: '请选择要上传的文件', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null, // 客户名称
        orderReceiver: null, // 接单方
        orderReceiverService: null, // 接单客服
        orderDispatcher: null, // 派单方
        orderNo: null, // 订单编号
        employeeName: null, // 姓名
        idNumber: null, // 证件号码
        employeeStatus: null, // 雇员状态
        type: null, // 类型
        importDateStart: null, // 导入日期从
        importDateEnd: null, // 导入日期到
        insuranceType: null, // 险种
        declarationResult: null, // 申报结果
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        remark: '', // 备注
        file: null // 文件
    }, // 导入表单
    dialogShow: false, // 导入弹窗
    dialogShow2: false, // 失败截图弹窗
    currentScreenshot: '', // 当前查看的截图
    ids: [], // 选中的id
    title: "", // 标题
})


/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item => item.customerName.includes(obj.queryParams.customerName));
        }

        if (obj.queryParams.orderReceiver) {
            filteredData = filteredData.filter(item => item.orderReceiver === obj.queryParams.orderReceiver);
        }

        if (obj.queryParams.orderReceiverService) {
            filteredData = filteredData.filter(item => item.orderReceiverService.includes(obj.queryParams.orderReceiverService));
        }

        if (obj.queryParams.orderDispatcher) {
            filteredData = filteredData.filter(item => item.orderDispatcher === obj.queryParams.orderDispatcher);
        }

        if (obj.queryParams.orderNo) {
            filteredData = filteredData.filter(item => item.orderNo.includes(obj.queryParams.orderNo));
        }

        if (obj.queryParams.employeeName) {
            filteredData = filteredData.filter(item => item.employeeName.includes(obj.queryParams.employeeName));
        }

        if (obj.queryParams.idNumber) {
            filteredData = filteredData.filter(item => item.idNumber.includes(obj.queryParams.idNumber));
        }

        if (obj.queryParams.employeeStatus) {
            filteredData = filteredData.filter(item => item.employeeStatus === obj.queryParams.employeeStatus);
        }

        if (obj.queryParams.type) {
            filteredData = filteredData.filter(item => item.type === obj.queryParams.type);
        }

        if (obj.queryParams.importDateStart) {
            const startDate = new Date(obj.queryParams.importDateStart).getTime();
            filteredData = filteredData.filter(item => new Date(item.importTime).getTime() >= startDate);
        }

        if (obj.queryParams.importDateEnd) {
            const endDate = new Date(obj.queryParams.importDateEnd).getTime();
            filteredData = filteredData.filter(item => new Date(item.importTime).getTime() <= endDate);
        }

        if (obj.queryParams.insuranceType && obj.queryParams.declarationResult) {
            // 根据险种和申报结果过滤
            const insuranceType = obj.queryParams.insuranceType;
            const declarationResult = obj.queryParams.declarationResult;

            filteredData = filteredData.filter(item => {
                let insurance;
                switch (insuranceType) {
                    case '1': insurance = item.pension; break;
                    case '2': insurance = item.medical; break;
                    case '3': insurance = item.unemployment; break;
                    case '4': insurance = item.workInjury; break;
                    case '6': insurance = item.seriousIllness; break;
                    case '10': insurance = item.housingFund; break;
                    default: return false;
                }

                return insurance && insurance.declarationResult === declarationResult;
            });
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 导入
function handleImport() {
    obj.dialogShow = true;
    obj.title = "上传导入数据";
    resetForm();
}

// 关闭导入对话框
function handleImportClose() {
    // 实际项目中应该在导入组件内部处理关闭逻辑
    // 这里只需要刷新列表
    getList(); // 刷新列表
}

// 查看失败截图
function handleViewScreenshot(row, insuranceType) {
    let insurance;
    switch (insuranceType) {
        case '1': insurance = row.pension; break;
        case '2': insurance = row.medical; break;
        case '3': insurance = row.unemployment; break;
        case '4': insurance = row.workInjury; break;
        case '6': insurance = row.seriousIllness; break;
        case '10': insurance = row.housingFund; break;
        default: return;
    }

    if (!insurance || !insurance.failureScreenshot) {
        proxy.$modal.msgInfo('没有失败截图');
        return;
    }

    obj.currentScreenshot = insurance.failureScreenshot;
    obj.dialogShow2 = true;
    obj.title = '失败截图查看';
}

// 重置表单
function resetForm() {
    obj.dialogForm = {
        remark: '', // 备注
        file: null // 文件
    };
}

getList();
</script>
<style lang="scss" scoped></style>