import hasRole from "./permission/hasRole";
import hasPermi from "./permission/hasPermi";
import copyText from "./common/copyText";
/**
 * 注册全局自定义指令
 * @param {Object} app - Vue应用实例
 */
export default function directive(app) {
  // 注册hasRole指令，用于判断用户是否具有特定角色
  app.directive("hasRole", hasRole);

  // 注册hasPermi指令，用于判断用户是否具有特定权限
  app.directive("hasPermi", hasPermi);

  // 注册copyText指令，用于复制文本内容
  app.directive("copyText", copyText);
}
