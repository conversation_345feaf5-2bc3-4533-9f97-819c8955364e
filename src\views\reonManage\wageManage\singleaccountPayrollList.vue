<!-- 单立户发薪名单 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractNo">
                <el-input class="width220" v-model="obj.queryParams.contractNo" placeholder="请输入合同编号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="importer">
                <el-select class="width220" v-model="obj.queryParams.importer" placeholder="请选择" clearable>
                    <el-option v-for="item in importerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="指定发薪方:" prop="importer">
                <el-select class="width220" v-model="obj.queryParams.importer" placeholder="请选择" clearable>
                    <el-option v-for="item in importerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete()">删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户名称" align="center" prop="customerName" min-width="100" />
            <el-table-column label="合同编号" align="center" prop="contractNo" width="120" />
            <el-table-column label="扣缴义务人名称" align="center" prop="withholdingName" min-width="150" />
            <el-table-column label="扣缴义务人类型" align="center" width="120">
                <template #default="scope">
                    {{ getWithholdingTypeName(scope.row.withholdingType) }}
                </template>
            </el-table-column>
            <el-table-column label="工资发放地" align="center" width="100">
                <template #default="scope">
                    {{ getSalaryLocationName(scope.row.salaryLocation) }}
                </template>
            </el-table-column>
            <el-table-column label="指定发薪方" align="center" width="100">
                <template #default="scope">
                    {{ getImporterName(scope.row.importer) }}
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="220" fixed="right">
                <template #default="scope">
                    <el-button text type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button text type="danger" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增/编辑对话框 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="20%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" label-width="auto">
                <el-form-item label="合同:" prop="contractNo">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.contractNo" placeholder="请输入合同编号"
                        clearable />
                </el-form-item>
                <el-form-item label="扣缴义务人:" prop="withholdingName">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.withholdingName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in withholdingOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="指定发薪方:" prop="importer">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.importer" placeholder="请选择" clearable>
                        <el-option v-for="item in importerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="SingleaccountPayrollList">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 导入人选项
const importerOptions = [
    { value: '1', label: '张三' },
    { value: '2', label: '李四' },
    { value: '3', label: '王五' },
    { value: '4', label: '赵六' },
    { value: '5', label: '孙七' }
];

// 扣缴义务人类型选项
const withholdingTypeOptions = [
    { value: '1', label: '企业' },
    { value: '2', label: '事业单位' },
    { value: '3', label: '个体工商户' },
    { value: '4', label: '其他组织' }
];

// 工资发放地选项
const salaryLocationOptions = [
    { value: '1', label: '北京市' },
    { value: '2', label: '上海市' },
    { value: '3', label: '广州市' },
    { value: '4', label: '深圳市' },
    { value: '5', label: '成都市' }
];

// 扣缴义务人选项
const withholdingOptions = [
    { value: '1', label: '北京科技有限公司' },
    { value: '2', label: '上海贸易有限公司' },
    { value: '3', label: '广州电子有限公司' },
    { value: '4', label: '深圳科技有限公司' },
    { value: '5', label: '成都信息有限公司' }
];

// 真实入离职时间白名单数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: '', // 客户名称
        contractNo: '', // 合同编号
        importer: '', // 导入人
        importNo: '' // 导入编号
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            customerName: '北京科技有限公司',
            contractNo: 'HT20230501001',
            withholdingName: '北京科技有限公司',
            withholdingType: '1',
            salaryLocation: '1',
            importNo: 'DR20230501001',
            importer: '1',
            importTime: '2023-05-01 10:00:00'
        },
        {
            id: 2,
            customerName: '上海贸易有限公司',
            contractNo: 'HT20230502001',
            withholdingName: '上海贸易有限公司',
            withholdingType: '2',
            salaryLocation: '2',
            importNo: 'DR20230502001',
            importer: '2',
            importTime: '2023-05-02 11:30:00'
        },
        {
            id: 3,
            customerName: '广州电子有限公司',
            contractNo: 'HT20230503001',
            withholdingName: '广州电子有限公司',
            withholdingType: '3',
            salaryLocation: '3',
            importNo: 'DR20230503001',
            importer: '3',
            importTime: '2023-05-03 09:15:00'
        },
        {
            id: 4,
            customerName: '深圳科技有限公司',
            contractNo: 'HT20230504001',
            withholdingName: '深圳科技有限公司',
            withholdingType: '1',
            salaryLocation: '4',
            importNo: 'DR20230504001',
            importer: '4',
            importTime: '2023-05-04 14:45:00'
        },
        {
            id: 5,
            customerName: '成都信息有限公司',
            contractNo: 'HT20230505001',
            withholdingName: '成都信息有限公司',
            withholdingType: '4',
            salaryLocation: '5',
            importNo: 'DR20230505001',
            importer: '5',
            importTime: '2023-05-05 16:20:00'
        }
    ],

    // 对话框相关数据
    dialogForm: {
        customerName: '',
        contractNo: '',
        withholdingName: '',
        withholdingType: '',
        salaryLocation: ''
    },
    dialogShow: false, // 新增/编辑对话框
    importDialogShow: false, // 导入对话框
    importForm: {
        contractNo: '',
        withholdingName: '',
        file: null
    },
    ids: [], // 选中的id
    title: '' // 对话框标题
})

// 表单验证规则
const rules = {
    customerName: [
        { required: true, message: '请输入客户名称', trigger: 'blur' }
    ],
    contractNo: [
        { required: true, message: '请输入合同编号', trigger: 'blur' }
    ],
    withholdingName: [
        { required: true, message: '请选择扣缴义务人', trigger: 'change' }
    ],
    withholdingType: [
        { required: true, message: '请选择扣缴义务人类型', trigger: 'change' }
    ],
    salaryLocation: [
        { required: true, message: '请选择工资发放地', trigger: 'change' }
    ]
};

/**
 * 获取扣缴义务人类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getWithholdingTypeName(typeId) {
    if (!typeId) return '-';

    const type = withholdingTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}

/**
 * 获取工资发放地名称
 * @param {string} locationId 地点ID
 * @returns {string} 地点名称
 */
function getSalaryLocationName(locationId) {
    if (!locationId) return '-';

    const location = salaryLocationOptions.find(item => item.value === locationId);
    return location ? location.label : '-';
}

/**
 * 获取导入人名称
 * @param {string} importerId 导入人ID
 * @returns {string} 导入人名称
 */
function getImporterName(importerId) {
    if (!importerId) return '-';

    const importer = importerOptions.find(item => item.value === importerId);
    return importer ? importer.label : '-';
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item =>
                item.customerName.includes(obj.queryParams.customerName)
            );
        }

        if (obj.queryParams.contractNo) {
            filteredData = filteredData.filter(item =>
                item.contractNo.includes(obj.queryParams.contractNo)
            );
        }

        if (obj.queryParams.importer) {
            filteredData = filteredData.filter(item =>
                item.importer === obj.queryParams.importer
            );
        }

        if (obj.queryParams.importNo) {
            filteredData = filteredData.filter(item =>
                item.importNo.includes(obj.queryParams.importNo)
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 新增白名单 */
function handleAdd() {
    // 重置表单
    obj.dialogForm = {
        customerName: '',
        contractNo: '',
        withholdingName: '',
        withholdingType: '',
        salaryLocation: ''
    };

    obj.dialogShow = true;
    obj.title = "新增真实入离职时间白名单";
}

/** 修改白名单 */
function handleEdit() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgInfo('请选择一条记录进行修改');
        return;
    }

    const recordInfo = obj.tableData.find(item => item.id === obj.ids[0]);
    if (!recordInfo) {
        proxy.$modal.msgInfo('找不到选中的记录');
        return;
    }

    // 复制数据到表单
    obj.dialogForm = JSON.parse(JSON.stringify(recordInfo));
    obj.dialogShow = true;
    obj.title = "修改真实入离职时间白名单";
}

/** 保存白名单信息 */
function handleSave() {
    if (!proxy.$refs["formRef"]) return;

    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            // 模拟保存操作
            const isNew = !obj.dialogForm.id;

            if (isNew) {
                // 新增记录
                const newRecord = {
                    ...obj.dialogForm,
                    id: obj.tableData.length + 1,
                    importNo: 'DR' + new Date().getTime().toString().substring(0, 10),
                    importer: '1', // 默认当前用户
                    importTime: new Date().toLocaleString()
                };

                obj.tableData.unshift(newRecord);
                proxy.$modal.msgSuccess('新增成功');
            } else {
                // 编辑记录
                const index = obj.tableData.findIndex(item => item.id === obj.dialogForm.id);
                if (index !== -1) {
                    obj.tableData[index] = {
                        ...obj.tableData[index],
                        ...obj.dialogForm
                    };
                }
                proxy.$modal.msgSuccess('修改成功');
            }

            obj.dialogShow = false;
            obj.total = obj.tableData.length;
        }
    });
}

/** 导出对话框 */
function handleExport() {

}

/** 删除白名单 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped></style>