<!-- 调整供应商实做 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateFrom">
                <el-input class="width220" v-model="obj.queryParams.importDateFrom" placeholder="请输入开始日期" />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateTo">
                <el-input class="width220" v-model="obj.queryParams.importDateTo" placeholder="请输入结束日期" />
            </el-form-item>
            <el-form-item label="导入编号:" prop="importCode">
                <el-input class="width220" v-model="obj.queryParams.importCode" placeholder="请输入导入编号" />
            </el-form-item>
            <el-form-item label="导入人:" prop="importUser">
                <el-input class="width220" v-model="obj.queryParams.importUser" placeholder="请输入导入人" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleDownload">下载模版</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="导入编号" align="center" prop="importCode" />
            <el-table-column label="导入人" align="center" prop="importUser" />
            <el-table-column label="导入时间" align="center" prop="importTime" />
            <el-table-column label="成功记录数" align="center" prop="successCount" />
            <el-table-column label="失败记录数" align="center" prop="failCount" />
            <el-table-column label="导入文件" align="center" prop="importFile" />
            <el-table-column label="处理状态" align="center" prop="processStatus" />
            <el-table-column label="历史信息查询" align="center">
                <template #default="scope">
                    <el-button type="primary" text plain icon="View" @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 导入弹窗 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm"
            :type="obj.importType" />
    </div>
</template>


<script setup name="SupplierImplementationAdjust">

import ImportForm from '@/views/reonManage/components/forms/import.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateFrom: null,
        importDateTo: null,
        importCode: null,
        importUser: null,
    },//查询表单
    total: 10,//总条数
    tableData: [],//列表
    dialogForm: {
        remark: '',
        file: null
    },//导入表单
    dialogShow: false,//导入弹窗
    ids: [],//选中的id
    title: "上传导入数据",//标题
    importType: 'supplierImplementationAdjust' // 导入类型
})


/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                importCode: 'IMP20230501001',
                importUser: '张三',
                importTime: '2023-05-01 10:30:00',
                successCount: 120,
                failCount: 5,
                importFile: '供应商实做调整_20230501.xlsx',
                processStatus: '已完成'
            },
            {
                id: 2,
                importCode: 'IMP20230510002',
                importUser: '李四',
                importTime: '2023-05-10 14:20:00',
                successCount: 85,
                failCount: 0,
                importFile: '供应商实做调整_20230510.xlsx',
                processStatus: '已完成'
            },
            {
                id: 3,
                importCode: 'IMP20230520003',
                importUser: '王五',
                importTime: '2023-05-20 09:15:00',
                successCount: 150,
                failCount: 10,
                importFile: '供应商实做调整_20230520.xlsx',
                processStatus: '处理中'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 导入
function handleImport() {
    obj.dialogShow = true;
}

// 关闭导入弹窗
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {
        remark: '',
        file: null
    };
}

// 下载模版
function handleDownload() {
    // 模拟下载模板文件
    const link = document.createElement('a');
    link.href = '#';
    link.setAttribute('download', '供应商实做调整模板.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 订单详情
function handleDetail(row) {
    console.log('查看详情', row);
    // 这里可以实现跳转到详情页面或者打开详情弹窗
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

getList();
</script>
<style lang="scss" scoped></style>