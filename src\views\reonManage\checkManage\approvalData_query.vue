<!-- 审批数据查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户账套:" prop="customerAccount">
                <el-input class="width220" v-model="obj.queryParams.customerAccount" placeholder="请输入客户账套" clearable />
            </el-form-item>
            <el-form-item label="账单年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择账单年月"
                    clearable />
            </el-form-item>
            <el-form-item label="提交分公司:" prop="submitBranch">
                <el-select class="width220" v-model="obj.queryParams.submitBranch" placeholder="请选择分公司" clearable>
                    <el-option label="北京分公司" value="北京分公司" />
                    <el-option label="上海分公司" value="上海分公司" />
                    <el-option label="广州分公司" value="广州分公司" />
                </el-select>
            </el-form-item>
            <el-form-item label="提交开始时间:" prop="submitStartTime">
                <el-date-picker class="width220" v-model="obj.queryParams.submitStartTime" type="datetime"
                    placeholder="请选择开始时间" clearable />
            </el-form-item>
            <el-form-item label="提交结束时间:" prop="submitEndTime">
                <el-date-picker class="width220" v-model="obj.queryParams.submitEndTime" type="datetime"
                    placeholder="请选择结束时间" clearable />
            </el-form-item>
            <el-form-item label="审批状态:" prop="approvalStatus">
                <el-select class="width220" v-model="obj.queryParams.approvalStatus" placeholder="请选择审批状态" clearable>
                    <el-option label="待审批" value="0" />
                    <el-option label="已审批" value="1" />
                    <el-option label="已驳回" value="2" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table ref="singleTableRef" border :data="obj.tableData" highlight-current-row
            @current-change="handleCurrentChange">
            <el-table-column type="expand">
                <template #default="props">
                    <el-table :data="props.row.family" :border="childBorder">
                        <el-table-column label="驳回原因" align="center" prop="name" />
                    </el-table>
                </template>
            </el-table-column>
            <el-table-column align="center" width="60">
                <template #default="scope">
                    <el-radio v-model="radio" :value="scope.row.id" @change="handleCurrentChange(scope.row)"></el-radio>
                </template>
            </el-table-column>
            <el-table-column label="提交分公司" align="center" prop="submitBranch" />
            <el-table-column label="提交人" align="center" prop="submitter" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户账套" align="center" prop="customerAccount" />
            <el-table-column label="账单年月" align="center" prop="billMonth" />
            <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
            <el-table-column label="应收金额" align="center" prop="receivableAmount" />
            <el-table-column label="账单状态" align="center" prop="billStatus" />
            <el-table-column label="提交时间" align="center" prop="submitTime" />
            <el-table-column label="审批状态" align="center" prop="approvalStatus" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <el-table class="mt20" :data="obj.tableData" border>
            <el-table-column label="序号" type="index" align="center" width="60" />
            <el-table-column label="一级类别" align="center" prop="primaryCategory" />
            <el-table-column label="二级类别" align="center" prop="secondaryCategory" />
            <el-table-column label="金额" align="center" prop="amount" />
            <el-table-column label="金额(不含税)" align="center" prop="amountExcludingTax" />
            <el-table-column label="增值税率" align="center" prop="vatRate" />
            <el-table-column label="增值税" align="center" prop="vat" />
            <el-table-column label="总人次" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.totalPersonTimes" placeholder="请输入总人次"
                        clearable />
                </template>
            </el-table-column>
            <el-table-column label="供应商人次" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.supplierPersonTimes" placeholder="请输入供应商人次"
                        clearable />
                </template>
            </el-table-column>
            <el-table-column label="供应商成本" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.supplierCost" placeholder="请输入供应商成本" clearable />
                </template>
            </el-table-column>
            <el-table-column label="一次性支持人员" align="center">
                <template #default="scope">
                    <el-select style="width: 100%;" v-model="scope.row.oneTimeStaff" placeholder="请选择一次性支持人员" clearable>
                        <el-option label="张三" value="1" />
                        <el-option label="李四" value="2" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="大合同名称" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.contractName" placeholder="请输入大合同名称" clearable />
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.remark" placeholder="请输入备注" clearable />
                </template>
            </el-table-column>
            <el-table-column label="上传文件" align="center" prop="uploadFile">
                <template #default="scope">
                    <!-- <file-upload v-model="scope.row.uploadFile" :isShowTip="false" /> -->
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup name="ApprovalData_query">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const radio = ref(null);

const childBorder = ref(true);

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        customerAccount: null,
        billMonth: null,
        submitBranch: null,
        submitStartTime: null,
        submitEndTime: null,
        approvalStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                submitBranch: '北京分公司',
                submitter: '张三',
                customerName: '客户名称1',
                customerAccount: '账套A',
                billMonth: '2023-01',
                financialReceivableMonth: '2023-02',
                receivableAmount: '10000',
                billStatus: '已生成',
                submitTime: '2023-01-15 10:00:00',
                approvalStatus: '待审批',
                family: [
                    { name: '无驳回原因' }
                ],
                primaryCategory: '工资',
                secondaryCategory: '基本工资',
                amount: '10000',
                amountExcludingTax: '9433.96',
                vatRate: '6%',
                vat: '566.04',
                totalPersonTimes: '10',
                supplierPersonTimes: '5',
                supplierCost: '5000',
                oneTimeStaff: '5',
                contractName: '大合同1',
                remark: '备注信息1',
                uploadFile: '文件1.pdf'
            },
            {
                id: 2,
                submitBranch: '上海分公司',
                submitter: '李四',
                customerName: '客户名称2',
                customerAccount: '账套B',
                billMonth: '2023-02',
                financialReceivableMonth: '2023-03',
                receivableAmount: '20000',
                billStatus: '已生成',
                submitTime: '2023-02-15 10:00:00',
                approvalStatus: '已审批',
                family: [
                    { name: '无驳回原因' }
                ],
                primaryCategory: '福利',
                secondaryCategory: '年终奖',
                amount: '20000',
                amountExcludingTax: '18867.92',
                vatRate: '6%',
                vat: '1132.08',
                totalPersonTimes: '20',
                supplierPersonTimes: '10',
                supplierCost: '10000',
                oneTimeStaff: '10',
                contractName: '大合同2',
                remark: '备注信息2',
                uploadFile: '文件2.pdf'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 处理当前行变化 */
function handleCurrentChange(row) {
    if (row) {
        radio.value = row.id;

    }
    console.log("已选产品:", row);
}
// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>