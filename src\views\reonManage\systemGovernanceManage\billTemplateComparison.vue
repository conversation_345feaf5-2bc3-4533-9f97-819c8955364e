<!-- 账单模板对比 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="创建日期:" prop="typeCode">
                <el-date-picker class="width220" v-model="obj.queryParams.createDate" type="date" />
            </el-form-item>
            <el-form-item label="订单编号:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.sales" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleGenerateData">生成数据</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="订单编号" align="center" prop="orderNumber" />
            <el-table-column label="员工姓名" align="center" prop="employeeName" />
            <el-table-column label="员工ID" align="center" prop="employeeId" />
            <el-table-column label="唯一号" align="center" prop="uniqueNumber" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="创建日" align="center" prop="createDate" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="BillTemplateComparison">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        createDate: null,
        orderNumber: null,
    },//查询表单
    total: 0,//总条数

    tableData: [],//列表
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.tableData.forEach(i => {
            i.orderNumber = i.productName
        });
        obj.total = response.total;
        obj.loading = false;
    });
}
/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
// 生成数据
function handleGenerateData() {
    obj.dialogShow = true;
}

getList();
</script>
<style lang="scss" scoped></style>