<!-- 核销查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户帐套:" prop="customerAccount">
                <el-input class="width220" v-model="obj.queryParams.customerAccount" placeholder="请输入客户帐套" clearable />
            </el-form-item>
            <el-form-item label="支付类型:" prop="paymentType">
                <el-select class="width220" v-model="obj.queryParams.paymentType" placeholder="请选择支付类型" clearable>
                    <el-option label="银行转账" value="1" />
                    <el-option label="支票" value="2" />
                    <el-option label="现金" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="到款时间>=:" prop="paymentTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentTimeStart" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="到款时间<=:" prop="paymentTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentTimeEnd" type="date"
                    placeholder="请选择结束日期" clearable />
            </el-form-item>
            <el-form-item label="到款金额>=:" prop="paymentAmountMin">
                <el-input class="width220" v-model="obj.queryParams.paymentAmountMin" placeholder="请输入最小金额" clearable />
            </el-form-item>
            <el-form-item label="到款金额<=:" prop="paymentAmountMax">
                <el-input class="width220" v-model="obj.queryParams.paymentAmountMax" placeholder="请输入最大金额" clearable />
            </el-form-item>
            <el-form-item label="核销时间>=:" prop="checkTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.checkTimeStart" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="核销时间<=:" prop="checkTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.checkTimeEnd" type="date"
                    placeholder="请选择结束日期" clearable />
            </el-form-item>
            <el-form-item label="核销金额>=:" prop="checkAmountMin">
                <el-input class="width220" v-model="obj.queryParams.checkAmountMin" placeholder="请输入最小金额" clearable />
            </el-form-item>
            <el-form-item label="核销金额<=:" prop="checkAmountMax">
                <el-input class="width220" v-model="obj.queryParams.checkAmountMax" placeholder="请输入最大金额" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleRowDblclick" :row-class-name="rowClassName">
            <el-table-column type="selection" width="55" />
            <el-table-column label="到款分公司" align="center" prop="branchCompany" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="到款客户" align="center" prop="paymentCustomer" />
            <el-table-column label="到款金额" align="center" prop="paymentAmount" />
            <el-table-column label="到款日期" align="center" prop="paymentDate" />
            <el-table-column label="到款银行" align="center" prop="paymentBank" />
            <el-table-column label="支付类型" align="center" prop="paymentType" />
            <el-table-column label="到款备注" align="center" prop="paymentRemark" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <border-box title="应收账单记录" v-show="obj.flag">
            <el-table border :data="obj.tableData" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="客户帐套" align="center" prop="customerAccount" />
                <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
                <el-table-column label="应收金额" align="center" prop="receivableAmount" />
                <el-table-column label="客户账单年月" align="center" prop="customerBillMonth" />
                <el-table-column label="核销状态" align="center" prop="checkStatus" />
                <el-table-column label="开票状态" align="center" prop="invoiceStatus" />
                <el-table-column label="核销金额" align="center" prop="checkAmount" />
                <el-table-column label="小额调整" align="center" prop="smallAdjustment" />
                <el-table-column label="核销日期" align="center" prop="checkDate" />
                <el-table-column label="核销备注" align="center" prop="checkRemark" />
                <el-table-column label="审批状态" align="center" prop="approvalStatus" />
            </el-table>
        </border-box>
        <border-box title="开票记录" v-show="obj.flag">
            <el-table border :data="obj.tableData">
                <el-table-column label="发票编号" align="center" prop="invoiceNumber" />
                <el-table-column label="发票时间" align="center" prop="invoiceDate" />
                <el-table-column label="开票时间" align="center" prop="billingDate" />
                <el-table-column label="开票状态" align="center" prop="invoiceStatus" />
                <el-table-column label="开票备注" align="center" prop="invoiceRemark" />
            </el-table>
        </border-box>
    </div>
</template>

<script setup name="Verification_query">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        customerAccount: null,
        paymentType: null,
        paymentTimeStart: null,
        paymentTimeEnd: null,
        paymentAmountMin: null,
        paymentAmountMax: null,
        checkTimeStart: null,
        checkTimeEnd: null,
        checkAmountMin: null,
        checkAmountMax: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
    flag: false,//是否双击
})

// 行样式
function rowClassName(row) {
    if (row.paymentAmount > 10000) {
        return 'D7D1C9';
    } else if (row.paymentAmount < 10000) {
        return 'BAD1D1';
    } else {
        return '';
    }
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                branchCompany: '上海分公司',
                customerName: '客户名称1',
                customerCode: 'CUST001',
                paymentCustomer: '到款客户A',
                paymentAmount: 10000.00,
                paymentDate: '2023-01-15',
                paymentBank: '中国银行',
                paymentType: '银行转账',
                paymentRemark: '首次付款',
                customerAccount: '帐套001',
                financialReceivableMonth: '2023-01',
                receivableAmount: 10000.00,
                customerBillMonth: '2023-01',
                checkStatus: '已核销',
                invoiceStatus: '已开票',
                checkAmount: 10000.00,
                smallAdjustment: 0.00,
                checkDate: '2023-01-20',
                checkRemark: '正常核销',
                approvalStatus: '已审批',
                invoiceNumber: 'INV20230120001',
                invoiceDate: '2023-01-20',
                billingDate: '2023-01-21',
                invoiceRemark: '正常开票'
            },
            {
                id: 2,
                branchCompany: '北京分公司',
                customerName: '客户名称2',
                customerCode: 'CUST002',
                paymentCustomer: '到款客户B',
                paymentAmount: 20000.00,
                paymentDate: '2023-02-15',
                paymentBank: '工商银行',
                paymentType: '支票',
                paymentRemark: '第二次付款',
                customerAccount: '帐套002',
                financialReceivableMonth: '2023-02',
                receivableAmount: 20000.00,
                customerBillMonth: '2023-02',
                checkStatus: '未核销',
                invoiceStatus: '未开票',
                checkAmount: 0.00,
                smallAdjustment: 0.00,
                checkDate: '',
                checkRemark: '',
                approvalStatus: '未审批',
                invoiceNumber: '',
                invoiceDate: '',
                billingDate: '',
                invoiceRemark: ''
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 双击行事件
function handleRowDblclick(row) {
    obj.flag = true;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>