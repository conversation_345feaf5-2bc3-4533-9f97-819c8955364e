<!-- 批量导入重点客户/待办事项 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="导入时间:" prop="importTime">
                <el-date-picker class="width220" v-model="obj.queryParams.importTime" type="datetime"
                    placeholder="请选择导入时间" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download"
                    @click="handleDownloadTemplate_client">下载重点客户导入模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download"
                    @click="handleDownloadTemplate_service">下载办事项导入模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Upload" @click="handleBatchImport">批量导入</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="待办事项" align="center" prop="todoItem" />
            <el-table-column label="上传时间" align="center" prop="uploadTime" />
            <el-table-column label="是否完成" align="center" prop="isCompleted">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isCompleted" />
                </template>
            </el-table-column>
            <el-table-column label="是否重点客户" align="center" prop="isKeyCustomer">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isKeyCustomer" />
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 批量导入 -->
        <el-dialog v-model="obj.dialogShow" title="批量导入" width="60%">
            <el-form :model="obj.form" ref="formRef" label-width="auto">
                <el-form-item label="上传文件:" prop="fileList"
                    :rules="[{ required: true, message: '请上传文件', trigger: 'change' }]">
                    <FileUpload v-model="obj.form.fileList" :limit="1" :fileSize="10" :fileType="['xlsx', 'xls']" />
                </el-form-item>
                <el-form-item label="文件类型:" prop="importFileType"
                    :rules="[{ required: true, message: '请选择文件类型', trigger: 'change' }]">
                    <el-radio-group v-model="obj.form.importFileType">
                        <el-radio label="1">重点客户导入文件</el-radio>
                        <el-radio label="2">待办事项导入文件</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-table :data="obj.tableData2" border>
                    <el-table-column label="导入编号" align="center" prop="importNumber" />
                    <el-table-column label="导入人" align="center" prop="importUser" />
                    <el-table-column label="导入时间" align="center" prop="importTime" />
                    <el-table-column label="成功记录数" align="center" prop="successCount" />
                    <el-table-column label="失败记录数" align="center" prop="failCount" />
                    <el-table-column label="导入文件" align="center" prop="importFile" />
                    <el-table-column label="处理状态" align="center" prop="processStatus">
                        <template #default="scope">
                            <el-tag :type="getProcessStatusType(scope.row.processStatus)">
                                {{ getProcessStatusLabel(scope.row.processStatus) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建人" align="center" prop="createBy" />
                    <el-table-column label="导入文件类型" align="center" prop="importFileType">
                        <template #default="scope">
                            <el-tag type="success" v-if="scope.row.importFileType === '1'">重点客户导入文件</el-tag>
                            <el-tag type="warning" v-else-if="scope.row.importFileType === '2'">待办事项导入文件</el-tag>
                            <span v-else>{{ scope.row.importFileType }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" align="center" prop="createTime" />
                    <el-table-column label="修改时间" align="center" prop="updateTime" />
                    <el-table-column label="历史信息查看" align="center">
                        <template #default="scope">
                            <el-button type="primary" link @click="handleViewHistory(scope.row)">查看</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleUpload">开始上传</el-button>
                <el-button type="primary" @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>

    </div>
</template>

<script setup name="BatchExportKeyCustomer">

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 处理状态选项
const processStatusOptions = [
    { value: '0', label: '处理中', type: 'warning' },
    { value: '1', label: '处理成功', type: 'success' },
    { value: '2', label: '处理失败', type: 'danger' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        importTime: null
    }, // 查询表单
    total: 0, // 总条数
    ids: [], // 选中id
    tableData: [], // 列表
    tableData2: [], // 导入历史记录
    dialogShow: false, // 批量导入对话框
    form: {
        fileList: [],
        importFileType: '1'
    } // 表单数据
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerCode: 'KH20230001',
                customerName: '客户A',
                todoItem: '联系客户确认需求',
                uploadTime: '2023-05-10 10:00:00',
                isCompleted: 'Y',
                isKeyCustomer: 'Y'
            },
            {
                id: 2,
                customerCode: '**********',
                customerName: '客户B',
                todoItem: '安排项目开发人员',
                uploadTime: '2023-05-15 14:30:00',
                isCompleted: 'N',
                isKeyCustomer: 'Y'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {

    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框操作 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 详情按钮操作 */
function handleDetail(row) {
    proxy.$modal.msgInfo('查看详情：' + row.customerName);
}

/** 查看历史信息 */
function handleViewHistory(row) {
    proxy.$modal.msgInfo('查看历史信息：' + row.importNumber);
}

/** 获取处理状态标签类型 */
function getProcessStatusType(status) {
    const option = processStatusOptions.find(item => item.value === status);
    return option ? option.type : '';
}

/** 获取处理状态标签文本 */
function getProcessStatusLabel(status) {
    const option = processStatusOptions.find(item => item.value === status);
    return option ? option.label : status;
}

/** 下载重点客户导入模版 */
function handleDownloadTemplate_client() {
    proxy.$modal.msgSuccess('重点客户导入模版下载成功');
}

/** 下载办事项导入模版 */
function handleDownloadTemplate_service() {
    proxy.$modal.msgSuccess('办事项导入模版下载成功');
}

/** 批量导入 */
function handleBatchImport() {
    obj.dialogShow = true;
    // 获取导入历史记录
    listScale().catch(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData2 = [
            {
                id: 1,
                importNumber: '**********',
                importUser: '管理员',
                importTime: '2023-05-10 10:00:00',
                successCount: 100,
                failCount: 0,
                importFile: '导入文件1.xlsx',
                processStatus: '1',
                createBy: '管理员',
                importFileType: '1',
                createTime: '2023-05-10 10:00:00',
                updateTime: '2023-05-10 10:05:00'
            },
            {
                id: 2,
                importNumber: '**********',
                importUser: '管理员',
                importTime: '2023-06-15 14:30:00',
                successCount: 95,
                failCount: 5,
                importFile: '导入文件2.xlsx',
                processStatus: '2',
                createBy: '管理员',
                importFileType: '2',
                createTime: '2023-06-15 14:30:00',
                updateTime: '2023-06-15 14:35:00'
            }
        ];
    });
}

/** 开始上传 */
function handleUpload() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.form.fileList.length === 0) {
                proxy.$modal.msgError('请选择要上传的文件');
                return;
            }
            proxy.$modal.msgSuccess('文件上传成功');
            obj.dialogShow = false;
            getList();
        }
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}

.mt10 {
    margin-top: 10px;
}

.mb10 {
    margin-bottom: 10px;
}

.formHight {
    max-height: 500px;
    overflow-y: auto;
}
</style>