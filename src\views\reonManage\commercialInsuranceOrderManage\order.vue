<!-- 订单查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" clearable />
            </el-form-item>
            <el-form-item label="姓名:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="请输入姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractId">
                <el-input class="width220" v-model="obj.queryParams.contractId" placeholder="请输入合同编号" clearable />
            </el-form-item>
            <el-form-item label="方案编号:" prop="planId">
                <el-input class="width220" v-model="obj.queryParams.planId" placeholder="请输入方案编号" clearable />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderId">
                <el-input class="width220" v-model="obj.queryParams.orderId" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="收费起始月:" prop="chargeStartMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.chargeStartMonth" type="month"
                    placeholder="请选择收费起始月" format="YYYY-MM" value-format="YYYY-MM" clearable />
            </el-form-item>
            <el-form-item label="账单起始月:" prop="billStartMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billStartMonth" type="month"
                    placeholder="请选择账单起始月" format="YYYY-MM" value-format="YYYY-MM" clearable />
            </el-form-item>
            <el-form-item label="处理状态:" prop="processStatus">
                <el-select class="width220" v-model="obj.queryParams.processStatus" placeholder="请选择处理状态" clearable>
                    <el-option v-for="item in processStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单状态:" prop="orderStatus">
                <el-select class="width220" v-model="obj.queryParams.orderStatus" placeholder="请选择订单状态" clearable>
                    <el-option v-for="item in orderStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单创建时间从:" prop="createTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.createTimeStart" type="datetime"
                    placeholder="请选择开始时间" value-format="YYYY-MM-DD HH:mm:ss" clearable />
            </el-form-item>
            <el-form-item label="订单创建时间到:" prop="createTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.createTimeEnd" type="datetime"
                    placeholder="请选择结束时间" value-format="YYYY-MM-DD HH:mm:ss" clearable />
            </el-form-item>
            <el-row class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Edit" @click="handleSubmit">提交确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain @click="handlePersonnelReduction">减员申请</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Download" @click="handleExport">数据导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="View" @click="handleHistory">历史日志查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Edit"
                    @click="handleUpdateEmployeeNameOrPhone">修改雇员姓名或者手机号</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Edit" @click="handleUpdateCertificateNumber">修改证件编号</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Edit" @click="handleUpdateAssociatedPerson">修改关联人员</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="订单编号" align="center" prop="orderId" min-width="120" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" min-width="120" />
            <el-table-column label="姓名" align="center" prop="name" width="100" />
            <el-table-column label="证件号码" align="center" prop="idNumber" min-width="150" />
            <el-table-column label="处理状态" align="center" width="100">
                <template #default="scope">
                    <el-tag :type="getProcessStatusType(scope.row.processStatus)">
                        {{ getProcessStatusLabel(scope.row.processStatus) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="订单状态" align="center" width="100">
                <template #default="scope">
                    <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
                        {{ getOrderStatusLabel(scope.row.orderStatus) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="客户名称" align="center" prop="customerName" min-width="180" />
            <el-table-column label="合同编号" align="center" prop="contractId" min-width="120" />
            <el-table-column label="方案编号" align="center" prop="planId" min-width="120" />
            <el-table-column label="收费起始月" align="center" prop="chargeStartMonth" width="100" />
            <el-table-column label="账单起始月" align="center" prop="billStartMonth" width="100" />
            <el-table-column label="增员确认时间" align="center" prop="increaseConfirmTime" width="150" />
            <el-table-column label="减员确认时间" align="center" prop="decreaseConfirmTime" width="150" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
            <el-table-column label="操作" align="center" width="100" fixed="right">
                <template #default="scope">
                    <el-button text type="primary" @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="65%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-divider content-position="left">商保订单信息</el-divider>
                <el-form-item label="服务年月" prop="serviceYearMonth">
                    <el-select class="width220" filterable v-model="obj.dialogForm.serviceYearMonth" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-select class="width220" filterable v-model="obj.dialogForm.customerName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="证件类型" prop="idType">
                    <el-select class="width220" filterable v-model="obj.dialogForm.idType" placeholder="请选择" clearable>
                        <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="证件号码" prop="idNumber">
                    <el-input class="width220" v-model="obj.dialogForm.idNumber" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="手机号码" prop="phone">
                    <el-input class="width220" v-model="obj.dialogForm.phone" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="合同编号" prop="contractId">
                    <el-select class="width220" filterable v-model="obj.dialogForm.contractId" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="方案编号" prop="planId">
                    <el-select class="width220" filterable v-model="obj.dialogForm.planId" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="收费起始月" prop="chargeStartMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.chargeStartMonth" type="month"
                        placeholder="请选择" format="YYYY-MM" value-format="YYYY-MM" clearable />
                </el-form-item>
                <el-form-item label="收费截止月" prop="chargeEndMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.chargeEndMonth" type="month"
                        placeholder="请选择" format="YYYY-MM" value-format="YYYY-MM" clearable />
                </el-form-item>
                <el-form-item label="账单起始月" prop="billStartMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.billStartMonth" type="month"
                        placeholder="请选择" format="YYYY-MM" value-format="YYYY-MM" clearable />
                </el-form-item>
                <el-form-item label="客户" prop="customer">
                    <el-select class="width220" filterable v-model="obj.dialogForm.customer" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工种" prop="workType">
                    <el-select class="width220" filterable v-model="obj.dialogForm.workType" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账单模板" prop="billTemplate">
                    <el-select class="width220" filterable v-model="obj.dialogForm.billTemplate" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-divider content-position="left">商保增员银行卡信息</el-divider>
                <el-form-item label="开户行" prop="bank">
                    <el-select class="width220" filterable v-model="obj.dialogForm.bank" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="开户行账号" prop="bankAccount">
                    <el-input class="width220" v-model="obj.dialogForm.bankAccount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开户地" prop="bankLocation">
                    <el-input class="width220" v-model="obj.dialogForm.bankLocation" placeholder="请输入" />
                </el-form-item>
                <border-box title="商保增员关联人员信息" style="font-size: 14px;">
                    <el-row :gutter="10" class="mb10 mt20">
                        <el-col :span="1.5">
                            <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                        </el-col>
                        <el-col :span="1.5">
                            <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
                        </el-col>
                    </el-row>
                    <el-table :data="obj.tableData" border @selection-change="handleSelectionChange">
                        <el-table-column type="selection" width="55" />
                        <el-table-column label="序号" width="60" align="center" type="index" />
                        <el-table-column label="关联人姓名" align="center">
                            <template #default="scope">
                                <el-input style="width:100%" v-model="scope.row.name" placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="关联人证件类型" align="center">
                            <template #default="scope">
                                <el-select style="width:100%" v-model="scope.row.idType" placeholder="请选择" clearable>
                                    <el-option label="身份证" value="1" />
                                    <el-option label="军人证" value="2" />
                                    <el-option label="港澳通行证" value="3" />
                                    <el-option label="台胞证" value="4" />
                                    <el-option label="护照" value="5" />
                                    <el-option label="台湾居住证" value="6" />
                                    <el-option label="其他" value="7" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="关联人证件号" align="center">
                            <template #default="scope">
                                <el-input style="width:100%" v-model="scope.row.idNumber" placeholder="请输入" />
                            </template>
                        </el-table-column>
                        <el-table-column label="关联人出生日期" align="center">
                            <template #default="scope">
                                <el-date-picker style="width:100%" v-model="scope.row.birthDate" type="date"
                                    placeholder="请选择" />
                            </template>
                        </el-table-column>
                        <el-table-column label="关联人性别" align="center">
                            <template #default="scope">
                                <el-select style="width:100%" v-model="scope.row.gender" placeholder="请选择" clearable>
                                    <el-option label="男" value="1" />
                                    <el-option label="女" value="2" />
                                </el-select>
                            </template>
                        </el-table-column>
                    </el-table>
                </border-box>
                <el-divider content-position="left">客户方案信息</el-divider>
                <el-form-item label="方案名称" prop="planName">
                    <el-input class="width220" v-model="obj.dialogForm.planName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="付费方式" prop="payType">
                    <el-input class="width220" v-model="obj.dialogForm.payType" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开户地" prop="bankLocation">
                    <el-input class="width220" v-model="obj.dialogForm.bankLocation" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="方案成本价" prop="planCost">
                    <el-input class="width220" v-model="obj.dialogForm.planCost" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="供应商" prop="supplier">
                    <el-input class="width220" v-model="obj.dialogForm.supplier" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="保险形式" prop="insuranceType">
                    <el-input class="width220" v-model="obj.dialogForm.insuranceType" placeholder="请输入" />
                </el-form-item>
                <el-divider content-position="left">商保产品关联方案详情信息</el-divider>
                <el-table :data="obj.tableData" border>
                    <el-table-column type="expand">
                        <template #default="props">
                            <el-table :data="props.row.family" border>
                                <el-table-column label="产品名称" align="center" prop="productName" />
                                <el-table-column label="产品类型" align="center" prop="productType" />
                                <el-table-column label="基准保额是否依据说明" align="center" prop="isBasedOn" />
                                <el-table-column label="说明" align="center" prop="remark" />
                                <el-table-column label="基准保额(万元)" align="center" prop="basedAmount" />
                            </el-table>
                        </template>
                    </el-table-column>
                    <el-table-column label="序号" align="center" prop="id" />
                    <el-table-column label="供应商" align="center" prop="supplier" />
                    <el-table-column label="产品名称" align="center" prop="productName" />
                    <el-table-column label="产品类型" align="center" prop="productType" />
                    <el-table-column label="基准保额是否依据说明" align="center" width="170" prop="isBasedOn" />
                    <el-table-column label="说明" align="center" prop="remark" />
                    <el-table-column label="基准保额" align="center" prop="basedAmount" />
                    <el-table-column label="保额是否可以加倍" align="center" width="150" prop="isBasedOn" />
                    <el-table-column label="基准月付成本" align="center" width="130" prop="basedMonthCost" />
                    <el-table-column label="基准年付成本" align="center" width="130" prop="basedYearCost" />
                    <el-table-column label="保额加倍系数" align="center" width="130" prop="basedAmountMultiple" />
                    <el-table-column label="是否为必选项" align="center" width="130" prop="isRequired" />
                    <el-table-column label="基准月付指导价" align="center" width="130" prop="basedMonthPrice" />
                    <el-table-column label="基准年付指导价" align="center" width="130" prop="basedYearPrice" />
                    <el-table-column label="指导价系数" align="center" width="120" prop="priceCoefficient" />
                    <el-table-column label="保额(万元)" align="center" width="120" prop="basedAmount" />
                    <el-table-column label="保额倍数" align="center" prop="basedAmountMultiple" />
                    <el-table-column label="成本价" align="center" prop="basedMonthCost" />
                    <el-table-column label="指导价" align="center" prop="basedYearPrice" />
                </el-table>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="success" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 历史日志查看 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="60%" append-to-body draggable>
            <el-table :data="obj.historyLogData" border>
                <el-table-column label="订单编号" align="center" prop="orderId" width="120" />
                <el-table-column label="员工ID" align="center" prop="employeeId" width="100" />
                <el-table-column label="操作类型" align="center" prop="operationType" width="120" />
                <el-table-column label="备注" align="center" prop="remark" min-width="200" />
                <el-table-column label="创建人" align="center" prop="creator" width="100" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
            </el-table>
            <pagination v-show="obj.historyLogData.length > 0" :total="obj.historyLogData.length"
                v-model:page="obj.queryParams.pageNum" v-model:limit="obj.queryParams.pageSize"
                @pagination="getHistoryLogList" />
        </el-dialog>
        <!-- 修改雇员姓名或者手机号 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow3" width="35%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="雇员姓名" prop="name">
                    <el-input v-model="obj.dialogForm.name" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="手机号码" prop="phone">
                    <el-input v-model="obj.dialogForm.phone" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-input v-model="obj.dialogForm.customerName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="证件编号" prop="idNumber">
                    <el-input v-model="obj.dialogForm.idNumber" placeholder="请输入" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="success" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow3 = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 关联人员 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow4" width="50%" append-to-body draggable>
            <el-row class="mb8">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-row>
            <el-table :data="obj.relatedPersonData" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="关联人姓名" align="center" prop="name" width="120" />
                <el-table-column label="关联人证件类型" align="center" width="120">
                    <template #default="scope">
                        {{idTypeOptions.find(item => item.value === scope.row.idType)?.label || ''}}
                    </template>
                </el-table-column>
                <el-table-column label="关联人证件号" align="center" prop="idNumber" width="150" />
                <el-table-column label="关联人出生日期" align="center" prop="birthDate" width="120" />
                <el-table-column label="关联人性别" align="center" prop="gender" width="80" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="Order">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { id_type } = proxy.useDict("id_type");


// 处理状态选项
const processStatusOptions = [
    { value: '1', label: '待处理' },
    { value: '2', label: '处理中' },
    { value: '3', label: '已处理' },
    { value: '4', label: '处理失败' },
    { value: '5', label: '已取消' }
];

// 订单状态选项
const orderStatusOptions = [
    { value: '1', label: '草稿' },
    { value: '2', label: '待审核' },
    { value: '3', label: '已审核' },
    { value: '4', label: '已取消' },
    { value: '5', label: '已完成' }
];

// 证件类型选项
const idTypeOptions = [
    { value: '1', label: '身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '军官证' },
    { value: '4', label: '港澳通行证' },
    { value: '5', label: '其他' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: '', // 唯一号
        name: '', // 姓名
        idNumber: '', // 证件号码
        customerName: '', // 客户名称
        contractId: '', // 合同编号
        planId: '', // 方案编号
        orderId: '', // 订单编号
        chargeStartMonth: '', // 收费起始月
        billStartMonth: '', // 账单起始月
        processStatus: '', // 处理状态
        orderStatus: '', // 订单状态
        createTimeStart: '', // 订单创建时间开始
        createTimeEnd: '' // 订单创建时间结束
    }, // 查询表单
    rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        idNumber: [{ required: true, message: '请输入证件号码', trigger: 'blur' }],
        customerName: [{ required: true, message: '请选择客户名称', trigger: 'blur' }],
        contractId: [{ required: true, message: '请选择合同编号', trigger: 'blur' }],
        planId: [{ required: true, message: '请选择方案编号', trigger: 'blur' }],
    },
    total: 0, // 总条数

    tableData: [
        {
            id: 1,
            orderId: 'ORD20230501001',
            uniqueId: 'UID20230501001',
            name: '张三',
            idNumber: '110101199001011234',
            processStatus: '3',
            orderStatus: '5',
            customerName: '北京科技有限公司',
            contractId: 'CON20230501001',
            planId: 'PLAN20230501001',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            increaseConfirmTime: '2023-05-01 10:00:00',
            decreaseConfirmTime: '',
            createTime: '2023-05-01 09:30:00'
        },
        {
            id: 2,
            orderId: 'ORD20230502001',
            uniqueId: 'UID20230502001',
            name: '李四',
            idNumber: '110101199002022345',
            processStatus: '3',
            orderStatus: '5',
            customerName: '上海贸易有限公司',
            contractId: 'CON20230502001',
            planId: 'PLAN20230502001',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            increaseConfirmTime: '2023-05-02 11:20:00',
            decreaseConfirmTime: '',
            createTime: '2023-05-02 10:15:00'
        },
        {
            id: 3,
            orderId: 'ORD20230503001',
            uniqueId: 'UID20230503001',
            name: '王五',
            idNumber: '110101199003033456',
            processStatus: '3',
            orderStatus: '5',
            customerName: '广州电子有限公司',
            contractId: 'CON20230503001',
            planId: 'PLAN20230503001',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            increaseConfirmTime: '2023-05-03 14:30:00',
            decreaseConfirmTime: '',
            createTime: '2023-05-03 13:45:00'
        },
        {
            id: 4,
            orderId: 'ORD20230504001',
            uniqueId: 'UID20230504001',
            name: '赵六',
            idNumber: '110101199004044567',
            processStatus: '1',
            orderStatus: '2',
            customerName: '深圳科技有限公司',
            contractId: 'CON20230504001',
            planId: 'PLAN20230504001',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            increaseConfirmTime: '',
            decreaseConfirmTime: '',
            createTime: '2023-05-04 09:10:00'
        },
        {
            id: 5,
            orderId: 'ORD20230505001',
            uniqueId: 'UID20230505001',
            name: '钱七',
            idNumber: '110101199005055678',
            processStatus: '4',
            orderStatus: '4',
            customerName: '成都信息有限公司',
            contractId: 'CON20230505001',
            planId: 'PLAN20230505001',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            increaseConfirmTime: '',
            decreaseConfirmTime: '2023-05-10 15:20:00',
            createTime: '2023-05-05 10:30:00'
        }
    ], // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    dialogShow2: false, // 历史日志弹出框
    dialogShow3: false, // 修改雇员信息弹出框
    dialogShow4: false, // 关联人员弹出框
    ids: [], // 选中的id
    title: "", // 标题

    // 关联人员数据
    relatedPersonData: [
        {
            id: 1,
            name: '张小三',
            idType: '1',
            idNumber: '110101201001011234',
            birthDate: '2010-01-01',
            gender: '男'
        },
        {
            id: 2,
            name: '张小四',
            idType: '1',
            idNumber: '110101201202022345',
            birthDate: '2012-02-02',
            gender: '女'
        }
    ],

    // 历史日志数据
    historyLogData: [
        {
            id: 1,
            orderId: 'ORD20230501001',
            employeeId: 'EMP001',
            operationType: '创建订单',
            remark: '新增订单',
            creator: '管理员',
            createTime: '2023-05-01 09:30:00'
        },
        {
            id: 2,
            orderId: 'ORD20230501001',
            employeeId: 'EMP001',
            operationType: '修改订单',
            remark: '修改客户信息',
            creator: '管理员',
            createTime: '2023-05-01 10:00:00'
        },
        {
            id: 3,
            orderId: 'ORD20230501001',
            employeeId: 'EMP001',
            operationType: '确认订单',
            remark: '订单审核通过',
            creator: '管理员',
            createTime: '2023-05-01 10:30:00'
        }
    ]
})

/**
 * 获取处理状态标签类型
 * @param {string} status 状态值
 * @returns {string} 标签类型
 */
function getProcessStatusType(status) {
    switch (status) {
        case '1': return 'info';
        case '2': return 'warning';
        case '3': return 'success';
        case '4': return 'danger';
        case '5': return '';
        default: return '';
    }
}

/**
 * 获取处理状态标签文本
 * @param {string} status 状态值
 * @returns {string} 标签文本
 */
function getProcessStatusLabel(status) {
    const option = processStatusOptions.find(item => item.value === status);
    return option ? option.label : '未知状态';
}

/**
 * 获取订单状态标签类型
 * @param {string} status 状态值
 * @returns {string} 标签类型
 */
function getOrderStatusType(status) {
    switch (status) {
        case '1': return '';
        case '2': return 'warning';
        case '3': return 'success';
        case '4': return 'info';
        case '5': return 'success';
        default: return '';
    }
}

/**
 * 获取订单状态标签文本
 * @param {string} status 状态值
 * @returns {string} 标签文本
 */
function getOrderStatusLabel(status) {
    const option = orderStatusOptions.find(item => item.value === status);
    return option ? option.label : '未知状态';
}

/** 列表 */
function getList() {
    obj.loading = true;

    // 模拟数据已经定义在 obj.tableData 中
    setTimeout(() => {
        obj.loading = false;
        obj.total = obj.tableData.length;
    }, 500);
}

// 表单重置
function reset() {
    obj.dialogForm = {};
    if (proxy.$refs["formRef"]) {
        proxy.$refs["formRef"].resetFields();
    }
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}

/** 修改按钮操作 */
function handleEdit() {
    if (obj.single) {
        proxy.$modal.msgWarning('请选择一条记录进行修改');
        return;
    }
    reset();
    const selectedOrder = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedOrder) {
        obj.dialogForm = JSON.parse(JSON.stringify(selectedOrder));
        obj.dialogShow = true;
        obj.title = "修改";
    }
}

/** 详情按钮操作 */
function handleDetail(row) {
    reset();
    const orderDetail = row || (obj.single ? null : obj.tableData.find(item => item.id === obj.ids[0]));
    if (!orderDetail) {
        proxy.$modal.msgWarning('请选择一条记录查看详情');
        return;
    }
    obj.dialogForm = JSON.parse(JSON.stringify(orderDetail));
    obj.dialogShow = true;
    obj.title = "详情";
}

/** 提交确认按钮操作 */
function handleSubmit() {
    if (obj.multiple) {
        proxy.$modal.msgWarning('请选择要提交的订单');
        return;
    }

    proxy.$modal.confirm('确定要提交选中的订单吗？').then(() => {
        proxy.$modal.msgSuccess('提交成功');
        getList();
    }).catch(() => { });
}

/** 减员申请按钮操作 */
function handlePersonnelReduction() {
    if (obj.multiple) {
        proxy.$modal.msgWarning('请选择要减员的订单');
        return;
    }

    proxy.$modal.confirm('确定要对选中的订单进行减员申请吗？').then(() => {
        proxy.$modal.msgSuccess('减员申请提交成功');
        getList();
    }).catch(() => { });
}

/** 数据导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 历史日志查看按钮操作 */
function handleHistory() {
    reset();
    obj.dialogShow2 = true;
    obj.title = "历史日志查看";
}

/** 修改雇员姓名或者手机号按钮操作 */
function handleUpdateEmployeeNameOrPhone() {
    if (obj.single) {
        proxy.$modal.msgWarning('请选择一条记录进行修改');
        return;
    }
    reset();
    obj.dialogShow3 = true;
    obj.title = "修改雇员姓名或者手机号";
}

/** 修改证件编号按钮操作 */
function handleUpdateCertificateNumber() {
    if (obj.single) {
        proxy.$modal.msgWarning('请选择一条记录进行修改');
        return;
    }
    reset();
    obj.dialogShow3 = true;
    obj.title = "修改证件编号";
}

/** 修改关联人员按钮操作 */
function handleUpdateAssociatedPerson() {
    if (obj.single) {
        proxy.$modal.msgWarning('请选择一条记录进行修改');
        return;
    }
    reset();
    obj.dialogShow4 = true;
    obj.title = "修改关联人员";
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

getList();
</script>
<style lang="scss" scoped></style>