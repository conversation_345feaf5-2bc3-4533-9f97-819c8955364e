<!-- 订单数据检查记录 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">

            <el-form-item label="订单编号:" prop="code">
                <el-input class="width220" v-model="obj.queryParams.code" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="雇员编号:" prop="employeeCode">
                <el-input class="width220" v-model="obj.queryParams.employeeCode" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable />
            </el-form-item>

            <el-form-item label="检查时间:" prop="checkDate">
                <el-date-picker class="width220" v-model="obj.queryParams.checkDate" type="date" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="startOrderDataCheckBtn">开始订单数据检查</el-button>
            </el-col>

            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="order-data-check-record" />
            <el-button type="primary" plain @click="handleExport">导出</el-button>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column v-if="obj.selectedColumns.includes('code')" label="订单编号" align="center" prop="code" />
            <el-table-column v-if="obj.selectedColumns.includes('groupCode')" label="社保组编码" align="center"
                prop="groupCode" />
            <el-table-column v-if="obj.selectedColumns.includes('ratioCode')" label="社保比例编码" align="center"
                prop="ratioCode" />
            <el-table-column v-if="obj.selectedColumns.includes('employeeCode')" label="雇员编号" align="center"
                prop="employeeCode" />
            <el-table-column v-if="obj.selectedColumns.includes('employeeName')" label="雇员姓名" align="center"
                prop="employeeName" />
            <el-table-column v-if="obj.selectedColumns.includes('checkDate')" label="检查记录日期" align="center"
                prop="checkDate" />
            <el-table-column v-if="obj.selectedColumns.includes('creators')" label="创建人" align="center"
                prop="creators" />
            <el-table-column v-if="obj.selectedColumns.includes('createTime')" label="创建时间" align="center"
                prop="createTime" />
            <el-table-column v-if="obj.selectedColumns.includes('creators')" label="修改人" align="center"
                prop="creators" />
            <el-table-column v-if="obj.selectedColumns.includes('updateTime')" label="修改时间" align="center"
                prop="updateTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="OrderDataCheckRecord">
import { listScale } from "@/api/reonApi/scale";


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数

    tableData: [],//列表

    selectedColumns: ['code', 'groupCode', 'ratioCode', 'employeeCode', 'employeeName', 'checkDate', 'creators', 'createTime', 'creators', 'updateTime'],//选中的列
    columnOptions: [
        { label: '订单编号', prop: 'code' },
        { label: '社保组编码', prop: 'groupCode' },
        { label: '社保比例编码', prop: 'ratioCode' },
        { label: '雇员编号', prop: 'employeeCode' },
        { label: '雇员姓名', prop: 'employeeName' },
        { label: '检查记录日期', prop: 'checkDate' },
        { label: '创建人', prop: 'creators' },
        { label: '创建时间', prop: 'createTime' },
        { label: '修改人', prop: 'creators' },
        { label: '修改时间', prop: 'updateTime' },
    ]
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.tableData.forEach(item => {
            item.code = item.cityName;
        });
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 开始订单数据检查 */
function startOrderDataCheckBtn() {
    console.log(obj.queryParams);
}

/** 导出 */
function handleExport() {
    console.log(obj.queryParams);
}

getList();

// 初始化时，如果没有缓存的列设置，则使用默认值
onMounted(() => {
    if (!obj.selectedColumns.length) {
        obj.selectedColumns = obj.columnOptions.map(col => col.prop)
    }
})
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>