// 全局组件样式 - 与首页风格保持一致
// 主要包含：弹窗、表单、表格的统一样式

// ==================== 弹窗样式 ====================
.el-dialog {
  border-radius: 15px !important;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2) !important;

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px 25px !important;
    margin: 0 !important;
    border-bottom: none !important;

    .el-dialog__title {
      color: white !important;
      font-size: 18px !important;
      font-weight: 600 !important;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white !important;
        font-size: 20px !important;

        &:hover {
          color: rgba(255, 255, 255, 0.8) !important;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 25px !important;
    background: #fafbfc;
  }

  .el-dialog__footer {
    padding: 20px 25px !important;
    background: #fafbfc;
    border-top: 1px solid #e8e8e8;
  }
}

// ==================== 表单样式 ====================
.el-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      color: #2c3e50 !important;
      font-weight: 500 !important;
      font-size: 14px;
    }

    .el-form-item__content {
      .el-input {
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            border-color: #667eea;
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
          }

          &.is-focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
          }
        }
      }

      .el-select {
        .el-select__wrapper {
          border-radius: 8px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            border-color: #667eea;
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
          }

          &.is-focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
          }
        }
      }

      .el-textarea {
        .el-textarea__inner {
          border-radius: 8px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            border-color: #667eea;
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
          }

          &:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
          }
        }
      }

      .el-date-editor {
        .el-input__wrapper {
          border-radius: 8px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            border-color: #667eea;
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
          }

          &.is-focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
          }
        }
      }
    }
  }
}

// ==================== 按钮样式 ====================
.el-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  &.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    color: #dddddd !important;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
    }
  }

  &.el-button--success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border: none !important;
    color: #e0f084 !important;

    &:hover {
      background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%) !important;
    }
  }

  &.el-button--warning {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
    border: none !important;
    color: #8b4513 !important;

    &:hover {
      background: linear-gradient(135deg, #ffd89b 0%, #fa9f7a 100%) !important;
    }
  }

  &.el-button--danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    border: none !important;
    color: #ee5e5e !important;

    &:hover {
      background: linear-gradient(135deg, #ff7b7f 0%, #fdb5e7 100%) !important;
    }
  }

  &.el-button--info {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
    border: none !important;
    color: #2c3e50 !important;

    &:hover {
      background: linear-gradient(135deg, #8ee5e1 0%, #fcc4d4 100%) !important;
    }
  }
}

// ==================== 表格样式 ====================
.el-table {
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #e8e8e8 !important;

  .el-table__header-wrapper {
    .el-table__header {
      th {
        background: linear-gradient(180deg, #f8f9fa 0%, #f1f3f4 100%) !important;
        font-weight: 600 !important;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      tr {
        transition: all 0.3s ease;

        td {
          font-size: 14px;
        }

        &:nth-child(even) {
          background-color: #fafbfc;
        }
      }
    }
  }

  // 表格内的链接样式
  .el-link {
    font-weight: 500 !important;

    &.el-link--primary {
      color: #667eea !important;

      &:hover {
        color: #5a6fd8 !important;
      }
    }
  }

  // 表格内的按钮样式
  .el-button {
    &.is-text {
      padding: 6px 12px !important;
      font-size: 13px !important;
      border-radius: 6px !important;
    }
  }
}

// ==================== 分页样式 ====================
.el-pagination {
  .el-pager {
    li {
      border-radius: 6px !important;
      margin: 0 2px !important;
      transition: all 0.3s ease !important;

      &.is-active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        border: none !important;
      }

      &:hover {
        background-color: #f0f9ff !important;
        transform: translateY(-1px);
      }
    }
  }

  .btn-prev,
  .btn-next {
    border-radius: 6px !important;
    transition: all 0.3s ease !important;

    &:hover {
      background-color: #f0f9ff !important;
      transform: translateY(-1px);
    }
  }
}

// ==================== 卡片样式增强 ====================
.el-card {
  border-radius: 15px !important;
  border: none !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12) !important;
  }

  .el-card__header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border-bottom: none !important;
    padding: 18px 25px !important;
    color: white !important;
  }

  .el-card__body {
    padding: 25px !important;
    background: white !important;
  }
}

// ==================== 消息提示样式 ====================
.el-message {
  border-radius: 10px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;

  &.el-message--success {
    // background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border: none !important;
  }

  &.el-message--warning {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
    border: none !important;
  }

  &.el-message--error {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    border: none !important;
  }

  &.el-message--info {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
    border: none !important;
  }
}

// ==================== 下拉菜单样式 ====================
.el-select-dropdown {
  border-radius: 10px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #e8e8e8 !important;

  .el-select-dropdown__item {
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #f0f9ff 0%, #e3f2fd 100%) !important;
      color: #667eea !important;
    }

    &.is-selected {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      color: white !important;
    }
  }
}

// ==================== 日期选择器样式 ====================
.el-date-picker {
  border-radius: 10px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;

  .el-date-picker__header {
    .el-date-picker__header-label {
      color: #667eea !important;
      font-weight: 600;
    }
  }

  .el-date-table {
    td {
      &.today {
        .el-date-table-cell__text {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
          color: white !important;
        }
      }

      &.current {
        .el-date-table-cell__text {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
          color: white !important;
        }
      }
    }
  }
}

// ==================== 响应式调整 ====================
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 0 auto !important;
  }

  .el-table {
    font-size: 12px !important;

    .el-table__header-wrapper {
      .el-table__header {
        th {
          padding: 12px 8px !important;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          td {
            padding: 10px 8px !important;
          }
        }
      }
    }
  }
}

// ==================== 动画效果 ====================
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 为新打开的弹窗添加动画
.el-dialog {
  animation: fadeInUp 0.3s ease-out;
}

// 为表格行添加进入动画
.el-table__body {
  tr {
    animation: slideInRight 0.3s ease-out;
  }
}

// ==================== 表单验证样式 ====================
.el-form-item {
  &.is-error {
    .el-input {
      .el-input__wrapper {
        border-color: #ff6b6b !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2) !important;
      }
    }

    .el-select {
      .el-select__wrapper {
        border-color: #ff6b6b !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2) !important;
      }
    }

    .el-textarea {
      .el-textarea__inner {
        border-color: #ff6b6b !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2) !important;
      }
    }
  }

  .el-form-item__error {
    color: #ff6b6b !important;
    font-size: 12px !important;
    margin-top: 4px;
  }
}

// ==================== 加载状态样式 ====================
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8) !important;

  .el-loading-spinner {
    .el-loading-text {
      color: #667eea !important;
      font-weight: 500;
    }

    .circular {
      stroke: #667eea !important;
    }
  }
}

// ==================== 空状态样式 ====================
.el-empty {
  .el-empty__description {
    color: #999 !important;
    font-size: 14px;
  }
}

// ==================== 标签样式 ====================
.el-tag {
  border-radius: 12px !important;
  border: none !important;
  font-weight: 500 !important;

  &.el-tag--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
  }

  &.el-tag--success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    color: white !important;
  }

  &.el-tag--warning {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
    color: #8b4513 !important;
  }

  &.el-tag--danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
    color: rgb(255, 255, 255) !important;
  }
}