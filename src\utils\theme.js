/**
 * 处理主题样式
 * @param {string} theme - 主题颜色
 */
export function handleThemeStyle(theme) {
  // 设置全局变量--el-color-primary的值为传入的主题颜色
  document.documentElement.style.setProperty("--el-color-primary", theme);
  // 循环生成9个变浅的主题颜色，并设置为全局变量
  for (let i = 1; i <= 9; i++) {
    document.documentElement.style.setProperty(
      `--el-color-primary-light-${i}`,
      `${getLightColor(theme, i / 10)}`
    );
  }
  // 循环生成9个变深的主题颜色，并设置为全局变量
  for (let i = 1; i <= 9; i++) {
    document.documentElement.style.setProperty(
      `--el-color-primary-dark-${i}`,
      `${getDarkColor(theme, i / 10)}`
    );
  }
}

/**
 * 将十六进制颜色值转换为RGB颜色值
 * @param {string} str - 十六进制颜色值
 * @returns {Array} - RGB颜色值数组
 */
export function hexToRgb(str) {
  // 去除颜色值中的#号
  str = str.replace("#", "");
  // 使用正则表达式匹配每两个字符，得到RGB颜色值的数组
  let hexs = str.match(/../g);
  // 将每个十六进制颜色值转换为十进制数值
  for (let i = 0; i < 3; i++) {
    hexs[i] = parseInt(hexs[i], 16);
  }
  // 返回RGB颜色值数组
  return hexs;
}

/**
 * 将RGB颜色值转换为十六进制颜色值
 * @param {number} r - 红色通道值（0-255）
 * @param {number} g - 绿色通道值（0-255）
 * @param {number} b - 蓝色通道值（0-255）
 * @returns {string} - 十六进制颜色值
 */
export function rgbToHex(r, g, b) {
  // 将RGB通道值转换为十六进制字符串
  let hexs = [r.toString(16), g.toString(16), b.toString(16)];
  // 如果十六进制字符串长度为1，则在前面补0
  for (let i = 0; i < 3; i++) {
    if (hexs[i].length == 1) {
      hexs[i] = `0${hexs[i]}`;
    }
  }
  // 返回十六进制颜色值
  return `#${hexs.join("")}`;
}

/**
 * 变浅颜色值
 * @param {string} color - 原始颜色值（十六进制格式）
 * @param {number} level - 变浅的程度（0-1之间的小数）
 * @returns {string} - 变浅后的颜色值（十六进制格式）
 */
export function getLightColor(color, level) {
  // 将十六进制颜色值转换为RGB颜色值
  let rgb = hexToRgb(color);
  // 遍历RGB颜色值的每个通道
  for (let i = 0; i < 3; i++) {
    // 计算变浅后的通道值
    rgb[i] = Math.floor((255 - rgb[i]) * level + rgb[i]);
  }
  // 将RGB颜色值转换回十六进制格式并返回
  return rgbToHex(rgb[0], rgb[1], rgb[2]);
}

/**
 * 变深颜色值
 * @param {string} color - 原始颜色值（十六进制格式）
 * @param {number} level - 变深的程度（0-1之间的小数）
 * @returns {string} - 变深后的颜色值（十六进制格式）
 */
export function getDarkColor(color, level) {
  // 将十六进制颜色值转换为RGB颜色值
  let rgb = hexToRgb(color);
  // 遍历RGB颜色值的每个通道
  for (let i = 0; i < 3; i++) {
    // 计算变深后的通道值
    rgb[i] = Math.floor(rgb[i] * (1 - level));
  }
  // 将RGB颜色值转换回十六进制格式并返回
  return rgbToHex(rgb[0], rgb[1], rgb[2]);
}
