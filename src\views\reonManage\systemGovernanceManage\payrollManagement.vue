<!-- 薪资计算治理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerId">
                <el-select class="width220" v-model="obj.queryParams.customerId" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资类别名称:" prop="salaryTypeName">
                <el-select class="width220" v-model="obj.queryParams.salaryTypeName" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="发放编号/名称:" prop="issueNumber">
                <el-select class="width220" v-model="obj.queryParams.issueNumber" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资所属月起:" prop="belongMonthStart">
                <el-select class="width220" v-model="obj.queryParams.belongMonthStart" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资计税月:" prop="taxMonth">
                <el-date-picker v-model="obj.queryParams.taxMonth" type="date" placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="发放状态:" prop="issueStatus">
                <el-input class="width220" readonly @click="obj.sendOrdersShow = true"
                    v-model="obj.queryParams.issueStatus" placeholder="请输入唯一号" />
            </el-form-item>
            <el-form-item label="客户账单月起:" prop="customerBillMonthStart">
                <el-select class="width220" v-model="obj.queryParams.customerBillMonthStart" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户账单月止:" prop="customerBillMonthEnd">
                <el-input class="width220" v-model="obj.queryParams.customerBillMonthEnd" placeholder="请输入唯一号" />
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectCustomer">
                <el-date-picker v-model="obj.queryParams.projectCustomer" type="date" placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="薪资客服:" prop="salaryCustomer">
                <el-date-picker v-model="obj.queryParams.salaryCustomer" type="date" placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="创建人:" prop="createBy">
                <el-select class="width220" v-model="obj.queryParams.createBy" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" icon="Download" plain @click="handleExport">导出</el-button>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain :disabled="obj.single" @click="handleCalculate">计算</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain :disabled="obj.multiple" @click="handleBatchCalculate">批量计算</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain :disabled="obj.single" icon='View' @click="handleDetail">查看</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="发放编号" align="center" prop="issueNumber" />
            <el-table-column label="发放名称" align="center" prop="issueName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="薪资类别名称" align="center" prop="salaryTypeName" width="120" />
            <el-table-column label="所属月" align="center" prop="belongMonth" />
            <el-table-column label="工资计税月" align="center" prop="taxMonth" width="120" />
            <el-table-column label="客户账单月" align="center" prop="customerBillMonth" width="120" />
            <el-table-column label="是否确认" align="center" prop="isConfirm" />
            <el-table-column label="数据确认时间" align="center" prop="confirmTime" width="120" />
            <el-table-column label="发放状态" align="center" prop="issueStatus" />
            <el-table-column label="缓发人数" align="center" prop="slowIssueCount" />
            <el-table-column label="未发放人数" align="center" prop="notIssueCount" width="120" />
            <el-table-column label="发放人数" align="center" prop="issueCount" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="项目客服" align="center" prop="projectCustomer" />
            <el-table-column label="导入历史" align="center" prop="importHistory" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <PersonnelDetails v-model:dialogShow="obj.dialogShow" :tableData="obj.tableData" menuName="payrollManagement" />
    </div>
</template>


<script setup name="PayrollManagement">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";
import PersonnelDetails from "@/views/reonManage/components/dialog/personnelDetails.vue";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    showMore: false,//显示更多
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {},//导入表单
    dialogShow: false,//导入弹窗
    ids: [],//选中的id
})


/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        obj.tableData = [
            {
                issueNumber: '123',
                issueName: '李磊',
                customerName: '张三',
                salaryTypeName: '工资',
                belongMonth: '2025-01',
                taxMonth: '2025-01',
                customerBillMonth: '2025-01',
                isConfirm: '是',
                confirmTime: '2025-01-01',
                issueStatus: '已发放',
                slowIssueCount: '1',
            }
        ];
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}
/** 计算 */
function handleCalculate() {
    proxy.$modal.confirm('确认要计算吗?').then(() => {
        console.log('handleCalculate')
    }).catch(() => { });
}
/** 批量计算 */
function handleBatchCalculate() {
    proxy.$modal.confirm('确认要批量计算吗?').then(() => {
        console.log('handleBatchCalculate')
    }).catch(() => { });
}
/** 查看 */
function handleDetail() {
    obj.dialogShow = true;
}
/** 导出 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped>
.content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out;
}

.content.expanded {
    max-height: 1000px;
    /* 设置一个足够大的值 */
}

:deep(.el-table__body tr.current-row>td.el-table__cell) {
    background-color: aqua;
}
</style>