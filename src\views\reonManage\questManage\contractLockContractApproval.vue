<!-- 契约锁合同审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="提交人:" prop="submitter">
                <el-select class="width220" v-model="obj.queryParams.submitter" placeholder="请选择" clearable>
                    <el-option v-for="item in submitterOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="业务分类:" prop="businessType">
                <el-select class="width220" v-model="obj.queryParams.businessType" placeholder="请选择" clearable>
                    <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="员工姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="创建时间起始:" prop="startTime">
                <el-date-picker class="width220" v-model="obj.queryParams.startTime" type="date" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="创建时间截止:" prop="endTime">
                <el-date-picker class="width220" v-model="obj.queryParams.endTime" type="date" placeholder="请选择" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="订单编号" align="center" prop="orderNo" />
            <el-table-column label="业务分类名称" align="center" prop="businessTypeName" />
            <el-table-column label="员工姓名" align="center" prop="employeeName" />
            <el-table-column label="提交人" align="center" prop="submitter" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="操作" align="center" width="180">
                <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="ContractLockContractApproval">
import { listApproval } from '@/api/reonApi/approval';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 提交人选项
const submitterOptions = [
    { value: '1', label: '张三' },
    { value: '2', label: '李四' },
    { value: '3', label: '王五' },
    { value: '4', label: '赵六' },
    { value: '5', label: '钱七' }
];

// 业务分类选项
const businessTypeOptions = [
    { value: '1', label: '劳动合同' },
    { value: '2', label: '服务合同' },
    { value: '3', label: '采购合同' },
    { value: '4', label: '其他合同' }
];

const obj = reactive({
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        submitter: null,
        businessType: null,
        employeeName: null,
        orderNo: null,
        startTime: null,
        endTime: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})




/** 获取列表数据 */
function getList() {
    obj.loading = true;
    listApproval(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 查看详情 */
function handleDetail(row) {
    proxy.$modal.msgInfo(`查看ID为${row.id}的详细信息`);
}


getList();
</script>
<style lang="scss" scoped></style>