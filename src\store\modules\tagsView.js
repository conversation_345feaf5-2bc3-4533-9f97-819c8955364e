const useTagsViewStore = defineStore("tags-view", {
  state: () => ({
    visitedViews: [],
    cachedViews: [],
    iframeViews: [],
  }),
  actions: {
    /**
     * 添加视图
     *
     * 该方法用于将给定的视图添加到访问过的视图列表和缓存视图列表中
     * 这在管理视图历史和优化视图渲染性能方面非常重要
     *
     * @param {Object} view - 要添加的视图对象
     */
    addView(view) {
      // 将视图添加到访问过的视图列表中
      this.addVisitedView(view);
      // 将视图添加到缓存视图列表中
      this.addCachedView(view);
    },
    /**
     * 添加iframe视图
     * 如果视图的路径已经存在，则不添加
     * @param {Object} view - 要添加的视图对象，包含路径和元数据
     */
    addIframeView(view) {
      // 检查是否已有相同的路径，如果有，则不进行任何操作
      if (this.iframeViews.some((v) => v.path === view.path)) return;

      // 将视图添加到iframeViews数组中，并确保每个视图都有标题
      // 如果视图的元数据中没有标题，则默认标题为'no-name'
      this.iframeViews.push(
        Object.assign({}, view, {
          title: view.meta.title || "no-name",
        })
      );
    },
    /**
     * 添加访问过的视图
     *
     * 此函数用于跟踪用户访问过的视图，避免重复记录，并确保每个视图都有标题
     * 如果视图已访问过（根据路径判断），则不执行任何操作
     * 否则，将视图信息复制后添加到访问过的视图列表中，并设置标题
     *
     * @param {Object} view - 代表视图的对象，应包含路径和元数据
     */
    addVisitedView(view) {
      // 检查visitedViews数组中是否已有相同路径的视图，如果有，则不执行后续操作
      if (this.visitedViews.some((v) => v.path === view.path)) return;

      // 将视图对象复制后添加到visitedViews数组中，并为其添加标题
      // 标题来自视图的meta属性中的title字段，如果没有提供，则默认为'no-name'
      this.visitedViews.push(
        Object.assign({}, view, {
          title: view.meta.title || "no-name",
        })
      );
    },
    /**
     * 添加缓存的视图
     *
     * 该方法用于将视图名称添加到缓存列表中，以实现视图的缓存功能它首先检查视图名称是否已经存在于缓存列表中，
     * 如果存在，则不执行任何操作如果视图不存在，并且视图没有设置noCache元数据，则将视图名称添加到缓存列表中
     *
     * @param {Object} view - 待添加到缓存的视图对象，包含视图的名称和元数据
     */
    addCachedView(view) {
      // 检查缓存列表中是否已存在该视图名称，如果存在，则不执行后续操作
      if (this.cachedViews.includes(view.name)) return;

      // 检查视图是否标记为不缓存，如果没有标记，则将视图名称添加到缓存列表中
      if (!view.meta.noCache) {
        this.cachedViews.push(view.name);
      }
    },
    /**
     * 删除视图
     * 该函数通过异步方式删除指定的视图，并更新相关的视图列表
     * @param {Object} view - 需要删除的视图对象
     * @returns {Promise} - 返回一个Promise对象，该对象在视图删除成功后解析，包含更新后的视图列表
     */
    delView(view) {
      return new Promise((resolve) => {
        // 删除访问过的视图记录
        this.delVisitedView(view);
        // 删除缓存的视图记录
        this.delCachedView(view);
        // 解析Promise，返回更新后的视图列表
        resolve({
          visitedViews: [...this.visitedViews],
          cachedViews: [...this.cachedViews],
        });
      });
    },
    /**
     * 删除已访问视图
     * 该方法用于从已访问视图列表中移除指定的视图，并更新iframe视图列表
     * @param {Object} view - 需要删除的视图对象，包含路径信息
     * @returns {Promise} 返回一个Promise对象，该对象在视图删除成功后解析，包含更新后的已访问视图列表
     */
    delVisitedView(view) {
      return new Promise((resolve) => {
        // 遍历已访问视图列表，寻找与指定视图路径匹配的项
        for (const [i, v] of this.visitedViews.entries()) {
          if (v.path === view.path) {
            // 找到匹配项后，从列表中移除该项
            this.visitedViews.splice(i, 1);
            break;
          }
        }
        // 更新iframe视图列表，移除与指定视图路径匹配的项
        this.iframeViews = this.iframeViews.filter(
          (item) => item.path !== view.path
        );
        // 解析Promise，返回更新后的已访问视图列表
        resolve([...this.visitedViews]);
      });
    },
    /**
     * 删除iframe视图
     * 该方法用于从iframeViews数组中移除指定的视图，并返回更新后的数组
     * 使用filter方法创建一个新数组，排除与指定视图路径相同的所有视图
     *
     * @param {Object} view - 要删除的视图对象，包含路径信息
     * @returns {Promise<Array<Object>>} - 返回一个Promise对象，解析为更新后的iframe视图数组
     */
    delIframeView(view) {
      return new Promise((resolve) => {
        // 过滤iframeViews数组，移除与给定视图路径匹配的项
        this.iframeViews = this.iframeViews.filter(
          (item) => item.path !== view.path
        );
        // 解析Promise，返回更新后的iframe视图数组的副本
        resolve([...this.iframeViews]);
      });
    },
    /**
     * 删除缓存的视图
     *
     * 本函数用于从缓存中删除指定的视图名称它通过接收视图名称作为参数，
     * 在cachedViews数组中找到并移除该视图名称，然后返回更新后的缓存视图数组
     *
     * @param {Object} view - 要删除的视图对象，其名称属性用于在缓存中查找
     * @returns {Promise} 返回一个Promise对象，该对象在视图被删除后解析，
     * 并包含更新后的缓存视图数组
     */
    delCachedView(view) {
      return new Promise((resolve) => {
        // 查找视图名称在缓存数组中的索引
        const index = this.cachedViews.indexOf(view.name);
        // 如果找到视图，则从缓存数组中移除
        index > -1 && this.cachedViews.splice(index, 1);
        // 解析Promise，返回更新后的缓存视图数组
        resolve([...this.cachedViews]);
      });
    },
    /**
     * 删除其他视图
     * 该函数创建并返回一个新的Promise，该Promise在删除其他视图的操作完成后解析
     * 主要目的是清除除给定视图外的其他视图，包括已访问的视图和缓存的视图
     *
     * @param {Object} view - 需要保留的视图对象
     * @returns {Promise} 返回一个Promise，该Promise解析为一个包含当前已访问视图和缓存视图副本的对象
     */
    delOthersViews(view) {
      return new Promise((resolve) => {
        // 删除其他已访问视图
        this.delOthersVisitedViews(view);
        // 删除其他缓存视图
        this.delOthersCachedViews(view);
        // 解析Promise，返回包含当前已访问视图和缓存视图副本的对象
        resolve({
          visitedViews: [...this.visitedViews],
          cachedViews: [...this.cachedViews],
        });
      });
    },
    /**
     * 删除其他访问过的视图
     * 该方法用于过滤并保留特定视图路径的访问记录和iframe视图
     * @param {Object} view - 当前视图对象，包含路径信息
     * @returns {Promise<Array<Object>>} - 返回一个Promise，解析为过滤后的访问视图数组
     */
    delOthersVisitedViews(view) {
      return new Promise((resolve) => {
        // 过滤visitedViews数组，仅保留带有affix元数据或路径与当前视图路径相同的视图
        this.visitedViews = this.visitedViews.filter((v) => {
          return v.meta.affix || v.path === view.path;
        });
        // 过滤iframeViews数组，仅保留路径与当前视图路径相同的iframe视图
        this.iframeViews = this.iframeViews.filter(
          (item) => item.path === view.path
        );
        // 解析Promise，返回过滤后的visitedViews数组副本
        resolve([...this.visitedViews]);
      });
    },
    /**
     * 删除其他缓存视图
     * 该方法旨在从缓存视图列表中移除指定视图之外的所有视图
     * 它首先尝试找到指定视图的索引，如果找到，则保留包括该视图在内的后续视图
     * 如果未找到指定视图，则清空缓存视图列表
     *
     * @param {Object} view - 要保留的视图对象
     * @returns {Promise} 返回一个Promise对象，该对象在操作完成后解析为更新后的缓存视图列表
     */
    delOthersCachedViews(view) {
      return new Promise((resolve) => {
        // 尝试在缓存视图列表中找到指定视图的索引
        const index = this.cachedViews.indexOf(view.name);
        // 如果找到指定视图，只保留从该视图开始到列表末尾的视图
        if (index > -1) {
          this.cachedViews = this.cachedViews.slice(index, index + 1);
        } else {
          // 如果未找到指定视图，清空缓存视图列表
          this.cachedViews = [];
        }
        // 解析Promise，返回更新后的缓存视图列表的副本
        resolve([...this.cachedViews]);
      });
    },
    /**
     * 删除所有视图
     *
     * 此函数旨在从两个方面删除指定的视图：已访问的视图和缓存的视图
     * 它首先通过调用`delAllVisitedViews`和`delAllCachedViews`方法来执行删除操作
     * 然后，它返回一个新的Promise对象，该对象在删除操作完成后解析
     * 解析值包含更新后的已访问视图和缓存视图数组
     *
     * @param {Object} view - 要删除的视图对象
     * @returns {Promise} 返回一个Promise，该Promise在视图删除后解析，包含更新后的视图数组
     */
    delAllViews(view) {
      return new Promise((resolve) => {
        // 删除所有已访问的视图
        this.delAllVisitedViews(view);
        // 删除所有缓存的视图
        this.delAllCachedViews(view);
        // 解析Promise，返回包含更新后的视图数组的对象
        resolve({
          visitedViews: [...this.visitedViews],
          cachedViews: [...this.cachedViews],
        });
      });
    },
    /**
     * 删除所有访问过的视图
     *
     * 此方法用于清除所有已访问的视图，但保留那些标记为固定（affix）的视图它通过过滤当前访问过的视图列表，
     * 移除不固定的所有视图，并返回一个新的只包含固定视图的列表同时，它清空了所有的iframe视图
     *
     * @param {Object} view - 当前视图对象，用于触发该函数，实际在函数内部未使用
     * @returns {Promise<Array<Object>>} 返回一个Promise对象，解析为新的只包含固定视图的访问视图列表
     */
    delAllVisitedViews(view) {
      return new Promise((resolve) => {
        // 过滤出所有固定（affix）的视图
        const affixTags = this.visitedViews.filter((tag) => tag.meta.affix);
        // 更新访问过的视图为仅包含固定视图的列表
        this.visitedViews = affixTags;
        // 清空所有的iframe视图
        this.iframeViews = [];
        // 解析Promise，返回新的访问视图列表
        resolve([...this.visitedViews]);
      });
    },
    /**
     * 删除所有缓存的视图
     *
     * 该函数用于清空当前缓存的所有视图它通过重置cachedViews数组为空，然后返回一个新的、空的视图缓存列表
     * 主要用于在需要的时候清除所有已经缓存的视图，以便释放内存或重新加载视图
     *
     * @param {Object} view - 视图对象，表示当前操作的视图
     * @returns {Promise<Array<Object>>} 返回一个Promise对象，解析为新的空的视图缓存列表
     */
    delAllCachedViews(view) {
      return new Promise((resolve) => {
        // 重置cachedViews数组为空，以删除所有缓存的视图
        this.cachedViews = [];
        // 解析Promise，返回新的空的视图缓存列表
        resolve([...this.cachedViews]);
      });
    },
    /**
     * 更新已访问视图
     * 当视图的路径与已访问视图数组中的视图路径匹配时，更新该视图的信息
     *
     * @param {Object} view - 需要更新的视图对象，包含路径等信息
     */
    updateVisitedView(view) {
      // 遍历已访问视图数组
      for (let v of this.visitedViews) {
        // 检查当前视图路径是否与传入的视图路径相匹配
        if (v.path === view.path) {
          // 使用传入的视图对象信息更新已访问视图数组中的视图信息
          v = Object.assign(v, view);
          // 更新完成后，终止遍历
          break;
        }
      }
    },
    /**
     * 删除指定视图右侧的标签
     * @param {Object} view - 需要删除右侧标签的视图对象
     * @returns {Promise} - 返回一个Promise对象，该对象在标签删除后解析
     */
    delRightTags(view) {
      return new Promise((resolve) => {
        // 查找指定视图在已访问视图列表中的索引
        const index = this.visitedViews.findIndex((v) => v.path === view.path);
        if (index === -1) {
          // 如果视图不在列表中，则直接返回
          return;
        }
        // 过滤已访问视图列表，移除指定视图右侧的标签
        this.visitedViews = this.visitedViews.filter((item, idx) => {
          // 保留索引小于等于指定视图索引或带有固定标签的视图
          if (idx <= index || (item.meta && item.meta.affix)) {
            return true;
          }
          // 移除缓存视图列表中的视图
          const i = this.cachedViews.indexOf(item.name);
          if (i > -1) {
            this.cachedViews.splice(i, 1);
          }
          // 如果视图包含链接，移除对应的iframe视图
          if (item.meta.link) {
            const fi = this.iframeViews.findIndex((v) => v.path === item.path);
            this.iframeViews.splice(fi, 1);
          }
          // 不保留该视图
          return false;
        });
        // 解析Promise，返回更新后的已访问视图列表
        resolve([...this.visitedViews]);
      });
    },
    /**
     * 删除左侧标签
     * 此函数旨在从已访问视图列表中移除指定视图左侧的所有视图，除非它们被标记为固定
     * 它同时更新缓存视图和iframe视图数组以保持一致性
     *
     * @param {Object} view - 要处理的视图对象，包含路径信息
     * @returns {Promise} 返回一个Promise，该Promise在处理完成后解析为新的已访问视图列表
     */
    delLeftTags(view) {
      return new Promise((resolve) => {
        // 查找指定视图在已访问视图列表中的索引
        const index = this.visitedViews.findIndex((v) => v.path === view.path);
        // 如果视图不在列表中，则不执行任何操作
        if (index === -1) {
          return;
        }
        // 过滤已访问视图列表，移除指定视图左侧的所有非固定视图
        this.visitedViews = this.visitedViews.filter((item, idx) => {
          // 保留索引大于等于指定视图索引或被标记为固定的视图
          if (idx >= index || (item.meta && item.meta.affix)) {
            return true;
          }
          // 移除缓存视图列表中的视图
          const i = this.cachedViews.indexOf(item.name);
          if (i > -1) {
            this.cachedViews.splice(i, 1);
          }
          // 如果视图包含链接信息，也从iframe视图列表中移除
          if (item.meta.link) {
            const fi = this.iframeViews.findIndex((v) => v.path === item.path);
            this.iframeViews.splice(fi, 1);
          }
          // 不保留该视图
          return false;
        });
        // 解析Promise为新的已访问视图列表
        resolve([...this.visitedViews]);
      });
    },
  },
});

export default useTagsViewStore;
