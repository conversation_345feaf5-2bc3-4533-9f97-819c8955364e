<!-- 在职人员产品报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择城市" clearable
                    @change="handleCityChange" multiple>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="自有公司/供应商:" prop="companyType">
                <el-select class="width220" v-model="obj.queryParams.companyType" placeholder="请选择公司类型" clearable>
                    <el-option v-for="item in companyTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="是否单立户:" prop="isSingleAccount">
                <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="集团名称:" prop="groupName">
                <el-input class="width220" v-model="obj.queryParams.groupName" placeholder="请输入集团名称" clearable />
            </el-form-item>
            <el-form-item label="接单方客服:" prop="receiverService">
                <el-select class="width220" v-model="obj.queryParams.receiverService" placeholder="请选择客服" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单方客服:" prop="dispatchService">
                <el-select class="width220" v-model="obj.queryParams.dispatchService" placeholder="请选择客服" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报表月份:" prop="reportMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.reportMonth" type="month" placeholder="请选择月份"
                    clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Download" @click="handleExport">数据导出</el-button>
                <el-button icon="Refresh" @click="handleReset">重置城市</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mt10 mb10">
            <el-col :span="24">
                <el-card style="max-width: 480px">
                    <div>
                        <span class="marr10">城市:</span>
                        <el-tag class="marr5" v-for="city in selectedCities" :key="city.value" closable
                            @close="handleCityTagClose(city)">
                            {{ city.label }}
                        </el-tag>
                        <span v-if="selectedCities.length === 0" class="text-muted">未选择城市</span>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script setup name="EmployeeProductReport">
const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 城市选项
const cityOptions = ref([
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
]);

// 公司类型选项
const companyTypeOptions = ref([
    { value: 'own', label: '自有公司' },
    { value: 'supplier', label: '供应商' }
]);

// 客服选项
const serviceOptions = ref([
    { value: '1', label: '客服1' },
    { value: '2', label: '客服2' },
    { value: '3', label: '客服3' },
    { value: '4', label: '客服4' },
    { value: '5', label: '客服5' }
]);

// 计算当前选中的城市
const selectedCities = computed(() => {
    if (!obj.queryParams.city || obj.queryParams.city.length === 0) {
        return [];
    }
    return obj.queryParams.city.map(value => {
        const city = cityOptions.value.find(item => item.value === value);
        return city || { value, label: value };
    });
});

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    tableData: [], // 表格数据
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        city: [], // 城市数组
        companyType: null,
        customerName: null,
        isSingleAccount: null,
        groupName: null,
        receiverService: null,
        dispatchService: null,
        reportMonth: null
    } // 查询表单
});
/** 城市选择变化处理 */
function handleCityChange() {
    // 当城市选择变化时，可以进行其他操作
    // 这里不需要额外处理，因为我们使用了计算属性来显示选中的城市
}

/** 城市标签关闭处理 */
function handleCityTagClose(city) {
    const index = obj.queryParams.city.indexOf(city.value);
    if (index !== -1) {
        obj.queryParams.city.splice(index, 1);
    }
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    obj.queryParams.city = []; // 手动清空城市选择，因为是数组类型
}

/** 导出数据按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        // 调用导出接口
        proxy.$modal.msgSuccess('导出成功');
    }).catch(() => { });
}
</script>
<style lang="scss" scoped>
.text-muted {
    color: #909399;
    font-size: 14px;
}
</style>