<template>
    <div class="component-upload-image">
        <el-upload multiple :file-list="fileList" :action="uploadImgUrl" :data="ossData" list-type="picture-card"
            :on-success="handleUploadSuccess" :before-upload="handleBeforeUpload" :limit="limit"
            :on-error="handleUploadError" :on-exceed="handleExceed" ref="imageUpload" :before-remove="handleDelete"
            :show-file-list="true" :on-preview="handlePictureCardPreview" :class="{ hide: fileList.length >= limit }">
            <el-icon class="avatar-uploader-icon">
                <plus />
            </el-icon>
        </el-upload>
        <!-- 上传提示 -->
        <div class="el-upload__tip" v-if="showTip">
            请上传
            <template v-if="fileSize">
                大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
            </template>
            <template v-if="fileType">
                格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
            </template>
            的文件
        </div>

        <el-dialog v-model="dialogVisible" title="预览" width="800px" append-to-body>
            <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
        </el-dialog>
    </div>
</template>

<script setup>
import { ossUpload } from "@/utils/ossUpload";
import _ from "lodash";
const props = defineProps({
    modelValue: [String, Object, Array],
    // 图片数量限制
    limit: {
        type: Number,
        default: 5,
    },
    // 大小限制(MB)
    fileSize: {
        type: Number,
        default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
        type: Array,
        default: () => ["png", "jpg", "jpeg"],
    },
    // 是否显示提示
    isShowTip: {
        type: Boolean,
        default: true
    },
});

const { proxy } = getCurrentInstance();
const emit = defineEmits();
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);

const uploadImgUrl = ref(''); // 上传的图片服务器地址
// 上传图片的参数
const ossData = ref({});
const fileList = ref([]);
const showTip = computed(
    () => props.isShowTip && (props.fileType || props.fileSize)
);

watch(() => props.modelValue, val => {
    if (val) {
        console.log('val', val);
        // 首先将值转为数组
        const list = Array.isArray(val) ? val : props.modelValue.split(",");
        // 然后将数组转为对象数组
        fileList.value = list.map(item => {
            if (typeof item === "string") {
                if (item.indexOf(baseUrl) === -1) {
                    item = { name: baseUrl + item, url: baseUrl + item };
                } else {
                    item = { name: item, url: item };
                }
            }
            return item;
        });
    } else {
        fileList.value = [];
        return [];
    }
    console.log(fileList.value);

}, { deep: true, immediate: true });

// 上传前loading加载
async function handleBeforeUpload(file) {
    let isImg = false;
    if (props.fileType.length) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
            fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        isImg = props.fileType.some(type => {
            if (file.type.indexOf(type) > -1) return true;
            if (fileExtension && fileExtension.indexOf(type) > -1) return true;
            return false;
        });
    } else {
        isImg = file.type.indexOf("image") > -1;
    }
    if (!isImg) {
        proxy.$modal.msgError(
            `文件格式不正确, 请上传${props.fileType.join("/")}图片格式文件!`
        );
        return false;
    }
    if (props.fileSize) {
        const isLt = file.size / 1024 / 1024 < props.fileSize;
        if (!isLt) {
            proxy.$modal.msgError(`上传头像图片大小不能超过 ${props.fileSize} MB!`);
            return false;
        }
    }
    number.value++;

    let fileSize = file.size;
    let fileName = file.name;
    const oss = await ossUpload("authors", fileName, fileSize);
    uploadImgUrl.value = oss.host;
    ossData.value = _.cloneDeep(oss);
    delete ossData.value.host;
}

// 文件个数超出
function handleExceed() {
    proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
    let imgUrl = uploadImgUrl.value + "/" + ossData.value.key;
    let img1 = {
        name: file.name,
        url: imgUrl,
    };
    uploadList.value.push(img1);
    uploadedSuccessfully();
}

// 删除图片
function handleDelete(file) {
    const findex = fileList.value.map(f => f.name).indexOf(file.name);
    if (findex > -1 && uploadList.value.length === number.value) {
        fileList.value.splice(findex, 1);
        emit("update:modelValue", fileList.value);
        return false;
    }
}
function uploadedSuccessfully() {
    // 检查是否有文件上传限制，并确认所有文件已上传完毕
    if (number.value > 0 && uploadList.value.length === number.value) {
        // 更新文件列表，移除未成功上传的文件，并添加新上传的文件
        fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
        // 清空上传列表，为下一次上传做准备
        uploadList.value = [];
        // 重置文件数量限制，确保下一次上传时重新计数
        number.value = 0;
        // 触发父组件的事件，更新modelValue以反映新的文件列表
        emit("update:modelValue", fileList.value);
    }
}

// 上传失败
function handleUploadError() {
    console.log('失败');
    proxy.$modal.msgError("上传图片失败");
}

// 预览
function handlePictureCardPreview(file) {
    dialogImageUrl.value = file.url;
    dialogVisible.value = true;
}
</script>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
    display: none;
}
</style>