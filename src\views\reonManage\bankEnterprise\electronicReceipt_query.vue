<!-- 查询电子回单 -->
<template>
    <div class="app-container">
        <el-tabs type="border-card" style="height: 85vh;">
            <el-tab-pane label="电子回单明细查询">
                <!-- 查询条件 -->
                <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
                    <el-form-item label="文件格式:" prop="fileFormat">
                        <el-select class="width220" v-model="obj.queryParams.fileFormat" placeholder="请选择文件格式"
                            clearable>
                            <el-option v-for="item in fileFormatOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="账号:" prop="accountNo">
                        <el-input class="width220" v-model="obj.queryParams.accountNo" placeholder="请输入账号" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="开始日期:" prop="startDate">
                        <el-date-picker class="width220" v-model="obj.queryParams.startDate" type="date"
                            placeholder="请选择开始日期" clearable />
                    </el-form-item>
                    <el-form-item label="结束日期:" prop="endDate">
                        <el-date-picker class="width220" v-model="obj.queryParams.endDate" type="date"
                            placeholder="请选择结束日期" clearable />
                    </el-form-item>
                    <el-form-item label="打印标志:" prop="printFlag">
                        <el-select class="width220" v-model="obj.queryParams.printFlag" placeholder="请选择打印标志" clearable>
                            <el-option v-for="item in printFlagOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="回单代码:" prop="receiptCode">
                        <el-input class="width220" v-model="obj.queryParams.receiptCode" placeholder="请输入回单代码" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="是否拉取银行流水:" prop="pullBankFlow">
                        <el-switch v-model="obj.queryParams.pullBankFlow" />
                    </el-form-item>
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <!-- 表格 -->
                <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="返回码" align="center" prop="returnCode" />
                    <el-table-column label="返回信息" align="center" prop="returnMessage" />
                    <el-table-column label="打印任务编号" align="center" prop="printTaskNo" />
                </el-table>
                <!-- 分页 -->
                <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                    v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
            </el-tab-pane>
            <el-tab-pane label="电子回单明细下载">
                <!-- 查询条件 -->
                <el-form @submit.prevent :model="obj.queryParams" ref="queryRef" inline label-width="auto">
                    <el-form-item label="打印ID:" prop="printId">
                        <el-input class="width220" v-model="obj.queryParams2.printId" placeholder="请输入打印ID" clearable />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>

                <!-- 表格 -->
                <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData2"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="打印编号" align="center" prop="printNo" />
                    <el-table-column label="账户号" align="center" prop="accountNo" />
                    <el-table-column label="回单编号" align="center" prop="receiptNo" />
                    <el-table-column label="流水号" align="center" prop="serialNo" />
                    <el-table-column label="业务参考号" align="center" prop="referenceNo" />
                    <el-table-column label="查询标记" align="center" prop="queryMark" />
                    <el-table-column label="下载路径" align="center" prop="downloadPath">
                        <template #default="scope">
                            <el-link type="primary">{{ scope.row.downloadPath }}</el-link>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                    v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup name="ElectronicReceipt_query">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 文件格式选项
const fileFormatOptions = [
    { value: 'PDF', label: 'PDF' },
    { value: 'JPG', label: 'JPG' },
    { value: 'PNG', label: 'PNG' },
    { value: 'TIF', label: 'TIF' }
];

// 打印标志选项
const printFlagOptions = [
    { value: '0', label: '未打印' },
    { value: '1', label: '已打印' }
];



const obj = reactive({
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        fileFormat: null,
        accountNo: null,
        startDate: null,
        endDate: null,
        printFlag: null,
        receiptCode: null,
        pullBankFlow: false,
    },//第一个标签页查询表单
    queryParams2: {
        pageNum: 1,
        pageSize: 10,
        printId: null,
    },//第二个标签页查询表单
    total: 0,//总条数
    tableData: [],//列表
    tableData2: [],//第二个标签页列表
    ids: [],//选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        // 第一个标签页数据
        obj.tableData = [
            {
                id: 1,
                returnCode: '0000',
                returnMessage: '成功',
                printTaskNo: 'PT***********'
            },
            {
                id: 2,
                returnCode: '0000',
                returnMessage: '成功',
                printTaskNo: 'PT***********'
            },
            {
                id: 3,
                returnCode: '0000',
                returnMessage: '成功',
                printTaskNo: 'PT20230503001'
            }
        ];

        // 第二个标签页数据
        obj.tableData2 = [
            {
                id: 1,
                printNo: 'PN***********',
                accountNo: '****************',
                receiptNo: 'RN***********',
                serialNo: 'SN***********',
                referenceNo: 'REF***********',
                queryMark: 'QM001',
                downloadPath: '/download/receipt/***********.pdf'
            },
            {
                id: 2,
                printNo: 'PN***********',
                accountNo: '****************',
                receiptNo: 'RN***********',
                serialNo: 'SN***********',
                referenceNo: 'REF***********',
                queryMark: 'QM002',
                downloadPath: '/download/receipt/***********.pdf'
            }
        ];

        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 下载电子回单
function handleDownload(row) {
    proxy.$modal.msgSuccess('开始下载电子回单，打印任务编号：' + row.printTaskNo);
    // 模拟下载文件
    const fileName = '电子回单_' + row.printTaskNo + '.pdf';
    const link = document.createElement('a');
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 下载电子回单明细
function handleDownloadDetail(row) {
    proxy.$modal.msgSuccess('开始下载电子回单明细，打印编号：' + row.printNo);
    // 模拟下载文件
    const fileName = '电子回单明细_' + row.printNo + '.pdf';
    const link = document.createElement('a');
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}


getList();
</script>
<style lang="scss" scoped>
// 重写el-link的样式
.el-link.is-underline::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    bottom: 0;
    border-bottom: 1px solid #1890ff;
}
</style>