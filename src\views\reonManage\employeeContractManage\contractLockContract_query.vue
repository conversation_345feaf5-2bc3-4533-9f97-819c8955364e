<!-- 契约锁合同查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="签署时间:" prop="signDate">
                <el-date-picker class="width220" v-model="obj.queryParams.signDate" type="date" placeholder="请选择签署时间"
                    clearable />
            </el-form-item>
            <el-form-item label="合同状态:" prop="contractStatus">
                <el-select class="width220" v-model="obj.queryParams.contractStatus" placeholder="请选择合同状态" clearable>
                    <el-option label="待签署" value="0" />
                    <el-option label="已签署" value="1" />
                    <el-option label="已撤回" value="2" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" plain icon="View" @click="handleViewContract">查看合同</el-button>
                <el-button type="info" plain icon="InfoFilled" @click="handleViewContractDetail">查看合同详情</el-button>
                <el-button type="warning" plain icon="Close" @click="handleWithdrawContract">撤回合同</el-button>
                <el-button type="success" plain icon="RefreshRight" @click="handleReinitiateContract">重新发起合同</el-button>
            </el-col>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="订单号" align="center" prop="orderNo" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="员工姓名" align="center" prop="employeeName" />
            <el-table-column label="身份证号" align="center" prop="idCard" />
            <el-table-column label="发起时间" align="center" prop="initiationTime" />
            <el-table-column label="当前审批人" align="center" prop="currentApprover" />
            <el-table-column label="业务分类名称" align="center" prop="businessCategoryName" />
            <el-table-column label="合同状态" align="center" prop="contractStatus" />
            <el-table-column label="契约锁合同ID" align="center" prop="contractId" />
            <el-table-column label="入离职状态" align="center" prop="inOutStatus" />
            <el-table-column label="离职原因" align="center" prop="resignationReason" />
            <el-table-column label="创建人" align="center" prop="creator" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 查看合同详情 -->
        <el-dialog v-model="obj.showViewContractDetail" title="查看合同" width="50%">
            <div>
                <el-form :model="obj.viewContractDetail" inline label-width="auto">
                    <el-form-item label="合同名称" prop="contractName">
                        <el-input class="width220" v-model="obj.viewContractDetail.contractName" placeholder="合同名称"
                            readonly />
                    </el-form-item>
                    <el-form-item label="发起方名称" prop="initiatorName">
                        <el-input class="width220" v-model="obj.viewContractDetail.initiatorName" placeholder="发起方名称"
                            readonly />
                    </el-form-item>
                    <el-form-item label="业务分类名称" prop="businessCategoryName">
                        <el-input class="width220" v-model="obj.viewContractDetail.businessCategoryName"
                            placeholder="业务分类名称" readonly />
                    </el-form-item>
                    <el-form-item label="合同状态" prop="contractStatus">
                        <el-input class="width220" v-model="obj.viewContractDetail.contractStatus" placeholder="合同状态"
                            readonly />
                    </el-form-item>
                    <el-form-item label="截止签署时间" prop="signDeadline">
                        <el-input class="width220" v-model="obj.viewContractDetail.signDeadline" placeholder="截止签署时间"
                            readonly />
                    </el-form-item>
                    <el-form-item label="合同发起时间" prop="initiationTime">
                        <el-input class="width220" v-model="obj.viewContractDetail.initiationTime" placeholder="合同发起时间"
                            readonly />
                    </el-form-item>
                    <el-divider content-position="left">reon审批详情</el-divider>
                    <el-table :data="obj.viewContractDetail.reonApprovalDetails" border>
                        <el-table-column label="审批人" align="center" prop="approver" />
                        <el-table-column label="是否当前审批人" align="center" prop="isCurrentApprover" />
                        <el-table-column label="是否已审批" align="center" prop="isApproved" />
                        <el-table-column label="审批时间" align="center" prop="approvalTime" />
                        <el-table-column label="是否自动审批" align="center" prop="isAutoApproval" />
                        <el-table-column label="详情" align="center" prop="details" />
                    </el-table>
                    <el-divider content-position="left">签署方</el-divider>
                    <el-table :data="obj.viewContractDetail.signatories" border>
                        <el-table-column label="签署方名称" align="center" prop="signatoryName" />
                        <el-table-column label="签署方类型" align="center" prop="signatoryType" />
                        <el-table-column label="签署方状态" align="center" prop="signatoryStatus" />
                        <el-table-column label="签署完成时间" align="center" prop="signCompletionTime" />
                        <el-table-column label="签署顺序" align="center" prop="signOrder" />
                    </el-table>
                    <el-divider content-position="left">操作记录</el-divider>
                    <el-table :data="obj.viewContractDetail.operationRecords" border>
                        <el-table-column label="操作人姓名" align="center" prop="operatorName" />
                        <el-table-column label="操作人联系方式" align="center" prop="operatorContact" />
                        <el-table-column label="操作类型" align="center" prop="operationType" />
                        <el-table-column label="操作时间" align="center" prop="operationTime" />
                        <el-table-column label="操作详情" align="center" prop="operationDetails" />
                        <el-table-column label="操作简述" align="center" prop="operationSummary" />
                        <el-table-column label="客户端类型" align="center" prop="clientType" />
                        <el-table-column label="浏览器类型" align="center" prop="browserType" />
                    </el-table>
                </el-form>
            </div>
        </el-dialog>
    </div>
</template>

<script setup name="ContractLock">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    contractName: [
        { required: false, message: '合同名称不能为空', trigger: 'blur' }
    ],
    signDate: [
        { required: false, message: '签署时间不能为空', trigger: 'change' }
    ],
    contractStatus: [
        { required: false, message: '合同状态不能为空', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractName: null,
        signDate: null,
        contractStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
    showViewContractDetail: false,//查看合同详情
    viewContractDetail: {},//查看合同详情
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                orderNo: '1234567890',
                contractName: '员工劳动合同1',
                customerName: '上海鼎捷数智软件有限公司',
                employeeName: '张三',
                idCard: '123456789012345678',
                initiationTime: '2023-01-01 10:00:00',
                currentApprover: '李四',
                businessCategoryName: '劳动合同',
                contractStatus: '已签署',
                contractId: '123456789012345678',
                inOutStatus: '已离职',
                resignationReason: '离职原因',
                creator: '张三'
            },
            {
                id: 2,
                orderNo: '1234567890',
                contractName: '员工劳动合同2',
                customerName: '上海鼎捷数智软件有限公司',
                employeeName: '李四',
                idCard: '123456789012345678',
                initiationTime: '2023-01-01 10:00:00',
                currentApprover: '王五',
                businessCategoryName: '劳动合同',
                contractStatus: '待签署',
                contractId: '123456789012345678',
                inOutStatus: '已离职',
                resignationReason: '离职原因',
                creator: '张三'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 查看合同
function handleViewContract() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要查看的合同数据');
        return;
    }
    // 这里可以调用API查看合同文件
    proxy.$modal.msgSuccess('查看合同成功');
}

// 查看合同详情
function handleViewContractDetail() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要查看的合同数据');
        return;
    }
    // 这里可以调用API获取合同详情
    // 模拟数据，实际开发时可以删除
    obj.viewContractDetail = {
        contractName: '员工劳动合同1',
        initiatorName: '上海鼎捷数智软件有限公司',
        businessCategoryName: '劳动合同',
        contractStatus: '已签署',
        signDeadline: '2023-12-31',
        initiationTime: '2023-01-01 10:00:00',
        reonApprovalDetails: [
            {
                approver: '李四',
                isCurrentApprover: '是',
                isApproved: '是',
                approvalTime: '2023-01-02 10:00:00',
                isAutoApproval: '否',
                details: '已审批通过'
            }
        ],
        signatories: [
            {
                signatoryName: '张三',
                signatoryType: '个人',
                signatoryStatus: '已签署',
                signCompletionTime: '2023-01-05 15:30:00',
                signOrder: '1'
            }
        ],
        operationRecords: [
            {
                operatorName: '李四',
                operatorContact: '***********',
                operationType: '发起合同',
                operationTime: '2023-01-01 10:00:00',
                operationDetails: '发起合同',
                operationSummary: '发起合同',
                clientType: 'PC',
                browserType: 'Chrome'
            },
            {
                operatorName: '张三',
                operatorContact: '13900139000',
                operationType: '签署合同',
                operationTime: '2023-01-05 15:30:00',
                operationDetails: '签署合同',
                operationSummary: '签署合同',
                clientType: 'PC',
                browserType: 'Chrome'
            }
        ]
    };
    obj.showViewContractDetail = true;
}

// 撤回合同
function handleWithdrawContract() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要撤回的合同数据');
        return;
    }
    proxy.$modal.confirm('是否确认撤回选中的合同？').then(function () {
        // 这里可以调用API撤回合同
        proxy.$modal.msgSuccess('撤回合同成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 重新发起合同
function handleReinitiateContract() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要重新发起的合同数据');
        return;
    }
    proxy.$modal.confirm('是否确认重新发起选中的合同？').then(function () {
        // 这里可以调用API重新发起合同
        proxy.$modal.msgSuccess('重新发起合同成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>