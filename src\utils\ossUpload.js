import { upload } from "@/api/auction/banner";

/**
 * 异步函数，用于将文件上传到OSS（对象存储服务）
 * 
 * @param {string} type - 文件类型，用于确定上传目录
 * @param {string} fileName - 文件名，用于在OSS中唯一标识文件
 * @param {number} fileSize - 文件大小，用于上传参数
 * @returns {Promise<Object>} - 返回一个Promise，解析为包含上传到OSS所需数据的对象
 */
export async function ossUpload(type, fileName, fileSize) {
  // 获取当前日期和时间，用于创建上传目录和文件名
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hour = String(now.getHours()).padStart(2, "0");
  const minute = String(now.getMinutes()).padStart(2, "0");
  const second = String(now.getSeconds()).padStart(2, "0");
  const currentTime = `${year}${month}${day}`; // 当前日期，格式为YYYYMMDD

  try {
    // 准备上传参数，包括目录、文件名和文件大小
    let params = {
      dir: type + "/" + currentTime,
      fileName: fileName,
      fileSize: fileSize,
    };
    // 调用upload函数上传文件，并等待结果
    const data = await upload(params);
    let oss = data.data; // 上传函数返回的数据

    // 构建上传到OSS所需的参数
    let ossData = {
      key: type + "/" + currentTime + "/" + fileName, // 文件在OSS中的完整路径
      policy: oss.policy,
      OSSAccessKeyId: oss.OSSAccessKeyId,
      signature: oss.signature,
      policy: oss.policy,
      signature: oss.signature,
      callback: oss.callback,
      success_action_status: "200", // 成功上传后的HTTP状态码
      host: oss.host, // 上传地址
    };
    return ossData; // 返回包含上传所需数据的对象
  } catch (r) {
    console.error(r); // 捕获并打印错误
  }
}
