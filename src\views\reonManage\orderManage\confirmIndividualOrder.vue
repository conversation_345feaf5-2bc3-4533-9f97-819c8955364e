<!-- 确认个人订单 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户编号:" prop="customerCode">
                <el-select class="width220" v-model="obj.queryParams.customerCode" placeholder="请选择" clearable>
                    <el-option v-for="item in customerList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderCode">
                <el-input class="width220" v-model="obj.queryParams.orderCode" placeholder="请输入订单编号" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" />
            </el-form-item>
            <el-form-item label="证件类型:" prop="idType">
                <el-select class="width220" v-model="obj.queryParams.idType" placeholder="请选择" clearable>
                    <el-option v-for="item in idTypeList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" />
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-select class="width220" v-model="obj.queryParams.smallContractName" placeholder="请选择" clearable>
                    <el-option v-for="item in smallContractList" :key="item.code" :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方客服:" prop="orderService">
                <el-select class="width220" v-model="obj.queryParams.orderService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方:" prop="orderReceiver">
                <el-select class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请选择" clearable>
                    <el-option v-for="item in receiverList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单方:" prop="orderSender">
                <el-select class="width220" v-model="obj.queryParams.orderSender" placeholder="请选择" clearable>
                    <el-option v-for="item in senderList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单创建时间(起):" prop="createTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.createTimeStart" type="date"
                    placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="订单创建时间(止):" prop="createTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.createTimeEnd" type="date"
                    placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="订单确认状态:" prop="orderStatus">
                <el-select class="width220" v-model="obj.queryParams.orderStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in orderStatusList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="人员分布:" prop="personnelDistribution">
                <el-select class="width220" v-model="obj.queryParams.personnelDistribution" placeholder="请选择" clearable>
                    <el-option v-for="item in distributionList" :key="item.code" :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否单立户:" prop="isSingleAccount">
                <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleDetail">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleComplete">完善订单</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleSuspend">挂起</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleReject">驳回</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleBatchComplete">批量完善订单</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员编号" align="center" prop="employeeCode" />
            <el-table-column label="订单编号" align="center" prop="orderCode" />
            <el-table-column label="订单状态" align="center" prop="orderStatus" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件类型" align="center" prop="idType" />
            <el-table-column label="退休提醒" align="center" prop="retirementReminder" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="人员分布" align="center" prop="personnelDistribution" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="派单类型" align="center" prop="orderType" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="是否单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="申请入职时间" align="center" prop="applyEntryDate" />
            <el-table-column label="申报入职人" align="center" prop="applyEntryPerson" />
            <el-table-column label="入职备注" align="center" prop="entryRemark" />
            <el-table-column label="入职过程" align="center" prop="entryProcess" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="是否外呼" align="center" prop="isOutCall" />
            <el-table-column label="电话" align="center" prop="telephone" />
            <el-table-column label="手机" align="center" prop="mobile" />
            <el-table-column label="派单方" align="center" prop="orderSender" />
            <el-table-column label="接单方客服" align="center" prop="orderService" />
            <el-table-column label="接单方" align="center" prop="orderReceiver" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" plain @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 绑定供应商账单模版/报价单 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="60%" append-to-body @close="handleClose">
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <el-divider content-position="left">个人订单</el-divider>
                <el-form-item label="小合同" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="人员分类" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="社保套餐" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="派单类型" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="城市" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="申请入职时间" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="申请入职人" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="订单状态" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="客户" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="客户编号" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="派单方" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="接单方" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="雇员姓名" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="雇员编号" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="电话" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="手机" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="证件类型" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="证件编号" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="入职日期" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="总收费时间" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="社保工资" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="公积金工资" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="户口性质" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公积金账号" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="有统筹医疗" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="有社保卡" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="申报离职日期" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="离职日期" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="是否存档" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="是否外呼" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="申报离职人" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="离职备注" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="签约方抬头" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>

                <el-form-item label="入职备注" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="入职过程" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-divider content-position="left">劳动合同</el-divider>

                <el-form-item label="是否需要签订" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="合同签订地" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="劳务合同类别" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同版本" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="合同版本地" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="劳动合同起始时间" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="劳动合同结束时间" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工作制" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="是否有试用期" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="试用期起始时间" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="试用期月数" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="试用工资" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="派遣期限起" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="派遣期限止" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="正式工资" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同原则" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="签署日期" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="试用期结束时间" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="用工单位" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="工作地(省/市)" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-form-item label="工作岗位" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-divider content-position="left">社保公积金</el-divider>
                <el-row :gutter="10" class="mb8" v-if="obj.btnsSave">
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleAdd">设定社保公积金组</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleAdd">删除</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleAdd">载入最高基数</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleAdd">载入最低基数</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleAdd">类似设定</el-button>
                    </el-col>
                </el-row>
                <el-table :data="obj.insuranceData" border @selection-change="handleInsuranceSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column typee="index" label="序号" width="60" />
                    <el-table-column label="产品名称" align="center" prop="productName" />
                    <el-table-column label="社保公积金" align="center" prop="insuranceType" />
                    <el-table-column label="收费起始月" align="center" prop="chargeStartMonth" />
                    <el-table-column label="收费截止月" align="center" prop="chargeEndMonth" />
                    <el-table-column label="（由项目客服填写)账单起始月" align="center" width="200" prop="billStartMonth" />
                    <el-table-column label="企业基数" align="center" prop="companyBase" />
                    <el-table-column label="个人基数" align="center" prop="personalBase" />
                    <el-table-column label="企业金额" align="center" prop="companyAmount" />
                    <el-table-column label="个人金额" align="center" prop="personalAmount" />
                    <el-table-column label="社保比例名称" align="center" prop="insuranceRatioName" />
                    <el-table-column label="企业比例" align="center" prop="companyRatio" />
                    <el-table-column label="个人比例" align="center" prop="personalRatio" />
                    <el-table-column label="企业附加" align="center" prop="companyAdditional" />
                    <el-table-column label="个人附加" align="center" prop="personalAdditional" />
                    <el-table-column label="账单模板" align="center" prop="billTemplate" />
                    <el-table-column label="收费模板" align="center" prop="chargeTemplate" />
                    <el-table-column label="备注" align="center" prop="remark" />
                </el-table>
                <el-divider content-position="left">非社保公积金</el-divider>
                <el-table :data="obj.nonInsuranceData" border>
                    <el-table-column label="产品名称" align="center" prop="productName" />
                    <el-table-column label="收费起始月" align="center" prop="chargeStartMonth" />
                    <el-table-column label="收费截止月" align="center" prop="chargeEndMonth" />
                    <el-table-column label="（由项目客服填写)账单起始月" align="center" width="200" prop="billStartMonth" />
                    <el-table-column label="报价单金额" align="center" prop="quoteAmount" />
                    <el-table-column label="金额(不含税)" align="center" prop="amountExcludingTax" />
                    <el-table-column label="增值税率" align="center" prop="vatRate" />
                    <el-table-column label="增值税" align="center" prop="vat" />
                    <el-table-column label="服务比率" align="center" prop="serviceRatio" />
                    <el-table-column label="账单模板" align="center" prop="billTemplate" />
                    <el-table-column label="收费模板" align="center" prop="chargeTemplate" />
                    <el-table-column label="实收金额" align="center" prop="actualAmount" />
                    <el-table-column label="实收金额不含税" align="center" prop="actualAmountExcludingTax" />
                    <el-table-column label="实收金额增值税" align="center" prop="actualAmountVat" />
                    <el-table-column label="应收金额" align="center" prop="receivableAmount" />
                    <el-table-column label="备注" align="center" prop="remark" />
                </el-table>
                <el-divider content-position="left">挂起</el-divider>
                <el-form-item label="挂起原因" prop="typeCode">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
                </el-form-item>
                <el-divider content-position="left">接单备注</el-divider>
                <el-form-item label="接单备注" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请填写内容(必填)" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="obj.dialogShow = false">取消</el-button>
                <el-button type="primary" @click="handleSave">确定</el-button>
            </template>
        </el-dialog>
        <!-- 挂起/驳回 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="30%" @close="handleClose2">
            <el-form-item label="挂起原因" prop="typeCode" v-if="!obj.rejectSave">
                <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="typeCode">
                <el-input type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入供应商名称" />
            </el-form-item>
            <template #footer>
                <el-button @click="obj.dialogShow2 = false">取消</el-button>
                <el-button type="primary" @click="handleSave">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 客户列表
const customerList = ref([]);
// 证件类型列表
const idTypeList = ref([]);
// 小合同列表
const smallContractList = ref([]);
// 客服列表
const serviceList = ref([]);
// 接单方列表
const receiverList = ref([]);
// 派单方列表
const senderList = ref([]);
// 订单状态列表
const orderStatusList = ref([]);
// 人员分布列表
const distributionList = ref([]);

// 表单验证规则
const rules = {
    // 可以根据需要添加验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerCode: null,
        customerName: null,
        uniqueId: null,
        orderCode: null,
        employeeName: null,
        idType: null,
        idNumber: null,
        smallContractName: null,
        orderService: null,
        orderReceiver: null,
        orderSender: null,
        createTimeStart: null,
        createTimeEnd: null,
        orderStatus: null,
        personnelDistribution: null,
        isSingleAccount: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    insuranceData: [],//社保公积金列表
    nonInsuranceData: [],//非社保公积金列表
    dialogForm: {},//导入表单
    dialogShow: false,//导入弹窗
    dialogShow2: false,//挂起弹窗
    ids: [],//选中的id
    insuranceIds: [],//选中的社保公积金id
    title: "",//标题
    btnsSave: false,//按钮
    bindSave: false,//绑定保存
    rejectSave: false,//驳回弹窗
})

/** 获取客户列表 */
function getCustomerList() {
    // 这里可以调用API获取客户列表
    customerList.value = [
        { code: '1', name: '客户姓名1' },
        { code: '2', name: '客户姓名2' },
        { code: '3', name: '客户姓名3' }
    ];
}

/** 获取证件类型列表 */
function getIdTypeList() {
    // 这里可以调用API获取证件类型列表
    idTypeList.value = [
        { code: '1', name: '身份证' },
        { code: '2', name: '护照' },
        { code: '3', name: '军官证' }
    ];
}

/** 获取小合同列表 */
function getSmallContractList() {
    // 这里可以调用API获取小合同列表
    smallContractList.value = [
        { code: '1', name: '小合同1' },
        { code: '2', name: '小合同2' },
        { code: '3', name: '小合同3' }
    ];
}

/** 获取客服列表 */
function getServiceList() {
    // 这里可以调用API获取客服列表
    serviceList.value = [
        { code: '1', name: '客服1' },
        { code: '2', name: '客服2' },
        { code: '3', name: '客服3' }
    ];
}

/** 获取接单方列表 */
function getReceiverList() {
    // 这里可以调用API获取接单方列表
    receiverList.value = [
        { code: '1', name: '接单方1' },
        { code: '2', name: '接单方2' },
        { code: '3', name: '接单方3' }
    ];
}

/** 获取派单方列表 */
function getSenderList() {
    // 这里可以调用API获取派单方列表
    senderList.value = [
        { code: '1', name: '派单方1' },
        { code: '2', name: '派单方2' },
        { code: '3', name: '派单方3' }
    ];
}

/** 获取订单状态列表 */
function getOrderStatusList() {
    // 这里可以调用API获取订单状态列表
    orderStatusList.value = [
        { code: '1', name: '待确认' },
        { code: '2', name: '已确认' },
        { code: '3', name: '已驳回' },
        { code: '4', name: '已挂起' }
    ];
}

/** 获取人员分布列表 */
function getDistributionList() {
    // 这里可以调用API获取人员分布列表
    distributionList.value = [
        { code: '1', name: '分布1' },
        { code: '2', name: '分布2' },
        { code: '3', name: '分布3' }
    ];
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeCode: 'YG20230001',
                orderCode: 'DD20230001',
                orderStatus: '已确认',
                employeeName: '雇员姓名1',
                idType: '身份证',
                retirementReminder: '否',
                idNumber: '110101199001011234',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                personnelDistribution: '分布1',
                contractType: '合同类型1',
                orderType: '派单类型1',
                smallContractName: '小合同名称1',
                isSingleAccount: 'Y',
                applyEntryDate: '2023-01-01',
                applyEntryPerson: '申报人1',
                entryRemark: '入职备注1',
                entryProcess: '入职过程1',
                remark: '备注1',
                isOutCall: 'Y',
                telephone: '010-********',
                mobile: '***********',
                orderSender: '派单方1',
                orderService: '客服1',
                orderReceiver: '接单方1'
            },
            {
                id: 2,
                employeeCode: 'YG20230002',
                orderCode: 'DD20230002',
                orderStatus: '待确认',
                employeeName: '雇员姓名2',
                idType: '身份证',
                retirementReminder: '否',
                idNumber: '110101199001011235',
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                personnelDistribution: '分布2',
                contractType: '合同类型2',
                orderType: '派单类型2',
                smallContractName: '小合同名称2',
                isSingleAccount: 'N',
                applyEntryDate: '2023-02-01',
                applyEntryPerson: '申报人2',
                entryRemark: '入职备注2',
                entryProcess: '入职过程2',
                remark: '备注2',
                isOutCall: 'N',
                telephone: '010-********',
                mobile: '***********',
                orderSender: '派单方2',
                orderService: '客服2',
                orderReceiver: '接单方2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 获取社保公积金数据 */
function getInsuranceData() {
    // 这里可以调用API获取社保公积金数据
    obj.insuranceData = [
        {
            id: 1,
            productName: '社保产品1',
            insuranceType: '养老保险',
            chargeStartMonth: '2023-01',
            chargeEndMonth: '2023-12',
            billStartMonth: '2023-01',
            companyBase: 5000,
            personalBase: 5000,
            companyAmount: 1000,
            personalAmount: 500,
            insuranceRatioName: '社保比例1',
            companyRatio: '20%',
            personalRatio: '10%',
            companyAdditional: 100,
            personalAdditional: 50,
            billTemplate: '账单模板1',
            chargeTemplate: '收费模板1',
            remark: '备注1'
        },
        {
            id: 2,
            productName: '社保产品2',
            insuranceType: '医疗保险',
            chargeStartMonth: '2023-01',
            chargeEndMonth: '2023-12',
            billStartMonth: '2023-01',
            companyBase: 6000,
            personalBase: 6000,
            companyAmount: 1200,
            personalAmount: 600,
            insuranceRatioName: '社保比例2',
            companyRatio: '20%',
            personalRatio: '10%',
            companyAdditional: 120,
            personalAdditional: 60,
            billTemplate: '账单模板2',
            chargeTemplate: '收费模板2',
            remark: '备注2'
        }
    ];
}

/** 获取非社保公积金数据 */
function getNonInsuranceData() {
    // 这里可以调用API获取非社保公积金数据
    obj.nonInsuranceData = [
        {
            id: 1,
            productName: '非社保产品1',
            chargeStartMonth: '2023-01',
            chargeEndMonth: '2023-12',
            billStartMonth: '2023-01',
            quoteAmount: 10000,
            amountExcludingTax: 9000,
            vatRate: '10%',
            vat: 1000,
            serviceRatio: '5%',
            billTemplate: '账单模板1',
            chargeTemplate: '收费模板1',
            actualAmount: 10000,
            actualAmountExcludingTax: 9000,
            actualAmountVat: 1000,
            receivableAmount: 10000,
            remark: '备注1'
        },
        {
            id: 2,
            productName: '非社保产品2',
            chargeStartMonth: '2023-01',
            chargeEndMonth: '2023-12',
            billStartMonth: '2023-01',
            quoteAmount: 20000,
            amountExcludingTax: 18000,
            vatRate: '10%',
            vat: 2000,
            serviceRatio: '5%',
            billTemplate: '账单模板2',
            chargeTemplate: '收费模板2',
            actualAmount: 20000,
            actualAmountExcludingTax: 18000,
            actualAmountVat: 2000,
            receivableAmount: 20000,
            remark: '备注2'
        }
    ];
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 社保公积金多选框选中数据 */
function handleInsuranceSelectionChange(selection) {
    obj.insuranceIds = selection.map(item => item.id);
}

// 关闭弹窗
function handleClose() {
    obj.btnsSave = false;
}

// 关闭挂起弹窗
function handleClose2() {
    obj.rejectSave = false;
}

// 查看
function handleDetail(row) {
    obj.title = '查看个人订单';
    obj.dialogShow = true;
    if (row) {
        // 如果是从表格行点击进入，可以加载该行的数据
        obj.dialogForm = { ...row };
    }
}

// 完善订单
function handleComplete() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要完善的订单');
        return;
    }
    obj.title = '完善个人订单';
    obj.dialogShow = true;
    obj.btnsSave = true;
    // 加载选中行的数据
    const selectedRow = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedRow) {
        obj.dialogForm = { ...selectedRow };
    }
    // 加载社保公积金数据和非社保公积金数据
    getInsuranceData();
    getNonInsuranceData();
}

// 挂起
function handleSuspend() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要挂起的订单');
        return;
    }
    obj.dialogShow2 = true;
    obj.title = '挂起';
}

// 驳回
function handleReject() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要驳回的订单');
        return;
    }
    obj.rejectSave = true;
    obj.dialogShow2 = true;
    obj.title = '驳回';
}

// 批量完善订单
function handleBatchComplete() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要批量完善的订单');
        return;
    }
    proxy.$modal.confirm('确认要批量完善选中的订单吗？').then(() => {
        // 这里可以调用API进行批量完善操作
        proxy.$modal.msgSuccess('操作成功');
        getList();
    }).catch(() => { });
}

// 保存操作
function handleSave() {
    proxy.$modal.confirm('确认要保存吗？').then(() => {
        // 这里可以调用API进行保存操作
        proxy.$modal.msgSuccess('保存成功');
        obj.dialogShow = false;
        obj.dialogShow2 = false;
        getList();
    }).catch(() => { });
}

// 初始化数据
getCustomerList();
getIdTypeList();
getSmallContractList();
getServiceList();
getReceiverList();
getSenderList();
getOrderStatusList();
getDistributionList();
getList();
</script>
<style lang="scss" scoped></style>