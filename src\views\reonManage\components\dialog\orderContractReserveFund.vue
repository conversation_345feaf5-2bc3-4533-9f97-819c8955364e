<template>
    <div>
        <el-dialog v-model="dialogShow" :title="props.title" width="80%" append-to-body draggable @close="handleClose">
            <el-form class="formHight" :model="formData" ref="orderFormRef" inline label-width="auto">
                <border-box title="个人订单">
                    <el-form-item label="小合同:" prop="subContract">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.subContract"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="人员分类:" prop="personnelCategory">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.personnelCategory"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="社保套餐:" prop="socialSecurityPackage">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityPackage"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in socialPackageOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="报价单:" prop="quotation">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.quotation"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in quotationOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="小合同编号:" prop="subContractNo">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.subContractNo"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="城市:" prop="city">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.city" placeholder="请选择"
                            clearable>
                            <el-option v-for="item in cityOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="社保套餐编号:" prop="socialSecurityPackageNo">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityPackageNo"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="报价单编号:" prop="quotationNo">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.quotationNo"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="客户:" prop="customer">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.customer"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="客户编号:" prop="customerNo">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.customerNo"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="派单方:" prop="dispatchParty">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.dispatchParty"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="接单方:" prop="receiveParty">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.receiveParty"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="雇员姓名:" prop="employeeName">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.employeeName"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="雇员编号:" prop="employeeNo">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.employeeNo"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="电话:" prop="phone">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.phone" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-form-item label="手机:" prop="mobile">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.mobile"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="证件类型:" prop="idType">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.idType"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="证件编号:" prop="idNo">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.idNo" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-form-item label="入职时间:" prop="entryDate">
                        <el-date-picker :disabled="props.isDetail" class="width220" v-model="formData.entryDate"
                            type="date" placeholder="请选择" />
                    </el-form-item>
                    <el-form-item label="总收费时间:" prop="totalChargeDate">
                        <el-date-picker :disabled="props.isDetail" class="width220" v-model="formData.totalChargeDate"
                            type="date" placeholder="请选择" />
                    </el-form-item>
                    <el-form-item label="合同名称:" prop="contractName">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.contractName"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="实际工作地:" prop="actualWorkplace">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.actualWorkplace"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="入职备注:" prop="entryRemark">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.entryRemark"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="增员原因:" prop="increaseReason">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.increaseReason"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="社保工资:" prop="socialSecuritySalary">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecuritySalary"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="公积金工资:" prop="housingFundSalary">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.housingFundSalary"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="户口性质:" prop="householdType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.householdType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="公积金账号:" prop="housingFundAccount">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.housingFundAccount"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="未增反馈:" prop="noIncreaseFeedback">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.noIncreaseFeedback"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="机动分类2:" prop="flexibleCategory2">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.flexibleCategory2"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="机动分类3:" prop="flexibleCategory3">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.flexibleCategory3"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="机动分类4:" prop="flexibleCategory4">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.flexibleCategory4"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="机动分类5:" prop="flexibleCategory5">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.flexibleCategory5"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="有统筹医疗:" prop="hasCoordinatedMedical">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.hasCoordinatedMedical"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="有社保卡:" prop="hasSocialSecurityCard">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.hasSocialSecurityCard"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="民族信息:" prop="ethnicInfo" v-if="props.type == 'ActualApplication' || props.type == 'ActuallyStopDoingIt' || props.type == 'Practice' || props.type == 'ActualModification'
                        || props.type == 'PracticeQuery' || props.type == 'IndividualOrder'">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.ethnicInfo"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-row>
                        <el-form-item label="集中地投保:" prop="centralizedInsurance">
                            <el-checkbox :disabled="props.isDetail" class="width220"
                                v-model="formData.centralizedInsurance" />
                        </el-form-item>
                        <el-form-item label="是否存档:" prop="isArchived">
                            <el-checkbox :disabled="props.isDetail" class="width220" v-model="formData.isArchived" />
                        </el-form-item>
                        <el-form-item label="是否外呼:" prop="isOutbound">
                            <el-checkbox :disabled="props.isDetail" class="width220" v-model="formData.isOutbound" />
                        </el-form-item>
                    </el-row>
                    <el-form-item label="入职过程:" prop="entryProcess" v-if="props.type == 'ActualApplication' || props.type == 'ActuallyStopDoingIt' || props.type == 'Practice' || props.type == 'ActualModification'
                        || props.type == 'PracticeQuery' || props.type == 'IndividualOrder'">
                        <el-input :disabled="props.isDetail" class="width420" type="textarea" :rows="3"
                            v-model="formData.entryProcess" placeholder="请输入内容" clearable />
                    </el-form-item>
                    <el-form-item label="变更确认过程:" prop="changeConfirmProcess" v-if="props.type == 'ActualApplication' || props.type == 'ActuallyStopDoingIt' || props.type == 'Practice' || props.type == 'ActualModification'
                        || props.type == 'PracticeQuery' || props.type == 'IndividualOrder'">
                        <el-input :disabled="props.isDetail" class="width420" type="textarea" :rows="3"
                            v-model="formData.changeConfirmProcess" placeholder="请输入内容" clearable />
                    </el-form-item>
                    <el-form-item label="社保基数标识:" prop="socialSecurityBaseFlag"
                        v-if="props.type == 'RecruitmentAcceptance_firm'">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityBaseFlag"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="公积金基数标识:" prop="housingFundBaseFlag"
                        v-if="props.type == 'RecruitmentAcceptance_firm'">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.housingFundBaseFlag"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="公积金比例:" prop="housingFundRatio"
                        v-if="props.type == 'RecruitmentAcceptance_firm'">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.housingFundRatio"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="补充公积金比例:" prop="supplementaryHousingFundRatio"
                        v-if="props.type == 'RecruitmentAcceptance_firm'">
                        <el-select :disabled="props.isDetail" class="width220"
                            v-model="formData.supplementaryHousingFundRatio" placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                </border-box>
                <border-box title="劳动合同">
                    <el-form-item label="是否需要签订:" prop="needSign">
                        <el-checkbox :disabled="props.isDetail" class="width220" v-model="formData.needSign" />
                    </el-form-item>
                    <el-form-item label="合同签订地:" prop="contractSigningPlace">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.contractSigningPlace"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="劳务合同类别:" prop="laborContractType">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.laborContractType"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="合同版本:" prop="contractVersion">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.contractVersion"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="合同版本地:" prop="contractVersionPlace">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.contractVersionPlace"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="劳动合同起始时间:" prop="laborContractStartDate">
                        <el-date-picker :disabled="props.isDetail" class="width220"
                            v-model="formData.laborContractStartDate" type="date" placeholder="请选择" />
                    </el-form-item>
                    <el-form-item label="劳动合同结束时间:" prop="laborContractEndDate">
                        <el-date-picker :disabled="props.isDetail" class="width220"
                            v-model="formData.laborContractEndDate" type="date" placeholder="请选择" />
                    </el-form-item>
                    <el-form-item label="工作制:" prop="workSystem">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.workSystem"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否有试用期:" prop="hasProbation">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.hasProbation"
                            placeholder="请选择" clearable>
                            <el-option label="请选择" value="1" />
                            <el-option label="请选择" value="2" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="试用期起始时间:" prop="probationStartDate">
                        <el-date-picker :disabled="props.isDetail" class="width220"
                            v-model="formData.probationStartDate" type="date" placeholder="请选择" />
                    </el-form-item>
                    <el-form-item label="试用期月数:" prop="probationMonths">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.probationMonths"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="试用工资:" prop="probationSalary">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.probationSalary"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="派遣期限起:" prop="dispatchStartDate">
                        <el-date-picker :disabled="props.isDetail" class="width220" v-model="formData.dispatchStartDate"
                            type="date" placeholder="请选择" />
                    </el-form-item>
                    <el-form-item label="派遣期限止:" prop="dispatchEndDate">
                        <el-date-picker :disabled="props.isDetail" class="width220" v-model="formData.dispatchEndDate"
                            type="date" placeholder="请选择" />
                    </el-form-item>
                    <el-form-item label="正式工资:" prop="formalSalary">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.formalSalary"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="合同原则:" prop="contractPrinciple">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.contractPrinciple"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="签署日期:" prop="signingDate">
                        <el-date-picker :disabled="props.isDetail" class="width220" v-model="formData.signingDate"
                            type="date" placeholder="请选择" />
                    </el-form-item>
                    <el-form-item label="试用期结束时间:" prop="probationEndDate">
                        <el-date-picker :disabled="props.isDetail" class="width220" v-model="formData.probationEndDate"
                            type="date" placeholder="请选择" />
                    </el-form-item>
                    <el-form-item label="用工单位:" prop="employmentUnit">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.employmentUnit"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="工作地(省/市):" prop="workPlace">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.workPlace"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="工作岗位:" prop="jobPosition">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.jobPosition"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="备注:" prop="remark">
                        <el-input :disabled="props.isDetail" class="width420" type="textarea" :rows="3"
                            v-model="formData.remark" placeholder="请输入内容" clearable />
                    </el-form-item>
                </border-box>

                <!-- 社保必填信息 -->
                <border-box title="社保必填信息" v-if="props.type === 'IndividualOrder'">
                    <el-row>
                        <el-form-item label="必填城市:" prop="socialSecurityType">
                            <el-button type="success" plain>天津市</el-button>
                            <el-button type="success" plain>石家庄市</el-button>
                            <el-button type="success" plain>北京市</el-button>
                            <el-button type="success" plain>唐山市</el-button>
                            <el-button type="success" plain>南京市</el-button>
                        </el-form-item>
                    </el-row>
                    <el-form-item label="实际用工单位:" prop="socialSecurityType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="实际用工单位注册地(省/市):" prop="socialSecurityType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="统一社会信用代码:" prop="socialSecurityType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="企业性质(企业/机关/事业单位/社会团体/民办):" prop="socialSecurityType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="实际发薪单位名称:" prop="socialSecurityType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="工作城市:" prop="socialSecurityType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="工作岗位:" prop="socialSecurityType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="居住地址:" prop="socialSecurityType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="户口所在地:" prop="socialSecurityType">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.socialSecurityType"
                            placeholder="请输入" clearable />
                    </el-form-item>
                </border-box>

                <!-- 社保公积金 -->
                <border-box title="社保公积金">
                    <el-row :gutter="10" class="mb8" v-if="!props.isDetail">
                        <el-button type="primary" @click="reserveFund_delete">删除</el-button>
                        <el-button type="primary" @click="reserveFund_reload">重新载入社保套餐</el-button>
                    </el-row>
                    <!-- 表格 -->
                    <el-table border :data="securityFundData" @selection-change="handleSelectionChange">
                        <el-table-column type="selection" width="55" />
                        <el-table-column label="序号" type="index" width="55" align="center" />
                        <el-table-column label="产品名称" prop="productName" align="center" />
                        <el-table-column label="社保公积金" prop="securityType" width="100" align="center" />
                        <el-table-column label="收费起始月" prop="chargeStartMonth" width="140" align="center" />
                        <el-table-column label="账单起始月" prop="billStartMonth" width="100" align="center" />
                        <el-table-column label="企业基数" prop="companyBase" width="100" align="center" />
                        <el-table-column label="个人基数" prop="personalBase" width="100" align="center" />
                        <el-table-column label="企业金额" prop="companyAmount" width="100" align="center" />
                        <el-table-column label="个人金额" prop="personalAmount" width="100" align="center" />
                        <el-table-column label="社保比例编号" prop="ratioCode" width="100" align="center" />
                        <el-table-column label="社保比例名称" prop="ratioName" width="100" align="center" />
                        <el-table-column label="企业比例" prop="companyRatio" width="100" align="center" />
                        <el-table-column label="个人比例" prop="personalRatio" width="100" align="center" />
                        <el-table-column label="企业附加" prop="companyExtra" width="100" align="center" />
                        <el-table-column label="个人附加" prop="personalExtra" width="100" align="center" />
                        <el-table-column label="账单模板" prop="billTemplate" width="100" align="center" />
                        <el-table-column label="收费模板" prop="chargeTemplate" width="100" align="center" />
                        <el-table-column label="备注" prop="remark" width="100" align="center" />
                    </el-table>
                </border-box>
                <!-- 非社保公积金 -->
                <border-box title="非社保公积金">
                    <el-table border :data="nonSecurityFundData">
                        <el-table-column label="操作" align="center" width="60">
                            <template #default="scope">
                                <el-button v-if="!props.isDetail" type="primary" plain
                                    @click="handleDelete(scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                        <el-table-column label="产品名称" prop="productName" width="120" align="center" />
                        <el-table-column label="收费起始月" width="140" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.chargeStartMonth" type="month"
                                    placeholder="请选择" />
                            </template>
                        </el-table-column>
                        <el-table-column label="收费截止月" width="140" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.chargeEndMonth" type="month"
                                    placeholder="请选择" />
                            </template>
                        </el-table-column>
                        <el-table-column label="账单起始月" width="140" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.billStartMonth" type="month"
                                    placeholder="请选择" />
                            </template>
                        </el-table-column>
                        <el-table-column label="金额" prop="amount" width="100" align="center" />
                        <el-table-column label="金额(不含税)" prop="amountExcludingTax" width="100" align="center" />
                        <el-table-column label="增值税率" prop="vatRate" width="100" align="center" />
                        <el-table-column label="增值税" prop="vat" width="100" align="center" />
                        <el-table-column label="服务比率" prop="serviceRatio" width="100" align="center" />
                        <el-table-column label="账单模板" width="140" align="center">
                            <template #default="scope">
                                <el-select style="width: 100%;" v-model="scope.row.billTemplate" placeholder="请选择"
                                    clearable>
                                    <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="收费模板" width="140" align="center">
                            <template #default="scope">
                                <el-select style="width: 100%;" v-model="scope.row.chargeTemplate" placeholder="请选择"
                                    clearable>
                                    <el-option v-for="item in chargeTemplateOptions" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="实收金额" width="100" align="center">
                            <template #default="scope">
                                <el-input style="width: 100%;" v-model="scope.row.actualAmount" placeholder="请输入"
                                    clearable />
                            </template>
                        </el-table-column>
                        <el-table-column label="实收金额不含税" width="120" align="center">
                            <template #default="scope">
                                <el-input style="width: 100%;" v-model="scope.row.actualAmountExcludingTax"
                                    placeholder="请输入" clearable />
                            </template>
                        </el-table-column>
                        <el-table-column label="实收金额增值税" width="120" align="center">
                            <template #default="scope">
                                <el-input style="width: 100%;" v-model="scope.row.actualVat" placeholder="请输入"
                                    clearable />
                            </template>
                        </el-table-column>
                        <el-table-column label="应收金额" width="100" align="center">
                            <template #default="scope">
                                <el-input style="width: 100%;" v-model="scope.row.receivableAmount" placeholder="请输入"
                                    clearable />
                            </template>
                        </el-table-column>
                    </el-table>
                </border-box>
                <border-box title="接单备注" v-if="props.isDetail">
                    <el-form-item label="接单备注:" prop="orderRemark">
                        <el-input :disabled="props.isDetail" class="width420" type="textarea" :rows="3"
                            v-model="formData.orderRemark" placeholder="请输入内容" clearable />
                    </el-form-item>
                </border-box>
            </el-form>
            <template #footer>
                <el-button @click="handleClose">关闭</el-button>
                <el-button type="primary" @click="handleConfirm" v-if="!props.isDetail">确认</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>


const props = defineProps({
    type: {
        type: String,
        default: ''
    },//类型
    dialoglog: {
        type: Boolean,
        default: false
    },//是否显示弹窗
    title: {
        type: String,
        default: ''
    },//标题
    form: {
        type: Object,
        default: () => { }
    },//表单数据
    tableData: {
        type: Array,
        default: () => []
    },//社保公积金表格数据
    tableData_no: {
        type: Array,
        default: () => []
    },//非社保公积金表格数据
    isDetail: {
        type: Boolean,
        default: false
    },//是否是详情
    isEdit: {
        type: Boolean,
        default: false
    },//是否是编辑
})
// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});
const emit = defineEmits(['close', 'update:dialogShow']);

// 表单引用
const orderFormRef = ref(null);

// 表单数据 - 如果需要编辑或详情视图，初始化时从props.form复制数据
const formData = reactive({
    subContract: props.form?.subContract || '',
    personnelCategory: props.form?.personnelCategory || '',
    socialSecurityPackage: props.form?.socialSecurityPackage || '',
    quotation: props.form?.quotation || '',
    subContractNo: props.form?.subContractNo || '',
    city: props.form?.city || '',
    // 社保套餐编号
    socialSecurityPackageNo: props.form?.socialSecurityPackageNo || '',
    // 报价单编号
    quotationNo: props.form?.quotationNo || '',
    // 客户
    customer: props.form?.customer || '',
    // 客户编号
    customerNo: props.form?.customerNo || '',
    // 派单方
    dispatchParty: props.form?.dispatchParty || '',
    // 接单方
    receiveParty: props.form?.receiveParty || '',
    // 雇员姓名
    employeeName: props.form?.employeeName || '',
    // 雇员编号
    employeeNo: props.form?.employeeNo || '',
    // 电话
    phone: props.form?.phone || '',
    // 手机
    mobile: props.form?.mobile || '',
    // 证件类型
    idType: props.form?.idType || '',
    // 证件编号
    idNo: props.form?.idNo || '',
    // 入职时间
    entryDate: props.form?.entryDate || '',
    // 总收费时间
    totalChargeDate: props.form?.totalChargeDate || '',
    // 合同名称
    contractName: props.form?.contractName || '',
    // 实际工作地
    actualWorkplace: props.form?.actualWorkplace || '',
    // 入职备注
    entryRemark: props.form?.entryRemark || '',
    // 增员原因
    increaseReason: props.form?.increaseReason || '',
    // 社保工资
    socialSecuritySalary: props.form?.socialSecuritySalary || '',
    // 公积金工资
    housingFundSalary: props.form?.housingFundSalary || '',
    // 户口性质
    householdType: props.form?.householdType || '',
    // 公积金账号
    housingFundAccount: props.form?.housingFundAccount || '',
    // 未增反馈
    noIncreaseFeedback: props.form?.noIncreaseFeedback || '',
    // 机动分类2
    flexibleCategory2: props.form?.flexibleCategory2 || '',
    // 机动分类3
    flexibleCategory3: props.form?.flexibleCategory3 || '',
    // 机动分类4
    flexibleCategory4: props.form?.flexibleCategory4 || '',
    // 机动分类5
    flexibleCategory5: props.form?.flexibleCategory5 || '',
    // 有统筹医疗
    hasCoordinatedMedical: props.form?.hasCoordinatedMedical || '',
    // 有社保卡
    hasSocialSecurityCard: props.form?.hasSocialSecurityCard || '',
    // 民族信息
    ethnicInfo: props.form?.ethnicInfo || '',
    // 集中地投保
    centralizedInsurance: props.form?.centralizedInsurance || false,
    // 是否存档
    isArchived: props.form?.isArchived || false,
    // 是否外呼
    isOutbound: props.form?.isOutbound || false,
    // 入职过程
    entryProcess: props.form?.entryProcess || '',
    // 变更确认过程
    changeConfirmProcess: props.form?.changeConfirmProcess || '',
    // 社保基数标识
    socialSecurityBaseFlag: props.form?.socialSecurityBaseFlag || '',
    // 公积金基数标识
    housingFundBaseFlag: props.form?.housingFundBaseFlag || '',
    // 公积金比例
    housingFundRatio: props.form?.housingFundRatio || '',
    // 补充公积金比例
    supplementaryHousingFundRatio: props.form?.supplementaryHousingFundRatio || '',
    // 劳动合同相关
    // 是否需要签订
    needSign: props.form?.needSign || false,
    // 合同签订地
    contractSigningPlace: props.form?.contractSigningPlace || '',
    // 劳务合同类别
    laborContractType: props.form?.laborContractType || '',
    // 合同版本
    contractVersion: props.form?.contractVersion || '',
    // 合同版本地
    contractVersionPlace: props.form?.contractVersionPlace || '',
    // 劳动合同起始时间
    laborContractStartDate: props.form?.laborContractStartDate || '',
    // 劳动合同结束时间
    laborContractEndDate: props.form?.laborContractEndDate || '',
    // 工作制
    workSystem: props.form?.workSystem || '',
    // 是否有试用期
    hasProbation: props.form?.hasProbation || '',
    // 试用期起始时间
    probationStartDate: props.form?.probationStartDate || '',
    // 试用期月数
    probationMonths: props.form?.probationMonths || '',
    // 试用工资
    probationSalary: props.form?.probationSalary || '',
    // 派遣期限起
    dispatchStartDate: props.form?.dispatchStartDate || '',
    // 派遣期限止
    dispatchEndDate: props.form?.dispatchEndDate || '',
    // 正式工资
    formalSalary: props.form?.formalSalary || '',
    // 合同原则
    contractPrinciple: props.form?.contractPrinciple || '',
    // 签署日期
    signingDate: props.form?.signingDate || '',
    // 试用期结束时间
    probationEndDate: props.form?.probationEndDate || '',
    // 用工单位
    employmentUnit: props.form?.employmentUnit || '',
    // 工作地(省/市)
    workPlace: props.form?.workPlace || '',
    // 工作岗位
    jobPosition: props.form?.jobPosition || '',
    // 备注
    remark: props.form?.remark || '',
    // 接单备注
    orderRemark: props.form?.orderRemark || '',
});

// 下拉选项数据
const socialPackageOptions = [
    { value: 'package1', label: '基础社保套餐' },
    { value: 'package2', label: '标准社保套餐' },
    { value: 'package3', label: '高端社保套餐' },
];

const quotationOptions = [
    { value: 'quote1', label: '基础报价' },
    { value: 'quote2', label: '标准报价' },
    { value: 'quote3', label: '高端报价' },
];

const cityOptions = [
    { value: 'beijing', label: '北京' },
    { value: 'shanghai', label: '上海' },
    { value: 'guangzhou', label: '广州' },
    { value: 'shenzhen', label: '深圳' },
];

const billTemplateOptions = [
    { value: 'template1', label: '模板一' },
    { value: 'template2', label: '模板二' },
];

const chargeTemplateOptions = [
    { value: 'charge1', label: '收费模板一' },
    { value: 'charge2', label: '收费模板二' },
];

// 社保公积金表格数据 - 使用props传入的数据或生成示例数据
const securityFundData = ref(props.tableData.length > 0 ? props.tableData : [
    {
        id: 1,
        productName: '养老保险',
        securityType: '社保',
        chargeStartMonth: '2023-01',
        billStartMonth: '2023-01',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 2000,
        personalAmount: 800,
        ratioCode: 'YL001',
        ratioName: '养老比例标准',
        companyRatio: '20%',
        personalRatio: '8%',
        companyExtra: 0,
        personalExtra: 0,
        billTemplate: '标准账单模板',
        chargeTemplate: '标准收费模板',
        remark: ''
    },
    {
        id: 2,
        productName: '医疗保险',
        securityType: '社保',
        chargeStartMonth: '2023-01',
        billStartMonth: '2023-01',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 600,
        personalAmount: 200,
        ratioCode: 'YL002',
        ratioName: '医疗比例标准',
        companyRatio: '6%',
        personalRatio: '2%',
        companyExtra: 0,
        personalExtra: 0,
        billTemplate: '标准账单模板',
        chargeTemplate: '标准收费模板',
        remark: ''
    },
    {
        id: 3,
        productName: '住房公积金',
        securityType: '公积金',
        chargeStartMonth: '2023-01',
        billStartMonth: '2023-01',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 1200,
        personalAmount: 1200,
        ratioCode: 'GJ001',
        ratioName: '公积金比例标准',
        companyRatio: '12%',
        personalRatio: '12%',
        companyExtra: 0,
        personalExtra: 0,
        billTemplate: '标准账单模板',
        chargeTemplate: '标准收费模板',
        remark: ''
    }
]);

// 非社保公积金表格数据
const nonSecurityFundData = ref(props.tableData_no.length > 0 ? props.tableData_no : [
    {
        id: 1,
        productName: '商业保险',
        chargeStartMonth: '2023-01',
        chargeEndMonth: '2023-12',
        billStartMonth: '2023-01',
        amount: 1000,
        amountExcludingTax: 943.40,
        vatRate: '6%',
        vat: 56.60,
        serviceRatio: '10%',
        billTemplate: 'template1',
        chargeTemplate: 'charge1',
        actualAmount: 1000,
        actualAmountExcludingTax: 943.40,
        actualVat: 56.60,
        receivableAmount: 1000
    },
    {
        id: 2,
        productName: '企业年金',
        chargeStartMonth: '2023-01',
        chargeEndMonth: '2023-12',
        billStartMonth: '2023-01',
        amount: 800,
        amountExcludingTax: 754.72,
        vatRate: '6%',
        vat: 45.28,
        serviceRatio: '8%',
        billTemplate: 'template1',
        chargeTemplate: 'charge1',
        actualAmount: 800,
        actualAmountExcludingTax: 754.72,
        actualVat: 45.28,
        receivableAmount: 800
    }
]);

// 选中的行数据ID
const selectedIds = ref([]);
const selectedRows = ref([]);

/** 关闭弹窗 */
function handleClose() {
    dialogShow.value = false;
    emit('close')
}

/** 选择 */
function handleSelectionChange(selection) {
    selectedRows.value = selection;
    selectedIds.value = selection.map(item => item.id);
}

/** 删除社保公积金 */
function reserveFund_delete() {
    if (selectedIds.value.length === 0) {
        // 这里应该使用你的消息提示组件
        alert('请至少选择一条记录');
        return;
    }

    // 从表格数据中删除选中的行
    securityFundData.value = securityFundData.value.filter(
        item => !selectedIds.value.includes(item.id)
    );

    // 清空选中状态
    selectedIds.value = [];
    selectedRows.value = [];
}

/** 删除非社保公积金项 */
function handleDelete(row) {
    nonSecurityFundData.value = nonSecurityFundData.value.filter(item => item.id !== row.id);
}

/** 重新载入社保套餐 */
function reserveFund_reload() {
    // 这里模拟重新加载数据
    securityFundData.value = [
        {
            id: 1,
            productName: '养老保险(已重载)',
            securityType: '社保',
            chargeStartMonth: '2023-01',
            billStartMonth: '2023-01',
            companyBase: 12000,  // 更新基数
            personalBase: 12000, // 更新基数
            companyAmount: 2400, // 更新金额
            personalAmount: 960, // 更新金额
            ratioCode: 'YL001',
            ratioName: '养老比例标准',
            companyRatio: '20%',
            personalRatio: '8%',
            companyExtra: 0,
            personalExtra: 0,
            billTemplate: '标准账单模板',
            chargeTemplate: '标准收费模板',
            remark: '已重新载入'
        },
        {
            id: 2,
            productName: '医疗保险(已重载)',
            securityType: '社保',
            chargeStartMonth: '2023-01',
            billStartMonth: '2023-01',
            companyBase: 12000,
            personalBase: 12000,
            companyAmount: 720,
            personalAmount: 240,
            ratioCode: 'YL002',
            ratioName: '医疗比例标准',
            companyRatio: '6%',
            personalRatio: '2%',
            companyExtra: 0,
            personalExtra: 0,
            billTemplate: '标准账单模板',
            chargeTemplate: '标准收费模板',
            remark: '已重新载入'
        },
        {
            id: 3,
            productName: '住房公积金(已重载)',
            securityType: '公积金',
            chargeStartMonth: '2023-01',
            billStartMonth: '2023-01',
            companyBase: 12000,
            personalBase: 12000,
            companyAmount: 1440,
            personalAmount: 1440,
            ratioCode: 'GJ001',
            ratioName: '公积金比例标准',
            companyRatio: '12%',
            personalRatio: '12%',
            companyExtra: 0,
            personalExtra: 0,
            billTemplate: '标准账单模板',
            chargeTemplate: '标准收费模板',
            remark: '已重新载入'
        },
        {
            id: 4,
            productName: '失业保险(新增)',
            securityType: '社保',
            chargeStartMonth: '2023-01',
            billStartMonth: '2023-01',
            companyBase: 12000,
            personalBase: 12000,
            companyAmount: 120,
            personalAmount: 60,
            ratioCode: 'SY001',
            ratioName: '失业比例标准',
            companyRatio: '1%',
            personalRatio: '0.5%',
            companyExtra: 0,
            personalExtra: 0,
            billTemplate: '标准账单模板',
            chargeTemplate: '标准收费模板',
            remark: '新增项目'
        }
    ];
}

/** 提交 */
function handleConfirm() {
    // 这里处理表单提交逻辑
    console.log('表单数据:', formData);
    console.log('社保公积金数据:', securityFundData.value);
    console.log('非社保公积金数据:', nonSecurityFundData.value);

    // 组合所有数据，可以发送给后端API
    const submitData = {
        formData,
        securityFundData: securityFundData.value,
        nonSecurityFundData: nonSecurityFundData.value
    };

    // 这里可以调用API提交数据
    // 成功后关闭弹窗
    handleClose();
}

// 生命周期钩子 - 组件挂载时执行
onMounted(() => {
    // 如果是编辑模式，可以在这里加载已有数据
    if (props.isEdit && props.form) {
        // 深拷贝props.form到formData
        Object.assign(formData, props.form);
    }
});

</script>
<style lang="scss" scoped>
// 日期选择器宽度
:deep(.el-date-editor) {
    width: 220px;
}

.width220 {
    width: 220px;
}

.width420 {
    width: 420px;
}

.mb8 {
    margin-bottom: 8px;
}

// 表单高度
.formHight {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;
}
</style>
