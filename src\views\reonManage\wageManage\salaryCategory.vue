<!-- 薪资类别 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资类别名称:" prop="categoryName">
                <el-select class="width220" v-model="obj.queryParams.categoryName" placeholder="请选择" clearable>
                    <el-option v-for="item in salaryCategoryOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资类别编号:" prop="categoryCode">
                <el-select class="width220" filterable v-model="obj.queryParams.categoryCode" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in salaryCategoryCodeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="状态:" prop="status">
                <el-select class="width220" filterable v-model="obj.queryParams.status" placeholder="请选择" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="View" :disabled="obj.single" @click="handleView">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single"
                    @click="handleSetSystemProjectAttribute">设置系统项目属性</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="SuccessFilled" :disabled="obj.single"
                    @click="handleEffective">生效</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="CircleClose" :disabled="obj.single"
                    @click="handleInvalid">失效</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button color="#626aef" plain :disabled="obj.single" @click="handleCopy">复制</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleRowDblclick">
            <el-table-column type="selection" width="55" />
            <el-table-column label="薪资类别编号" align="center" prop="categoryCode">
            </el-table-column>
            <el-table-column label="薪资类别名称" align="center" prop="categoryName">
            </el-table-column>
            <el-table-column label="默认税率表" align="center" prop="taxRateTable">
            </el-table-column>
            <el-table-column label="是否拉取社保数据" align="center" prop="pullSocialSecurity">
            </el-table-column>
            <el-table-column label="拉哪月数据" align="center" prop="pullMonth" />
            <el-table-column label="工资单格式" align="center" prop="payslipFormat">
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status">
            </el-table-column>
            <el-table-column label="是否竞业员工" align="center" prop="isCompetitionEmployee" />
            <el-table-column label="合同编号" align="center" prop="contractNumber" />
            <el-table-column label="客户合同" align="center" prop="customerContract" />
            <el-table-column label="账单模版编号" align="center" prop="billTemplateNumber" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="薪资项" align="center" prop="salaryItems">
                <template #default="scope">
                    <el-button type="primary" size="small" @click="handleViewSalaryItems(scope.row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column label="项目客服" align="center" prop="projectService" />
            <el-table-column label="薪资客服" align="center" prop="salaryService" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="55%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="客户名称:" prop="customerName">
                    <el-select class="width220" filterable v-model="obj.dialogForm.customerName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="薪资类别名称:" prop="categoryName">
                    <el-select class="width220" filterable v-model="obj.dialogForm.categoryName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in salaryCategoryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="薪资类别编号:" prop="categoryCode">
                    <el-select class="width220" filterable v-model="obj.dialogForm.categoryCode" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in salaryCategoryCodeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="默认税率表:" prop="taxRateTable">
                    <el-select class="width220" v-model="obj.dialogForm.taxRateTable" placeholder="请选择" clearable>
                        <el-option v-for="item in taxRateTableOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否拉取社保数据:" prop="pullSocialSecurity">
                    <el-select class="width220" v-model="obj.dialogForm.pullSocialSecurity" placeholder="请选择" clearable>
                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="拉哪月数据:" prop="pullMonth">
                    <el-input class="width220" v-model="obj.dialogForm.pullMonth" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="社保依据:" prop="socialSecurityBasis">
                    <el-select class="width220" filterable v-model="obj.dialogForm.socialSecurityBasis"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in socialSecurityBasisOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工资单格式:" prop="payslipFormat">
                    <el-select class="width220" filterable v-model="obj.dialogForm.payslipFormat" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in payslipFormatOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户合同:" prop="customerContract">
                    <el-input class="width220" v-model="obj.dialogForm.customerContract" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="账单模板:" prop="billTemplateNumber">
                    <el-input class="width220" v-model="obj.dialogForm.billTemplateNumber" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="增值税率:" prop="vatRate">
                    <el-select class="width220" filterable v-model="obj.dialogForm.vatRate" placeholder="请选择" clearable>
                        <el-option v-for="item in vatRateOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="附加税率:" prop="additionalTaxRate">
                    <el-input style="width: calc(220px - 15px);" v-model="obj.dialogForm.additionalTaxRate"
                        placeholder="请输入" />
                    <span class="ml5">%</span>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 薪资项 -->
        <el-dialog v-model="obj.salaryItemsDialogShow" title="查看薪资项" width="35%" append-to-body draggable>
            <el-table :data="obj.salaryItemsData" border>
                <el-table-column label="薪资项目名称" align="center" prop="salaryItemName" />
                <el-table-column label="薪资项目编号" align="center" prop="salaryItemCode" />
                <el-table-column label="英文名称" align="center" prop="englishName" />
                <el-table-column label="审批状态" align="center" prop="approvalStatus" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="SalaryCategory">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 初始化字典
proxy.useDict();

// 客户选项
const customerOptions = ref([
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' },
    { value: '4', label: '客户D' },
    { value: '5', label: '客户E' }
]);

// 薪资类别选项
const salaryCategoryOptions = ref([
    { value: '1', label: '基本工资' },
    { value: '2', label: '绩效工资' },
    { value: '3', label: '岗位津贴' },
    { value: '4', label: '交通补贴' },
    { value: '5', label: '餐补' }
]);

// 薪资类别编号选项
const salaryCategoryCodeOptions = ref([
    { value: 'SC001', label: 'SC001' },
    { value: 'SC002', label: 'SC002' },
    { value: 'SC003', label: 'SC003' },
    { value: 'SC004', label: 'SC004' },
    { value: 'SC005', label: 'SC005' }
]);

// 状态选项
const statusOptions = ref([
    { value: '1', label: '生效' },
    { value: '0', label: '失效' }
]);

// 是否选项
const yesNoOptions = ref([
    { value: 'Y', label: '是' },
    { value: 'N', label: '否' }
]);

// 税率表选项
const taxRateTableOptions = ref([
    { value: '1', label: '一般税率表' },
    { value: '2', label: '优惠税率表' },
    { value: '3', label: '特殊税率表' }
]);

// 社保依据选项
const socialSecurityBasisOptions = ref([
    { value: '1', label: '实际工资' },
    { value: '2', label: '最低工资' },
    { value: '3', label: '固定比例' }
]);

// 工资单格式选项
const payslipFormatOptions = ref([
    { value: '1', label: '标准格式' },
    { value: '2', label: '简单格式' },
    { value: '3', label: '详细格式' }
]);

// 增值税率选项
const vatRateOptions = ref([
    { value: '0.03', label: '3%' },
    { value: '0.06', label: '6%' },
    { value: '0.09', label: '9%' },
    { value: '0.13', label: '13%' }
]);

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        categoryCode: 'SC001', // 薪资类别编号
        categoryName: '1', // 薪资类别名称
        taxRateTable: '1', // 默认税率表
        pullSocialSecurity: 'Y', // 是否拉取社保数据
        pullMonth: '1', // 拉哪月数据
        payslipFormat: '1', // 工资单格式
        status: '1', // 状态
        contractNumber: 'HT001', // 合同编号
        customerContract: 'KH001', // 客户合同
        billTemplateNumber: 'MB001', // 账单模版编号
        customerName: '1', // 客户名称
        salaryItems: '基本工资,绩效工资', // 薪资项
        projectService: '张三', // 项目客服
        salaryService: '李四' // 薪资客服
    },
    {
        id: 2,
        categoryCode: 'SC002', // 薪资类别编号
        categoryName: '2', // 薪资类别名称
        taxRateTable: '2', // 默认税率表
        pullSocialSecurity: 'N', // 是否拉取社保数据
        pullMonth: '2', // 拉哪月数据
        payslipFormat: '2', // 工资单格式
        status: '1', // 状态
        contractNumber: 'HT002', // 合同编号
        customerContract: 'KH002', // 客户合同
        billTemplateNumber: 'MB002', // 账单模版编号
        customerName: '2', // 客户名称
        salaryItems: '岗位津贴,交通补贴', // 薪资项
        projectService: '王五', // 项目客服
        salaryService: '赵六' // 薪资客服
    }
]

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null, // 客户名称
        categoryName: null, // 薪资类别名称
        categoryCode: null, // 薪资类别编号
        status: null, // 状态
    }, // 查询表单
    rules: {
        customerName: [{ required: true, message: '请选择客户名称', trigger: 'blur' }],
        categoryName: [{ required: true, message: '请选择薪资类别名称', trigger: 'blur' }],
        categoryCode: [{ required: true, message: '请选择薪资类别编号', trigger: 'blur' }],
        taxRateTable: [{ required: true, message: '请选择默认税率表', trigger: 'blur' }],
        pullSocialSecurity: [{ required: true, message: '请选择是否拉取社保数据', trigger: 'blur' }],
        payslipFormat: [{ required: true, message: '请选择工资单格式', trigger: 'blur' }],
    }, // 表单验证规则
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    salaryItemsDialogShow: false, // 薪资项弹出框
    salaryItemsData: [], // 薪资项数据
    ids: [], // 选中的id
    title: "", // 标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟异步请求
    setTimeout(() => {
        // 这里可以根据查询条件过滤数据
        const filteredData = mockTableData.filter(item => {
            const matchCustomerName = !obj.queryParams.customerName ||
                item.customerName === obj.queryParams.customerName;
            const matchCategoryName = !obj.queryParams.categoryName ||
                item.categoryName === obj.queryParams.categoryName;
            const matchCategoryCode = !obj.queryParams.categoryCode ||
                item.categoryCode === obj.queryParams.categoryCode;
            const matchStatus = !obj.queryParams.status ||
                item.status === obj.queryParams.status;
            return matchCustomerName && matchCategoryName && matchCategoryCode && matchStatus;
        });

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}



// 表单重置
function reset() {
    obj.dialogForm = {
        customerName: '',
        categoryName: '',
        categoryCode: '',
        taxRateTable: '',
        pullSocialSecurity: 'Y',
        pullMonth: '',
        socialSecurityBasis: '',
        payslipFormat: '',
        customerContract: '',
        billTemplateNumber: '',
        vatRate: '',
        additionalTaxRate: ''
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 行双击
function handleRowDblclick(row) {
    handleView(row);
}

/** 查看薪资项 */
function handleViewSalaryItems(row) {
    obj.salaryItemsDialogShow = true;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}
/** 查看按钮操作 */
function handleView(row) {
    reset();
    // 如果是从表格行点击进来的，直接使用行数据
    if (row) {
        obj.dialogForm = JSON.parse(JSON.stringify(row));
    }
    // 如果是从工具栏按钮点击进来的，使用选中的ID获取数据
    else if (obj.ids.length > 0) {
        const selectedItem = obj.tableData.find(item => item.id === obj.ids[0]);
        if (selectedItem) {
            obj.dialogForm = JSON.parse(JSON.stringify(selectedItem));
        }
    }
    obj.dialogShow = true;
    obj.title = "详情";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 如果是从表格行点击进来的，直接使用行数据
    if (row) {
        obj.dialogForm = JSON.parse(JSON.stringify(row));
    }
    // 如果是从工具栏按钮点击进来的，使用选中的ID获取数据
    else if (obj.ids.length > 0) {
        const selectedItem = obj.tableData.find(item => item.id === obj.ids[0]);
        if (selectedItem) {
            obj.dialogForm = JSON.parse(JSON.stringify(selectedItem));
        }
    }
    obj.dialogShow = true;
    obj.title = "修改";
}



/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 设置系统项目属性按钮操作 */
function handleSetSystemProjectAttribute() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    proxy.$modal.msgSuccess('设置系统项目属性操作成功');
}

/** 生效按钮操作 */
function handleEffective() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    // 将选中的数据状态设置为生效
    obj.tableData.forEach(item => {
        if (obj.ids.includes(item.id)) {
            item.status = '1';
        }
    });
    proxy.$modal.msgSuccess('生效操作成功');
}

/** 失效按钮操作 */
function handleInvalid() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    // 将选中的数据状态设置为失效
    obj.tableData.forEach(item => {
        if (obj.ids.includes(item.id)) {
            item.status = '0';
        }
    });
    proxy.$modal.msgSuccess('失效操作成功');
}

/** 复制按钮操作 */
function handleCopy() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    const selectedItem = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedItem) {
        const newItem = JSON.parse(JSON.stringify(selectedItem));
        newItem.id = obj.tableData.length > 0 ? Math.max(...obj.tableData.map(item => item.id)) + 1 : 1;
        newItem.categoryCode = newItem.categoryCode + '_copy';
        obj.tableData.push(newItem);
        proxy.$modal.msgSuccess('复制操作成功');
    }
}



/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped></style>