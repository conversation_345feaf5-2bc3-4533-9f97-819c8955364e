import autoImport from "unplugin-auto-import/vite";

/**
 * 创建自动导入配置
 * @returns {Object} 返回自动导入配置对象
 */
export default function createAutoImport() {
  // 使用 unplugin-auto-import 插件创建自动导入配置
  return autoImport({
    // 指定需要自动导入的库
    imports: [
      "vue", // 自动导入 Vue 相关的 API
      "vue-router", // 自动导入 Vue Router 相关的 API
      "pinia", // 自动导入 Pinia 相关的 API
    ],
    // 不生成 TypeScript 声明文件
    dts: false,
  });
}
