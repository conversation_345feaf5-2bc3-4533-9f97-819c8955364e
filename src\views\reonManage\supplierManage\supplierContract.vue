<!-- 供应商合同 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商/联系人:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="参保地省级:" prop="province">
                <el-select class="width220" v-model="obj.queryParams.province" placeholder="请选择" clearable
                    @change="handleProvinceChange">
                    <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="参保地市级:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择" clearable>
                    <el-option v-if="obj.queryParams.province" v-for="item in cityList" :key="item.code"
                        :label="item.city" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="客服负责人:" prop="username">
                <el-input class="width220" v-model="obj.queryParams.username" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增合同</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single"
                    @click="handleUpdate">修改合同</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain :disabled="obj.multiple" @click="handleRenew">续签合同</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain @click="handleAutoRenew">自动延顺</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain @click="handleUploadSupplement">上传补充协议</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleUploadFile">上传文件</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleAddRemark">新增/修改报价备注</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="供应商名称" align="center" prop="supplierName" />
            <el-table-column label="供应商状态" align="center" prop="supplierStatus" />
            <el-table-column label="供应商类型" align="center" prop="supplierType" />
            <el-table-column label="我方签约公司" align="center" prop="myCompany" />
            <el-table-column label="合同开始日期" align="center" prop="contractStartDate" />
            <el-table-column label="合同截止日期" align="center" prop="contractEndDate" />
            <el-table-column label="账单日" align="center" prop="billDate" />
            <el-table-column label="付款日" align="center" prop="paymentDate" />
            <el-table-column label="创建人" align="center" prop="createBy" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="70%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="供应商:" prop="supplierId">
                    <el-select class="width220" v-model="obj.dialogForm.supplierId" placeholder="请选择" clearable>
                        <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="供应商类型:" prop="supplierType">
                    <el-select class="width220" v-model="obj.dialogForm.supplierType" placeholder="请选择" clearable>
                        <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同类型:" prop="contractType">
                    <el-select class="width220" v-model="obj.dialogForm.contractType" placeholder="请选择" clearable>
                        <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="产品类型:" prop="productType">
                    <el-select class="width220" v-model="obj.dialogForm.productType" placeholder="请选择" clearable>
                        <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="我方签约公司:" prop="myCompany">
                    <el-select class="width220" v-model="obj.dialogForm.myCompany" placeholder="请选择" clearable>
                        <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同开始日期:" prop="contractStartDate">
                    <el-date-picker class="width220" v-model="obj.dialogForm.contractStartDate" placeholder="请选择"
                        clearable />
                </el-form-item>
                <el-form-item label="合同结束日期:" prop="contractEndDate">
                    <el-date-picker class="width220" v-model="obj.dialogForm.contractEndDate" placeholder="请选择"
                        clearable />
                </el-form-item>
                <el-form-item label="账单日:" prop="billDate">
                    <el-date-picker class="width220" v-model="obj.dialogForm.billDate" placeholder="请选择" clearable />
                </el-form-item>
                <el-form-item label="付款日:" prop="paymentDate">
                    <el-date-picker class="width220" v-model="obj.dialogForm.paymentDate" placeholder="请选择" clearable />
                </el-form-item>
                <el-form-item label="合同名称:" prop="contractName">
                    <el-input class="width220" v-model="obj.dialogForm.contractName" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="是否自动顺延:" prop="isAutoRenew">
                    <el-select class="width220" v-model="obj.dialogForm.isAutoRenew" placeholder="请选择" clearable>
                        <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否关联公司:" prop="isRelatedCompany">
                    <el-select class="width220" v-model="obj.dialogForm.isRelatedCompany" placeholder="请选择" clearable>
                        <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="关联公司:" prop="relatedCompany">
                    <el-button type="primary" round plain @click="handleAddRelatedCompany">点击选择关联公司</el-button>
                </el-form-item>
                <el-row>
                    <el-form-item label="备注:" prop="remark">
                        <el-input class="width420" type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入" />
                    </el-form-item>
                </el-row>
                <el-divider content-position="left">文件上传</el-divider>
                <FileUpload ref="fileUploadRef" />
                <el-divider content-position="left">报价单</el-divider>
                <el-row class="mb8">
                    <el-button type="primary" plain icon="Plus" @click="handleAdd">新增报价单</el-button>
                    <el-button type="danger" plain icon="Delete" @click="handleDelete">删除报价单</el-button>
                </el-row>
                <el-table border :data="obj.dialogForm.supplierList" style="width: 100%"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" type="index" align="center" width="60" />
                    <el-table-column label="报价单编号" align="center">
                        <template #default="scope">
                            <el-input style="width: 100%;" v-model="scope.row.quoteCode" placeholder="请输入" />
                        </template>
                    </el-table-column>
                    <el-table-column label="报价单名称" align="center">
                        <template #default="scope">
                            <el-input style="width: 100%;" v-model="scope.row.quoteName" placeholder="请输入" />
                        </template>
                    </el-table-column>
                    <el-table-column label="服务费类型" align="center">
                        <template #default="scope">
                            <el-select style="width: 100%;" v-model="scope.row.serviceType" placeholder="请选择" clearable>
                                <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="指定城市" align="center">
                        <template #default="scope">
                            <el-input style="width: 100%;" v-model="scope.row.city" placeholder="请输入" />
                        </template>
                    </el-table-column>
                    <el-table-column label="服务费" align="center">
                        <template #default="scope">
                            <el-input style="width: 100%;" v-model="scope.row.serviceFee" placeholder="请输入" />
                        </template>
                    </el-table-column>
                    <el-table-column label="是否含税" align="center">
                        <template #default="scope">
                            <el-select style="width: 100%;" v-model="scope.row.isTax" placeholder="请选择" clearable>
                                <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="税率" align="center">
                        <template #default="scope">
                            <el-input style="width: 100%;" v-model="scope.row.taxRate" placeholder="请输入" />
                        </template>
                    </el-table-column>
                    <el-table-column label="是否默认" align="center">
                        <template #default="scope">
                            <el-select style="width: 100%;" v-model="scope.row.isDefault" placeholder="请选择" clearable>
                                <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center">
                        <template #default="scope">
                            <el-input style="width: 100%;" v-model="scope.row.remark" placeholder="请输入" />
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 详情 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="60%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-tabs type="border-card">
                    <el-tab-pane label="基本信息">
                        <el-form-item label="合同名称:" prop="contractName">
                            <el-input class="width220" v-model="obj.dialogForm.contractName" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="合同编号:" prop="contractNo">
                            <el-input class="width220" v-model="obj.dialogForm.contractNo" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="供应商:" prop="supplierName">
                            <el-input class="width220" v-model="obj.dialogForm.supplierName" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="供应商类型:" prop="supplierType">
                            <el-input class="width220" v-model="obj.dialogForm.supplierType" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="合同类型:" prop="contractType">
                            <el-input class="width220" v-model="obj.dialogForm.contractType" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="产品类型:" prop="productType">
                            <el-input class="width220" v-model="obj.dialogForm.productType" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="我方签约公司:" prop="myCompany">
                            <el-input class="width220" v-model="obj.dialogForm.myCompany" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="合同开始日期:" prop="contractStartDate">
                            <el-input class="width220" v-model="obj.dialogForm.contractStartDate" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="合同结束日期:" prop="contractEndDate">
                            <el-input class="width220" v-model="obj.dialogForm.contractEndDate" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="账单日:" prop="billDate">
                            <el-input class="width220" v-model="obj.dialogForm.billDate" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="付款日:" prop="paymentDate">
                            <el-input class="width220" v-model="obj.dialogForm.paymentDate" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="是否自动顺延:" prop="isAutoRenew">
                            <el-input class="width220" v-model="obj.dialogForm.isAutoRenew" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="是否关联公司:" prop="isRelatedCompany">
                            <el-input class="width220" v-model="obj.dialogForm.isRelatedCompany" placeholder="请输入" />
                        </el-form-item>
                        <el-divider content-position="left">关联公司</el-divider>
                        <el-form-item>
                            <el-checkbox-group v-model="obj.dialogForm.relatedCompany">
                                <el-checkbox label="上海十发信息技术中心" value="Value1" border />
                                <el-checkbox label="南京定山信息技术有限公司" value="Value2" border />
                                <el-checkbox label="南京易联碧诚人力资源顾问有限公司" value="Value3" border />
                                <el-checkbox label="南京致仟人力资源顾问有限公司" value="Value4" border />
                                <el-checkbox label="北京定山信息技术有限公司" value="Value5" border />
                            </el-checkbox-group>
                        </el-form-item>
                        <el-form-item label="备注:" prop="remark">
                            <el-input class="width420" type="textarea" v-model="obj.dialogForm.remark"
                                placeholder="请输入" />
                        </el-form-item>
                        <el-divider content-position="left">文件展示</el-divider>

                        <el-divider content-position="left">报价单</el-divider>
                        <el-table :data="obj.dialogForm.supplierList" style="width: 100%">
                            <el-table-column label="序号" align="center" prop="quoteCode" />
                            <el-table-column label="报价编号" align="center" prop="quoteName" />
                            <el-table-column label="报价单名称" align="center" prop="serviceType" />
                            <el-table-column label="服务费类型" align="center" prop="serviceType" />
                            <el-table-column label="指定城市" align="center" prop="city" />
                            <el-table-column label="服务费" align="center" prop="serviceFee" />
                            <el-table-column label="是否含税" align="center" prop="isTax" />
                            <el-table-column label="税率" align="center" prop="taxRate" />
                            <el-table-column label="是否默认" align="center" prop="isDefault" />
                        </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="流程信息">
                        <ProcessInformation :tableData="obj.dialogForm.supplierList" />
                    </el-tab-pane>
                </el-tabs>
            </el-form>
            <template #footer>
                <el-button @click="obj.dialogShow2 = false">关 闭</el-button>
            </template>
        </el-dialog>
        <!-- 新增/修改报价单 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow3" width="30%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item label="供应商名称:" prop="supplierName">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.supplierName" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="合同编号:" prop="contractNo">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.contractNo" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="合同名称:" prop="contractName">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.contractName" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="报价备注备注内容:" prop="remark">
                    <el-input style="width: 100%;" type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入"
                        clearable />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="obj.dialogShow3 = false">取 消</el-button>
            </template>
        </el-dialog>

        <!-- 关联公司 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow4" width="50%" append-to-body>
            <el-button class="mb10" type="warning" round @click="submitForm">保存</el-button>
            <el-table :data="obj.dialogForm.supplierList" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="公司编号" align="center" prop="quoteCode" />
                <el-table-column label="公司名称" align="center" prop="handler" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="SupplierContract">
import { listInstance, delInstance } from '@/api/reonApi/instance';
import ChinaCitys from "@/utils/ChinaCitys.json";
import ProcessInformation from '@/views/reonManage/components/processInformation.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        province: null,
        city: null,
        username: null,
    },//查询表单
    rules: {
        username: [
            { required: true, message: "2不能为空", trigger: "blur" }
        ],
        email: [
            { required: true, message: "3不能为空", trigger: "blur" }
        ],
    },
    total: 0,//总条数

    tableData: [
        {
            contractCode: 'kh11212',
            contractName: '合同名称',
            supplierName: '供应商名称',
            supplierType: '供应商类型',
            contractType: '合同类型',
            productType: '产品类型',
            myCompany: '我方签约公司',
            contractStartDate: '2021-01-01',
            contractEndDate: '2021-01-01',
            billDate: '2021-01-01',
            paymentDate: '2021-01-01',
            createBy: '创建人',
            createTime: '2021-01-01',
            updateBy: '修改人',
            updateTime: '2021-01-01',
            remark: '备注',
            isAutoRenew: '是否自动顺延',
            isRelatedCompany: '是否关联公司',
            relatedCompany: '关联公司',
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //新增、修改弹出框
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listInstance(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}
const cityList = ref([]);//市列表
function handleProvinceChange(code) {
    console.log(code);
    obj.queryParams.city = null;
    cityList.value = ChinaCitys.filter(item => item.code == code)[0].citys;
}



// 表单重置
function reset() {
    obj.dialogForm = {
        supplierList: []
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    console.log(obj.queryParams);
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增报价单";

    obj.dialogForm.supplierList.push({});
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || obj.ids
    getUsers(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改报价单";
    });
}
/** 续约按钮操作 */
function handleRenew(row) {
    obj.dialogShow3 = true;
    obj.title = "续约";
}



/** 详情按钮操作 */
function handleDetail(row) {
    obj.dialogShow2 = true;
    obj.title = "详情";
}
/** 自动续约按钮操作 */
function handleAutoRenew(row) {

    obj.title = "自动续约";
}
/** 上传补充协议按钮操作 */
function handleUploadSupplement(row) {

    obj.title = "上传补充协议";
}
/** 上传文件按钮操作 */
function handleUploadFile(row) {

    obj.title = "上传文件";
}
/** 添加备注按钮操作 */
function handleAddRemark(row) {
    obj.dialogShow3 = true;
    obj.title = "添加备注";
}

/** 添加关联公司按钮操作 */
function handleAddRelatedCompany() {
    obj.dialogShow4 = true;
    obj.title = "关联公司";
}










/** 提交按钮 */
function submitForm() {
    console.log(obj.dialogForm);
    return
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}


getList();
</script>
<style lang="scss" scoped></style>