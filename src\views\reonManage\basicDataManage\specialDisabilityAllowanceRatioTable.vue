<!-- 特殊残障金比率表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="城市:" prop="cityCode">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="计算方式:" prop="calculationMethod">
                <el-select class="width220" filterable v-model="obj.queryParams.calculationMethod" placeholder="请选择计算方式"
                    clearable>
                    <el-option label="应发计算" value="1" />
                    <el-option label="实发计算" value="2" />
                    <el-option label="固定金额" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请选择合同"
                    @focus="handleContract" clearable readonly />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" type="index" width="80" align="center" />
            <el-table-column label="城市名称" align="center" prop="cityCode" />
            <el-table-column label="扣缴义务人名称" align="center" prop="withholdingName" />
            <el-table-column label="扣缴义务人类型" align="center" prop="type" />
            <el-table-column label="残障金比率" align="center" prop="rate" />
            <el-table-column label="合同编号" align="center" prop="contractNo" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="客户名称" align="center" prop="custName" />
            <el-table-column label="计算方式" align="center" prop="specialType" />
            <el-table-column label="固定金额" align="center" prop="fixed" />
            <el-table-column label="reon系统封顶金额" align="center" prop="reonUpperLimit" />
            <el-table-column label="收取上限" align="center" prop="upperLimit" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="25%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item label="城市" prop="cityCode">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.cityCode" placeholder="请选择城市" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="残障金比率" prop="rate">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.rate" placeholder="例:0.015" />
                </el-form-item>
                <el-form-item label="扣缴义务人类型" prop="withholdingType">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.withholdingType" placeholder="请选择扣缴义务人类型"
                        clearable>
                        <el-option v-for="item in withholdingTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="扣缴义务人名称" prop="withholdingName">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.withholdingName" placeholder="请输入扣缴义务人名称" />
                </el-form-item>
                <el-form-item label="合同名称" prop="contractName">
                    <el-input readonly style="width: 100%;" v-model="obj.dialogForm.contractName" placeholder="请选择合同"
                        @click="handleCustomer" />
                </el-form-item>
                <el-form-item label="合同编号" prop="contractNo">
                    <el-input readonly style="width: 100%;" v-model="obj.dialogForm.contractNo" placeholder="自动带出" />
                </el-form-item>
                <el-form-item label="客户名称" prop="custName">
                    <el-input readonly style="width: 100%;" v-model="obj.dialogForm.custName" placeholder="自动带出" />
                </el-form-item>

                <el-form-item label="计算方式" prop="specialType">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.specialType" placeholder="请选择计算方式"
                        clearable>
                        <el-option label="应发计算" value="1" />
                        <el-option label="实发计算" value="2" />
                        <el-option label="固定金额" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="固定金额" prop="fixed">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.fixed" placeholder="请输入固定金额" />
                </el-form-item>
                <el-form-item label="reon系统封顶金额" prop="reonUpperLimit">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.reonUpperLimit"
                        placeholder="请输入reon系统封顶金额" />
                </el-form-item>
                <el-form-item label="收取上限" prop="upperLimit">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.upperLimit" placeholder="请输入收取上限" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <contract v-model:show="contractShow" @select="handleSelect" />
    </div>
</template>

<script setup name="SpecialDisabilityAllowanceRatioTable">

import { useAreaStore } from '@/store/modules/area'
import contract from '@/views/reonManage/components/contract.vue'
import { listImport } from "@/api/reonApi/import";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据

// 扣缴义务人类型选项
const withholdingTypeOptions = [
    { value: '1', label: '企业' },
    { value: '2', label: '个人' },
    { value: '3', label: '其他' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cityCode: null,
        calculationMethod: null,
        contractName: null,
    },//查询表单
    customerQueryParams: {
        pageNum: 1,
        pageSize: 10,
    },//客户查询表单
    rules: {
        cityCode: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        rate: [{ required: true, message: '请输入残障金比率', trigger: 'blur' }],
        withholdingType: [{ required: true, message: '请选择扣缴义务人类型', trigger: 'blur' }],
        withholdingName: [{ required: true, message: '请输入扣缴义务人名称', trigger: 'blur' }],
        contractName: [{ required: true, message: '请选择合同', trigger: 'blur' }],
        specialType: [{ required: true, message: '请选择计算方式', trigger: 'blur' }],
    },
    total: 0,//总条数
    customerTotal: 0,//客户总条数
    tableData: [
        {
            id: 1,
            cityCode: '110000',
            cityName: '北京市',
            withholdingName: '北京扣缴义务人',
            withholdingType: '1',
            type: '企业',
            rate: '0.015',
            contractNo: 'HT20230501001',
            contractName: '残障金服务合同A',
            custName: '客户A',
            specialType: '1',
            fixed: '',
            reonUpperLimit: '10000',
            upperLimit: '8000'
        },
        {
            id: 2,
            cityCode: '310000',
            cityName: '上海市',
            withholdingName: '上海扣缴义务人',
            withholdingType: '2',
            type: '个人',
            rate: '0.02',
            contractNo: 'HT20230502001',
            contractName: '残障金服务合同B',
            custName: '客户B',
            specialType: '2',
            fixed: '',
            reonUpperLimit: '12000',
            upperLimit: '10000'
        },
        {
            id: 3,
            cityCode: '440100',
            cityName: '广州市',
            withholdingName: '广州扣缴义务人',
            withholdingType: '3',
            type: '其他',
            rate: '',
            contractNo: 'HT20230503001',
            contractName: '残障金服务合同C',
            custName: '客户C',
            specialType: '3',
            fixed: '5000',
            reonUpperLimit: '15000',
            upperLimit: '12000'
        }
    ],//列表
    customerTableData: [
        {
            id: 1,
            no: 'HT20230501001',
            name: '残障金服务合同A',
            customer: '客户A',
            type: '残障金服务合同'
        },
        {
            id: 2,
            no: 'HT20230502001',
            name: '残障金服务合同B',
            customer: '客户B',
            type: '残障金服务合同'
        },
        {
            id: 3,
            no: 'HT20230503001',
            name: '残障金服务合同C',
            customer: '客户C',
            type: '残障金服务合同'
        }
    ],//客户列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
})

const contractShow = ref(false);
/** 选择 */
function handleSelect(row) {
    console.log("已选产品:", row);
    if (row) {
        if (obj.dialogShow) {
            // 弹窗中选择合同
            obj.dialogForm.contractName = row.contractName;
            obj.dialogForm.contractNo = row.contractCode;
            obj.dialogForm.custName = row.customerName;
        } else {
            // 查询条件中选择合同
            obj.queryParams.contractName = row.contractName;
        }
    } else {
        if (obj.dialogShow) {
            obj.dialogForm.contractName = null;
            obj.dialogForm.contractNo = null;
            obj.dialogForm.custName = null;
        } else {
            obj.queryParams.contractName = null;
        }
    }
    contractShow.value = false
}
/** 合同 */
function handleContract() {
    contractShow.value = true;
}
/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = lodash.cloneDeep(row || obj.tableData.find(item => obj.ids.includes(item.id)));
    obj.dialogShow = true;
    obj.title = "修改";
}

/** 客户信息 */
function handleCustomer() {
    contractShow.value = true;
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}
getList();
</script>
<style lang="scss" scoped></style>