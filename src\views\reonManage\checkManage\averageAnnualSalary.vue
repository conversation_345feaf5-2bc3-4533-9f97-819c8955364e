<!-- 年度平均薪资 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="年度:" prop="year">
                <el-select class="width220" v-model="obj.queryParams.year" placeholder="请选择年度" clearable>
                    <el-option label="2023年" value="2023" />
                    <el-option label="2022年" value="2022" />
                    <el-option label="2021年" value="2021" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资开始月:" prop="salaryStartMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryStartMonth" type="month"
                    placeholder="请选择开始月" clearable />
            </el-form-item>
            <el-form-item label="工资截止月:" prop="salaryEndMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryEndMonth" type="month"
                    placeholder="请选择截止月" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="姓名:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="请输入姓名" clearable />
            </el-form-item>
            <el-form-item label="是否单立户:" prop="isSingleAccount">
                <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="withholdingAgent">
                <el-input class="width220" v-model="obj.queryParams.withholdingAgent" placeholder="请输入扣缴义务人"
                    clearable />
            </el-form-item>
            <el-form-item label="是否在职:" prop="isOnJob">
                <el-select class="width220" v-model="obj.queryParams.isOnJob" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资发放状态:" prop="salaryStatus">
                <el-select class="width220" v-model="obj.queryParams.salaryStatus" placeholder="请选择状态" clearable>
                    <el-option label="已发放" value="1" />
                    <el-option label="未发放" value="0" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="success" @click="handleCalculate">计算</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" type="index" width="60" />
            <el-table-column label="客户名称" align="center" prop="customerName" width="180" />
            <el-table-column label="薪资类型" align="center" prop="salaryType" />
            <el-table-column label="是否单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="年度" align="center" prop="year" />
            <el-table-column label="月份数" align="center" prop="monthCount" />
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="手机号码" align="center" prop="phoneNumber" />
            <el-table-column label="是否在职" align="center" prop="isOnJob" />
            <el-table-column label="开户银行" align="center" prop="bank" />
            <el-table-column label="银行卡号码" align="center" prop="bankCardNumber" />
            <el-table-column label="薪资发放状态" align="center" prop="salaryStatus" />
            <el-table-column label="工资发放地" align="center" prop="salaryLocation" />
            <el-table-column label="扣缴义务人名称" align="center" prop="withholdingAgent" />
            <el-table-column label="应发工资" align="center" prop="grossSalary" />
            <el-table-column label="总代扣社会保险公积金" align="center">
                <el-table-column prop="pensionInsurance" label="养老保险" align="center" />
                <el-table-column prop="medicalInsurance" label="医疗保险" align="center" />
                <el-table-column prop="longTermCareInsurance" label="个人长护险" align="center" />
                <el-table-column prop="majorMedicalSubsidy" label="大额医疗补助" align="center" />
                <el-table-column prop="supplementaryMedicalInsurance" label="补充医疗保险" align="center" />
                <el-table-column prop="outpatientMedical" label="门诊医疗(社保)" align="center" />
                <el-table-column prop="criticalIllnessInsuranceAfterTax" label="大病保险(税后扣除)" align="center" />
                <el-table-column prop="supplementaryMedicalInsuranceAfterTax" label="补充医疗保险(税后扣除)" align="center" />
                <el-table-column prop="longTermCareInsuranceAfterTax" label="个人长护险(税后扣除)" align="center" />
                <el-table-column prop="unemploymentInsurance" label="失业保险" align="center" />
                <el-table-column prop="criticalIllnessInsurance" label="大病保险" align="center" />
                <el-table-column prop="housingFund" label="住房公积金" align="center" />
            </el-table-column>
            <el-table-column label="应税工资(含个税基数)" align="center" prop="taxableSalary" />
            <el-table-column label="实发合计" align="center" prop="netSalary" />
            <el-table-column label="服务费" align="center" prop="serviceFee" />
            <el-table-column label="跨行手续费" align="center" prop="interBankFee" />
            <el-table-column label="工会费" align="center" prop="unionFee" />
            <el-table-column label="费用合计" align="center" prop="totalFee" />
            <el-table-column label="税金合计" align="center" prop="totalTax" />
            <el-table-column label="企业支付总计" align="center" prop="totalCompanyPayment" />
        </el-table>

        <el-table class="mt20" :data="obj.tableData" border>
            <el-table-column label="序号" align="center" type="index" />
            <el-table-column label="客户名称" align="center" prop="customerName" width="180" />
            <el-table-column label="薪资类型" align="center" prop="salaryType" />
            <el-table-column label="是否单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="年度" align="center" prop="year" />
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="薪资发放状态" align="center" prop="salaryStatus" />
            <el-table-column label="工资发放地" align="center" prop="salaryLocation" />
            <el-table-column label="扣缴义务人名称" align="center" prop="withholdingAgent" />
            <el-table-column label="总应发工资" align="center" prop="totalGrossSalary" />
            <el-table-column label="平均工资" align="center" prop="averageSalary" />
            <el-table-column label="总代扣社会保险公积金" align="center">
                <el-table-column prop="totalPensionInsurance" label="养老保险" align="center" />
                <el-table-column prop="totalMedicalInsurance" label="医疗保险" align="center" />
                <el-table-column prop="totalLongTermCareInsurance" label="个人长护险" align="center" />
                <el-table-column prop="totalMajorMedicalSubsidy" label="大额医疗补助" align="center" />
                <el-table-column prop="totalSupplementaryMedicalInsurance" label="补充医疗保险" align="center" />
                <el-table-column prop="totalOutpatientMedical" label="门诊医疗(社保)" align="center" />
                <el-table-column prop="totalCriticalIllnessInsuranceAfterTax" label="大病保险(税后扣除)" align="center" />
                <el-table-column prop="totalSupplementaryMedicalInsuranceAfterTax" label="补充医疗保险(税后扣除)" align="center"
                    width="180" />
                <el-table-column prop="totalLongTermCareInsuranceAfterTax" label="个人长护险(税后扣除)" align="center" />
                <el-table-column prop="totalUnemploymentInsurance" label="失业保险" align="center" />
                <el-table-column prop="totalCriticalIllnessInsurance" label="大病保险" align="center" />
                <el-table-column prop="totalHousingFund" label="住房公积金" align="center" />
            </el-table-column>
            <el-table-column label="应税工资（含个税基数）" align="center" prop="totalTaxableSalary" />
            <el-table-column label="实发合计" align="center" prop="totalNetSalary" />
        </el-table>

        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="AverageAnnualSalary">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        year: null,
        salaryStartMonth: null,
        salaryEndMonth: null,
        idNumber: null,
        name: null,
        isSingleAccount: null,
        withholdingAgent: null,
        isOnJob: null,
        salaryStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerName: '客户名称1',
                salaryType: '工资',
                isSingleAccount: '是',
                year: '2023',
                monthCount: '12',
                name: '张三',
                idNumber: '110101199001011234',
                phoneNumber: '***********',
                isOnJob: '是',
                bank: '中国银行',
                bankCardNumber: '6222021234567890123',
                salaryStatus: '已发放',
                salaryLocation: '北京',
                withholdingAgent: '上海鼎捷数智软件有限公司',
                grossSalary: '10000',
                pensionInsurance: '800',
                medicalInsurance: '200',
                longTermCareInsurance: '100',
                majorMedicalSubsidy: '50',
                supplementaryMedicalInsurance: '100',
                outpatientMedical: '50',
                criticalIllnessInsuranceAfterTax: '100',
                supplementaryMedicalInsuranceAfterTax: '100',
                longTermCareInsuranceAfterTax: '50',
                unemploymentInsurance: '100',
                criticalIllnessInsurance: '100',
                housingFund: '1200',
                taxableSalary: '8000',
                netSalary: '7000',
                serviceFee: '200',
                interBankFee: '10',
                unionFee: '50',
                totalFee: '260',
                totalTax: '1000',
                totalCompanyPayment: '11260',
                totalGrossSalary: '120000',
                averageSalary: '10000',
                totalPensionInsurance: '9600',
                totalMedicalInsurance: '2400',
                totalLongTermCareInsurance: '1200',
                totalMajorMedicalSubsidy: '600',
                totalSupplementaryMedicalInsurance: '1200',
                totalOutpatientMedical: '600',
                totalCriticalIllnessInsuranceAfterTax: '1200',
                totalSupplementaryMedicalInsuranceAfterTax: '1200',
                totalLongTermCareInsuranceAfterTax: '600',
                totalUnemploymentInsurance: '1200',
                totalCriticalIllnessInsurance: '1200',
                totalHousingFund: '14400',
                totalTaxableSalary: '96000',
                totalNetSalary: '84000'
            },
            {
                id: 2,
                customerName: '客户名称2',
                salaryType: '工资',
                isSingleAccount: '否',
                year: '2023',
                monthCount: '12',
                name: '李四',
                idNumber: '110101199001021234',
                phoneNumber: '***********',
                isOnJob: '是',
                bank: '中国工商银行',
                bankCardNumber: '6222021234567890124',
                salaryStatus: '已发放',
                salaryLocation: '上海',
                withholdingAgent: '上海鼎捷数智软件有限公司',
                grossSalary: '12000',
                pensionInsurance: '960',
                medicalInsurance: '240',
                longTermCareInsurance: '120',
                majorMedicalSubsidy: '60',
                supplementaryMedicalInsurance: '120',
                outpatientMedical: '60',
                criticalIllnessInsuranceAfterTax: '120',
                supplementaryMedicalInsuranceAfterTax: '120',
                longTermCareInsuranceAfterTax: '60',
                unemploymentInsurance: '120',
                criticalIllnessInsurance: '120',
                housingFund: '1440',
                taxableSalary: '9600',
                netSalary: '8400',
                serviceFee: '240',
                interBankFee: '12',
                unionFee: '60',
                totalFee: '312',
                totalTax: '1200',
                totalCompanyPayment: '13512',
                totalGrossSalary: '144000',
                averageSalary: '12000',
                totalPensionInsurance: '11520',
                totalMedicalInsurance: '2880',
                totalLongTermCareInsurance: '1440',
                totalMajorMedicalSubsidy: '720',
                totalSupplementaryMedicalInsurance: '1440',
                totalOutpatientMedical: '720',
                totalCriticalIllnessInsuranceAfterTax: '1440',
                totalSupplementaryMedicalInsuranceAfterTax: '1440',
                totalLongTermCareInsuranceAfterTax: '720',
                totalUnemploymentInsurance: '1440',
                totalCriticalIllnessInsurance: '1440',
                totalHousingFund: '17280',
                totalTaxableSalary: '115200',
                totalNetSalary: '100800'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>