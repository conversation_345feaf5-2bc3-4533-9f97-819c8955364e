import useUserStore from "@/store/modules/user";

/**
 * 验证用户是否具备指定权限
 * @param {string} permission - 需要验证的权限字符串
 * @returns {boolean} - 如果用户具备指定权限，返回 true，否则返回 false
 */
function authPermission(permission) {
  // 定义一个表示所有权限的字符串
  const all_permission = "*:*:*";
  // 从用户存储中获取当前用户的权限列表
  const permissions = useUserStore().permissions;

  // 检查 permission 是否是一个非空字符串
  if (permission && permission.length > 0) {
    // 检查当前用户的权限列表中是否存在满足条件的权限
    return permissions.some((v) => {
      // 如果权限是通配符或者权限列表中包含该权限，则返回 true
      return all_permission === v || v === permission;
    });
  } else {
    // 如果 permission 不是一个有效的字符串，则返回 false
    return false;
  }
}

/**
 * 验证用户是否具备指定角色
 * @param {string} role - 需要验证的角色字符串
 * @returns {boolean} - 如果用户具备指定角色，返回 true，否则返回 false
 */
function authRole(role) {
  // 定义一个表示超级管理员的字符串
  const super_admin = "admin";
  // 从用户存储中获取当前用户的角色列表
  const roles = useUserStore().roles;

  // 检查 role 是否是一个非空字符串
  if (role && role.length > 0) {
    // 检查当前用户的角色列表中是否存在满足条件的角色
    return roles.some((v) => {
      // 如果角色是超级管理员或者角色列表中包含该角色，则返回 true
      return super_admin === v || v === role;
    });
  } else {
    // 如果 role 不是一个有效的字符串，则返回 false
    return false;
  }
}

export default {
  // 验证用户是否具备某权限
  hasPermi(permission) {
    return authPermission(permission);
  },
  // 验证用户是否含有指定权限，只需包含其中一个
  hasPermiOr(permissions) {
    return permissions.some((item) => {
      return authPermission(item);
    });
  },
  // 验证用户是否含有指定权限，必须全部拥有
  hasPermiAnd(permissions) {
    return permissions.every((item) => {
      return authPermission(item);
    });
  },
  // 验证用户是否具备某角色
  hasRole(role) {
    return authRole(role);
  },
  // 验证用户是否含有指定角色，只需包含其中一个
  hasRoleOr(roles) {
    return roles.some((item) => {
      return authRole(item);
    });
  },
  // 验证用户是否含有指定角色，必须全部拥有
  hasRoleAnd(roles) {
    return roles.every((item) => {
      return authRole(item);
    });
  },
};
