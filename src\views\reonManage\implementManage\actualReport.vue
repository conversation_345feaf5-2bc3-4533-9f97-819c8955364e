<!-- 实做报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-select class="width220" v-model="obj.queryParams.customer" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable
                    @change="handleWelfarePackageChange">
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报表年月:" prop="reportMonth">
                <el-select class="width220" v-model="obj.queryParams.reportMonth" placeholder="请选择" clearable>
                    <el-option v-for="item in reportMonthOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="锁定状态:" prop="lockStatus">
                <el-select class="width220" v-model="obj.queryParams.lockStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in lockStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="生成状态:" prop="generateStatus">
                <el-select class="width220" v-model="obj.queryParams.generateStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in generateStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付状态:" prop="paymentStatus">
                <el-select class="width220" v-model="obj.queryParams.paymentStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in paymentStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="单立户名称:" prop="singleAccountName">
                <el-select class="width220" v-model="obj.queryParams.singleAccountName" placeholder="请选择" clearable>
                    <el-option v-for="item in singleAccountOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-col :span="12">
                <el-card class="export-card">
                    <div class="tag-container">
                        <span class="tag-label">报表需导出福利包:</span>
                        <div class="tags-wrapper" :class="{ 'tags-expanded': exportPackages.length > 3 }">
                            <template>
                                <el-tag v-for="tag in exportPackages" :key="tag.value" closable :type="tag.type"
                                    class="welfare-tag" @close="handleRemoveExportPackage(tag)">
                                    {{ tag.name }}
                                </el-tag>
                            </template>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-row :gutter="10" class="mb8 mt10">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" icon="Upload" @click="handleGenerateReport">生成报表</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" icon="Download" @click="handleExportReport">导出报表</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" icon="Lock" @click="handleLock">锁定</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" icon="Unlock" @click="handleUnlock">解锁</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="Lock" @click="handlePersonalLock">个人锁定</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="是否是单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="单立户名称" align="center" prop="singleAccountName" />
            <el-table-column label="费用类型" align="center" prop="feeType" />
            <el-table-column label="支付中金额" align="center" prop="payingAmount" />
            <el-table-column label="支付完成金额" align="center" prop="paidAmount" />
            <el-table-column label="费用金额" align="center" prop="feeAmount" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="锁定年月" align="center" prop="lockMonth" />
            <el-table-column label="生成状态" align="center" prop="generateStatus" />
            <el-table-column label="锁定人" align="center" prop="lockPerson" />
            <el-table-column label="锁定状态" align="center" prop="lockStatus" />
            <el-table-column label="支付状态" align="center" prop="paymentStatus" />
            <el-table-column label="锁定时间" align="center" prop="lockTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 详情 -->
        <el-dialog v-model="obj.dialogShow" title="导入结果查看" width="60%">
            <border-box title="实做报表信息">
                <el-row :gutter="10" class="mb8">
                    <el-button type="success" icon="Lock" @click="handleLock">锁定</el-button>
                    <el-button type="warning" icon="Unlock" @click="handleLock">解锁</el-button>
                </el-row>
                <el-table :data="obj.tableData" border @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column align="center" prop="welfareHandler" label="福利办理方" />
                    <el-table-column align="center" prop="welfarePackageName" label="福利包名称" />
                    <el-table-column align="center" prop="isSingleAccount" label="是否是单立户" />
                    <el-table-column align="center" prop="feeType" label="费用类型" />
                    <el-table-column align="center" prop="feeAmount" label="费用金额" />
                    <el-table-column align="center" prop="customerName" label="客户名称" />
                    <el-table-column align="center" prop="lockMonth" label="锁定年月" />
                    <el-table-column align="center" prop="generateStatus" label="生成状态" />
                    <el-table-column align="center" prop="lockPerson" label="锁定人" />
                    <el-table-column align="center" prop="lockStatus" label="锁定状态" />
                    <el-table-column align="center" prop="lockTime" label="锁定时间" />
                </el-table>
            </border-box>
            <border-box title="实做报表信息">
                <el-form :model="obj.dialogForm" inline label-width="auto">
                    <el-form-item label="订单编号">
                        <el-input class="width220" v-model="obj.dialogForm.orderNo" />
                    </el-form-item>
                    <el-form-item label="费用类型">
                        <el-input class="width220" v-model="obj.dialogForm.feeType" />
                    </el-form-item>
                    <el-form-item label="锁定状态">
                        <el-input class="width220" v-model="obj.dialogForm.lockStatus" />
                    </el-form-item>
                    <el-form-item label="比例名称">
                        <el-input class="width220" v-model="obj.dialogForm.scaleName" />
                    </el-form-item>
                    <el-form-item label="比例编号">
                        <el-input class="width220" v-model="obj.dialogForm.scaleNo" />
                    </el-form-item>
                    <el-form-item label="证件号码">
                        <el-input class="width220" v-model="obj.dialogForm.idCard" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </border-box>
            <border-box title="费用信息">
                <el-row :gutter="10" class="mb8">
                    <el-button type="success" icon="Lock" @click="handleLock">锁定</el-button>
                    <el-button type="warning" icon="Unlock" @click="handleLock">解锁</el-button>
                </el-row>
                <el-table :data="obj.dialogData" border @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column align="center" prop="orderNo" label="订单号" />
                    <el-table-column align="center" prop="name" label="姓名" />
                    <el-table-column align="center" prop="lockStatus" label="锁定状态" />
                    <el-table-column align="center" prop="payStatus" label="支付状态" />
                    <el-table-column align="center" prop="feeType" label="费用类型" />
                    <el-table-column align="center" prop="totalAmount" label="总金额(不含滞纳金)" />
                    <el-table-column align="center" prop="enterprisePenalty" label="企业滞纳金" />
                    <el-table-column align="center" prop="personalPenalty" label="个人滞纳金" />
                    <el-table-column align="center" prop="lockStatus" label="锁定状态" />
                    <el-table-column align="center" prop="lockTime" label="锁定时间" />
                </el-table>
            </border-box>

        </el-dialog>
    </div>
</template>


<script setup name="ActualReport">

import { listScale } from "@/api/reonApi/scale";
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 单立户选项
const singleAccountOptions = [
    { value: '1', label: '单立户A' },
    { value: '2', label: '单立户B' },
    { value: '3', label: '单立户C' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 报表年月选项
const reportMonthOptions = [
    { value: '2023-01', label: '2023年01月' },
    { value: '2023-02', label: '2023年02月' },
    { value: '2023-03', label: '2023年03月' },
    { value: '2023-04', label: '2023年04月' },
    { value: '2023-05', label: '2023年05月' },
    { value: '2023-06', label: '2023年06月' }
];

// 锁定状态选项
const lockStatusOptions = [
    { value: '0', label: '未锁定' },
    { value: '1', label: '已锁定' },
    { value: '2', label: '个人锁定' }
];

// 生成状态选项
const generateStatusOptions = [
    { value: '0', label: '未生成' },
    { value: '1', label: '已生成' }
];

// 支付状态选项
const paymentStatusOptions = [
    { value: '0', label: '未支付' },
    { value: '1', label: '部分支付' },
    { value: '2', label: '全部支付' }
];

// 导出福利包列表
const exportPackages = ref([]);

// 获取福利包名称
function getWelfarePackageName(value) {
    const option = welfarePackageOptions.find(item => item.value === value);
    return option ? option.label : '';
}

// 从导出列表中移除福利包
function handleRemoveExportPackage(tag) {
    const index = exportPackages.value.findIndex(item => item.value === tag.value);
    if (index !== -1) {
        exportPackages.value.splice(index, 1);
    }
}

// 福利包选择变更
function handleWelfarePackageChange(value) {
    // 当清空选择时不做处理
    if (!value) return;

    // 添加到导出列表（包含重复提醒）
    addPackageToExportList(value);
}

// 添加福利包到导出列表
function addPackageToExportList(packageValue) {
    // 查找选中的福利包信息
    const selectedPackage = welfarePackageOptions.find(item => item.value === packageValue);
    if (!selectedPackage) return;

    // 检查是否已经添加过
    const exists = exportPackages.value.some(item => item.value === packageValue);
    if (exists) {
        // 已存在则显示提醒，但不重复添加
        proxy.$modal.msgInfo(`"${selectedPackage.label}"已添加到导出列表，不需要重复添加`);
        return;
    }

    // 添加到导出列表
    exportPackages.value.push({
        name: selectedPackage.label,
        value: selectedPackage.value,
        type: ''
    });
}

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        reportNo: 'RPT20230001',
        welfarePackageName: '标准福利包',
        reportType: '社保报表',
        reportFile: '社保报表_202301.xlsx',
        reportDate: '2023-01-15',
        successCount: 120,
        failCount: 0,
        processStatus: '处理完成',
        creator: '张三',
        lockStatus: '1',
        generateStatus: '1',
        paymentStatus: '2',
        lockMonth: '2023-01',
        lockPerson: '张三',
        lockTime: '2023-01-15',
        payingAmount: 120,
        paidAmount: 120,
        feeAmount: 120,
        customerName: '客户1',
        welfareHandler: '福利办理方1',
        welfarePackageName: '福利包1',
        isSingleAccount: '是',
        singleAccountName: '单立户1',
        feeType: '社保',
        reportType: '社保报表',
        reportFile: '社保报表_202301.xlsx',
        reportDate: '2023-01-15',
        successCount: 120,
        failCount: 0,
        processStatus: '处理完成',
        creator: '张三',
        lockStatus: '1',
        generateStatus: '1',
        paymentStatus: '2'
    }
];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        reportMonth: null, // 报表年月
        lockStatus: null, // 锁定状态
        generateStatus: null, // 生成状态
        paymentStatus: null, // 支付状态
        singleAccountName: null, // 单立户名称
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {}, // 导入表单
    dialogShow: false, // 导入弹窗
    ids: [], // 选中的id
    title: "" // 标题


})


/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;

    // 如果选择了福利包，添加到导出列表
    if (obj.queryParams.welfarePackageName) {
        // 添加到导出列表并处理重复提醒
        addPackageToExportList(obj.queryParams.welfarePackageName);
    }

    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 生成报表
function handleGenerateReport() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要生成报表的记录');
        return;
    }

    // 检查选中的记录是否已生成
    const selectedRows = obj.tableData.filter(item => obj.ids.includes(item.id));
    const alreadyGenerated = selectedRows.some(row => row.generateStatus === '1');

    if (alreadyGenerated) {
        proxy.$modal.msgError('选中的记录中包含已生成的报表，请重新选择');
        return;
    }

    proxy.$modal.confirm('确定要生成选中的报表吗？').then(() => {
        // 实际项目中应该调用API进行生成报表
        proxy.$modal.msgSuccess('报表生成成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

// 导出报表
function handleExportReport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 锁定
function handleLock() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要锁定的记录');
        return;
    }

    // 检查选中的记录是否已锁定
    const selectedRows = obj.tableData.filter(item => obj.ids.includes(item.id));
    const alreadyLocked = selectedRows.some(row => row.lockStatus === '1');

    if (alreadyLocked) {
        proxy.$modal.msgError('选中的记录中包含已锁定的报表，请重新选择');
        return;
    }

    proxy.$modal.confirm('确定要锁定选中的报表吗？').then(() => {
        // 实际项目中应该调用API进行锁定
        proxy.$modal.msgSuccess('锁定成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

// 解锁
function handleUnlock() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要解锁的记录');
        return;
    }

    // 检查选中的记录是否已锁定
    const selectedRows = obj.tableData.filter(item => obj.ids.includes(item.id));
    const notLocked = selectedRows.some(row => row.lockStatus === '0');

    if (notLocked) {
        proxy.$modal.msgError('选中的记录中包含未锁定的报表，请重新选择');
        return;
    }

    proxy.$modal.confirm('确定要解锁选中的报表吗？').then(() => {
        // 实际项目中应该调用API进行解锁
        proxy.$modal.msgSuccess('解锁成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

// 个人锁定
function handlePersonalLock() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要个人锁定的记录');
        return;
    }

    // 检查选中的记录是否已锁定
    const selectedRows = obj.tableData.filter(item => obj.ids.includes(item.id));
    const alreadyLocked = selectedRows.some(row => row.lockStatus === '1' || row.lockStatus === '2');

    if (alreadyLocked) {
        proxy.$modal.msgError('选中的记录中包含已锁定的报表，请重新选择');
        return;
    }

    proxy.$modal.confirm('确定要个人锁定选中的报表吗？').then(() => {
        // 实际项目中应该调用API进行个人锁定
        proxy.$modal.msgSuccess('个人锁定成功');
        getList(); // 刷新列表
    }).catch(() => { });
}

// 查看详情
function handleDetail(row) {
    obj.dialogShow = true;
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}


getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}

.mt10 {
    margin-top: 10px;
}

.tag-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
}

.tag-label {
    font-weight: bold;
    margin-right: 8px;
}

.welfare-tag {
    margin-right: 5px;
}

.add-button {
    margin-left: auto;
}

.note-text {
    color: #909399;
    font-size: 14px;
    font-style: italic;
}

.export-card {
    margin-bottom: 10px;
}

.tags-wrapper {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    min-width: 0;
    gap: 8px;
    transition: all 0.3s;
}

.tags-expanded {
    min-height: 60px;
}
</style>