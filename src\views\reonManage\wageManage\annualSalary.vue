<!-- 年度薪资查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="年月:" prop="yearMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.yearMonth" type="month" format="YYYY-MM"
                    value-format="YYYY-MM" placeholder="请选择年月" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions" cache-key="annualSalary" />
            <PrintButton print-id="print-table-area" print-title="年度薪资表格" icon="Printer" />
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="姓名" align="center" prop="name" width="80" />
            <el-table-column label="证件号码" align="center" prop="idNumber" width="150" />
            <el-table-column label="工资年月" align="center" prop="yearMonth" width="100" />
            <el-table-column label="手机号码" align="center" prop="phoneNumber" width="120" />
            <el-table-column label="客户名称" align="center" prop="customerName" min-width="150" />
            <el-table-column label="入离职状态" align="center" width="100">
                <template #default="scope">
                    {{ getEmploymentStatusName(scope.row.employmentStatus) }}
                </template>
            </el-table-column>
            <el-table-column label="开户银行" align="center" prop="bankName" width="120" />
            <el-table-column label="银行卡号码" align="center" prop="bankAccount" width="180" />
            <el-table-column label="工资发放地" align="center" width="100">
                <template #default="scope">
                    {{ getSalaryLocationName(scope.row.salaryLocation) }}
                </template>
            </el-table-column>
            <el-table-column label="扣缴义务人名称" align="center" width="150">
                <template #default="scope">
                    {{ getWithholdingName(scope.row.withholdingName) }}
                </template>
            </el-table-column>
            <el-table-column label="入职时间" align="center" prop="entryDate" width="100" />
            <el-table-column label="离职时间" align="center" prop="leaveDate" width="100" />
            <el-table-column label="年度总应发工资" align="center" width="120">
                <template #default="scope">
                    {{ formatAmount(scope.row.totalSalary) }}
                </template>
            </el-table-column>
            <el-table-column label="养老保险" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.pensionInsurance) }}
                </template>
            </el-table-column>
            <el-table-column label="医疗保险" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.medicalInsurance) }}
                </template>
            </el-table-column>
            <el-table-column label="个人长护险" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.personalLongTermCare) }}
                </template>
            </el-table-column>
            <el-table-column label="大额医疗补助" align="center" width="120">
                <template #default="scope">
                    {{ formatAmount(scope.row.majorMedicalSubsidy) }}
                </template>
            </el-table-column>
            <el-table-column label="补充医疗保险" align="center" width="120">
                <template #default="scope">
                    {{ formatAmount(scope.row.supplementaryMedical) }}
                </template>
            </el-table-column>
            <el-table-column label="门诊医疗(社保)" align="center" width="120">
                <template #default="scope">
                    {{ formatAmount(scope.row.outpatientMedical) }}
                </template>
            </el-table-column>
            <el-table-column label="大病保险(税后扣除)" align="center" width="150">
                <template #default="scope">
                    {{ formatAmount(scope.row.criticalIllnessAfterTax) }}
                </template>
            </el-table-column>
            <el-table-column label="补充医疗保险(税后扣除)" align="center" width="180">
                <template #default="scope">
                    {{ formatAmount(scope.row.supplementaryMedicalAfterTax) }}
                </template>
            </el-table-column>
            <el-table-column label="个人长护险(税后扣除)" align="center" width="180">
                <template #default="scope">
                    {{ formatAmount(scope.row.personalLongTermCareAfterTax) }}
                </template>
            </el-table-column>
            <el-table-column label="失业保险" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.unemploymentInsurance) }}
                </template>
            </el-table-column>
            <el-table-column label="大病保险" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.criticalIllness) }}
                </template>
            </el-table-column>
            <el-table-column label="住房公积金" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.housingFund) }}
                </template>
            </el-table-column>
            <el-table-column label="应税薪资（含个税基数）" align="center" width="180">
                <template #default="scope">
                    {{ formatAmount(scope.row.taxableSalary) }}
                </template>
            </el-table-column>
            <el-table-column label="总实发合计" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.totalActualPaid) }}
                </template>
            </el-table-column>
            <el-table-column label="经济补偿金应税金额" align="center" width="150">
                <template #default="scope">
                    {{ formatAmount(scope.row.economicCompensationTaxable) }}
                </template>
            </el-table-column>
            <el-table-column label="经济补偿金个税" align="center" width="120">
                <template #default="scope">
                    {{ formatAmount(scope.row.economicCompensationTax) }}
                </template>
            </el-table-column>
            <el-table-column label="经济补偿金实发金额" align="center" width="150">
                <template #default="scope">
                    {{ formatAmount(scope.row.economicCompensationActual) }}
                </template>
            </el-table-column>
            <el-table-column label="总服务费" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.totalServiceFee) }}
                </template>
            </el-table-column>
            <el-table-column label="跨行手续费" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.crossBankFee) }}
                </template>
            </el-table-column>
            <el-table-column label="工会费" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.unionFee) }}
                </template>
            </el-table-column>
            <el-table-column label="费用合计" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.totalExpense) }}
                </template>
            </el-table-column>
            <el-table-column label="总残障金" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.totalDisabilityFund) }}
                </template>
            </el-table-column>
            <el-table-column label="税金合计" align="center" width="100">
                <template #default="scope">
                    {{ formatAmount(scope.row.totalTax) }}
                </template>
            </el-table-column>
            <el-table-column label="企业支付总计" align="center" width="120">
                <template #default="scope">
                    {{ formatAmount(scope.row.totalCompanyPaid) }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <div id="print-table-area" class="print-area">
            <table class="print-table">
                <thead>
                    <tr>
                        <th v-if="obj.selectedColumns.includes('name')">姓名</th>
                        <th v-if="obj.selectedColumns.includes('idNumber')">证件号码</th>
                        <th v-if="obj.selectedColumns.includes('yearMonth')">工资年月</th>
                        <th v-if="obj.selectedColumns.includes('phoneNumber')">手机号码</th>
                        <th v-if="obj.selectedColumns.includes('customerName')">客户名称</th>
                        <th v-if="obj.selectedColumns.includes('employmentStatus')">入离职状态</th>
                        <th v-if="obj.selectedColumns.includes('bankName')">开户银行</th>
                        <th v-if="obj.selectedColumns.includes('bankAccount')">银行卡号码</th>
                        <th v-if="obj.selectedColumns.includes('salaryLocation')">工资发放地</th>
                        <th v-if="obj.selectedColumns.includes('withholdingName')">扣缴义务人名称</th>
                        <th v-if="obj.selectedColumns.includes('entryDate')">入职时间</th>
                        <th v-if="obj.selectedColumns.includes('leaveDate')">离职时间</th>
                        <th v-if="obj.selectedColumns.includes('totalSalary')">年度总应发工资</th>
                        <th v-if="obj.selectedColumns.includes('pensionInsurance')">养老保险</th>
                        <th v-if="obj.selectedColumns.includes('medicalInsurance')">医疗保险</th>
                        <th v-if="obj.selectedColumns.includes('personalLongTermCare')">个人长护险</th>
                        <th v-if="obj.selectedColumns.includes('majorMedicalSubsidy')">大额医疗补助</th>
                        <th v-if="obj.selectedColumns.includes('supplementaryMedical')">补充医疗保险</th>
                        <th v-if="obj.selectedColumns.includes('outpatientMedical')">门诊医疗(社保)</th>
                        <th v-if="obj.selectedColumns.includes('criticalIllnessAfterTax')">大病保险(税后扣除)</th>
                        <th v-if="obj.selectedColumns.includes('supplementaryMedicalAfterTax')">补充医疗保险(税后扣除)</th>
                        <th v-if="obj.selectedColumns.includes('personalLongTermCareAfterTax')">个人长护险(税后扣除)</th>
                        <th v-if="obj.selectedColumns.includes('unemploymentInsurance')">失业保险</th>
                        <th v-if="obj.selectedColumns.includes('criticalIllness')">大病保险</th>
                        <th v-if="obj.selectedColumns.includes('housingFund')">住房公积金</th>
                        <th v-if="obj.selectedColumns.includes('taxableSalary')">应税薪资（含个税基数）</th>
                        <th v-if="obj.selectedColumns.includes('totalActualPaid')">总实发合计</th>
                        <th v-if="obj.selectedColumns.includes('economicCompensationTaxable')">经济补偿金应税金额</th>
                        <th v-if="obj.selectedColumns.includes('economicCompensationTax')">经济补偿金个税</th>
                        <th v-if="obj.selectedColumns.includes('economicCompensationActual')">经济补偿金实发金额</th>
                        <th v-if="obj.selectedColumns.includes('totalServiceFee')">总服务费</th>
                        <th v-if="obj.selectedColumns.includes('crossBankFee')">跨行手续费</th>
                        <th v-if="obj.selectedColumns.includes('unionFee')">工会费</th>
                        <th v-if="obj.selectedColumns.includes('totalExpense')">费用合计</th>
                        <th v-if="obj.selectedColumns.includes('totalDisabilityFund')">总残障金</th>
                        <th v-if="obj.selectedColumns.includes('totalTax')">税金合计</th>
                        <th v-if="obj.selectedColumns.includes('totalCompanyPaid')">企业支付总计</th>

                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in obj.tableData" :key="index">
                        <td v-if="obj.selectedColumns.includes('name')">{{ item.name }}</td>
                        <td v-if="obj.selectedColumns.includes('idNumber')">{{ item.idNumber }}</td>
                        <td v-if="obj.selectedColumns.includes('yearMonth')">{{ item.yearMonth }}</td>
                        <td v-if="obj.selectedColumns.includes('phoneNumber')">{{ item.phoneNumber }}</td>
                        <td v-if="obj.selectedColumns.includes('customerName')">{{ item.customerName }}</td>
                        <td v-if="obj.selectedColumns.includes('employmentStatus')">{{ item.employmentStatus }}</td>
                        <td v-if="obj.selectedColumns.includes('bankName')">{{ item.bankName }}</td>
                        <td v-if="obj.selectedColumns.includes('bankAccount')">{{ item.bankAccount }}</td>
                        <td v-if="obj.selectedColumns.includes('salaryLocation')">{{ item.salaryLocation }}</td>
                        <td v-if="obj.selectedColumns.includes('withholdingName')">{{ item.withholdingName }}</td>
                        <td v-if="obj.selectedColumns.includes('entryDate')">{{ item.entryDate }}</td>
                        <td v-if="obj.selectedColumns.includes('leaveDate')">{{ item.leaveDate }}</td>
                        <td v-if="obj.selectedColumns.includes('totalSalary')">{{ item.totalSalary }}</td>
                        <td v-if="obj.selectedColumns.includes('pensionInsurance')">{{ item.pensionInsurance }}</td>
                        <td v-if="obj.selectedColumns.includes('medicalInsurance')">{{ item.medicalInsurance }}</td>
                        <td v-if="obj.selectedColumns.includes('personalLongTermCare')">{{ item.personalLongTermCare }}
                        </td>
                        <td v-if="obj.selectedColumns.includes('majorMedicalSubsidy')">{{ item.majorMedicalSubsidy }}
                        </td>
                        <td v-if="obj.selectedColumns.includes('supplementaryMedical')">{{ item.supplementaryMedical }}
                        </td>
                        <td v-if="obj.selectedColumns.includes('outpatientMedical')">{{ item.outpatientMedical }}</td>
                        <td v-if="obj.selectedColumns.includes('criticalIllnessAfterTax')">{{
                            item.criticalIllnessAfterTax }}
                        </td>
                        <td v-if="obj.selectedColumns.includes('supplementaryMedicalAfterTax')">{{
                            item.supplementaryMedicalAfterTax }}</td>
                        <td v-if="obj.selectedColumns.includes('personalLongTermCareAfterTax')">{{
                            item.personalLongTermCareAfterTax }}</td>
                        <td v-if="obj.selectedColumns.includes('unemploymentInsurance')">{{ item.unemploymentInsurance
                            }}</td>
                        <td v-if="obj.selectedColumns.includes('criticalIllness')">{{ item.criticalIllness }}</td>
                        <td v-if="obj.selectedColumns.includes('housingFund')">{{ item.housingFund }}</td>
                        <td v-if="obj.selectedColumns.includes('taxableSalary')">{{ item.taxableSalary }}</td>
                        <td v-if="obj.selectedColumns.includes('totalActualPaid')">{{ item.totalActualPaid }}</td>
                        <td v-if="obj.selectedColumns.includes('economicCompensationTaxable')">{{
                            item.economicCompensationTaxable }}</td>
                        <td v-if="obj.selectedColumns.includes('economicCompensationTax')">{{
                            item.economicCompensationTax }}
                        </td>
                        <td v-if="obj.selectedColumns.includes('economicCompensationActual')">{{
                            item.economicCompensationActual
                            }}</td>
                        <td v-if="obj.selectedColumns.includes('totalServiceFee')">{{ item.totalServiceFee }}</td>
                        <td v-if="obj.selectedColumns.includes('crossBankFee')">{{ item.crossBankFee }}</td>
                        <td v-if="obj.selectedColumns.includes('unionFee')">{{ item.unionFee }}</td>
                        <td v-if="obj.selectedColumns.includes('totalExpense')">{{ item.totalExpense }}</td>
                        <td v-if="obj.selectedColumns.includes('totalDisabilityFund')">{{ item.totalDisabilityFund }}
                        </td>
                        <td v-if="obj.selectedColumns.includes('totalTax')">{{ item.totalTax }}</td>
                        <td v-if="obj.selectedColumns.includes('totalCompanyPaid')">{{ item.totalCompanyPaid }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script setup name="AnnualSalary">

import PrintButton from '@/components/PrintButton.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 工资发放地选项
const salaryLocationOptions = [
    { value: '1', label: '北京市' },
    { value: '2', label: '上海市' },
    { value: '3', label: '广州市' },
    { value: '4', label: '深圳市' },
    { value: '5', label: '成都市' }
];

// 扣缴义务人选项
const withholdingOptions = [
    { value: '1', label: '北京科技有限公司' },
    { value: '2', label: '上海贸易有限公司' },
    { value: '3', label: '广州电子有限公司' },
    { value: '4', label: '深圳科技有限公司' },
    { value: '5', label: '成都信息有限公司' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '北京科技有限公司' },
    { value: '2', label: '上海贸易有限公司' },
    { value: '3', label: '广州电子有限公司' },
    { value: '4', label: '深圳科技有限公司' },
    { value: '5', label: '成都信息有限公司' }
];

// 入离职状态选项
const employmentStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '待入职' }
];

// 年度薪资查询数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: '', // 客户名称
        employeeName: '', // 雇员姓名
        idNumber: '', // 证件号码
        yearMonth: '' // 年月
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            name: '张三',
            idNumber: '110101199001011234',
            yearMonth: '2023-05',
            phoneNumber: '***********',
            customerName: '北京科技有限公司',
            employmentStatus: '1',
            bankName: '中国工商银行',
            bankAccount: '6222021234567890123',
            salaryLocation: '1',
            withholdingName: '1',
            entryDate: '2022-01-01',
            leaveDate: '',
            totalSalary: 120000.00,
            pensionInsurance: 9600.00,
            medicalInsurance: 3600.00,
            personalLongTermCare: 600.00,
            majorMedicalSubsidy: 300.00,
            supplementaryMedical: 1200.00,
            outpatientMedical: 600.00,
            criticalIllnessAfterTax: 300.00,
            supplementaryMedicalAfterTax: 600.00,
            personalLongTermCareAfterTax: 300.00,
            unemploymentInsurance: 600.00,
            criticalIllness: 300.00,
            housingFund: 12000.00,
            taxableSalary: 90000.00,
            totalActualPaid: 80000.00,
            economicCompensationTaxable: 0.00,
            economicCompensationTax: 0.00,
            economicCompensationActual: 0.00,
            totalServiceFee: 3600.00,
            crossBankFee: 0.00,
            unionFee: 1200.00,
            totalExpense: 4800.00,
            totalDisabilityFund: 2400.00,
            totalTax: 10000.00,
            totalCompanyPaid: 137200.00
        },
        {
            id: 2,
            name: '李四',
            idNumber: '310101199002022345',
            yearMonth: '2023-05',
            phoneNumber: '***********',
            customerName: '上海贸易有限公司',
            employmentStatus: '1',
            bankName: '中国建设银行',
            bankAccount: '6227001234567890123',
            salaryLocation: '2',
            withholdingName: '2',
            entryDate: '2022-02-01',
            leaveDate: '',
            totalSalary: 150000.00,
            pensionInsurance: 12000.00,
            medicalInsurance: 4500.00,
            personalLongTermCare: 750.00,
            majorMedicalSubsidy: 375.00,
            supplementaryMedical: 1500.00,
            outpatientMedical: 750.00,
            criticalIllnessAfterTax: 375.00,
            supplementaryMedicalAfterTax: 750.00,
            personalLongTermCareAfterTax: 375.00,
            unemploymentInsurance: 750.00,
            criticalIllness: 375.00,
            housingFund: 15000.00,
            taxableSalary: 112500.00,
            totalActualPaid: 100000.00,
            economicCompensationTaxable: 0.00,
            economicCompensationTax: 0.00,
            economicCompensationActual: 0.00,
            totalServiceFee: 4500.00,
            crossBankFee: 0.00,
            unionFee: 1500.00,
            totalExpense: 6000.00,
            totalDisabilityFund: 3000.00,
            totalTax: 12500.00,
            totalCompanyPaid: 171500.00
        }
    ],
    ids: [], // 选中的id

    selectedColumns: ['name', 'idNumber', 'yearMonth', 'phoneNumber', 'customerName', 'employmentStatus', 'bankName',
        'bankAccount', 'salaryLocation', 'withholdingName', 'entryDate', 'leaveDate', 'totalSalary', 'pensionInsurance',
        'medicalInsurance', 'personalLongTermCare', 'majorMedicalSubsidy', 'supplementaryMedical', 'outpatientMedical',
        'criticalIllnessAfterTax', 'supplementaryMedicalAfterTax', 'personalLongTermCareAfterTax', 'unemploymentInsurance',
        'criticalIllness', 'housingFund', 'taxableSalary', 'totalActualPaid', 'economicCompensationTaxable',
        'economicCompensationTax', 'economicCompensationActual', 'totalServiceFee', 'crossBankFee', 'unionFee',
        'totalExpense', 'totalDisabilityFund', 'totalTax', 'totalCompanyPaid'],
    columnOptions: [
        { label: '姓名', prop: 'name' },
        { label: '证件号码', prop: 'idNumber' },
        { label: '工资年月', prop: 'yearMonth' },
        { label: '手机号码', prop: 'phoneNumber' },
        { label: '客户名称', prop: 'customerName' },
        { label: '入离职状态', prop: 'employmentStatus' },
        { label: '开户银行', prop: 'bankName' },
        { label: '银行卡号码', prop: 'bankAccount' },
        { label: '工资发放地', prop: 'salaryLocation' },
        { label: '扣缴义务人名称', prop: 'withholdingName' },
        { label: '入职时间', prop: 'entryDate' },
        { label: '离职时间', prop: 'leaveDate' },
        { label: '年度总应发工资', prop: 'totalSalary' },
        { label: '养老保险', prop: 'pensionInsurance' },
        { label: '医疗保险', prop: 'medicalInsurance' },
        { label: '个人长护险', prop: 'personalLongTermCare' },
        { label: '大额医疗补助', prop: 'majorMedicalSubsidy' },
        { label: '补充医疗保险', prop: 'supplementaryMedical' },
        { label: '门诊医疗(社保)', prop: 'outpatientMedical' },
        { label: '大病保险(税后扣除)', prop: 'criticalIllnessAfterTax' },
        { label: '补充医疗保险(税后扣除)', prop: 'supplementaryMedicalAfterTax' },
        { label: '个人长护险(税后扣除)', prop: 'personalLongTermCareAfterTax' },
        { label: '失业保险', prop: 'unemploymentInsurance' },
        { label: '大病保险', prop: 'criticalIllness' },
        { label: '住房公积金', prop: 'housingFund' },
        { label: '应税薪资（含个税基数）', prop: 'taxableSalary' },
        { label: '总实发合计', prop: 'totalActualPaid' },
        { label: '经济补偿金应税金额', prop: 'economicCompensationTaxable' },
        { label: '经济补偿金个税', prop: 'economicCompensationTax' },
        { label: '经济补偿金实发金额', prop: 'economicCompensationActual' },
        { label: '总服务费', prop: 'totalServiceFee' },
        { label: '跨行手续费', prop: 'crossBankFee' },
        { label: '工会费', prop: 'unionFee' },
        { label: '费用合计', prop: 'totalExpense' },
        { label: '总残障金', prop: 'totalDisabilityFund' },
        { label: '税金合计', prop: 'totalTax' },
        { label: '企业支付总计', prop: 'totalCompanyPaid' },
    ],
});

/**
 * 获取工资发放地名称
 * @param {string} locationId 地点ID
 * @returns {string} 地点名称
 */
function getSalaryLocationName(locationId) {
    if (!locationId) return '-';

    const location = salaryLocationOptions.find(item => item.value === locationId);
    return location ? location.label : '-';
}

/**
 * 获取扣缴义务人名称
 * @param {string} withholdingId 扣缴义务人ID
 * @returns {string} 扣缴义务人名称
 */
function getWithholdingName(withholdingId) {
    if (!withholdingId) return '-';

    const withholding = withholdingOptions.find(item => item.value === withholdingId);
    return withholding ? withholding.label : '-';
}

/**
 * 获取入离职状态名称
 * @param {string} statusId 状态ID
 * @returns {string} 状态名称
 */
function getEmploymentStatusName(statusId) {
    if (!statusId) return '-';

    const status = employmentStatusOptions.find(item => item.value === statusId);
    return status ? status.label : '-';
}



/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item =>
                item.customerName.includes(obj.queryParams.customerName)
            );
        }

        if (obj.queryParams.employeeName) {
            filteredData = filteredData.filter(item =>
                item.name.includes(obj.queryParams.employeeName)
            );
        }

        if (obj.queryParams.idNumber) {
            filteredData = filteredData.filter(item =>
                item.idNumber.includes(obj.queryParams.idNumber)
            );
        }

        if (obj.queryParams.yearMonth) {
            filteredData = filteredData.filter(item =>
                item.yearMonth === obj.queryParams.yearMonth
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 导出数据 */
function handleExport() {
    console.log('导出数据');
}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>