<!-- 服务网点操作信息 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="所属城市:" prop="cityCode">
                <el-select class="width220" v-model="obj.queryParams.cityCode" placeholder="请选择城市" clearable>
                    <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.label" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="服务网点:" prop="servicePoint">
                <el-select class="width220" v-model="obj.queryParams.servicePoint" placeholder="请选择服务网点" clearable>
                    <el-option v-for="item in servicePointOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="人员类型:" prop="personType">
                <el-select class="width220" v-model="obj.queryParams.personType" placeholder="请选择人员类型" clearable>
                    <el-option v-for="item in personTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="使用状态:" prop="useStatus">
                <el-select class="width220" v-model="obj.queryParams.useStatus" placeholder="请选择使用状态" clearable>
                    <el-option v-for="item in useStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb20">
            <el-col :span="1.5">
                <el-button type="success" plain icon="Upload" @click="handleUpload">上传修改文件</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleDownloadTemplate">下载修改模板</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Upload" @click="handleUpload">上传客服文件</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleDownload">下载客服模板</el-button>
            </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="View" :disabled="obj.single" @click="handleDetail">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="View" :disabled="obj.single"
                    @click="handleViewUpdateRecord">查看修改记录</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="城市名称" align="center" fixed prop="cityName" />
            <el-table-column label="服务网点" align="center" prop="servicePoint" />
            <el-table-column label="使用状态" align="center" prop="useStatus" />
            <el-table-column label="使用状态说明" align="center" width="120" prop="useStatusDesc" />
            <el-table-column label="社保增员截点" align="center" width="120" prop="socialInsAddDeadline" />
            <el-table-column label="社保减员截点" align="center" width="120" prop="socialInsReduceDeadline" />
            <el-table-column label="公积金增员截点" align="center" width="120" prop="housingFundAddDeadline" />
            <el-table-column label="公积金减员截点" align="center" width="120" prop="housingFundReduceDeadline" />
            <el-table-column label="社保申报频率" align="center" width="120" prop="socialInsReportFreq" />
            <el-table-column label="公积金申报频率" align="center" width="120" prop="housingFundReportFreq" />
            <el-table-column label="大病申报频率" align="center" width="120" prop="majorIllnessReportFreq" />
            <el-table-column label="人员类别" align="center" prop="personType" />
            <el-table-column label="账单收付规则" align="center" width="120" prop="billPaymentRule" />
            <el-table-column label="是否可以落地发薪和报税" align="center" width="180" prop="canPayAndTax" />
            <el-table-column label="是否必须落地发薪" align="center" width="150" prop="mustPayLocal" />
            <el-table-column label="残障金政策标准" align="center" width="120" prop="disabilityPolicyStandard" />
            <el-table-column label="政策链接" align="center" width="120" prop="policyLink" />
            <el-table-column label="残障金我司收取标准" align="center" width="150" prop="disabilityCollectionStandard" />
            <el-table-column label="缴纳基本方式" align="center" width="120" prop="paymentMethod" />
            <el-table-column label="大户所在区" align="center" width="120" prop="largeHouseholdArea" />
            <el-table-column label="工伤享受" align="center" prop="workInjuryEnjoy" />
            <el-table-column label="是否离职补差" align="center" width="120" prop="isResignationMakeup" />
            <el-table-column label="是否可以异地发薪" align="center" width="150" prop="canRemotePay" />
            <el-table-column label="当地单立户可操作区县" align="center" width="180" prop="localOperableCounties" />
            <el-table-column label="离职补差规则" align="center" width="120" prop="resignationMakeupRule" />
            <el-table-column label="离职补差起始月" align="center" width="120" prop="resignationMakeupStartMonth" />
            <el-table-column label="是否在职补差" align="center" width="120" prop="isOnJobMakeup" />
            <el-table-column label="每年基数调整时间" align="center" width="150" prop="annualBaseAdjustTime" />
            <el-table-column label="能否接收同行代理" align="center" width="150" prop="canAcceptPeerAgency" />
            <el-table-column label="享受工伤" align="center" prop="enjoyWorkInjury" />
            <el-table-column label="接单方客服" align="center" width="120" prop="customerService" />
            <el-table-column label="增员材料" align="center" prop="addMaterials" />
            <el-table-column label="减员材料" align="center" prop="reduceMaterials" />
            <el-table-column label="补缴材料" align="center" prop="makeupMaterials" />
            <el-table-column label="特殊注意事项" align="center" width="120" prop="specialNotes" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="45%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="所属城市" prop="cityCode">
                    <el-select :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.cityCode"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="服务网点" prop="servicePoint">
                    <el-select class="width220" v-model="obj.dialogForm.servicePoint" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="使用状态" prop="useStatus">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.useStatus"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="使用状态说明" prop="useStatusDesc">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.useStatusDesc"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="接单客服" prop="customerService">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.customerService"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="人员类型" prop="personType">
                    <el-select :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.personType"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="社保增员截点" prop="socialInsAddDeadline">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.socialInsAddDeadline"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="社保减员截点" prop="socialInsReduceDeadline">
                    <el-input :disabled="obj.isDetails" class="width220"
                        v-model="obj.dialogForm.socialInsReduceDeadline" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="公积金增员截点" prop="housingFundAddDeadline">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.housingFundAddDeadline"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="公积金减员截点" prop="housingFundReduceDeadline">
                    <el-input :disabled="obj.isDetails" class="width220"
                        v-model="obj.dialogForm.housingFundReduceDeadline" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="社保申报频率" prop="socialInsReportFreq">
                    <el-select :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.socialInsReportFreq"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公积金申报频率" prop="housingFundReportFreq">
                    <el-select :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.housingFundReportFreq"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账单收付规则" prop="billPaymentRule">
                    <el-select :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.billPaymentRule"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否可以落地发薪和报税" prop="canPayAndTax">
                    <el-select :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.canPayAndTax"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="残障金政策标准" prop="disabledGoldPolicyStandard">
                    <el-input :disabled="obj.isDetails" class="width220"
                        v-model="obj.dialogForm.disabledGoldPolicyStandard" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="政策链接" prop="policyLink">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.policyLink"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="残障金我司收取标准" prop="disabledGoldCollectionStandard">
                    <el-input :disabled="obj.isDetails" class="width220"
                        v-model="obj.dialogForm.disabledGoldCollectionStandard" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="缴纳基本方式" prop="paymentMethod">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.paymentMethod"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="大病申报频率" prop="majorIllnessReportFreq">
                    <el-select :disabled="obj.isDetails" class="width220"
                        v-model="obj.dialogForm.majorIllnessReportFreq" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="能否接收同行代理" prop="canAcceptPeerAgency">
                    <el-select :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.canAcceptPeerAgency"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="当地单立户可操作区县" prop="localOperableCounties">
                    <el-input :disabled="obj.isDetails" class="width220" type="textarea"
                        v-model="obj.dialogForm.localOperableCounties" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="大区所在区" prop="area">
                    <el-input :disabled="obj.isDetails" class="width220" type="textarea" v-model="obj.dialogForm.area"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="工伤享受" prop="workInjuryEnjoy">
                    <el-input :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.workInjuryEnjoy"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="是否必须落地发薪" prop="mustPayLocal">
                    <el-select :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.mustPayLocal"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否离职补差" prop="isResignationMakeup">
                    <el-select :disabled="obj.isDetails" class="width220" v-model="obj.dialogForm.isResignationMakeup"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="离职补差起始月" prop="resignationMakeupStartMonth">
                    <el-input :disabled="obj.isDetails" class="width220"
                        v-model="obj.dialogForm.resignationMakeupStartMonth" placeholder="请输入" />
                </el-form-item>

                <el-form-item label="离职补差规则" prop="resignationMakeupRule">
                    <el-input :disabled="obj.isDetails" class="width420" type="textarea" :rows="3"
                        v-model="obj.dialogForm.resignationMakeupRule" placeholder="请输入" />
                </el-form-item>

                <el-form-item label="增员材料" prop="addMaterials">
                    <el-input :disabled="obj.isDetails" class="width420" type="textarea" :rows="3"
                        v-model="obj.dialogForm.addMaterials" placeholder="请输入" />
                </el-form-item>

                <el-form-item label="减员材料" prop="reduceMaterials">
                    <el-input :disabled="obj.isDetails" class="width420" type="textarea" :rows="3"
                        v-model="obj.dialogForm.reduceMaterials" placeholder="请输入" />
                </el-form-item>

                <el-form-item label="补缴材料" prop="makeupMaterials">
                    <el-input :disabled="obj.isDetails" class="width420" type="textarea" :rows="3"
                        v-model="obj.dialogForm.makeupMaterials" placeholder="请输入" />
                </el-form-item>

                <el-form-item label="特殊注意事项" prop="specialNotes">
                    <el-input :disabled="obj.isDetails" class="width420" type="textarea" :rows="3"
                        v-model="obj.dialogForm.specialNotes" placeholder="请输入" />
                </el-form-item>

                <el-form-item label="客户端特殊注意事项" prop="clientSpecialNotes">
                    <el-input :disabled="obj.isDetails" class="width420" type="textarea" :rows="3"
                        v-model="obj.dialogForm.clientSpecialNotes" placeholder="请输入" />
                </el-form-item>

            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">立即提交</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">重置</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 上传修改文件 -->
        <el-dialog title="上传修改文件" v-model="obj.uploadShow" width="30%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules">
                <el-form-item label=" 备注" prop="name">
                    <el-input class="width420" type="textarea" :rows="3" v-model="obj.dialogForm.name"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="上传文件" prop="name">
                    <el-upload class="upload-demo" action="https://jsonplaceholder.typicode.com/posts/"
                        :on-preview="handlePreview" :on-remove="handleRemove" :before-remove="beforeRemove" multiple
                        :limit="3" :on-exceed="handleExceed" :file-list="fileList">
                        <el-button type="primary">点击上传</el-button>
                        <template #tip>
                            <div class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup name="ServiceNetworkOperationInformation">

import ChinaCitys from "@/utils/ChinaCitys.json";
import { listImport } from "@/api/reonApi/import";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');

// 服务网点选项
const servicePointOptions = [
    { value: '1', label: '北京市网点A' },
    { value: '2', label: '北京市网点B' },
    { value: '3', label: '上海市网点A' },
    { value: '4', label: '上海市网点B' },
    { value: '5', label: '广州市网点A' }
];

// 人员类型选项
const personTypeOptions = [
    { value: '1', label: '全日制职工' },
    { value: '2', label: '非全日制职工' },
    { value: '3', label: '实习生' },
    { value: '4', label: '兼职人员' }
];

// 使用状态选项
const useStatusOptions = [
    { value: '1', label: '正常使用' },
    { value: '2', label: '暂停使用' },
    { value: '3', label: '已停用' }
];

// 申报频率选项
const reportFrequencyOptions = [
    { value: '1', label: '每月' },
    { value: '2', label: '每季度' },
    { value: '3', label: '每半年' },
    { value: '4', label: '每年' }
];


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cityCode: null,
        servicePoint: null,
        personType: null,
        useStatus: null,
    },//查询表单
    rules: {
        cityCode: [
            { required: true, message: "请选择所属城市", trigger: "change" }
        ],
        servicePoint: [
            { required: true, message: "请选择服务网点", trigger: "change" }
        ],
        personType: [
            { required: true, message: "请选择人员类型", trigger: "change" }
        ],
        useStatus: [
            { required: true, message: "请选择使用状态", trigger: "change" }
        ],
    },
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            cityCode: '110000',
            cityName: '北京市',
            servicePoint: '北京市网点A',
            useStatus: '正常使用',
            useStatusDesc: '正常运营中',
            socialInsAddDeadline: '15日',
            socialInsReduceDeadline: '25日',
            housingFundAddDeadline: '10日',
            housingFundReduceDeadline: '20日',
            socialInsReportFreq: '每月',
            housingFundReportFreq: '每月',
            majorIllnessReportFreq: '每季度',
            personType: '全日制职工',
            billPaymentRule: '先付款后办理',
            canPayAndTax: '是',
            mustPayLocal: '否',
            disabilityPolicyStandard: '1.5%',
            policyLink: 'http://example.com/policy',
            disabilityCollectionStandard: '1.6%',
            paymentMethod: '银行转账',
            largeHouseholdArea: '朝阳区',
            workInjuryEnjoy: '是',
            isResignationMakeup: '是',
            canRemotePay: '是',
            localOperableCounties: '朝阳区,海淀区,丰台区',
            resignationMakeupRule: '离职后可补缴上月社保费用',
            resignationMakeupStartMonth: '离职当月',
            isOnJobMakeup: '是',
            annualBaseAdjustTime: '7月',
            canAcceptPeerAgency: '是',
            enjoyWorkInjury: '是',
            customerService: '张三',
            addMaterials: '身份证复印件,劳动合同',
            reduceMaterials: '离职证明,解除劳动合同',
            makeupMaterials: '补缴申请表,补缴证明',
            specialNotes: '特殊情况需提前协调',
            clientSpecialNotes: '客户端需要注意的特殊事项'
        },
        {
            id: 2,
            cityCode: '310000',
            cityName: '上海市',
            servicePoint: '上海市网点A',
            useStatus: '正常使用',
            useStatusDesc: '正常运营中',
            socialInsAddDeadline: '10日',
            socialInsReduceDeadline: '20日',
            housingFundAddDeadline: '5日',
            housingFundReduceDeadline: '15日',
            socialInsReportFreq: '每月',
            housingFundReportFreq: '每月',
            majorIllnessReportFreq: '每季度',
            personType: '全日制职工',
            billPaymentRule: '先付款后办理',
            canPayAndTax: '是',
            mustPayLocal: '是',
            disabilityPolicyStandard: '1.6%',
            policyLink: 'http://example.com/policy',
            disabilityCollectionStandard: '1.7%',
            paymentMethod: '银行转账',
            largeHouseholdArea: '黄浦区',
            workInjuryEnjoy: '是',
            isResignationMakeup: '是',
            canRemotePay: '否',
            localOperableCounties: '黄浦区,静安区,徐汇区',
            resignationMakeupRule: '离职后可补缴上月社保费用',
            resignationMakeupStartMonth: '离职当月',
            isOnJobMakeup: '是',
            annualBaseAdjustTime: '6月',
            canAcceptPeerAgency: '否',
            enjoyWorkInjury: '是',
            customerService: '李四',
            addMaterials: '身份证复印件,劳动合同',
            reduceMaterials: '离职证明,解除劳动合同',
            makeupMaterials: '补缴申请表,补缴证明',
            specialNotes: '特殊情况需提前协调',
            clientSpecialNotes: '客户端需要注意的特殊事项'
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //新增、修改弹出框
    uploadShow: false,//导入接单客服弹出框
    ids: [],//选中的id
    title: "",//标题
    isDetails: false,//是否详情

})

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}


// 表单重置
function reset() {
    obj.dialogForm = {};
    obj.isDetails = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增服务网点";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = JSON.parse(JSON.stringify(row || obj.tableData.find(item => obj.ids.includes(item.id))));
    obj.dialogShow = true;
    obj.title = "修改服务网点";
}

/** 详情按钮操作 */
function handleDetail(row) {
    obj.isDetails = true;
    // 模拟获取数据
    obj.dialogForm = JSON.parse(JSON.stringify(row || obj.tableData.find(item => obj.ids.includes(item.id))));
    obj.dialogShow = true;
    obj.title = '查看服务网点详情'
}

/** 上传修改文件 */
function handleUpload() {
    obj.uploadShow = true;
}


/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 下载模板 */
function handleDownloadTemplate() {
    proxy.download('system/import/export', {
        ...obj.queryParams
    }, `服务网点修改模板.xlsx`)
}

/** 下载客服模板 */
function handleDownload() {
    proxy.download('system/import/export', {
        ...obj.queryParams
    }, `服务网点客服模板.xlsx`)
}

/** 查看修改记录 */
function handleViewUpdateRecord() {
    proxy.$modal.msgSuccess('查看修改记录');
    // 模拟查看修改记录
    obj.dialogShow = true;
    obj.title = '查看修改记录';
}

getList();
</script>
<style lang="scss" scoped></style>