import useUserStore from "@/store/modules/user";

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkPermi(value) {
  // 检查传入的值是否为数组且长度大于0
  if (value && value instanceof Array && value.length > 0) {
    // 从用户存储中获取权限列表
    const permissions = useUserStore().permissions;
    // 获取需要校验的权限数据
    const permissionDatas = value;
    // 定义一个表示所有权限的字符串
    const all_permission = "*:*:*";

    // 检查权限列表中是否存在需要校验的权限
    const hasPermission = permissions.some((permission) => {
      // 如果存在所有权限或者需要校验的权限包含在权限列表中，则返回true
      return (
        all_permission === permission || permissionDatas.includes(permission)
      );
    });

    // 如果没有权限，则返回false
    if (!hasPermission) {
      return false;
    }
    // 如果有权限，则返回true
    return true;
  } else {
    // 如果传入的值不符合要求，则打印错误信息并返回false
    console.error(
      `need roles! Like checkPermi="['system:user:add','system:user:edit']"`
    );
    return false;
  }
}

/**
 * 角色权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkRole(value) {
  // 检查传入的值是否为数组且长度大于0
  if (value && value instanceof Array && value.length > 0) {
    // 从用户存储中获取角色列表
    const roles = useUserStore().roles;
    // 获取需要校验的角色数据
    const permissionRoles = value;
    // 定义一个表示超级管理员的字符串
    const super_admin = "admin";

    // 检查角色列表中是否存在需要校验的角色
    const hasRole = roles.some((role) => {
      // 如果是超级管理员或者需要校验的角色包含在角色列表中，则返回true
      return super_admin === role || permissionRoles.includes(role);
    });

    // 如果没有角色权限，则返回false
    if (!hasRole) {
      return false;
    }
    // 如果有角色权限，则返回true
    return true;
  } else {
    // 如果传入的值不符合要求，则打印错误信息并返回false
    console.error(`need roles! Like checkRole="['admin','editor']"`);
    return false;
  }
}
