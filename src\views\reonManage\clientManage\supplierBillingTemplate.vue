<!-- 供应商账单模板管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="账单模板编号:" prop="templateCode">
                <el-input class="width220" v-model="obj.queryParams.templateCode" placeholder="请输入账单模板编号" clearable />
            </el-form-item>
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-select class="width220" filterable v-model="obj.queryParams.supplierName" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" filterable v-model="obj.queryParams.customerName" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单模板类型:" prop="templateType">
                <el-select class="width220" v-model="obj.queryParams.templateType" placeholder="请选择" clearable>
                    <el-option v-for="item in templateTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Setting" @click="handleFrequencySetting">收费频率设置</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除账单模版</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商名称" align="center" prop="supplierName" />
            <el-table-column label="账单模板名称" align="center" prop="templateName" />
            <el-table-column label="账单模板编号" align="center" prop="templateCode" />
            <el-table-column label="账单模板类型" align="center" prop="templateType" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="约定账单生成日" align="center" prop="generateDay" width="120" />
            <el-table-column label="约定账单锁定日" align="center" prop="lockDay" width="120" />
            <el-table-column label="是否社保计入总额" align="center" prop="includeSocialSecurity" width="140">
                <template #default="scope">
                    <el-tag :type="scope.row.includeSocialSecurity === '1' ? 'success' : 'info'">
                        {{ scope.row.includeSocialSecurity === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="是否公积金计入总额" align="center" prop="includeHousingFund" width="140">
                <template #default="scope">
                    <el-tag :type="scope.row.includeHousingFund === '1' ? 'success' : 'info'">
                        {{ scope.row.includeHousingFund === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="是否工资计入总额" align="center" prop="includeSalary" width="140">
                <template #default="scope">
                    <el-tag :type="scope.row.includeSalary === '1' ? 'success' : 'info'">
                        {{ scope.row.includeSalary === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="操作" align="center" width="150">
                <template #default="scope">
                    <el-button text size="small" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button text size="small" @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="60%" append-to-body draggable>
            <el-form class="form-container" ref="formRef" :model="obj.dialogForm" inline :rules="obj.rules"
                label-width="auto">
                <el-form-item label="供应商名称" prop="supplierName" required>
                    <el-select class="width220" filterable v-model="obj.dialogForm.supplierName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账单模板名称" prop="templateName" required>
                    <el-input class="width220" v-model="obj.dialogForm.templateName" placeholder="请输入账单模板名称" />
                </el-form-item>
                <el-form-item label="账单模板类别" prop="templateType" required>
                    <el-select class="width220" v-model="obj.dialogForm.templateType" placeholder="请选择" clearable>
                        <el-option v-for="item in templateTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName" required>
                    <el-select class="width220" filterable v-model="obj.dialogForm.customerName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="约定账单生成日" prop="generateDay" required>
                    <el-input class="width220" v-model="obj.dialogForm.generateDay" placeholder="请输入生成日" />
                </el-form-item>
                <el-form-item label="约定账单锁定日" prop="lockDay" required>
                    <el-input class="width220" v-model="obj.dialogForm.lockDay" placeholder="请输入锁定日" />
                </el-form-item>
                <el-form-item label="是否社保计入总额" prop="includeSocialSecurity">
                    <el-select class="width220" v-model="obj.dialogForm.includeSocialSecurity" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in includeSocialSecurityOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否公积金计入总额" prop="includeHousingFund">
                    <el-select class="width220" v-model="obj.dialogForm.includeHousingFund" placeholder="请选择" clearable>
                        <el-option v-for="item in includeHousingFundOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否工资计入总额" prop="includeSalary">
                    <el-select class="width220" v-model="obj.dialogForm.includeSalary" placeholder="请选择" clearable>
                        <el-option v-for="item in includeSalaryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 收费频率设置对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="60%" append-to-body draggable>
            <div class="flex-between-center mb20">
                <div>
                    <el-button type="primary" plain icon="Plus" @click="handleAddFrequency">新增频率</el-button>
                    <el-button type="danger" plain icon="Delete" @click="handleDeleteFrequency"
                        :disabled="obj.frequencyIds.length === 0">删除频率</el-button>
                </div>
                <div>
                    <el-button type="success" plain icon="Check" @click="handleSaveFrequency">保存</el-button>
                    <el-button plain icon="Close" @click="obj.dialogShow2 = false">取消</el-button>
                </div>
            </div>
            <el-table :data="obj.frequencyData" border @selection-change="handleFrequencySelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="序号" align="center" type="index" width="60" />
                <el-table-column label="收费频率编号" align="center" prop="frequencyCode" width="180" />
                <el-table-column label="提前几月收费" align="center">
                    <template #default="scope">
                        <el-input v-model="scope.row.advanceMonths" placeholder="请输入提前月数" />
                    </template>
                </el-table-column>
                <el-table-column label="收费频率名称" align="center">
                    <template #default="scope">
                        <el-input v-model="scope.row.frequencyName" placeholder="请输入收费频率名称" />
                    </template>
                </el-table-column>
                <el-table-column label="是否默认" align="center">
                    <template #default="scope">
                        <el-select v-model="scope.row.isDefault" placeholder="请选择" clearable>
                            <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="obj.frequencyTotal > 0" :total="obj.frequencyTotal"
                v-model:page="obj.frequencyParams.pageNum" v-model:limit="obj.frequencyParams.pageSize"
                @pagination="getFrequencyList" />
        </el-dialog>
    </div>
</template>

<script setup name="SupplierBillingTemplate">


// 初始化常量和对象
const { proxy } = getCurrentInstance();

const { sys_yes_no } = proxy.useDict('sys_yes_no');

const formRef = ref(null)
const queryRef = ref(null)
const frequencyFormRef = ref(null)

// 选项数据
const supplierOptions = ref([
    { value: '1', label: '供应商A' },
    { value: '2', label: '供应商B' },
    { value: '3', label: '供应商C' }
])

const customerOptions = ref([
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
])

const templateTypeOptions = ref([
    { value: '1', label: '标准模板' },
    { value: '2', label: '定制模板' },
    { value: '3', label: '特殊模板' }
])

// 响应式数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateCode: null,
        supplierName: null,
        customerName: null,
        templateType: null
    }, // 查询表单
    rules: {
        supplierName: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        templateName: [{ required: true, message: '请输入账单模板名称', trigger: 'blur' }],
        templateType: [{ required: true, message: '请选择账单模板类型', trigger: 'change' }],
        customerName: [{ required: true, message: '请选择客户', trigger: 'change' }],
        generateDay: [{ required: true, message: '请输入约定账单生成日', trigger: 'blur' }],
        lockDay: [{ required: true, message: '请输入约定账单锁定日', trigger: 'blur' }]
    },
    frequencyRules: {
        frequencyName: [{ required: true, message: '请输入频率名称', trigger: 'blur' }],
        advanceMonths: [{ required: true, message: '请输入提前月数', trigger: 'blur' }]
    },
    total: 0, // 总条数
    frequencyTotal: 0, // 频率总条数
    tableData: [], // 模板列表
    frequencyData: [], // 频率列表
    dialogForm: {}, // 模板表单
    frequencyForm: {}, // 频率表单
    dialogShow: false, // 模板弹出框
    dialogShow2: false, // 频率列表弹出框
    frequencyDialogShow: false, // 频率编辑弹出框
    ids: [], // 选中的模板id
    frequencyIds: [], // 选中的频率id
    title: "", // 模板标题
    frequencyTitle: "", // 频率标题
    frequencyParams: { // 频率查询参数
        pageNum: 1,
        pageSize: 10
    },
    currentTemplateId: null // 当前操作的模板ID
})

/**
 * 获取模板列表
 */
function getList() {
    obj.loading = true;

    // 模拟数据
    setTimeout(() => {
        // 生成模拟数据
        const mockData = [];
        for (let i = 0; i < 10; i++) {
            mockData.push({
                id: i + 1,
                supplierName: `供应商${String.fromCharCode(65 + i % 3)}`,
                templateName: `账单模板${i + 1}`,
                templateCode: `TEMP${String(1000 + i).padStart(4, '0')}`,
                templateType: i % 3 === 0 ? '标准模板' : (i % 3 === 1 ? '定制模板' : '特殊模板'),
                customerName: `客户${String.fromCharCode(65 + i % 3)}`,
                generateDay: Math.floor(Math.random() * 28) + 1,
                lockDay: Math.floor(Math.random() * 28) + 1,
                includeSocialSecurity: i % 2 === 0 ? '1' : '0',
                includeHousingFund: i % 3 === 0 ? '1' : '0',
                includeSalary: i % 2 === 1 ? '1' : '0',
                createTime: `2023-${String(i % 12 + 1).padStart(2, '0')}-${String(i % 28 + 1).padStart(2, '0')} 10:00:00`,
                creator: `管理员${i % 3 + 1}`
            });
        }

        // 应用查询条件过滤
        let filteredData = [...mockData];
        const params = obj.queryParams;

        if (params.templateCode) {
            filteredData = filteredData.filter(item => item.templateCode.includes(params.templateCode));
        }

        if (params.supplierName) {
            filteredData = filteredData.filter(item => item.supplierName === params.supplierName);
        }

        if (params.customerName) {
            filteredData = filteredData.filter(item => item.customerName === params.customerName);
        }

        if (params.templateType) {
            filteredData = filteredData.filter(item => item.templateType === params.templateType);
        }

        // 分页处理
        const start = (params.pageNum - 1) * params.pageSize;
        const end = start + params.pageSize;
        const pageData = filteredData.slice(start, end);

        obj.tableData = pageData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 500); // 模拟网络延迟
}

/**
 * 获取频率列表
 */
function getFrequencyList() {
    // 模拟数据
    setTimeout(() => {
        // 生成模拟数据
        const mockData = [];
        for (let i = 0; i < 5; i++) {
            mockData.push({
                id: i + 1,
                frequencyCode: `FREQ${String(1000 + i).padStart(4, '0')}`,
                frequencyName: `${i === 0 ? '月付' : (i === 1 ? '季付' : (i === 2 ? '半年付' : (i === 3 ? '年付' : '一次性')))}`,
                advanceMonths: i,
                isDefault: i === 0 ? '1' : '0'
            });
        }

        // 分页处理
        const params = obj.frequencyParams;
        const start = (params.pageNum - 1) * params.pageSize;
        const end = start + params.pageSize;
        const pageData = mockData.slice(start, end);

        obj.frequencyData = pageData;
        obj.frequencyTotal = mockData.length;
    }, 300);
}

/**
 * 表单重置
 */
function reset() {
    obj.dialogForm = {
        includeSocialSecurity: '0',
        includeHousingFund: '0',
        includeSalary: '0'
    };
    if (formRef.value) {
        formRef.value.resetFields();
    }
}

/**
 * 频率表单重置
 */
function resetFrequencyForm() {
    obj.frequencyForm = {
        isDefault: '0'
    };
    if (frequencyFormRef.value) {
        frequencyFormRef.value.resetFields();
    }
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/**
 * 重置按钮操作
 */
function resetQuery() {
    if (queryRef.value) {
        queryRef.value.resetFields();
    }
    handleQuery();
}

/**
 * 模板多选框选中数据
 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = selection.length === 0;
}

/**
 * 频率多选框选中数据
 */
function handleFrequencySelectionChange(selection) {
    obj.frequencyIds = selection.map(item => item.id);
}

/**
 * 新增模板按钮操作
 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增账单模板";
}

/**
 * 查看模板按钮操作
 */
function handleDetail(row) {
    reset();
    // 模拟获取详情数据
    setTimeout(() => {
        obj.dialogForm = { ...row };
        obj.dialogShow = true;
        obj.title = "账单模板详情";
    }, 300);
}

/**
 * 修改模板按钮操作
 */
function handleUpdate(row) {
    reset();
    const id = row?.id || obj.ids[0];
    if (id) {
        // 模拟获取详情数据
        setTimeout(() => {
            const rowData = row || obj.tableData.find(item => item.id === id);
            if (rowData) {
                obj.dialogForm = { ...rowData };
                obj.dialogShow = true;
                obj.title = "修改账单模板";
            }
        }, 300);
    } else {
        proxy.$modal.msgError("请选择要修改的数据");
    }
}

/**
 * 收费频率设置按钮操作
 */
function handleFrequencySetting() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError("请选择一个账单模板");
        return;
    }

    const row = obj.tableData.find(item => item.id === obj.ids[0]);
    obj.currentTemplateId = row?.id;
    obj.dialogShow2 = true;
    obj.title = `收费频率设置 - ${row?.templateName || ''}`;
    getFrequencyList();
}

/**
 * 新增频率按钮操作
 */
function handleAddFrequency() {
    // 生成一个临时ID，确保每个新增项都有唯一ID
    const tempId = Date.now() + Math.floor(Math.random() * 1000);
    obj.frequencyData.push({
        id: tempId,
        frequencyCode: `NEW${tempId}`,
        frequencyName: '',
        advanceMonths: 0,
        isDefault: '0'
    });
}


/**
 * 删除频率按钮操作
 */
function handleDeleteFrequency() {
    if (obj.frequencyIds.length === 0) {
        proxy.$modal.msgError("请选择要删除的频率");
        return;
    }

    proxy.$modal.confirm(`是否确认删除选中的 ${obj.frequencyIds.length} 个频率？`).then(() => {
        // 从频率数据中过滤掉被选中的项
        obj.frequencyData = obj.frequencyData.filter(item => !obj.frequencyIds.includes(item.id));
        obj.frequencyIds = []; // 清空选中项
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/**
 * 保存频率设置
 */
function handleSaveFrequency() {
    proxy.$modal.msgSuccess("收费频率设置保存成功");
    obj.dialogShow2 = false;
}


/**
 * 删除账单模板按钮操作
 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/**
 * 提交模板表单
 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 页面加载时获取数据
getList();
</script>
<style lang="scss" scoped>
.form-container {
    padding: 10px;
    max-height: 500px;
    overflow-y: auto;
}

.dialog-footer {
    text-align: center;
    margin-top: 20px;
}
</style>