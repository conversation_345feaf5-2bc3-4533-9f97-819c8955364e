<!-- 薪资发放人员信息查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="身份证号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="劳动合同类型:" prop="contractType">
                <el-select class="width220" v-model="obj.queryParams.contractType" placeholder="请选择" clearable>
                    <el-option v-for="item in contractTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="primary" plain icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Upload" @click="handleUploadEmployeeContract">上传员工合同</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Upload"
                    @click="handleUploadEmployeeContractBatch">批量上传员工合同</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleUploadIdentityInfo">上传身份信息</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload"
                    @click="handleUploadIdentityInfoBatch">批量上传身份信息</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <div style="color: red;margin-bottom: 10px;font-weight: bold;">
            <div class="mb8">单个上传/批量上传员工合同(需传入压缩包)支持的文件类型为 jpg|png|gif|bmp|jpeg|zip|pdf|doc|docx</div>
            <div class="mb8">单个上传/批量上传身份信息(需传入压缩包)支持的文件类型为 jpg|png|gif|bmp|jpeg|zip|pdf</div>
            <div>若已有文件类型不满足实际需要,请联系技术人员</div>
        </div>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="订单编号" align="center" prop="orderNumber" />
            <el-table-column label="员工姓名" align="center" prop="employeeName" />
            <el-table-column label="身份证号" align="center" prop="idNumber" />
            <el-table-column label="客户名称" align="center" prop="customerName">
                <template #default="scope">
                    <dict-tag :options="customerOptions" :value="scope.row.customerName" />
                </template>
            </el-table-column>
            <el-table-column label="合同编号" align="center" prop="contractNumber" />
            <el-table-column label="税率表" align="center" prop="taxRateTable" />
            <el-table-column label="税率类型" align="center" prop="taxRateType">
                <template #default="scope">
                    <dict-tag :options="taxRateTypeOptions" :value="scope.row.taxRateType" />
                </template>
            </el-table-column>
            <el-table-column label="员工合同文件是否上传" align="center" prop="isEmployeeContractUploaded">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isEmployeeContractUploaded" />
                </template>
            </el-table-column>
            <el-table-column label="身份信息是否上传" align="center" prop="isIdentityInfoUploaded">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isIdentityInfoUploaded" />
                </template>
            </el-table-column>
            <el-table-column label="劳务合同文件是否上传" align="center" prop="isLaborContractUploaded">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isLaborContractUploaded" />
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 上传合同 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="30%">
            <div style="text-align: center;color: red;font-weight: 400;font-size: 18px;" v-if="obj.title == '批量上传员工合同'">
                <div>如果上传的合同为劳动合同请用订单号作为文件名称</div>
                <div>如果传的合同为劳务合同请用证件号+合同号作为文件名称</div>
                <div>例:135848,HT-20250848545</div>
                <el-radio-group v-model="radio1">
                    <el-radio value="1" size="large">劳动合同</el-radio>
                    <el-radio value="2" size="large">劳务合同</el-radio>
                </el-radio-group>
            </div>
            <el-row class="mt20">
                <el-col :span="24">
                    <file-upload @update:modelValue="uploadedSuccessfully" v-model="obj.dialogForm.isActive" />
                </el-col>
            </el-row>
            <template #footer>
                <el-button type="primary" @click="handleFileUpload">上传</el-button>
                <el-button type="primary" plain @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="PayrollOfficer">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 初始化字典
proxy.useDict();

// 客户选项
const customerOptions = ref([
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' },
    { value: '4', label: '客户D' },
    { value: '5', label: '客户E' }
]);

// 合同类型选项
const contractTypeOptions = ref([
    { value: '1', label: '劳务合同' },
    { value: '2', label: '劳动合同' },
    { value: '3', label: '服务合同' }
]);

// 是否选项
const yesNoOptions = ref([
    { value: 'Y', label: '是' },
    { value: 'N', label: '否' }
]);

// 税率类型选项
const taxRateTypeOptions = ref([
    { value: '1', label: '一般税率' },
    { value: '2', label: '优惠税率' },
    { value: '3', label: '特殊税率' }
]);

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        orderNumber: 'DD001', // 订单编号
        employeeName: '张三', // 员工姓名
        idNumber: '110101199001011234', // 身份证号
        customerName: '1', // 客户名称
        contractNumber: 'HT001', // 合同编号
        taxRateTable: '一般税率表', // 税率表
        taxRateType: '1', // 税率类型
        isEmployeeContractUploaded: 'Y', // 员工合同文件是否上传
        isIdentityInfoUploaded: 'Y', // 身份信息是否上传
        isLaborContractUploaded: 'N' // 劳务合同文件是否上传
    },
    {
        id: 2,
        orderNumber: 'DD002', // 订单编号
        employeeName: '李四', // 员工姓名
        idNumber: '310101199102023456', // 身份证号
        customerName: '2', // 客户名称
        contractNumber: 'HT002', // 合同编号
        taxRateTable: '优惠税率表', // 税率表
        taxRateType: '2', // 税率类型
        isEmployeeContractUploaded: 'Y', // 员工合同文件是否上传
        isIdentityInfoUploaded: 'N', // 身份信息是否上传
        isLaborContractUploaded: 'Y' // 劳务合同文件是否上传
    },
    {
        id: 3,
        orderNumber: 'DD003', // 订单编号
        employeeName: '王五', // 员工姓名
        idNumber: '440101199203034567', // 身份证号
        customerName: '3', // 客户名称
        contractNumber: 'HT003', // 合同编号
        taxRateTable: '特殊税率表', // 税率表
        taxRateType: '3', // 税率类型
        isEmployeeContractUploaded: 'N', // 员工合同文件是否上传
        isIdentityInfoUploaded: 'Y', // 身份信息是否上传
        isLaborContractUploaded: 'Y' // 劳务合同文件是否上传
    }
];

// 上传类型
const radio1 = ref('1');

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        idNumber: null, // 身份证号
        contractType: null, // 劳动合同类型
        customerName: null, // 客户名称
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: { isActive: false }, // 表单
    dialogShow: false, // 弹出框
    ids: [], // 选中的id
    title: "", // 标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟异步请求
    setTimeout(() => {
        // 这里可以根据查询条件过滤数据
        const filteredData = mockTableData.filter(item => {
            const matchIdNumber = !obj.queryParams.idNumber ||
                item.idNumber.includes(obj.queryParams.idNumber);
            const matchContractType = !obj.queryParams.contractType ||
                item.contractType === obj.queryParams.contractType;
            const matchCustomerName = !obj.queryParams.customerName ||
                item.customerName === obj.queryParams.customerName;
            return matchIdNumber && matchContractType && matchCustomerName;
        });

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}





/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}
// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 上传成功回调
function uploadedSuccessfully(value) {
    console.log('上传文件状态变化:', value);
    obj.dialogForm.isActive = value;
}

// 上传员工合同
function handleUploadEmployeeContract() {
    handleUpload(1);
}

// 批量上传员工合同
function handleUploadEmployeeContractBatch() {
    handleUpload(2);
}

// 上传身份信息
function handleUploadIdentityInfo() {
    handleUpload(3);
}

// 批量上传身份信息
function handleUploadIdentityInfoBatch() {
    handleUpload(4);
}

// 处理上传操作
function handleUpload(type) {
    obj.dialogForm = {
        isActive: false
    };

    switch (type) {
        case 1:
            obj.title = "上传员工合同";
            break;
        case 2:
            obj.title = "批量上传员工合同";
            break;
        case 3:
            obj.title = "上传身份信息";
            break;
        case 4:
            obj.title = "批量上传身份信息";
            break;
        default:
            obj.title = "文件上传";
    }

    obj.dialogShow = true;
}

// 处理文件上传
function handleFileUpload() {
    if (!obj.dialogForm.isActive) {
        proxy.$modal.msgError('请先选择要上传的文件');
        return;
    }

    // 模拟上传成功
    proxy.$modal.msgSuccess('文件上传成功');
    obj.dialogShow = false;

    // 如果有选中的行，更新其上传状态
    if (obj.ids.length > 0) {
        obj.tableData.forEach(item => {
            if (obj.ids.includes(item.id)) {
                if (obj.title.includes('员工合同')) {
                    item.isEmployeeContractUploaded = 'Y';
                } else if (obj.title.includes('身份信息')) {
                    item.isIdentityInfoUploaded = 'Y';
                } else if (obj.title.includes('劳务合同')) {
                    item.isLaborContractUploaded = 'Y';
                }
            }
        });
    }
}

getList();
</script>
<style lang="scss" scoped></style>