<!-- 薪资项目 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="薪资类别名称:" prop="salaryCategory">
                <el-input class="width220" v-model="obj.queryParams.salaryCategory" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="薪资项目种类:" prop="salaryItemType">
                <el-input class="width220" v-model="obj.queryParams.salaryItemType" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="薪资项目名称:" prop="salaryItemName">
                <el-select class="width220" filterable v-model="obj.queryParams.salaryItemName" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in salaryItemOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="View" :disabled="obj.single" @click="handleView">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single"
                    @click="handleSetSystemProjectAttribute">设置系统项目属性</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="SuccessFilled" :disabled="obj.single"
                    @click="handleEffective">生效</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="CircleClose" :disabled="obj.single"
                    @click="handleInvalid">失效</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button color="#626aef" plain :disabled="obj.single" @click="handleCopy">复制</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleRowDblclick">
            <el-table-column type="selection" width="55" />
            <el-table-column label="薪资项目名称" align="center" prop="itemName">
                <template #default="scope">
                    <dict-tag :options="salaryItemOptions" :value="scope.row.itemName" />
                </template>
            </el-table-column>
            <el-table-column label="薪资项目编号" align="center" prop="itemCode" />
            <el-table-column label="英文名称" align="center" prop="englishName" />
            <el-table-column label="是否扣税" align="center" prop="isTaxable">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isTaxable" />
                </template>
            </el-table-column>
            <el-table-column label="增减属性" align="center" prop="attribute">
                <template #default="scope">
                    <dict-tag :options="attributeOptions" :value="scope.row.attribute" />
                </template>
            </el-table-column>
            <el-table-column label="数据来源" align="center" prop="dataSource">
                <template #default="scope">
                    <dict-tag :options="dataSourceOptions" :value="scope.row.dataSource" />
                </template>
            </el-table-column>
            <el-table-column label="是否显示" align="center" prop="isVisible">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isVisible" />
                </template>
            </el-table-column>
            <el-table-column label="是否在薪资单显示" align="center" prop="isShowInPayslip">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isShowInPayslip" />
                </template>
            </el-table-column>
            <el-table-column label="是否文本" align="center" prop="isText">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isText" />
                </template>
            </el-table-column>
            <el-table-column label="审批状态" align="center" prop="approvalStatus">
                <template #default="scope">
                    <dict-tag :options="approvalStatusOptions" :value="scope.row.approvalStatus" />
                </template>
            </el-table-column>
            <el-table-column label="操作历史" align="center" prop="operationHistory" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="55%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="薪资项目名称:" prop="itemName">
                    <el-select class="width220" filterable v-model="obj.dialogForm.itemName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in salaryItemOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="英文名称:" prop="englishName">
                    <el-input class="width220" v-model="obj.dialogForm.englishName" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="薪资项目编号:" prop="itemCode">
                    <el-input class="width220" v-model="obj.dialogForm.itemCode" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="是否文本:" prop="isText">
                    <el-select class="width220" v-model="obj.dialogForm.isText" placeholder="请选择" clearable>
                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否扣税:" prop="isTaxable">
                    <el-select class="width220" v-model="obj.dialogForm.isTaxable" placeholder="请选择" clearable>
                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="增减属性:" prop="attribute">
                    <el-select class="width220" v-model="obj.dialogForm.attribute" placeholder="请选择" clearable>
                        <el-option v-for="item in attributeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="小数位数:" prop="decimalPlaces">
                    <el-select class="width220" v-model="obj.dialogForm.decimalPlaces" placeholder="请选择" clearable>
                        <el-option v-for="item in decimalPlacesOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否年终奖:" prop="isYearEndBonus">
                    <el-select class="width220" v-model="obj.dialogForm.isYearEndBonus" placeholder="请选择" clearable>
                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否劳务费:" prop="isLaborCost">
                    <el-select class="width220" v-model="obj.dialogForm.isLaborCost" placeholder="请选择" clearable>
                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否显示:" prop="isVisible">
                    <el-select class="width220" v-model="obj.dialogForm.isVisible" placeholder="请选择" clearable>
                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否在薪资单中:" prop="isShowInPayslip">
                    <el-select class="width220" v-model="obj.dialogForm.isShowInPayslip" placeholder="请选择" clearable>
                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="选择数据来源:" prop="dataSource">
                    <el-select class="width220" v-model="obj.dialogForm.dataSource" placeholder="请选择" clearable>
                        <el-option v-for="item in dataSourceOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="系统提供:" prop="isSystemProvided">
                    <el-select class="width220" v-model="obj.dialogForm.isSystemProvided" placeholder="请选择" clearable>
                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公式:" prop="formula">
                    <el-input class="width220" type="textarea" v-model="obj.dialogForm.formula" placeholder="请输入" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>


        <!-- 设置系统项目属性 -->
        <el-dialog v-model="obj.dialogShow2" title="设置薪资项目顺序" width="30%" append-to-body draggable>
            <el-row>
                <el-button type="primary" @click="moveUp" :disabled="!hasSelectedItems">上移</el-button>
                <el-button type="primary" @click="moveDown" :disabled="!hasSelectedItems">下移</el-button>
                <el-button type="warning" @click="moveToTop" :disabled="!hasSelectedItems">置顶</el-button>
                <el-button type="warning" @click="moveToBottom" :disabled="!hasSelectedItems">置底</el-button>
                <el-button type="success" @click="saveOrder">保存</el-button>
            </el-row>
            <div class="salary-item">
                <div class="salary-item-title">薪资项目名称</div>
                <div class="salary-item-content">
                    <template v-for="(item, index) in salaryItemName" :key="index">
                        <div class="salary-item-item"
                            :class="{ 'salary-item-item-active': obj.selectedSalaryItems.includes(item.value) }"
                            @click="handleClick(item)">
                            {{ item.label }}
                        </div>
                    </template>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup name="PayrollItem">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const salaryItemName = ref([
    { value: '1', label: '累计其它扣除（专项）' },
    { value: '2', label: '残障金' },
    { value: '3', label: '本次扣减的3岁以下婴幼儿照护' },
    { value: '4', label: '累计3岁以下婴幼儿照护' },
    { value: '5', label: '本次扣减的其它扣除' },
    { value: '6', label: '其他个人' },
    { value: '7', label: '经济补偿金' },
    { value: '8', label: '经济补偿金超额计税部分' },
    { value: '9', label: '应发工资' },
    { value: '10', label: '公积金超额计税部分' },
    { value: '11', label: '累计公积金超额计税部分' },
    { value: '12', label: '累计经济补偿金超额计税部分' },
    { value: '13', label: '本次个人养老金' },
    { value: '14', label: '累计个人养老金' },
    { value: '15', label: '经济补偿金应税金额' },
    { value: '16', label: '经济补偿金个税' },
    { value: '17', label: '跨年个税调' },

]);

// 薪资项目选项
const salaryItemOptions = ref([
    { value: '1', label: '基本工资' },
    { value: '2', label: '绩效工资' },
    { value: '3', label: '岗位津贴' },
    { value: '4', label: '交通补贴' },
    { value: '5', label: '餐补' }
]);

// 是否选项
const yesNoOptions = ref([
    { value: 'Y', label: '是' },
    { value: 'N', label: '否' }
]);

// 增减属性选项
const attributeOptions = ref([
    { value: '1', label: '增加' },
    { value: '2', label: '减少' }
]);

// 数据来源选项
const dataSourceOptions = ref([
    { value: '1', label: '系统录入' },
    { value: '2', label: '手工录入' },
    { value: '3', label: '自动计算' }
]);

// 审批状态选项
const approvalStatusOptions = ref([
    { value: '1', label: '待审批' },
    { value: '2', label: '已审批' },
    { value: '3', label: '已驳回' }
]);

// 小数位数选项
const decimalPlacesOptions = ref([
    { value: '0', label: '0' },
    { value: '1', label: '1' },
    { value: '2', label: '2' },
    { value: '3', label: '3' },
    { value: '4', label: '4' }
]);

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        itemName: '1', // 薪资项目名称
        itemCode: 'BS001', // 薪资项目编号
        englishName: 'Base Salary', // 英文名称
        isTaxable: 'Y', // 是否扣税
        attribute: '1', // 增减属性
        dataSource: '1', // 数据来源
        isVisible: 'Y', // 是否显示
        isShowInPayslip: 'Y', // 是否在薪资单显示
        isText: 'N', // 是否文本
        approvalStatus: '2', // 审批状态
        operationHistory: '2023-06-01 创建' // 操作历史
    },
    {
        id: 2,
        itemName: '2', // 薪资项目名称
        itemCode: 'PB002', // 薪资项目编号
        englishName: 'Performance Bonus', // 英文名称
        isTaxable: 'Y', // 是否扣税
        attribute: '1', // 增减属性
        dataSource: '2', // 数据来源
        isVisible: 'Y', // 是否显示
        isShowInPayslip: 'Y', // 是否在薪资单显示
        isText: 'N', // 是否文本
        approvalStatus: '2', // 审批状态
        operationHistory: '2023-06-02 创建' // 操作历史
    },
    {
        id: 3,
        itemName: '3', // 薪资项目名称
        itemCode: 'PA003', // 薪资项目编号
        englishName: 'Position Allowance', // 英文名称
        isTaxable: 'N', // 是否扣税
        attribute: '1', // 增减属性
        dataSource: '1', // 数据来源
        isVisible: 'Y', // 是否显示
        isShowInPayslip: 'Y', // 是否在薪资单显示
        isText: 'N', // 是否文本
        approvalStatus: '1', // 审批状态
        operationHistory: '2023-06-03 创建' // 操作历史
    },
    {
        id: 4,
        itemName: '4', // 薪资项目名称
        itemCode: 'TA004', // 薪资项目编号
        englishName: 'Transportation Allowance', // 英文名称
        isTaxable: 'N', // 是否扣税
        attribute: '1', // 增减属性
        dataSource: '1', // 数据来源
        isVisible: 'Y', // 是否显示
        isShowInPayslip: 'Y', // 是否在薪资单显示
        isText: 'N', // 是否文本
        approvalStatus: '2', // 审批状态
        operationHistory: '2023-06-04 创建' // 操作历史
    },
    {
        id: 5,
        itemName: '5', // 薪资项目名称
        itemCode: 'MA005', // 薪资项目编号
        englishName: 'Meal Allowance', // 英文名称
        isTaxable: 'N', // 是否扣税
        attribute: '1', // 增减属性
        dataSource: '1', // 数据来源
        isVisible: 'Y', // 是否显示
        isShowInPayslip: 'Y', // 是否在薪资单显示
        isText: 'N', // 是否文本
        approvalStatus: '3', // 审批状态
        operationHistory: '2023-06-05 创建' // 操作历史
    }
]

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        salaryCategory: null, // 薪资类别名称
        salaryItemType: null, // 薪资项目种类
        salaryItemName: null, // 薪资项目名称
    }, // 查询表单
    rules: {
        itemName: [{ required: true, message: '请选择薪资项目名称', trigger: 'blur' }],
        englishName: [{ required: true, message: '请输入英文名称', trigger: 'blur' }],
        itemCode: [{ required: true, message: '请输入薪资项目编号', trigger: 'blur' }],
        isTaxable: [{ required: true, message: '请选择是否扣税', trigger: 'blur' }],
        attribute: [{ required: true, message: '请选择增减属性', trigger: 'blur' }],
        dataSource: [{ required: true, message: '请选择数据来源', trigger: 'blur' }],
    }, // 表单验证规则
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    ids: [], // 选中的id
    title: "", // 标题

    selectedSalaryItems: [], // 选中的薪资项目（多选）
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟异步请求
    setTimeout(() => {
        // 这里可以根据查询条件过滤数据
        const filteredData = mockTableData.filter(item => {
            const matchSalaryCategory = !obj.queryParams.salaryCategory ||
                item.itemName.includes(obj.queryParams.salaryCategory);
            const matchSalaryItemType = !obj.queryParams.salaryItemType ||
                item.itemName.includes(obj.queryParams.salaryItemType);
            const matchSalaryItemName = !obj.queryParams.salaryItemName ||
                item.itemName === obj.queryParams.salaryItemName;
            return matchSalaryCategory && matchSalaryItemType && matchSalaryItemName;
        });

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}



// 表单重置
function reset() {
    obj.dialogForm = {
        itemName: '',
        englishName: '',
        itemCode: '',
        isText: 'N',
        isTaxable: 'Y',
        attribute: '1',
        decimalPlaces: '2',
        isYearEndBonus: 'N',
        isLaborCost: 'N',
        isVisible: 'Y',
        isShowInPayslip: 'Y',
        dataSource: '1',
        isSystemProvided: 'N',
        formula: ''
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 行双击
function handleRowDblclick(row) {
    handleView(row);
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}

/** 查看按钮操作 */
function handleView(row) {
    reset();
    // 如果是从表格行点击进来的，直接使用行数据
    if (row) {
        obj.dialogForm = JSON.parse(JSON.stringify(row));
    }
    // 如果是从工具栏按钮点击进来的，使用选中的ID获取数据
    else if (obj.ids.length > 0) {
        const selectedItem = obj.tableData.find(item => item.id === obj.ids[0]);
        if (selectedItem) {
            obj.dialogForm = JSON.parse(JSON.stringify(selectedItem));
        }
    }
    obj.dialogShow = true;
    obj.title = "详情";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 如果是从表格行点击进来的，直接使用行数据
    if (row) {
        obj.dialogForm = JSON.parse(JSON.stringify(row));
    }
    // 如果是从工具栏按钮点击进来的，使用选中的ID获取数据
    else if (obj.ids.length > 0) {
        const selectedItem = obj.tableData.find(item => item.id === obj.ids[0]);
        if (selectedItem) {
            obj.dialogForm = JSON.parse(JSON.stringify(selectedItem));
        }
    }
    obj.dialogShow = true;
    obj.title = "修改";
}



/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 设置系统项目属性按钮操作 */
function handleSetSystemProjectAttribute() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    obj.dialogShow2 = true;
}

/** 生效按钮操作 */
function handleEffective() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    proxy.$modal.msgSuccess('生效操作成功');
}

/** 失效按钮操作 */
function handleInvalid() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    proxy.$modal.msgSuccess('失效操作成功');
}

/** 复制按钮操作 */
function handleCopy() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    const selectedItem = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedItem) {
        const newItem = JSON.parse(JSON.stringify(selectedItem));
        newItem.id = obj.tableData.length > 0 ? Math.max(...obj.tableData.map(item => item.id)) + 1 : 1;
        newItem.itemCode = newItem.itemCode + '_copy';
        obj.tableData.push(newItem);
        proxy.$modal.msgSuccess('复制操作成功');
    }
}

// 选择薪资项目名称（多选）
function handleClick(item) {
    const index = obj.selectedSalaryItems.indexOf(item.value);
    if (index === -1) {
        // 如果不在数组中，添加进去
        obj.selectedSalaryItems.push(item.value);
    } else {
        // 如果已在数组中，移除它
        obj.selectedSalaryItems.splice(index, 1);
    }
}

// 是否有选中项目的计算属性
const hasSelectedItems = computed(() => {
    return obj.selectedSalaryItems.length > 0;
});

// 将选中项目上移
function moveUp() {
    // 按照项目在列表中的顺序获取选中项
    const selectedIndexes = [];

    // 找出所有选中项在原列表中的索引
    salaryItemName.value.forEach((item, index) => {
        if (obj.selectedSalaryItems.includes(item.value)) {
            selectedIndexes.push(index);
        }
    });

    // 按照索引从小到大排序
    selectedIndexes.sort((a, b) => a - b);

    // 对每个选中项，如果不是第一项，就与前一项交换位置
    for (let i = 0; i < selectedIndexes.length; i++) {
        const currentIndex = selectedIndexes[i];
        // 如果已经是第一项或前一项也是选中项，则跳过
        if (currentIndex === 0 || selectedIndexes.includes(currentIndex - 1)) {
            continue;
        }

        // 交换当前项与前一项
        const temp = salaryItemName.value[currentIndex];
        salaryItemName.value[currentIndex] = salaryItemName.value[currentIndex - 1];
        salaryItemName.value[currentIndex - 1] = temp;

        // 更新选中项的索引
        selectedIndexes[i] = currentIndex - 1;
    }
}

// 将选中项目下移
function moveDown() {
    // 按照项目在列表中的顺序获取选中项
    const selectedIndexes = [];

    // 找出所有选中项在原列表中的索引
    salaryItemName.value.forEach((item, index) => {
        if (obj.selectedSalaryItems.includes(item.value)) {
            selectedIndexes.push(index);
        }
    });

    // 按照索引从大到小排序（下移需要从后往前处理）
    selectedIndexes.sort((a, b) => b - a);

    // 对每个选中项，如果不是最后一项，就与后一项交换位置
    for (let i = 0; i < selectedIndexes.length; i++) {
        const currentIndex = selectedIndexes[i];
        // 如果已经是最后一项或后一项也是选中项，则跳过
        if (currentIndex === salaryItemName.value.length - 1 || selectedIndexes.includes(currentIndex + 1)) {
            continue;
        }

        // 交换当前项与后一项
        const temp = salaryItemName.value[currentIndex];
        salaryItemName.value[currentIndex] = salaryItemName.value[currentIndex + 1];
        salaryItemName.value[currentIndex + 1] = temp;

        // 更新选中项的索引
        selectedIndexes[i] = currentIndex + 1;
    }
}

// 将选中项目置顶
function moveToTop() {
    // 按照项目在列表中的顺序获取选中项
    const selectedItems = [];
    const unselectedItems = [];

    // 分离选中项和非选中项
    salaryItemName.value.forEach(item => {
        if (obj.selectedSalaryItems.includes(item.value)) {
            selectedItems.push(item);
        } else {
            unselectedItems.push(item);
        }
    });

    // 重组列表，将选中项放在最前面
    salaryItemName.value = [...selectedItems, ...unselectedItems];
}

// 将选中项目置底
function moveToBottom() {
    // 按照项目在列表中的顺序获取选中项
    const selectedItems = [];
    const unselectedItems = [];

    // 分离选中项和非选中项
    salaryItemName.value.forEach(item => {
        if (obj.selectedSalaryItems.includes(item.value)) {
            selectedItems.push(item);
        } else {
            unselectedItems.push(item);
        }
    });

    // 重组列表，将选中项放在最后面
    salaryItemName.value = [...unselectedItems, ...selectedItems];
}

// 保存排序
function saveOrder() {
    proxy.$modal.msgSuccess('排序保存成功');
    // 这里可以实现保存排序到后端的逻辑
}


/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped>
.salary-item {
    width: 80%;
    margin: 20px auto;
    text-align: center;

    .salary-item-title {
        background-color: #EBEEF6;
        padding: 10px 0;
    }

    .salary-item-content {
        height: 450px;
        overflow-y: auto;

        .salary-item-item {
            padding: 10px 0;
            border-bottom: 1px solid #E6E6E6;
            cursor: pointer;
        }

        .salary-item-item:not(.salary-item-item-active):hover {
            background: #F5F7FA;
        }

        .salary-item-item-active {
            background: #5F9EA0;
            color: #fff;
            border-bottom: 1px solid #fff;
            cursor: pointer;
        }
    }


}
</style>