<!-- 财务需求统计报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-row>
                <el-form-item>
                    <el-button type="primary" icon="Download" @click="handleDownloadTemplate">下载模版</el-button>
                </el-form-item>
                <el-form-item label="上传文件:" prop="file">
                    <FileUpload v-model="obj.queryParams.file" :limit="1" :fileSize="10" :fileType="['xlsx', 'xls']" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Upload" @click="handleUpload"
                        :loading="obj.uploadLoading">上传需求数据</el-button>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item label="报表年月:" prop="reportMonth">
                    <el-date-picker class="width220" v-model="obj.queryParams.reportMonth" type="month"
                        placeholder="请选择年月" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                </el-form-item>
            </el-row>
        </el-form>
    </div>
</template>

<script setup name="FinancialDemand_statistics">

import FileUpload from '@/components/FileUpload'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 状态选项
const statusOptions = [
    { value: '0', label: '待处理', elTagType: 'info' },
    { value: '1', label: '处理中', elTagType: 'warning' },
    { value: '2', label: '已完成', elTagType: 'success' },
    { value: '3', label: '已拒绝', elTagType: 'danger' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    uploadLoading: false, // 上传加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportMonth: null,
        file: null
    }, // 查询表单
    total: 0, // 总条数
    ids: [], // 选中id
    tableData: [] // 列表
})

/** 下载模版操作 */
function handleDownloadTemplate() {
    // 调用下载模版接口
    proxy.$modal.msgSuccess('模版下载成功');
}

/** 上传需求数据操作 */
function handleUpload() {
    if (!obj.queryParams.file) {
        proxy.$modal.msgError('请选择要上传的文件');
        return;
    }
    obj.uploadLoading = true;
    // 调用上传接口
    setTimeout(() => {
        obj.uploadLoading = false;
        proxy.$modal.msgSuccess('数据上传成功');
        // 上传成功后刷新列表
        getList();
    }, 1500);
}

/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

</script>

<style lang="scss" scoped>
.width220 {
    width: 220px;
}
</style>