<!-- 非纯代发人员维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件类型:" prop="idType">
                <el-select class="width220" v-model="obj.queryParams.idType" placeholder="请选择" clearable>
                    <el-option v-for="item in idTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="证件编号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row class="mb8">
                <el-col :span="24">
                    <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="View" @click="handleHistory">查看更改历史</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 表格 -->
        <PureConsignmentTable :tableData="obj.tableData" :loading="obj.loading" :page="obj.queryParams.pageNum"
            :limit="obj.queryParams.pageSize" @handlePagination="handlePagination"
            @selection-change="handleSelectionChange" />
        <!-- 修改 -->
        <PureConsignmentForm :dialogShow="obj.dialogShow" :dialogForm="obj.dialogForm" :title="obj.title" :rules="rules"
            menuName="non_pureAgent" @submit="submit" @handleClose="obj.dialogShow = false" />

        <!-- 查看更改历史 -->
        <PureConsignmentHistory v-model:dialogShow="obj.historydialogShow" :tableData="obj.tableData" :title="obj.title"
            @handlePagination="handlePagination" />
    </div>
</template>

<script setup name="Non_pureAgent">
import PureConsignmentTable from '@/views/reonManage/components/table/pureConsignmentTable.vue';
import PureConsignmentForm from '@/views/reonManage/components/dialog/pureConsignmentForm.vue';
import PureConsignmentHistory from '@/views/reonManage/components/dialog/pureConsignmentHistory.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 证件类型选项
const idTypeOptions = [
    { value: '1', label: '居民身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '港澳居民来往内地通行证' },
    { value: '4', label: '台湾同胞来往内地通行证' },
    { value: '5', label: '外国人永久居留证' },
    { value: '6', label: '其他' }
];


// 客服选项
const serviceOptions = [
    { value: 'S001', label: '张三' },
    { value: 'S002', label: '李四' },
    { value: 'S003', label: '王五' },
    { value: 'S004', label: '赵六' },
    { value: 'S005', label: '孙七' }
];

// 非纯代发人员数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: '', // 唯一号
        employeeName: '', // 雇员姓名
        idType: '', // 证件类型
        idNumber: '', // 证件编号
        customerName: '', // 客户名称
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            uniqueId: 'U20230601001',
            employeeName: '张三',
            idType: '1',
            idNumber: '110101199001011234',
            customerCode: 'C001',
            customerName: '北京科技有限公司',
            dispatchService: 'S001',
            salaryService: 'S002',
            receiveService: 'S003',
            phoneNumber: '13800138001',
            email: '<EMAIL>',
            salaryIdType: '1',
            salaryOtherIdType: '',
            nationality: '中国',
            gender: '1',
            birthDate: '1990-01-01',
            taxReason: '1',
            birthCountry: '中国',
            firstEntryDate: '2020-01-01',
            expectedDepartureDate: '2025-01-01',
            otherIdNumber: '',
            otherName: '',
            employmentType: '1'
        },
        {
            id: 2,
            uniqueId: 'U20230601002',
            employeeName: '李四',
            idType: '1',
            idNumber: '310101199002022345',
            customerCode: 'C002',
            customerName: '上海贸易有限公司',
            dispatchService: 'S002',
            salaryService: 'S003',
            receiveService: 'S004',
            phoneNumber: '13900139001',
            email: '<EMAIL>',
            salaryIdType: '1',
            salaryOtherIdType: '',
            nationality: '中国',
            gender: '2',
            birthDate: '1990-02-02',
            taxReason: '1',
            birthCountry: '中国',
            firstEntryDate: '2020-02-02',
            expectedDepartureDate: '2025-02-02',
            otherIdNumber: '',
            otherName: '',
            employmentType: '1'
        },
    ],

    // 历史记录数据
    historyData: [
        {
            id: 1,
            uniqueId: 'U20230601001',
            employeeName: '张三',
            idType: '1',
            idNumber: '110101199001011234',
            phoneNumber: '13800138000',
            backupTime: '2023-05-01 10:00:00'
        },
        {
            id: 2,
            uniqueId: 'U20230601001',
            employeeName: '张三',
            idType: '1',
            idNumber: '110101199001011234',
            phoneNumber: '13800138001',
            backupTime: '2023-06-01 10:00:00'
        }
    ],

    // 对话框相关数据
    dialogShow: false,
    historydialogShow: false,
    title: '',
    dialogForm: {},
    rules: {
        employeeName: [{ required: true, message: '请输入雇员姓名', trigger: 'blur' }],
        idType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
        idNumber: [{ required: true, message: '请输入证件号码', trigger: 'blur' }],
        phoneNumber: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
        gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
        birthDate: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
        employmentType: [{ required: true, message: '请选择任职受雇从业类型', trigger: 'change' }]
    },
    ids: [] // 选中的id
});

/**
 * 获取证件类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getIdTypeName(typeId) {
    if (!typeId) return '-';

    const type = idTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}

/**
 * 获取客服名称
 * @param {string} code 客服编号
 * @returns {string} 客服名称
 */
function getServiceName(code) {
    if (!code) return '-';

    const service = serviceOptions.find(item => item.value === code);
    return service ? service.label : '-';
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.uniqueId) {
            filteredData = filteredData.filter(item =>
                item.uniqueId.includes(obj.queryParams.uniqueId)
            );
        }

        if (obj.queryParams.employeeName) {
            filteredData = filteredData.filter(item =>
                item.employeeName.includes(obj.queryParams.employeeName)
            );
        }

        if (obj.queryParams.idType) {
            filteredData = filteredData.filter(item =>
                item.idType === obj.queryParams.idType
            );
        }

        if (obj.queryParams.idNumber) {
            filteredData = filteredData.filter(item =>
                item.idNumber.includes(obj.queryParams.idNumber)
            );
        }

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item =>
                item.customerName.includes(obj.queryParams.customerName)
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}
// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 修改按钮操作 */
function handleEdit() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条记录进行修改');
        return;
    }

    const id = obj.ids[0];
    const row = obj.tableData.find(item => item.id === id);

    if (!row) {
        proxy.$modal.msgError('未找到该记录');
        return;
    }

    obj.dialogShow = true;
    obj.title = '修改';
    obj.form = JSON.parse(JSON.stringify(row));
}

/** 查看更改历史按钮操作 */
function handleHistory() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条记录查看历史');
        return;
    }

    obj.historydialogShow = true;
    obj.title = '查看更改历史';
}

/** 提交表单 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 初始化加载数据
onMounted(() => {
    getList();
});
</script>
<style lang="scss" scoped></style>