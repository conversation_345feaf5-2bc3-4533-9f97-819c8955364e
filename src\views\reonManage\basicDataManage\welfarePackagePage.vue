<!-- 福利包主页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="城市:" prop="cityCode">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利办理方:" prop="benefitsHandler">
                <el-select class="width220" filterable v-model="obj.queryParams.benefitsHandler" placeholder="请选择福利办理方"
                    clearable>
                    <el-option v-for="item in benefitsHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" filterable v-model="obj.queryParams.accountType" placeholder="请选择大户/单立户"
                    clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="福利包名称:" prop="packageName">
                <el-input class="width220" v-model="obj.queryParams.packageName" placeholder="请输入福利包名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="福利包编号:" prop="packageNo">
                <el-input class="width220" v-model="obj.queryParams.packageNo" placeholder="请输入福利包编号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态:" prop="status">
                <el-select class="width220" filterable v-model="obj.queryParams.status" placeholder="请选择状态" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="VideoPause" :disabled="obj.multiple"
                    @click="handlePause">暂停</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="VideoPlay" :disabled="obj.multiple"
                    @click="handleEnable">启用</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="CopyDocument" :disabled="obj.single"
                    @click="handleCopy">复制福利包</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="城市" align="center" prop="cityName" />
            <el-table-column label="福利包编号" align="center" prop="packageNo" />
            <el-table-column label="福利包名称" align="center" prop="packageName" />
            <el-table-column label="社保公积金比例" align="center" prop="socialRatio" />
            <el-table-column label="福利办理方" align="center" prop="benefitsHandler" />
            <el-table-column label="是否是大户" align="center" prop="isBigAccount">
                <template #default="scope">
                    <el-tag :type="scope.row.isBigAccount === '1' ? 'success' : 'info'">
                        {{ scope.row.isBigAccount === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="单立户名称" align="center" prop="standaloneName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.status === '1' ? 'success' : scope.row.status === '2' ? 'warning' : 'danger'">
                        {{ scope.row.status === '1' ? '有效' : scope.row.status === '2' ? '暂停' : '无效' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="是否引用" align="center" prop="isReference">
                <template #default="scope">
                    <el-tag :type="scope.row.isReference === '1' ? 'success' : 'info'">
                        {{ scope.row.isReference === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="60%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="城市" prop="cityCode">
                    <el-select :disabled="obj.isDetail" class="width220" filterable v-model="obj.dialogForm.cityCode"
                        placeholder="请选择城市" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="福利办理方" prop="benefitsHandler">
                    <el-select :disabled="obj.isDetail" class="width220" filterable
                        v-model="obj.dialogForm.benefitsHandler" placeholder="请选择福利办理方" clearable>
                        <el-option v-for="item in benefitsHandlerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="福利包名称" prop="packageName">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.packageName"
                        placeholder="请输入福利包名称" />
                </el-form-item>
                <el-form-item label="大户/单立户" prop="accountType">
                    <el-select :disabled="obj.isDetail" class="width220" filterable v-model="obj.dialogForm.accountType"
                        placeholder="请选择大户/单立户" clearable>
                        <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.customerName"
                        placeholder="请输入客户名称" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select :disabled="obj.isDetail" class="width220" filterable v-model="obj.dialogForm.status"
                        placeholder="请选择状态" clearable>
                        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="实操增员截点" prop="addDeadline">
                    <el-date-picker :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.addDeadline"
                        type="date" placeholder="请选择日期" clearable />
                </el-form-item>
                <el-form-item label="实操减员截点" prop="reduceDeadline">
                    <el-date-picker :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.reduceDeadline"
                        type="date" placeholder="请选择日期" clearable />
                </el-form-item>
                <el-form-item label="增员申报方式" prop="addMethod">
                    <el-select :disabled="obj.isDetail" class="width220" filterable v-model="obj.dialogForm.addMethod"
                        placeholder="请选择增员申报方式" clearable>
                        <el-option label="线上申报" value="1" />
                        <el-option label="线下申报" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="减员申报方式" prop="reduceMethod">
                    <el-select :disabled="obj.isDetail" class="width220" filterable
                        v-model="obj.dialogForm.reduceMethod" placeholder="请选择减员申报方式" clearable>
                        <el-option label="线上申报" value="1" />
                        <el-option label="线下申报" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="单立户名称" prop="standaloneName">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.standaloneName"
                        placeholder="请输入单立户名称" />
                </el-form-item>
                <el-row>
                    <el-form-item label="社保公积金比例" prop="socialRatio">
                        <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.socialRatio"
                            placeholder="请输入社保公积金比例" />
                    </el-form-item>
                </el-row>
                <el-row>
                    <el-col class="mb8" v-if="!obj.isDetail">
                        <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
                    </el-col>
                    <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
                        @selection-change="handleSelectionChange">
                        <el-table-column type="selection" width="55" />
                        <el-table-column label="序号" type="index" width="60" align="center" />
                        <el-table-column label="社保比例编号" align="center" prop="scaleNo" />
                        <el-table-column label="社保比例名称" align="center" prop="scaleName" />
                        <el-table-column label="是否有效" align="center" prop="isValid">
                            <template #default="scope">
                                <el-tag :type="scope.row.isValid === '1' ? 'success' : 'danger'">
                                    {{ scope.row.isValid === '1' ? '有效' : '无效' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="WelfarePackagePage">

import { useAreaStore } from '@/store/modules/area'
import { listPackage, getPackage, delPackage, addPackage, updatePackage } from "@/api/reonApi/package";


const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据

// 福利办理方选项
const benefitsHandlerOptions = [
    { value: '1', label: '总公司' },
    { value: '2', label: '分公司' },
    { value: '3', label: '供应商' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 状态选项
const statusOptions = [
    { value: '1', label: '有效' },
    { value: '2', label: '暂停' },
    { value: '3', label: '无效' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cityCode: null,
        benefitsHandler: null,
        accountType: null,
        customerName: null,
        packageName: null,
        packageNo: null,
        status: null,
    },//查询表单
    rules: {
        cityCode: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        benefitsHandler: [{ required: true, message: '请选择福利办理方', trigger: 'blur' }],
        packageName: [{ required: true, message: '请输入福利包名称', trigger: 'blur' }],
        accountType: [{ required: true, message: '请选择大户/单立户', trigger: 'blur' }],
        customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
    isDetail: false,//是否详情
})

/** 列表 */
function getList() {
    obj.loading = true;
    listPackage(obj.queryParams).then(response => {
        obj.tableData = [{
            id: 1,
            cityCode: '110000',
            cityName: '北京市',
            packageNo: 'FL20230501001',
            packageName: '北京标准福利包A',
            socialRatio: '养老保险(8%+20%),医疗保险(2%+10%),公积金(12%+12%)',
            benefitsHandler: '总公司',
            isBigAccount: '1',
            standaloneName: '北京单立户',
            customerName: '客户A',
            status: '1',
            createBy: 'admin',
            createTime: '2023-05-01 10:00:00',
            isReference: '0'
        },
        {
            id: 2,
            cityCode: '310000',
            cityName: '上海市',
            packageNo: 'FL20230502001',
            packageName: '上海标准福利包B',
            socialRatio: '养老保险(8%+20%),医疗保险(2%+10%),公积金(7%+7%)',
            benefitsHandler: '分公司',
            isBigAccount: '0',
            standaloneName: '上海单立户A',
            customerName: '客户B',
            status: '1',
            createBy: 'admin',
            createTime: '2023-05-02 10:00:00',
            isReference: '1'
        },]

        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    obj.isDetail = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}
/** 查看按钮操作 */
function handleDetail(row) {
    console.log(row);
    obj.dialogShow = true;
    obj.title = "详情";
    obj.isDetail = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = JSON.parse(JSON.stringify(row || obj.tableData.find(item => obj.ids.includes(item.id))));
    obj.dialogShow = true;
    obj.title = "修改";
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updatePackage(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addPackage(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除编号为"' + _ids + '"的数据项？').then(function () {
        return delPackage(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}
/** 暂停按钮操作 */
function handlePause() {
    proxy.$modal.confirm('是否确认暂停').then(function () {
        return pausePackage(obj.ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("暂停成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消暂停");
    });
}
/** 启用按钮操作 */
function handleEnable() {
    proxy.$modal.confirm('是否确认启用').then(function () {
        return resumePackage(obj.ids);
    }).then(() =>
        getList(),
        proxy.$modal.msgSuccess("启用成功")
    ).catch(() => {
        proxy.$modal.msgWarning("用户取消启用");
    });
}
/** 复制按钮操作 */
function handleCopy() {
    proxy.$modal.confirm('是否确认复制').then(function () {
        return copyPackage(obj.ids);
    }).then(() =>
        getList(),
        proxy.$modal.msgSuccess("复制成功")
    ).catch(() => {
        proxy.$modal.msgWarning("用户取消复制");
    });
}

getList();
</script>
<style lang="scss" scoped></style>