<!-- 账单对比报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="报表年月:" prop="reportMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.reportMonth" type="month" placeholder="请选择年月"
                    clearable />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="客户帐套:" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户帐套" clearable>
                    <el-option v-for="item in customerAccountOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单类型:" prop="billTypes">
                <el-checkbox-group v-model="obj.queryParams.billTypes">
                    <el-checkbox label="social">社保</el-checkbox>
                    <el-checkbox label="commercial">商保</el-checkbox>
                    <el-checkbox label="salary">工资</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-row class="mb8" :gutter="10">
                <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
                <el-button icon="Refresh" @click="handleReset">重置</el-button>
            </el-row>
        </el-form>
        <el-row class="mb8">
            <el-col :span="24">
                <el-button type="primary" icon="Download" @click="handleExport">数据导出</el-button>
                <el-button type="primary" icon="Refresh" @click="handleGenerate">数据生成</el-button>
            </el-col>
        </el-row>
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" border style="width: 100%">
            <el-table-column typee="index" label="序号" width="80" align="center" />
            <el-table-column prop="contractCode" label="合同编号" align="center" />
            <el-table-column prop="contractName" label="合同名称" align="center" />
            <el-table-column prop="billTemplate" label="账单模板" align="center" />
            <el-table-column prop="customerName" label="客户" align="center" />
            <el-table-column prop="signLocation" label="签单地" align="center" />
            <el-table-column prop="lastMonthPersonCount" label="上月人数" align="center">
                <template #default="scope">
                    <span>{{ scope.row.lastMonthPersonCount }} 人</span>
                </template>
            </el-table-column>
            <el-table-column prop="lastMonthIncome" label="上月收入" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.lastMonthIncome) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="currentMonthPersonCount" label="当月人数" align="center">
                <template #default="scope">
                    <span>{{ scope.row.currentMonthPersonCount }} 人</span>
                </template>
            </el-table-column>
            <el-table-column prop="currentMonthIncome" label="当月收入" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.currentMonthIncome) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="reportMonth" label="报表年月" align="center" />
            <el-table-column prop="billType" label="账单类型" align="center">
                <template #default="scope">
                    <el-tag v-if="scope.row.billType === 'social'" type="success">社保</el-tag>
                    <el-tag v-else-if="scope.row.billType === 'commercial'" type="warning">商保</el-tag>
                    <el-tag v-else-if="scope.row.billType === 'salary'" type="info">工资</el-tag>
                    <span v-else>{{ scope.row.billType }}</span>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="ComparisonStatement">

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客户帐套选项
const customerAccountOptions = ref([
    { value: '1', label: '帐套A' },
    { value: '2', label: '帐套B' },
    { value: '3', label: '帐套C' }
]);

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    tableData: [], // 表格数据
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportMonth: null,
        contractName: null,
        customerAccount: null,
        billTypes: [] // 账单类型数组
    } // 查询表单
})



/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        // obj.total = response.total;
        // obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                contractCode: 'HT20230001',
                contractName: '合同1',
                billTemplate: '模板1',
                customerName: '客户A',
                signLocation: '北京',
                lastMonthPersonCount: 90,
                lastMonthIncome: 45000.00,
                currentMonthPersonCount: 100,
                currentMonthIncome: 50000.00,
                reportMonth: '2023-05',
                billType: 'social'
            },
            {
                id: 2,
                contractCode: 'HT20230002',
                contractName: '合同2',
                billTemplate: '模板2',
                customerName: '客户B',
                signLocation: '上海',
                lastMonthPersonCount: 80,
                lastMonthIncome: 40000.00,
                currentMonthPersonCount: 75,
                currentMonthIncome: 37500.00,
                reportMonth: '2023-05',
                billType: 'commercial'
            },
            {
                id: 3,
                contractCode: 'HT20230003',
                contractName: '合同3',
                billTemplate: '模板3',
                customerName: '客户C',
                signLocation: '广州',
                lastMonthPersonCount: 120,
                lastMonthIncome: 60000.00,
                currentMonthPersonCount: 130,
                currentMonthIncome: 65000.00,
                reportMonth: '2023-05',
                billType: 'salary'
            }
        ];
        obj.total = 3;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 数据导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 数据生成按钮操作 */
function handleGenerate() {
    proxy.$modal.confirm('确认生成数据吗？').then(() => {
        // 调用数据生成接口
        proxy.$modal.msgSuccess('数据生成成功');
        getList();
    }).catch(() => { });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>