<template>
    <el-dialog v-model="dialogShow" :title="props.title" width="65%" append-to-body @close="handleClose">
        <el-button class="mb10" type="primary" @click="handleExport">导出数据</el-button>
        <el-table :data="props.tableData" border>
            <el-table-column label="导入编号" align="center" prop="importNo" />
            <el-table-column label="行号" align="center" prop="rowNo" />
            <el-table-column label="错误描述" align="center" prop="errorDesc" />
            <el-table-column label="提醒描述" align="center" prop="warningDesc" />
            <el-table-column label="导入结果" align="center" prop="importResult">
                <template #default="scope">
                    <el-tag :type="scope.row.importResult === '成功' ? 'success' : 'danger'">
                        {{ scope.row.importResult }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="导入信息" align="center" prop="importInfo" />
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
    </el-dialog>
</template>
<script setup>
const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    tableData: {
        type: Array,
        default: () => []
    },
})
const dialogShow = ref(props.dialogShow)
// 监听props.dialogShow的变化
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal
})

const emit = defineEmits(['close'])
// 关闭对话框
function handleClose() {
    emit('close')
}

// 导出数据
function handleExport() {
    console.log(props.tableData)
}
</script>