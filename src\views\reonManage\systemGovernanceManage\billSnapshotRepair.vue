<!-- 账单快照修复 -->
<template>
    <div class="app-container">
        <border-box title="恢复账单数据快照">
            <el-form :model="obj.queryParams" label-width="auto">
                <el-row class="mb8" justify="center">
                    <el-form-item label="合同名称:">
                        <el-input class="width220" readonly v-model="obj.queryParams.contractName" @focus="showContract"
                            placeholder="请输入" />
                    </el-form-item>
                </el-row>
                <el-row class="mb8" justify="center">
                    <el-form-item>
                        <el-button type="primary" @click="submit">提交</el-button>
                    </el-form-item>
                </el-row>
            </el-form>
        </border-box>
        <Contract v-model:show="obj.show" :width="obj.width" @select="selectContract" />
    </div>
</template>


<script setup name="BillSnapshotRepair">
import Contract from '@/views/reonManage/components/contract.vue'

const obj = reactive({
    show: false,
    width: '45%',
    queryParams: {
        contractName: ''
    }
})

// 合同弹窗
const showContract = () => {
    obj.show = true;
}
// 选择合同
const selectContract = (row) => {
    if (row) {
        obj.queryParams.contractName = row.contractName;
    } else {
        obj.queryParams.contractName = '';
    }
    obj.show = false;
}

// 提交
function submit() {
    console.log(queryParams.value);
}

</script>
<style lang="scss" scoped></style>