<!-- 扣缴义务人发薪人数报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="发放年月:" prop="date">
                <el-date-picker class="width220" v-model="queryParams.date" type="date" placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="扣缴义务人名称" prop="typeCode">
                <el-input class="width220" v-model="queryParams.withholdingAgentName" placeholder="请输入扣缴义务人名称"
                    clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Download" @click="handleExport">数据导出</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup name="WithholdingAgent_payrollReport">

const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    date: null,
    withholdingAgentName: null,
})

/** 导出按钮 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...queryParams.value
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

</script>
<style lang="scss" scoped></style>