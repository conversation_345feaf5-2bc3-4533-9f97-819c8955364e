<!-- 查询一次性项目 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户账套:" prop="customerAccount">
                <el-input class="width220" v-model="obj.queryParams.customerAccount" placeholder="请输入客户账套" clearable />
            </el-form-item>
            <el-form-item label="账单年月起:" prop="billMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonthStart" type="month"
                    placeholder="请选择开始年月" clearable />
            </el-form-item>
            <el-form-item label="账单年月止:" prop="billMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonthEnd" type="month"
                    placeholder="请选择结束年月" clearable />
            </el-form-item>
            <el-form-item label="财务应收年月:" prop="financialReceivableMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.financialReceivableMonth" type="month"
                    placeholder="请选择年月" clearable />
            </el-form-item>
            <el-form-item label="提交分公司:" prop="submitBranch">
                <el-select class="width220" v-model="obj.queryParams.submitBranch" placeholder="请选择分公司" clearable>
                    <el-option label="上海分公司" value="上海分公司" />
                    <el-option label="北京分公司" value="北京分公司" />
                    <el-option label="广州分公司" value="广州分公司" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" plain icon="Download" @click="handleExport">数据导出</el-button>
            </el-col>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="提交分公司" align="center" prop="submitBranch" />
            <el-table-column label="提交人" align="center" prop="submitter" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户帐套" align="center" prop="customerAccount" />
            <el-table-column label="大合同编号" align="center" prop="masterContractCode" />
            <el-table-column label="大合同名称" align="center" prop="masterContractName" />
            <el-table-column label="账单年月" align="center" prop="billMonth" />
            <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
            <el-table-column label="产品类型" align="center" prop="productType" />
            <el-table-column label="产品分类" align="center" prop="productCategory" />
            <el-table-column label="应收金额" align="center" prop="receivableAmount" />
            <el-table-column label="金额" align="center" prop="amount" />
            <el-table-column label="人数" align="center" prop="personCount" />
            <el-table-column label="审批备注" align="center" prop="approvalRemark" />
            <el-table-column label="原因备注" align="center" prop="reasonRemark" />
            <el-table-column label="客服审批人" align="center" prop="serviceApprover" />
            <el-table-column label="客服审批时间" align="center" prop="serviceApprovalTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="OneTimeProjectConsolidation_query">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        customerAccount: null,
        billMonthStart: null,
        billMonthEnd: null,
        financialReceivableMonth: null,
        submitBranch: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                submitBranch: '上海分公司',
                submitter: '张三',
                customerName: '客户名称1',
                customerCode: 'CUST001',
                customerAccount: '帐套001',
                masterContractCode: 'MASTER001',
                masterContractName: '大合同1',
                billMonth: '2023-01',
                financialReceivableMonth: '2023-01',
                productType: '一次性项目',
                productCategory: '服务费',
                receivableAmount: 10000.00,
                amount: 10000.00,
                personCount: 5,
                approvalRemark: '已审批',
                reasonRemark: '特殊项目费用',
                serviceApprover: '李四',
                serviceApprovalTime: '2023-01-15 10:00:00'
            },
            {
                id: 2,
                submitBranch: '北京分公司',
                submitter: '王五',
                customerName: '客户名称2',
                customerCode: 'CUST002',
                customerAccount: '帐套002',
                masterContractCode: 'MASTER002',
                masterContractName: '大合同2',
                billMonth: '2023-02',
                financialReceivableMonth: '2023-02',
                productType: '一次性项目',
                productCategory: '咨询费',
                receivableAmount: 20000.00,
                amount: 20000.00,
                personCount: 10,
                approvalRemark: '已审批',
                reasonRemark: '项目需求变更费用',
                serviceApprover: '赵六',
                serviceApprovalTime: '2023-02-15 10:00:00'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 数据导出
function handleExport() {
    proxy.$modal.msgSuccess('导出数据成功');
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>