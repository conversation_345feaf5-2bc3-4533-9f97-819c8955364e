<!-- 欠款报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="财务应收年月(起):" prop="financeReceivableMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.financeReceivableMonthStart" type="month"
                    placeholder="请选择起始月份" clearable />
            </el-form-item>
            <el-form-item label="财务应收年月(止):" prop="financeReceivableMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.financeReceivableMonthEnd" type="month"
                    placeholder="请选择结束月份" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="欠款余额>=:" prop="minArrearsAmount">
                <el-input class="width220" v-model="obj.queryParams.minArrearsAmount" placeholder="请输入最小欠款余额"
                    clearable />
            </el-form-item>
            <el-form-item label="欠款余额<=:" prop="maxArrearsAmount">
                <el-input class="width220" v-model="obj.queryParams.maxArrearsAmount" placeholder="请输入最大欠款余额"
                    clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup name="ArrearsStatement">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    // 加载状态
    loading: false,
    // 选中数据
    ids: [],
    // 单选
    single: true,
    // 多选
    multiple: true,
    // 总条数
    total: 0,
    // 表格数据
    tableData: [],
    // 查询参数
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        financeReceivableMonthStart: null,
        financeReceivableMonthEnd: null,
        customerName: null,
        minArrearsAmount: null,
        maxArrearsAmount: null
    }
})

/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

</script>

<style lang="scss" scoped></style>