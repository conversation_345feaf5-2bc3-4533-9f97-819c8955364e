<!-- 公司相关信息维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form @submit.prevent :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch"
            label-width="auto">
            <el-form-item label="公司名称:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入公司名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="View" :disabled="obj.single" @click="handleDetail">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleDownload">下载</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="公司名称" align="center" prop="companyName" />
            <el-table-column label="公司电话" align="center" prop="phone" />
            <el-table-column label="公司地址" align="center" prop="address" />
            <el-table-column label="公司邮箱" align="center" prop="email" />
            <el-table-column label="公司类型" align="center" prop="companyType" />
            <el-table-column label="公司客服" align="center">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="45%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="公司名称" prop="companyName">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.companyName"
                        placeholder="请输入公司名称" />
                </el-form-item>
                <el-form-item label="公司类型" prop="companyType">
                    <el-select :disabled="obj.isDetail" class="width220" filterable v-model="obj.dialogForm.companyType"
                        placeholder="请选择公司类型" clearable>
                        <el-option v-for="item in companyTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公司电话" prop="phone">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.phone"
                        placeholder="请输入公司电话" />
                </el-form-item>
                <el-form-item label="公司地址" prop="address">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.address"
                        placeholder="请输入公司地址" />
                </el-form-item>
                <el-form-item label="公司邮箱" prop="email">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.email"
                        placeholder="请输入公司邮箱" />
                </el-form-item>
                <el-divider content-position="left">文件上传</el-divider>
                <el-row class="mb8">
                    <el-col :span="24">
                        <FileUpload v-model="obj.dialogForm.fileList" />
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 查看项目客服 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="45%" append-to-body draggable>
            <el-form class="formHight" ref="formRef2" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="员工姓名" prop="name">
                    <el-input class="width220" v-model="obj.dialogForm.name" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery2">查询</el-button>
                </el-form-item>
                <el-table :data="obj.tableData2" border>
                    <el-table-column label="姓名" align="center" prop="name" />
                    <el-table-column label="电话" align="center" prop="phone" />
                    <el-table-column label="邮箱" align="center" prop="email" />
                    <el-table-column label="所属公司" align="center" prop="companyName" />
                </el-table>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup name="CompanyRelatedInformation_maintenance">

import { useAreaStore } from '@/store/modules/area'

import { listImport } from "@/api/reonApi/import";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据

// 公司类型选项
const companyTypeOptions = [
    { value: '1', label: '总公司' },
    { value: '2', label: '分公司' },
    { value: '3', label: '子公司' },
    { value: '4', label: '合作公司' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
    },//查询表单
    rules: {
        companyName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
        companyType: [{ required: true, message: '请选择公司类型', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入公司电话', trigger: 'blur' }],
        email: [{ pattern: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/, message: '请输入正确的邮箱地址', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            companyName: '北京总公司',
            companyType: '总公司',
            phone: '010-12345678',
            address: '北京市海淀区中关村大街123号',
            email: '<EMAIL>',
        },
        {
            id: 2,
            companyName: '上海分公司',
            companyType: '分公司',
            phone: '021-87654321',
            address: '上海市浦东新区张江路456号',
            email: '<EMAIL>',
        },
        {
            id: 3,
            companyName: '广州子公司',
            companyType: '子公司',
            phone: '020-98765432',
            address: '广州市天河区体育西路789号',
            email: '<EMAIL>',
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    dialogShow2: false, //弹出框
    tableData2: [
        {
            id: 1,
            name: '张三',
            phone: '13800138000',
            email: '<EMAIL>',
            companyName: '北京总公司'
        },
        {
            id: 2,
            name: '李四',
            phone: '13900139000',
            email: '<EMAIL>',
            companyName: '北京总公司'
        },
        {
            id: 3,
            name: '王五',
            phone: '13700137000',
            email: '<EMAIL>',
            companyName: '上海分公司'
        }
    ],//客服列表
    ids: [],//选中的id
    title: "",//标题
    isDetail: false,//是否详情
})

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    obj.isDetail = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}
/** 查看按钮操作 */
function handleDetail(row) {
    reset();
    obj.title = "详情";
    obj.dialogForm = row;
    obj.isDetail = true;
    obj.dialogShow = true;
}


/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = JSON.parse(JSON.stringify(row || obj.tableData.find(item => obj.ids.includes(item.id))));
    obj.dialogShow = true;
    obj.title = "修改";
}
/** 查看按钮操作 */
function handleView(row) {
    reset();
    obj.dialogShow2 = true;
    obj.title = "查看项目客服";
    // 模拟根据公司筛选客服数据
    const allData = [
        {
            id: 1,
            name: '张三',
            phone: '13800138000',
            email: '<EMAIL>',
            companyName: '北京总公司'
        },
        {
            id: 2,
            name: '李四',
            phone: '13900139000',
            email: '<EMAIL>',
            companyName: '北京总公司'
        },
        {
            id: 3,
            name: '王五',
            phone: '13700137000',
            email: '<EMAIL>',
            companyName: '上海分公司'
        }
    ];
    obj.tableData2 = allData.filter(item => item.companyName === row.companyName);
}

/** 客服查询按钮操作 */
function handleQuery2() {
    // 模拟客服查询
    const name = obj.dialogForm.name;
    if (name) {
        obj.tableData2 = obj.tableData2.filter(item => item.name.includes(name));
    }
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}



/** 下载按钮操作 */
function handleDownload() {
    proxy.$modal.msgSuccess('开始下载文件');
    // 模拟下载文件
    const fileName = '公司相关信息_' + new Date().getTime() + '.xlsx';
    const link = document.createElement('a');
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

getList();
</script>
<style lang="scss" scoped></style>