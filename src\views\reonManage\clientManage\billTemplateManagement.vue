<!-- 账单模板管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="合同编号:" prop="contractCode" required>
                <el-input class="width220" v-model="obj.queryParams.contractCode" placeholder="请输入合同编号" />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName" required>
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" />
            </el-form-item>
            <el-form-item label="付款方编号:" prop="payerCode" required>
                <el-input class="width220" v-model="obj.queryParams.payerCode" placeholder="请输入付款方编号" />
            </el-form-item>
            <el-form-item label="付款方:" prop="payer" required>
                <el-input class="width220" v-model="obj.queryParams.payer" placeholder="请输入付款方" />
            </el-form-item>
            <el-form-item label="账单模板编号:" prop="templateCode" required>
                <el-input class="width220" v-model="obj.queryParams.templateCode" placeholder="请输入账单模板编号" />
            </el-form-item>
            <el-form-item label="账单模板名称:" prop="templateName" required>
                <el-input class="width220" v-model="obj.queryParams.templateName" placeholder="请输入账单模板名称" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.single" @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Setting" @click="handleConfig">配置账单模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Calendar" @click="handleFrequency">收费频率设置</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" @click="handleExport">数据导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Sort" @click="handleSort">账单模版排序</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column type="index" label="序号" width="55" align="center" />
            <el-table-column label="供应商名称" align="center" fixed prop="supplierName" />
            <el-table-column label="账单模板名称" align="center" fixed prop="templateName" />
            <el-table-column label="账单模板编号" align="center" fixed prop="templateCode" />
            <el-table-column label="账单模板类型" align="center" prop="templateType" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="约定账单生成日" align="center" prop="billGenerateDay" />
            <el-table-column label="约定账单锁定日" align="center" prop="billLockDay" />
            <el-table-column label="是否社保计入总额" align="center" prop="isSocialSecurityIncluded">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isSocialSecurityIncluded" />
                </template>
            </el-table-column>
            <el-table-column label="是否公积金计入总额" align="center" prop="isHousingFundIncluded">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isHousingFundIncluded" />
                </template>
            </el-table-column>
            <el-table-column label="是否工资计入总额" align="center" prop="isSalaryIncluded">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isSalaryIncluded" />
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="修改时间" align="center" prop="updateTime" />
            <el-table-column label="操作" align="center" width="240">
                <template #default="scope">
                    <el-button type="primary" link icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
                    <el-button type="primary" link icon="View" @click="handleView(scope.row)">查看</el-button>
                    <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="65%" append-to-body draggable destroy-on-close>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="合同名称" prop="contractName">
                    <el-input class="width420" v-model="obj.dialogForm.contractName" placeholder="请输入合同名称" />
                </el-form-item>
                <el-form-item label=" 二级合同分类" prop="contractSubType">
                    <el-select class="width220" filterable v-model="obj.dialogForm.contractSubType"
                        placeholder="请选择二级合同分类" clearable>
                        <el-option v-for="item in contractSubTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账单模板名称" prop="templateName">
                    <el-input class="width420" v-model="obj.dialogForm.templateName" placeholder="请输入模板名称" />
                </el-form-item>
                <el-form-item label="账单模板类别" prop="templateType">
                    <el-select class="width220" filterable v-model="obj.dialogForm.templateType" placeholder="请选择模板类别"
                        clearable>
                        <el-option v-for="item in templateTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="付款方" prop="payer">
                    <el-select class="width220" filterable v-model="obj.dialogForm.payer" placeholder="请选择付款方"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="约定账单生成日" prop="billGenerateDay">
                    <el-input class="width220" v-model="obj.dialogForm.billGenerateDay" placeholder="请输入生成日" />
                </el-form-item>
                <el-form-item label="约定数据提交日/增减员截止日" prop="dataSubmitDay">
                    <el-input class="width220" v-model="obj.dialogForm.dataSubmitDay" placeholder="请输入提交日" />
                </el-form-item>
                <el-form-item label="收款方" prop="payee">
                    <el-select class="width220" filterable v-model="obj.dialogForm.payee" placeholder="请选择收款方"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="约定账单锁定日" prop="billLockDay">
                    <el-input class="width220" v-model="obj.dialogForm.billLockDay" placeholder="请输入锁定日" />
                </el-form-item>
                <el-form-item label="是否社保计算总额" prop="isSocialSecurityIncluded">
                    <el-select class="width220" v-model="obj.dialogForm.isSocialSecurityIncluded" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账单方" prop="billParty">
                    <el-select class="width220" filterable v-model="obj.dialogForm.billParty" placeholder="请选择账单方"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="付款约定日(天)" prop="paymentDay">
                    <el-input class="width220" v-model="obj.dialogForm.paymentDay" placeholder="请输入付款日" />
                </el-form-item>
                <el-form-item label="是否公积金计算总额" prop="isHousingFundIncluded">
                    <el-select class="width220" v-model="obj.dialogForm.isHousingFundIncluded" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="到款所属月" prop="paymentMonth">
                    <el-select class="width220" filterable v-model="obj.dialogForm.paymentMonth" placeholder="请选择月份"
                        clearable>
                        <el-option v-for="item in monthOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="约定工资发放日" prop="salaryReleaseDay">
                    <el-input class="width220" v-model="obj.dialogForm.salaryReleaseDay" placeholder="请输入发放日" />
                </el-form-item>
                <el-form-item label="是否按服务年月分行显示" prop="isDisplayByServiceMonth">
                    <el-select class="width220" v-model="obj.dialogForm.isDisplayByServiceMonth" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否个税计算总额" prop="isTaxIncluded">
                    <el-select class="width220" v-model="obj.dialogForm.isTaxIncluded" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否工资实发计算总额" prop="isSalaryIncluded">
                    <el-select class="width220" v-model="obj.dialogForm.isSalaryIncluded" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="责任客服" prop="responsibleService">
                    <el-input class="width220" v-model="obj.dialogForm.responsibleService" placeholder="请输入客服名称" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 配置账单模版 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow1" width="65%" append-to-body draggable destroy-on-close>
            <el-transfer style="text-align: center;" v-model="value" :data="data" />
            <div style="display: flex; justify-content: space-between;" class="mt20 mb20">
                <div>
                    <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                    <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
                </div>
                <div>
                    <el-button type="primary" @click="submitForm">保存</el-button>
                    <el-button @click="obj.dialogShow1 = false">取消</el-button>
                </div>
            </div>
            <el-table :data="obj.tableData2" border @selection-change="handleSelectionChange2">
                <el-table-column type="selection" width="55" />
                <el-table-column label="序号" align="center" prop="serialNumber" />
                <el-table-column label="社保组" align="center" prop="socialSecurityGroup" />
                <el-table-column label="产品名称" align="center" prop="productName" />
                <el-table-column label="单位金额精度" align="center" prop="companyAmountPrecision" />
                <el-table-column label="单位金额进位" align="center" prop="companyAmountRounding" />
                <el-table-column label="个人金额精度" align="center" prop="personalAmountPrecision" />
                <el-table-column label="个人金额进位" align="center" prop="personalAmountRounding" />
                <el-table-column label="操作" align="center" width="120">
                    <template #default="scope">
                        <el-button type="primary" link icon="Edit" @click="handleEditConfig(scope.row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <!-- 收费频率设置 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="65%" append-to-body draggable destroy-on-close>
            <div style="display: flex; justify-content: space-between;" class="mt20 mb20">
                <div>
                    <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                    <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
                </div>
                <div>
                    <el-button type="primary" @click="submitForm">保存</el-button>
                    <el-button @click="obj.dialogShow2 = false">取消</el-button>
                </div>
            </div>
            <el-table :data="obj.tableData2" border @selection-change="handleSelectionChange2">
                <el-table-column type="selection" width="55" />
                <el-table-column label="序号" align="center" prop="serialNumber" />
                <el-table-column label="收费频率编号" align="center" prop="frequencyCode" />
                <el-table-column label="提前几月收" align="center" prop="advanceMonths" />
                <el-table-column label="收费月" align="center" prop="chargeMonth" />
                <el-table-column label="收费频率" align="center" prop="chargeFrequency" />
                <el-table-column label="频率名称" align="center" prop="frequencyName" />
                <el-table-column label="是否默认" align="center" prop="isDefault">
                    <template #default="scope">
                        <dict-tag :options="sys_yes_no" :value="scope.row.isDefault" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="120">
                    <template #default="scope">
                        <el-button type="primary" link icon="Edit"
                            @click="handleEditFrequency(scope.row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <!-- 账单模版排序 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow3" width="45%" append-to-body draggable destroy-on-close>
            <el-form-item label="合同编号">
                <el-input class="width220" v-model="obj.dialogForm.contractCode" @focus="handleCustomer"
                    placeholder="请输入合同编号" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" plain icon="ArrowUp" @click="handleUp">上移</el-button>
                <el-button type="primary" plain icon="ArrowDown" @click="handleDown">下移</el-button>
                <el-button type="success" plain icon="Top" @click="handleTop">置顶</el-button>
                <el-button type="success" plain icon="Bottom" @click="handleBottom">置底</el-button>
                <el-button type="primary" icon="Check" @click="handleSaveSort">保存</el-button>
                <el-button type="info" icon="Close" @click="obj.dialogShow3 = false">取消</el-button>
            </el-form-item>
            <el-table :data="obj.tableData3" border @selection-change="handleSortSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column label="账单模板名称" align="center" prop="templateName" />
            </el-table>
        </el-dialog>
        <Client v-model:show="clientShow" @select="handleSelect" />
    </div>
</template>

<script setup name="BillTemplateManagement">
import Client from '@/views/reonManage/components/client.vue';
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' },
    { value: '4', label: '客户D' },
    { value: '5', label: '客户E' }
];

// 合同选项
const contractOptions = [
    { value: '1', label: '合同1' },
    { value: '2', label: '合同2' },
    { value: '3', label: '合同3' }
];

// 模板选项
const templateOptions = [
    { value: '1', label: '模板1' },
    { value: '2', label: '模板2' },
    { value: '3', label: '模板3' }
];

// 月份选项
const monthOptions = [
    { value: '1', label: '1月' },
    { value: '2', label: '2月' },
    { value: '3', label: '3月' },
    { value: '4', label: '4月' },
    { value: '5', label: '5月' },
    { value: '6', label: '6月' },
    { value: '7', label: '7月' },
    { value: '8', label: '8月' },
    { value: '9', label: '9月' },
    { value: '10', label: '10月' },
    { value: '11', label: '11月' },
    { value: '12', label: '12月' }
];


const generateData = () => {
    const data = []
    for (let i = 1; i <= 15; i++) {
        data.push({
            key: i,
            label: `Option ${i}`,
            disabled: i % 4 === 0,
        })
    }
    return data
}

const data = ref(generateData())
const value = ref([])

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractCode: null,
        contractName: null,
        payerCode: null,
        payer: null,
        templateCode: null,
        templateName: null
    }, // 查询表单
    rules: {

    },
    total: 0, // 总条数
    tableData: [], // 列表
    tableData2: [], // 配置账单模版和收费频率设置列表
    tableData3: [], // 账单模版排序列表
    dialogForm: {}, // 表单
    dialogShow: false, // 新增/修改弹出框
    dialogShow1: false, // 配置账单模版弹出框
    dialogShow2: false, // 收费频率设置弹出框
    dialogShow3: false, // 账单模版排序弹出框
    ids: [], // 选中的id
    title: "", // 标题
    selectedSortIndex: -1, // 排序时选中的行索引
    selectedSortIds: [], // 排序时选中的行ID数组
})
const clientShow = ref(false)
/** 客户信息 */
function handleCustomer() {
    clientShow.value = true;
}
/** 选择 */
function handleSelect(row) {
    console.log(row);
    if (row) {
        obj.dialogForm.contractCode = row.name;
    } else {
        obj.dialogForm.contractCode = null;
    }
}
/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                supplierName: '供应商A',
                templateName: '账单模板1',
                templateCode: 'TPL20230001',
                templateType: '社保账单',
                customerName: '客户A',
                billGenerateDay: '5',
                billLockDay: '10',
                isSocialSecurityIncluded: 'Y',
                isHousingFundIncluded: 'Y',
                isSalaryIncluded: 'Y',
                createTime: '2023-05-10 10:00:00',
                createBy: '管理员',
                updateTime: '2023-05-15 14:30:00'
            },
            {
                id: 2,
                supplierName: '供应商B',
                templateName: '账单模板2',
                templateCode: 'TPL20230002',
                templateType: '公积金账单',
                customerName: '客户B',
                billGenerateDay: '10',
                billLockDay: '15',
                isSocialSecurityIncluded: 'N',
                isHousingFundIncluded: 'Y',
                isSalaryIncluded: 'N',
                createTime: '2023-06-10 10:00:00',
                createBy: '管理员',
                updateTime: '2023-06-15 14:30:00'
            }
        ];
        obj.total = 2;
        obj.loading = false;


        // 模拟配置账单模版和收费频率设置的数据
        obj.tableData2 = [
            {
                id: 1,
                serialNumber: 1,
                socialSecurityGroup: '社保组A',
                productName: '养老保险',
                companyAmountPrecision: '2',
                companyAmountRounding: '四舍五入',
                personalAmountPrecision: '2',
                personalAmountRounding: '四舍五入',
                frequencyCode: 'FREQ001',
                advanceMonths: '1',
                chargeMonth: '1,4,7,10',
                chargeFrequency: '季度',
                frequencyName: '季度收费',
                isDefault: 'Y'
            },
            {
                id: 2,
                serialNumber: 2,
                socialSecurityGroup: '社保组B',
                productName: '医疗保险',
                companyAmountPrecision: '2',
                companyAmountRounding: '四舍五入',
                personalAmountPrecision: '2',
                personalAmountRounding: '四舍五入',
                frequencyCode: 'FREQ002',
                advanceMonths: '0',
                chargeMonth: '1-12',
                chargeFrequency: '月度',
                frequencyName: '月度收费',
                isDefault: 'N'
            }
        ];
    }).catch(() => {

    });

}



// 表单重置
function reset() {
    obj.dialogForm = {
        contractName: '',
        contractSubType: '',
        templateName: '',
        templateType: '',
        payer: '',
        billGenerateDay: '',
        dataSubmitDay: '',
        payee: '',
        billLockDay: '',
        isSocialSecurityIncluded: '',
        billParty: '',
        paymentDay: '',
        isHousingFundIncluded: '',
        paymentMonth: '',
        salaryReleaseDay: '',
        isDisplayByServiceMonth: '',
        isTaxIncluded: '',
        isSalaryIncluded: '',
        responsibleService: ''
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 配置账单模版和收费频率设置多选框选中数据
function handleSelectionChange2(selection) {
    // 处理配置账单模版和收费频率设置的选中数据
    console.log('选中数据:', selection);
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增账单模板";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const id = row.id || obj.ids[0];
    if (!id) {
        proxy.$modal.msgError('请选择要修改的数据');
        return;
    }
    // 获取详情数据，实际开发时可以调用接口
    const data = obj.tableData.find(item => item.id === id) || row;
    if (data) {
        obj.dialogForm = { ...data };
        obj.dialogShow = true;
        obj.title = "修改账单模板";
    }
}

/** 查看按钮操作 */
function handleView(row) {
    reset();
    const id = row.id || obj.ids[0];
    if (!id) {
        proxy.$modal.msgError('请选择要查看的数据');
        return;
    }
    // 获取详情数据，实际开发时可以调用接口
    const data = obj.tableData.find(item => item.id === id) || row;
    if (data) {
        obj.dialogForm = { ...data };
        obj.dialogShow = true;
        obj.title = "查看账单模板";
    }
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 配置账单模版 */
function handleConfig() {
    if (obj.single) {
        proxy.$modal.msgError('请选择要配置的账单模板');
        return;
    }
    obj.dialogShow1 = true;
    obj.title = "配置账单模版";
}

/** 编辑配置 */
function handleEditConfig(row) {
    proxy.$modal.msgInfo('编辑配置：' + row.productName);
}

/** 收费频率设置 */
function handleFrequency() {
    if (obj.single) {
        proxy.$modal.msgError('请选择要设置收费频率的账单模板');
        return;
    }
    obj.dialogShow2 = true;
    obj.title = "收费频率设置";
}

/** 编辑频率 */
function handleEditFrequency(row) {
    proxy.$modal.msgInfo('编辑频率：' + row.frequencyName);
}

/** 账单模版排序 */
function handleSort() {
    obj.dialogShow3 = true;
    obj.title = "账单模版排序";
    // 初始化排序数据
    obj.tableData3 = [
        { id: 1, templateName: '账单模板1' },
        { id: 2, templateName: '账单模板2' },
        { id: 3, templateName: '账单模板3' },
        { id: 4, templateName: '账单模板4' },
        { id: 5, templateName: '账单模板5' }
    ];
    obj.selectedSortIndex = -1;
}

/**
 * 排序表格行点击事件
 */
function handleRowClick(row) {
    // 获取选中行的索引
    obj.selectedSortIndex = obj.tableData3.findIndex(item => item.id === row.id);
}

/**
 * 排序表格多选框选中数据
 */
function handleSortSelectionChange(selection) {
    obj.selectedSortIds = selection.map(item => item.id);
}

/**
 * 上移按钮操作
 */
function handleUp() {
    if (obj.selectedSortIds.length === 0) {
        proxy.$modal.msgError('请选择要移动的模板');
        return;
    }

    // 按照在表格中的顺序排序选中的ID
    const selectedIndices = obj.selectedSortIds.map(id =>
        obj.tableData3.findIndex(item => item.id === id)
    ).sort((a, b) => a - b);

    // 检查是否有选中的项是第一项
    if (selectedIndices[0] === 0) {
        proxy.$modal.msgInfo('选中项中包含第一项，无法上移');
        return;
    }

    // 逐个上移选中的项
    for (const index of selectedIndices) {
        if (index > 0) {
            // 交换当前选中项与前一项的位置
            const temp = obj.tableData3[index];
            obj.tableData3[index] = obj.tableData3[index - 1];
            obj.tableData3[index - 1] = temp;
        }
    }
}

/**
 * 下移按钮操作
 */
function handleDown() {
    if (obj.selectedSortIds.length === 0) {
        proxy.$modal.msgError('请选择要移动的模板');
        return;
    }

    // 按照在表格中的顺序逆序排序选中的ID
    const selectedIndices = obj.selectedSortIds.map(id =>
        obj.tableData3.findIndex(item => item.id === id)
    ).sort((a, b) => b - a);

    // 检查是否有选中的项是最后一项
    if (selectedIndices[0] === obj.tableData3.length - 1) {
        proxy.$modal.msgInfo('选中项中包含最后一项，无法下移');
        return;
    }

    // 逐个下移选中的项
    for (const index of selectedIndices) {
        if (index < obj.tableData3.length - 1) {
            // 交换当前选中项与后一项的位置
            const temp = obj.tableData3[index];
            obj.tableData3[index] = obj.tableData3[index + 1];
            obj.tableData3[index + 1] = temp;
        }
    }
}

/**
 * 置顶按钮操作
 */
function handleTop() {
    if (obj.selectedSortIds.length === 0) {
        proxy.$modal.msgError('请选择要移动的模板');
        return;
    }

    // 按照在表格中的顺序排序选中的ID
    const selectedIndices = obj.selectedSortIds.map(id =>
        obj.tableData3.findIndex(item => item.id === id)
    ).sort((a, b) => a - b);

    // 如果第一个已经是置顶，则不需要操作
    if (selectedIndices[0] === 0 && selectedIndices.length === 1) {
        proxy.$modal.msgInfo('已经是第一项，无需置顶');
        return;
    }

    // 取出所有选中的项
    const selectedItems = selectedIndices.map(index => obj.tableData3[index]);

    // 从数组中删除所有选中的项（从后往前删除，避免索引变化）
    for (let i = selectedIndices.length - 1; i >= 0; i--) {
        obj.tableData3.splice(selectedIndices[i], 1);
    }

    // 将选中的项添加到数组开头
    obj.tableData3.unshift(...selectedItems);
}

/**
 * 置底按钮操作
 */
function handleBottom() {
    if (obj.selectedSortIds.length === 0) {
        proxy.$modal.msgError('请选择要移动的模板');
        return;
    }

    // 按照在表格中的顺序排序选中的ID
    const selectedIndices = obj.selectedSortIds.map(id =>
        obj.tableData3.findIndex(item => item.id === id)
    ).sort((a, b) => a - b);

    // 如果最后一个已经是置底，则不需要操作
    if (selectedIndices[selectedIndices.length - 1] === obj.tableData3.length - 1 && selectedIndices.length === 1) {
        proxy.$modal.msgInfo('已经是最后一项，无需置底');
        return;
    }

    // 取出所有选中的项
    const selectedItems = selectedIndices.map(index => obj.tableData3[index]);

    // 从数组中删除所有选中的项（从后往前删除，避免索引变化）
    for (let i = selectedIndices.length - 1; i >= 0; i--) {
        obj.tableData3.splice(selectedIndices[i], 1);
    }

    // 将选中的项添加到数组末尾
    obj.tableData3.push(...selectedItems);
}

/**
 * 保存排序结果
 */
function handleSaveSort() {
    // 这里可以调用API保存排序结果
    // 模拟保存成功
    proxy.$modal.msgSuccess("账单模板排序保存成功");
    obj.dialogShow3 = false;
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 提交按钮 */
function submitForm() {
    console.log(obj.dialogForm);
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                // 调用修改接口
                proxy.$modal.msgSuccess("修改成功");
                obj.dialogShow = false;
                getList();
            } else {
                // 调用新增接口
                proxy.$modal.msgSuccess("新增成功");
                obj.dialogShow = false;
                getList();
            }
        }
    });
}

getList();
</script>
<style lang="scss" scoped></style>