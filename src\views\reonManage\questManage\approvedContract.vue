<!-- 已审批合同流程 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入"
                    @focus="handleCustomer" />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="创建时间起始:" prop="startTime">
                <el-date-picker class="width220" v-model="obj.queryParams.startTime" type="date" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="创建时间截止:" prop="endTime">
                <el-date-picker class="width220" v-model="obj.queryParams.endTime" type="date" placeholder="请选择" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @row-dblclick="handleDetail">
            <el-table-column width="60" align="center" prop="index" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="节点名称" align="center" prop="nodeName" />
            <el-table-column label="提交人" align="center" prop="submitter" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 查看详情 -->
        <el-dialog v-model="obj.dialogShow" title="审批详情" width="35%">
            <el-table :data="detailData" border>
                <el-table-column label="节点名称" align="center" prop="nodeName" />
                <el-table-column label="耗时" align="center" prop="duration" />
            </el-table>
        </el-dialog>

        <client :width="'30%'" v-model:show="clientShow" :isShow="false" @select="handleSelect" />
    </div>
</template>

<script setup name="ApprovedContract">
import { listInstance, delInstance } from '@/api/reonApi/instance';
import client from '@/views/reonManage/components/client.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客户选择显示
const clientShow = ref(false);

// 详情数据
const detailData = ref([
    {
        id: 1,
        nodeName: '部门经理审批',
        duration: '2小时'
    },
    {
        id: 2,
        nodeName: '财务审批',
        duration: '3小时'
    },
    {
        id: 3,
        nodeName: '总经理审批',
        duration: '1小时'
    }
]);

const obj = reactive({
    loading: false,// 加载状态
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        contractName: null,
        startTime: null,
        endTime: null
    },
    tableData: [],
    dialogForm: {},
    dialogShow: false,
    total: 0,

})

/** 客户信息选择 */
function handleCustomer() {
    clientShow.value = true;
}

/** 选择客户 */
function handleSelect(row) {
    if (row) {
        queryParams.customerName = row.name;
    } else {
        queryParams.customerName = null;
    }
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    listInstance(obj.queryParams).then(response => {
        // 添加序号
        const startIdx = (obj.queryParams.pageNum - 1) * obj.queryParams.pageSize + 1;
        obj.tableData = response.rows.map((item, index) => ({
            ...item,
            index: startIdx + index
        }));
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 查看详情 */
function handleDetail(row) {
    obj.dialogShow = true;
}


getList();
</script>
<style lang="scss" scoped></style>