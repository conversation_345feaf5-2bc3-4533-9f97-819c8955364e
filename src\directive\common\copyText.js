export default {
  /**
   * 在元素挂载之前执行的钩子函数
   * @param {HTMLElement} el - 指令所绑定的元素
   * @param {Object} binding - 一个对象，包含指令的相关信息
   */
  beforeMount(el, { value, arg }) {
    // 如果指令的参数是 "callback"
    if (arg === "callback") {
      // 将回调函数存储在元素的 $copyCallback 属性中
      el.$copyCallback = value;
    } else {
      // 将需要复制的值存储在元素的 $copyValue 属性中
      el.$copyValue = value;
      // 定义一个点击事件处理函数
      const handler = () => {
        // 调用 copyTextToClipboard 函数，传入需要复制的值
        copyTextToClipboard(el.$copyValue);
        // 如果存在回调函数，则调用回调函数，传入需要复制的值
        if (el.$copyCallback) {
          el.$copyCallback(el.$copyValue);
        }
      };
      // 为元素添加点击事件监听器，触发 handler 函数
      el.addEventListener("click", handler);
      // 将移除事件监听器的函数存储在元素的 $destroyCopy 属性中
      el.$destroyCopy = () => el.removeEventListener("click", handler);
    }
  },
};

/**
 * 将给定的文本复制到剪贴板
 *
 * @param {string} input - 需要复制到剪贴板的文本
 * @param {Object} options - 可选配置对象
 * @param {HTMLElement} options.target - 将创建的textarea元素附加到的目标元素，默认为document.body
 * @returns {boolean} - 表示复制操作是否成功的布尔值
 */
function copyTextToClipboard(input, { target = document.body } = {}) {
  // 创建一个隐藏的textarea元素来容纳要复制的文本
  const element = document.createElement("textarea");
  // 保存当前焦点的元素，以便在操作后恢复焦点
  const previouslyFocusedElement = document.activeElement;

  // 将输入的文本设置为textarea的值
  element.value = input;

  // 防止键盘在移动设备上显示
  element.setAttribute("readonly", "");

  // 通过CSS样式将textarea元素隐藏并定位到页面外
  element.style.contain = "strict";
  element.style.position = "absolute";
  element.style.left = "-9999px";
  // 避免在iOS上缩放
  element.style.fontSize = "12pt";

  // 获取当前选中的文本范围
  const selection = document.getSelection();
  const originalRange = selection.rangeCount > 0 && selection.getRangeAt(0);

  // 将textarea元素附加到指定的目标元素上
  target.append(element);
  // 选择textarea中的文本
  element.select();

  // 显式选择textarea中的所有文本，以确保兼容性
  element.selectionStart = 0;
  element.selectionEnd = input.length;

  // 尝试执行复制命令
  let isSuccess = false;
  try {
    isSuccess = document.execCommand("copy");
  } catch {}

  // 移除textarea元素
  element.remove();

  // 如果存在原始选中的文本范围，恢复该范围
  if (originalRange) {
    selection.removeAllRanges();
    selection.addRange(originalRange);
  }

  // 让焦点回到之前关注的元素上（如果有的话）
  if (previouslyFocusedElement) {
    previouslyFocusedElement.focus();
  }

  // 返回复制操作是否成功的布尔值
  return isSuccess;
}
