<template>
    <div>
        <el-dialog v-model="dialogShow" :title="props.title" width="65%" append-to-body draggable @close="handleClose">
            <el-form :model="formData" class="formHight" ref="formRef" inline label-width="auto">
                <border-box title="社保公积金办理信息">
                    <el-form-item label="福利办理方" prop="welfareHandler">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.welfareHandler"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="福利包名称" prop="welfarePackageName">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.welfarePackageName"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="订单状态" prop="orderStatus">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.orderStatus"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in orderStatusOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="变更状态" prop="changeStatus">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.changeStatus"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in changeStatusOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="入离职状态" prop="employmentStatus">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.employmentStatus"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in employmentStatusOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="客户编号" prop="customerNo">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.customerNo"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="客户名称" prop="customerName">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.customerName"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="接单客服" prop="serviceStaff">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.serviceStaff"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="实作状态" prop="implementationStatus">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.implementationStatus"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in implementationStatusOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="唯一号" prop="uniqueId">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.uniqueId"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="证件号码" prop="idCard">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.idCard"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="入职时间" prop="entryDate">
                        <el-date-picker :disabled="props.isDetail" class="width220" v-model="formData.entryDate"
                            type="date" placeholder="请选择日期" clearable />
                    </el-form-item>
                    <el-form-item label="办理人" prop="processor">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.processor"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="新增方式" prop="addMethod">
                        <el-select :disabled="props.isDetail" class="width220" v-model="formData.addMethod"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in addMethodOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="新办备注" prop="newProcessRemark">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.newProcessRemark"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="办停人" prop="terminateProcessor">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.terminateProcessor"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="办停时间" prop="terminateDate">
                        <el-date-picker :disabled="props.isDetail" class="width220" v-model="formData.terminateDate"
                            type="date" placeholder="请选择日期" clearable />
                    </el-form-item>
                    <el-form-item label="办停备注" prop="terminateRemark">
                        <el-input :disabled="props.isDetail" class="width220" v-model="formData.terminateRemark"
                            placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="修改备注:" prop="modifyRemark">
                        <el-input :disabled="props.isDetail" type="textarea" class="width420"
                            v-model="formData.modifyRemark" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="退回备注:" prop="returnRemark">
                        <el-input :disabled="props.isDetail" type="textarea" class="width420"
                            v-model="formData.returnRemark" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="修改过程:" prop="modifyProcess">
                        <el-input :disabled="props.isDetail" type="textarea" class="width420"
                            v-model="formData.modifyProcess" placeholder="请输入" clearable />
                    </el-form-item>
                </border-box>
                <border-box title="产品办理信息">
                    <el-table :data="productData" border>
                        <el-table-column label="社保公积金产品" prop="productName" align="center" />
                        <el-table-column label="比例编号" prop="ratioCode" align="center" />
                        <el-table-column label="比例名称" prop="ratioName" align="center" />
                        <el-table-column label="企业基数" prop="companyBase" align="center" />
                        <el-table-column label="个人基数" prop="personalBase" align="center" />
                        <el-table-column label="企业金额" prop="companyAmount" align="center" />
                        <el-table-column label="个人金额" prop="personalAmount" align="center" />
                        <el-table-column label="企业比例" prop="companyRatio" align="center" />
                        <el-table-column label="个人比例" prop="personalRatio" align="center" />
                        <el-table-column label="福利办理月" prop="welfareProcessMonth" align="center" />
                        <el-table-column label="福利起始月" prop="welfareStartMonth" align="center" />
                        <el-table-column label="福利截止月" prop="welfareEndMonth" align="center" />
                        <el-table-column label="总计" prop="total" align="center" />
                    </el-table>
                    <pagination v-show="state.total > 0" :total="state.total" v-model:page="state.queryParams.pageNum"
                        v-model:limit="state.queryParams.pageSize" @pagination="getProductList" />
                </border-box>
                <border-box title="补缴信息">
                    <el-table :data="supplementData" border>
                        <el-table-column label="社保公积金产品" prop="productName" align="center" />
                        <el-table-column label="比例编号" prop="ratioCode" align="center" />
                        <el-table-column label="比例名称" prop="ratioName" align="center" />
                        <el-table-column label="补缴起始月" prop="supplementStartMonth" align="center" />
                        <el-table-column label="补缴截止月" prop="supplementEndMonth" align="center" />
                        <el-table-column label="补缴发生月" prop="supplementOccurMonth" align="center" />
                        <el-table-column label="企业基数" prop="companyBase" align="center" />
                        <el-table-column label="个人基数" prop="personalBase" align="center" />
                        <el-table-column label="企业金额" prop="companyAmount" align="center" />
                        <el-table-column label="个人金额" prop="personalAmount" align="center" />
                        <el-table-column label="企业比例" prop="companyRatio" align="center" />
                        <el-table-column label="个人比例" prop="personalRatio" align="center" />
                        <el-table-column label="企业滞纳金" prop="companyLateFee" align="center" />
                        <el-table-column label="个人滞纳金" prop="personalLateFee" align="center" />
                    </el-table>
                </border-box>
            </el-form>
            <template #footer>
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSave" v-if="!props.isDetail">保存</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>


const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    form: {
        type: Object,
        default: () => ({})
    },
    tableData: {
        type: Array,
        default: () => []
    },
    tableData2: {
        type: Array,
        default: () => []
    },
    isDetail: {
        type: Boolean,
        default: false
    }
})
// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});
const emit = defineEmits(['close', 'update:dialogShow'])

const formRef = ref(null);

// 表单数据
const formData = reactive({
    welfareHandler: props.form?.welfareHandler || '',
    welfarePackageName: props.form?.welfarePackageName || '',
    orderStatus: props.form?.orderStatus || '',
    changeStatus: props.form?.changeStatus || '',
    employmentStatus: props.form?.employmentStatus || '',
    customerNo: props.form?.customerNo || '',
    customerName: props.form?.customerName || '',
    serviceStaff: props.form?.serviceStaff || '',
    implementationStatus: props.form?.implementationStatus || '',
    uniqueId: props.form?.uniqueId || '',
    idCard: props.form?.idCard || '',
    entryDate: props.form?.entryDate || '',
    processor: props.form?.processor || '',
    addMethod: props.form?.addMethod || '',
    newProcessRemark: props.form?.newProcessRemark || '',
    terminateProcessor: props.form?.terminateProcessor || '',
    terminateDate: props.form?.terminateDate || '',
    terminateRemark: props.form?.terminateRemark || '',
    modifyRemark: props.form?.modifyRemark || '',
    returnRemark: props.form?.returnRemark || '',
    modifyProcess: props.form?.modifyProcess || ''
});

// 下拉选项数据
const orderStatusOptions = [
    { value: '1', label: '待处理' },
    { value: '2', label: '处理中' },
    { value: '3', label: '已完成' },
    { value: '4', label: '已取消' }
];

const changeStatusOptions = [
    { value: '1', label: '未变更' },
    { value: '2', label: '变更中' },
    { value: '3', label: '已变更' }
];

const employmentStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '待入职' }
];

const implementationStatusOptions = [
    { value: '1', label: '未开始' },
    { value: '2', label: '进行中' },
    { value: '3', label: '已完成' },
    { value: '4', label: '已暂停' }
];

const addMethodOptions = [
    { value: '1', label: '正常新增' },
    { value: '2', label: '批量导入' },
    { value: '3', label: '系统自动' }
];

// 状态数据
const state = reactive({
    total: 0,
    queryParams: {
        pageNum: 1,
        pageSize: 10
    }
});

// 产品办理信息表格数据
const productData = ref(props.tableData.length > 0 ? props.tableData : [
    {
        id: 1,
        productName: '养老保险',
        ratioCode: 'YL001',
        ratioName: '养老比例标准',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 2000,
        personalAmount: 800,
        companyRatio: '20%',
        personalRatio: '8%',
        welfareProcessMonth: '2023-03',
        welfareStartMonth: '2023-04',
        welfareEndMonth: '2024-03',
        total: 2800
    },
    {
        id: 2,
        productName: '医疗保险',
        ratioCode: 'YL002',
        ratioName: '医疗比例标准',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 600,
        personalAmount: 200,
        companyRatio: '6%',
        personalRatio: '2%',
        welfareProcessMonth: '2023-03',
        welfareStartMonth: '2023-04',
        welfareEndMonth: '2024-03',
        total: 800
    },
    {
        id: 3,
        productName: '住房公积金',
        ratioCode: 'GJ001',
        ratioName: '公积金比例标准',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 1200,
        personalAmount: 1200,
        companyRatio: '12%',
        personalRatio: '12%',
        welfareProcessMonth: '2023-03',
        welfareStartMonth: '2023-04',
        welfareEndMonth: '2024-03',
        total: 2400
    }
]);

// 补缴信息表格数据
const supplementData = ref(props.tableData2.length > 0 ? props.tableData2 : [
    {
        id: 1,
        productName: '养老保险',
        ratioCode: 'YL001',
        ratioName: '养老比例标准',
        supplementStartMonth: '2023-01',
        supplementEndMonth: '2023-03',
        supplementOccurMonth: '2023-04',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 6000,
        personalAmount: 2400,
        companyRatio: '20%',
        personalRatio: '8%',
        companyLateFee: 300,
        personalLateFee: 120
    },
    {
        id: 2,
        productName: '医疗保险',
        ratioCode: 'YL002',
        ratioName: '医疗比例标准',
        supplementStartMonth: '2023-01',
        supplementEndMonth: '2023-03',
        supplementOccurMonth: '2023-04',
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 1800,
        personalAmount: 600,
        companyRatio: '6%',
        personalRatio: '2%',
        companyLateFee: 90,
        personalLateFee: 30
    }
]);

// 获取产品办理信息列表数据
const getProductList = () => {
    // 这里是模拟分页，实际应用中可能需要调用API
    state.total = productData.value.length;
};

// 关闭弹窗
const handleClose = () => {
    dialogShow.value = false;
    emit('close');
};

// 保存数据
const handleSave = () => {
    // 这里处理表单提交逻辑
    console.log('表单数据:', formData);
    console.log('产品办理信息:', productData.value);
    console.log('补缴信息:', supplementData.value);

    // 组合所有数据，可以发送给后端API
    const submitData = {
        formData,
        productData: productData.value,
        supplementData: supplementData.value
    };

    // 这里可以调用API提交数据
    // 成功后关闭弹窗
    handleClose();
};

// 组件挂载时执行
onMounted(() => {
    // 初始化页面数据
    if (props.form) {
        // 深拷贝props.form到formData
        Object.assign(formData, props.form);
    }

    // 初始化产品数据总数
    getProductList();
});
</script>

<style scoped lang="scss">
.width220 {
    width: 220px;
}

.width420 {
    width: 420px;
}

.formHight {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;
}

// 日期选择器宽度
:deep(.el-date-editor) {
    width: 220px;
}
</style>
