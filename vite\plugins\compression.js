import compression from "vite-plugin-compression";

/**
 * 创建压缩插件配置
 * @param {Object} env - 环境变量对象
 * @returns {Array} 返回压缩插件配置数组
 */
export default function createCompression(env) {
  // 从环境变量中获取 VITE_BUILD_COMPRESS 的值
  const { VITE_BUILD_COMPRESS } = env;
  // 初始化插件数组
  const plugin = [];
  // 如果 VITE_BUILD_COMPRESS 存在
  if (VITE_BUILD_COMPRESS) {
    // 将 VITE_BUILD_COMPRESS 按逗号分割成数组
    const compressList = VITE_BUILD_COMPRESS.split(",");
    // 如果数组中包含 'gzip'
    if (compressList.includes("gzip")) {
      // http://doc.ruoyi.vip/ruoyi-vue/other/faq.html#使用gzip解压缩静态文件
      // 添加 gzip 压缩插件
      plugin.push(
        compression({
          ext: ".gz", // 压缩文件的扩展名
          deleteOriginFile: false, // 是否删除原始文件
        })
      );
    }
    // 如果数组中包含 'brotli'
    if (compressList.includes("brotli")) {
      // 添加 brotli 压缩插件
      plugin.push(
        compression({
          ext: ".br", // 压缩文件的扩展名
          algorithm: "brotliCompress", // 压缩算法
          deleteOriginFile: false, // 是否删除原始文件
        })
      );
    }
  }
  // 返回插件数组
  return plugin;
}
