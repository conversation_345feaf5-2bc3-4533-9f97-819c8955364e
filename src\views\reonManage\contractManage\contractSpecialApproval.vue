<!-- 合同特殊审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="审批类型:" prop="typeCode">
                <el-select class="width220" filterable v-model="obj.queryParams.province" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="名称:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.province" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="审批状态:" prop="typeCode">
                <el-select class="width220" filterable v-model="obj.queryParams.province" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="Plus" plain @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="Edit" plain @click="handleEdit">修改</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="特殊审批名称" align="center" prop="contractCode" />
            <el-table-column label="合同编号" align="center" prop="contractName" />
            <el-table-column label="类型" align="center" prop="isArchived">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isArchived" />
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="processType" />
            <el-table-column label="提交人" align="center" prop="contractType" />
            <el-table-column label="审批状态" align="center" prop="quoteCode" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增/修改弹窗 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="60%" append-to-body>
            <el-tabs type="border-card">
                <el-tab-pane label="合同特殊审批">
                    <el-form :model="obj.dialogForm" class="formHight" ref="formRef" inline label-width="auto">
                        <el-form-item label="变更名称:" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.typeCode" placeholder="请输入" />
                        </el-form-item>
                        <el-form-item label="回款频率:" prop="typeCode">
                            <el-select class="width220" filterable v-model="obj.dialogForm.typeCode" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="开具发票方式:" prop="typeCode">
                            <el-select class="width220" filterable v-model="obj.dialogForm.typeCode" placeholder="请选择"
                                clearable>
                                <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="到款日:" prop="typeCode">
                            <el-date-picker class="width220" v-model="obj.dialogForm.typeCode" placeholder="请选择" />
                        </el-form-item>
                        <el-form-item label="合同选择:" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.typeCode" placeholder="请输入" />
                        </el-form-item>
                        <el-divider content-position="left">特殊审批项选择</el-divider>
                        <el-table :data="obj.dialogForm.tableData" border @selection-change="handleSelectionChange">
                            <el-table-column type="selection" width="55" />
                            <el-table-column label="项目" prop="projectName" />
                            <el-table-column label="应用场景" prop="applicationScenario" />
                            <el-table-column label="备注" prop="remark" />
                        </el-table>
                        <el-divider content-position="left">备注</el-divider>
                        <el-input type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入" />
                        <el-divider content-position="left">文件上传</el-divider>
                        <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="特殊审批流程图"></el-tab-pane>
            </el-tabs>
            <template #footer>
                <el-button @click="obj.dialogShow = false">取消</el-button>
                <el-button type="primary" @click="obj.dialogShow = false">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ContractSpecial_approval">

import { useAreaStore } from '@/store/modules/area'
// import { listScale } from "@/api/reonApi/scale";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {},//弹窗表单
    dialogShow: false,//弹窗
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取合同特殊审批列表
    // listScale(obj.queryParams).then(response => {
    //     obj.tableData = response.rows;
    //     obj.total = response.total;
    //     obj.loading = false;
    // });

    // 模拟数据，实际开发时可以删除
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                contractCode: 'HT20230001',
                contractName: '合同1',
                isArchived: 'Y',
                processType: '流程类型1',
                contractType: '合同类型1',
                quoteCode: 'BJ20230001',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                salesCompany: '销售公司1',
                dispatchCompany: '分公司1'
            },
            {
                id: 2,
                contractCode: 'HT20230002',
                contractName: '合同2',
                isArchived: 'N',
                processType: '流程类型2',
                contractType: '合同类型2',
                quoteCode: '**********',
                customerCode: '**********',
                customerName: '客户名称2',
                salesCompany: '销售公司2',
                dispatchCompany: '分公司2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    obj.dialogShow = true;
    obj.title = '新增';
}

/** 修改按钮操作 */
function handleEdit() {
    obj.dialogShow = true;
    obj.title = '修改';
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>
