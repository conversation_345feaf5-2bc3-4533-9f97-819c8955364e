<!-- 企业端减员受理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="100px">
            <el-form-item label="唯一号:">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" clearable />
            </el-form-item>
            <el-form-item label="订单编号:">
                <el-input class="width220" v-model="obj.queryParams.orderCode" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="客户编号:">
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入客户编号" clearable />
            </el-form-item>
            <el-form-item label="客户名称:">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="证件号码:">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="接单方:">
                <el-select class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请选择" clearable>
                    <el-option v-for="item in receiverList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方客服:">
                <el-select class="width220" v-model="obj.queryParams.receiverService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单方:">
                <el-select class="width220" v-model="obj.queryParams.orderSender" placeholder="请选择" clearable>
                    <el-option v-for="item in senderList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单方客服:">
                <el-select class="width220" v-model="obj.queryParams.senderService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="人员分布:">
                <el-select class="width220" v-model="obj.queryParams.personnelDistribution" placeholder="请选择" clearable>
                    <el-option v-for="item in distributionList" :key="item.code" :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="小合同名称:">
                <el-input class="width220" v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" plain icon="View" @click="handleOrderDetail">订单详情</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Document" @click="handleDeclareLeave">申报离职</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Setting" @click="handleSimilarSetting">类似设定</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" prop="id" />
            <el-table-column label="雇员编号" align="center" prop="employeeCode" />
            <el-table-column label="订单编号" align="center" prop="orderCode" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="人员分布" align="center" prop="personnelDistribution" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="入职日期" align="center" prop="entryDate" />
            <el-table-column label="申请入职时间" align="center" prop="applyEntryDate" />
            <el-table-column label="入离职状态" align="center" prop="employmentStatus" />
            <el-table-column label="订单状态" align="center" prop="orderStatus" />
            <el-table-column label="变更状态" align="center" prop="changeStatus" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm"
            type="attritionAcceptance_firm" />
    </div>
</template>

<script setup name="EnterpriseDownsizingAcceptance">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 客服列表
const serviceList = ref([]);
// 派单方列表
const senderList = ref([]);
// 接单方列表
const receiverList = ref([]);
// 人员分布列表
const distributionList = ref([]);

// 表单验证规则
const rules = {
    // 可以根据需要添加验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null,
        orderCode: null,
        employeeName: null,
        customerCode: null,
        customerName: null,
        idNumber: null,
        orderReceiver: null,
        receiverService: null,
        orderSender: null,
        senderService: null,
        personnelDistribution: null,
        smallContractName: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {},//导入表单
    dialogShow: false,//导入弹窗
    ids: [],//选中的id
    title: "",//标题
})

/** 获取客服列表 */
function getServiceList() {
    // 这里可以调用API获取客服列表
    serviceList.value = [
        { code: '1', name: '客服1' },
        { code: '2', name: '客服2' },
        { code: '3', name: '客服3' }
    ];
}

/** 获取派单方列表 */
function getSenderList() {
    // 这里可以调用API获取派单方列表
    senderList.value = [
        { code: '1', name: '派单方1' },
        { code: '2', name: '派单方2' },
        { code: '3', name: '派单方3' }
    ];
}

/** 获取接单方列表 */
function getReceiverList() {
    // 这里可以调用API获取接单方列表
    receiverList.value = [
        { code: '1', name: '接单方1' },
        { code: '2', name: '接单方2' },
        { code: '3', name: '接单方3' }
    ];
}

/** 获取人员分布列表 */
function getDistributionList() {
    // 这里可以调用API获取人员分布列表
    distributionList.value = [
        { code: '1', name: '分布1' },
        { code: '2', name: '分布2' },
        { code: '3', name: '分布3' }
    ];
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeCode: 'YG20230001',
                orderCode: 'DD20230001',
                employeeName: '雇员姓名1',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                personnelDistribution: '分布1',
                smallContractName: '小合同名称1',
                entryDate: '2023-01-01',
                applyEntryDate: '2022-12-15',
                employmentStatus: '在职',
                orderStatus: '正常',
                changeStatus: '未变更'
            },
            {
                id: 2,
                employeeCode: 'YG20230002',
                orderCode: 'DD20230002',
                employeeName: '雇员姓名2',
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                personnelDistribution: '分布2',
                smallContractName: '小合同名称2',
                entryDate: '2023-02-01',
                applyEntryDate: '2023-01-15',
                employmentStatus: '在职',
                orderStatus: '正常',
                changeStatus: '未变更'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 订单详情 */
function handleOrderDetail() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要查看的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

/** 申报离职 */
function handleDeclareLeave() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要申报离职的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

/** 类似设定 */
function handleSimilarSetting() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要设定的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

/** 导出 */
function handleExport() {
    proxy.download('system/scale/export', {
        ...obj.queryParams
    }, `scale_${new Date().getTime()}.xlsx`)
}

/** 关闭导入弹窗 */
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {};
}

// 初始化数据
getServiceList();
getSenderList();
getReceiverList();
getDistributionList();
getList();
</script>
<style lang="scss" scoped></style>