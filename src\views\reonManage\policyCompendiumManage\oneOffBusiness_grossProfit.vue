<!-- 一次性业务毛利测算 -->
<template>
    <div class="app-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="header-content">
                <h1 class="header-title">一次性毛利计算器</h1>
                <p class="header-subtitle">快速计算项目毛利和净利润</p>
            </div>
        </div>

        <!-- 计算表单 -->
        <el-card class="calculator-card" shadow="hover">
            <template #header>
                <div class="card-header">
                    <el-icon class="card-icon">
                        <Money />
                    </el-icon>
                    <span class="card-title">参数设置</span>
                </div>
            </template>

            <el-form :model="date" ref="queryRef" label-width="120px" class="calculator-form">
                <el-row :gutter="24">
                    <el-col :span="12">
                        <el-form-item label="项目收入" prop="project_income">
                            <el-input v-model="date.project_income" placeholder="请输入含税收入金额" clearable size="large"
                                class="input-with-icon">
                                <template #suffix>
                                    <span class="input-suffix">元</span>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="项目成本" prop="project_costing">
                            <el-input v-model="date.project_costing" placeholder="请输入含税成本金额" clearable size="large"
                                class="input-with-icon">
                                <template #suffix>
                                    <span class="input-suffix">元</span>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="24">
                    <el-col :span="8">
                        <el-form-item label="进项抵扣" prop="deduction">
                            <el-radio-group v-model="date.deduction" class="radio-group">
                                <el-radio value="1" class="radio-item">
                                    <el-icon>
                                        <Check />
                                    </el-icon>
                                    <span>是</span>
                                </el-radio>
                                <el-radio value="2" class="radio-item">
                                    <el-icon>
                                        <Close />
                                    </el-icon>
                                    <span>否</span>
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="税率" prop="tax_rate">
                            <el-select v-model="date.tax_rate" placeholder="请选择税率" size="large" class="select-full">
                                <el-option value="1" label="6%">
                                    <span class="option-content">
                                        <span>6%</span>
                                        <span class="option-desc">福利、体检、一次性、商保</span>
                                    </span>
                                </el-option>
                                <el-option value="2" label="13%">
                                    <span class="option-content">
                                        <span>13%</span>
                                        <span class="option-desc">福利实物类</span>
                                    </span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="附加税率" prop="additional_tax_rate">
                            <el-select v-model="date.additional_tax_rate" placeholder="请选择附加税率" size="large"
                                class="select-full">
                                <el-option value="1" label="6%">
                                    <span class="option-content">
                                        <span>6%</span>
                                        <span class="option-desc">南京致仟</span>
                                    </span>
                                </el-option>
                                <el-option value="2" label="10%">
                                    <span class="option-content">
                                        <span>10%</span>
                                        <span class="option-desc">上海定山</span>
                                    </span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item class="button-group">
                    <el-button type="primary" size="large" icon="EditPen" @click="handleCalculate" class="calc-button">
                        开始计算
                    </el-button>
                    <el-button size="large" icon="Refresh" @click="handleReset" class="reset-button">
                        重置数据
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <!-- 税率说明 -->
        <el-card class="info-card" shadow="hover">
            <template #header>
                <div class="card-header">
                    <el-icon class="card-icon">
                        <InfoFilled />
                    </el-icon>
                    <span class="card-title">税率说明</span>
                </div>
            </template>

            <div class="info-content">
                <div class="info-row">
                    <div class="info-item">
                        <el-tag type="primary" size="large" class="info-tag">税率说明</el-tag>
                        <div class="info-details">
                            <span class="info-label">福利、体检、一次性、商保：</span>
                            <el-tag type="success" class="rate-tag">6%</el-tag>
                            <span class="info-separator">|</span>
                            <span class="info-label">福利实物类：</span>
                            <el-tag type="warning" class="rate-tag">13%</el-tag>
                        </div>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <el-tag type="info" size="large" class="info-tag">附加税率说明</el-tag>
                        <div class="info-details">
                            <span class="info-label">南京致仟：</span>
                            <el-tag type="success" class="rate-tag">6%</el-tag>
                            <span class="info-separator">|</span>
                            <span class="info-label">上海定山：</span>
                            <el-tag type="warning" class="rate-tag">10%</el-tag>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 计算结果 -->
        <el-card class="result-card" shadow="hover" v-if="result.profit || result.tax || result.income">
            <template #header>
                <div class="card-header">
                    <el-icon class="card-icon">
                        <TrendCharts />
                    </el-icon>
                    <span class="card-title">计算结果</span>
                </div>
            </template>

            <div class="result-content">
                <div class="result-grid">
                    <div class="result-item profit-item">
                        <div class="result-icon">
                            <el-icon>
                                <TrendCharts />
                            </el-icon>
                        </div>
                        <div class="result-info">
                            <div class="result-label">毛利润</div>
                            <div class="result-desc">收入(不含税) - 成本(不含税)</div>
                            <div class="result-value profit-value">{{ result.profit || '0.00' }} 元</div>
                        </div>
                    </div>

                    <div class="result-item tax-item">
                        <div class="result-icon">
                            <el-icon>
                                <Document />
                            </el-icon>
                        </div>
                        <div class="result-info">
                            <div class="result-label">税金及附加</div>
                            <div class="result-desc">附加税金额</div>
                            <div class="result-value tax-value">{{ result.tax || '0.00' }} 元</div>
                        </div>
                    </div>

                    <div class="result-item income-item">
                        <div class="result-icon">
                            <el-icon>
                                <Trophy />
                            </el-icon>
                        </div>
                        <div class="result-info">
                            <div class="result-label">净利润</div>
                            <div class="result-desc">最终收益金额</div>
                            <div class="result-value income-value">{{ result.income || '0.00' }} 元</div>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script setup name="OneOffBusiness_grossProfit">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const date = reactive({
    project_income: '',//项目收入
    project_costing: '',//项目成本
    deduction: '1',//进项是否抵扣
    tax_rate: '1',//税率
    additional_tax_rate: '1',//附加税率
})

const result = reactive({
    profit: '',//毛利
    tax: '',//税金及附加
    income: '',//该业务净赚
})

/** 计算按钮操作 */
function handleCalculate() {
    // 获取税率和附加税率
    const tax_rate = date.tax_rate === '1' ? 0.06 : 0.13
    const additional_tax_rate = date.additional_tax_rate === '1' ? 0.06 : 0.10

    // 计算不含税收入
    const project_income = parseFloat(date.project_income) / (1 + tax_rate)

    // 根据是否抵扣计算不含税成本
    const project_costing = date.deduction === '1'
        ? parseFloat(date.project_costing) / (1 + tax_rate)
        : parseFloat(date.project_costing)

    // 计算毛利
    result.profit = (project_income - project_costing).toFixed(2)

    // 计算收入和成本的税额
    const income_tax = project_income * tax_rate
    const cost_tax = date.deduction === '1' ? project_costing * tax_rate : 0

    // 计算附加税
    result.tax = ((income_tax - cost_tax) * additional_tax_rate).toFixed(2)

    // 计算净利润
    result.income = (parseFloat(result.profit) - parseFloat(result.tax)).toFixed(2)
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    result.profit = ''
    result.tax = ''
    result.income = ''
}

</script>
<style lang="scss" scoped>
.app-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 20px;
}

// 页面标题样式
.page-header {
    text-align: center;
    margin-bottom: 30px;

    .header-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px 0px;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        .header-title {
            font-size: 36px;
            font-weight: 700;
            margin: 0 0 10px 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }
    }
}

// 卡片通用样式
.calculator-card,
.info-card,
.result-card {
    margin-bottom: 25px;
    border-radius: 15px;
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }

    :deep(.el-card__header) {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border-bottom: none;
        padding: 20px 25px;

        .card-header {
            display: flex;
            align-items: center;
            color: white;

            .card-icon {
                font-size: 24px;
                margin-right: 12px;
            }

            .card-title {
                font-size: 18px;
                font-weight: 600;
            }
        }
    }

    :deep(.el-card__body) {
        padding: 30px;
        background: white;
    }
}

// 表单样式
.calculator-form {
    .el-form-item {
        margin-bottom: 25px;

        :deep(.el-form-item__label) {
            font-weight: 600;
            color: #2c3e50;
            font-size: 15px;
        }
    }

    .input-with-icon {

        .input-suffix {
            color: #909399;
            font-weight: 500;
        }
    }

    .radio-group {
        .radio-item {
            :deep(.el-radio__label) {
                display: flex;
                align-items: center;
                gap: 5px;
                font-weight: 500;
            }

            :deep(.el-radio__input.is-checked .el-radio__inner) {
                background-color: #4facfe;
                border-color: #4facfe;
            }
        }
    }

    .select-full {
        width: 100%;

        :deep(.el-select__wrapper) {
            border-radius: 10px;
            border: 2px solid #e1e8ed;
            transition: all 0.3s ease;

            &:hover,
            &.is-focused {
                border-color: #4facfe;
                box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            }
        }
    }

    .option-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .option-desc {
            font-size: 12px;
            color: #909399;
        }
    }

    .button-group {
        text-align: center;
        margin-top: 30px;

        .calc-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            margin-right: 15px;
            transition: all 0.3s ease;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            }
        }

        .reset-button {
            border: 2px solid #ddd;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;

            &:hover {
                border-color: #4facfe;
                color: #4facfe;
                transform: translateY(-2px);
            }
        }
    }
}

// 信息卡片样式
.info-card {
    :deep(.el-card__header) {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);

        .card-header {
            color: #8b4513;
        }
    }

    .info-content {
        .info-row {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .info-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;

            .info-tag {
                border-radius: 20px;
                font-weight: 600;
            }

            .info-details {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;

                .info-label {
                    color: #2c3e50;
                    font-weight: 500;
                }

                .rate-tag {
                    border-radius: 15px;
                    font-weight: 600;
                    font-size: 14px;
                }

                .info-separator {
                    color: #ddd;
                    font-weight: bold;
                }
            }
        }
    }
}

// 结果卡片样式
.result-card {
    :deep(.el-card__header) {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

        .card-header {
            color: #2c3e50;
        }
    }

    .result-content {
        .result-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .result-item {
            display: flex;
            align-items: center;
            padding: 25px;
            border-radius: 15px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                opacity: 0.1;
                border-radius: 15px;
            }

            &:hover {
                transform: translateY(-3px);
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }

            .result-icon {
                font-size: 40px;
                margin-right: 20px;
                padding: 15px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .result-info {
                flex: 1;

                .result-label {
                    font-size: 16px;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 5px;
                }

                .result-desc {
                    font-size: 12px;
                    color: #7f8c8d;
                    margin-bottom: 10px;
                }

                .result-value {
                    font-size: 24px;
                    font-weight: 700;
                }
            }

            &.profit-item {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;

                &::before {
                    background: white;
                }

                .result-icon {
                    background: rgba(255, 255, 255, 0.2);
                    color: white;
                }

                .result-info {

                    .result-label,
                    .result-desc {
                        color: white;
                    }

                    .result-desc {
                        opacity: 0.8;
                    }

                    .profit-value {
                        color: #ffd700;
                    }
                }
            }

            &.tax-item {
                background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
                color: #8b4513;

                &::before {
                    background: #8b4513;
                }

                .result-icon {
                    background: rgba(139, 69, 19, 0.1);
                    color: #8b4513;
                }

                .tax-value {
                    color: #d2691e;
                }
            }

            &.income-item {
                background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
                color: #2c3e50;

                &::before {
                    background: #2c3e50;
                }

                .result-icon {
                    background: rgba(44, 62, 80, 0.1);
                    color: #2c3e50;
                }

                .income-value {
                    color: #27ae60;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
                }
            }
        }
    }
}

// 动画效果
@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    60% {
        transform: translateY(-5px);
    }
}

@keyframes shimmer {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .app-container {
        padding: 10px;
    }

    .page-header {
        .header-content {
            padding: 30px 15px;

            .header-title {
                font-size: 28px;
            }

            .header-icon {
                font-size: 36px;
            }
        }
    }

    .calculator-form {
        .el-row {
            .el-col {
                margin-bottom: 15px;
            }
        }

        .button-group {

            .calc-button,
            .reset-button {
                width: 100%;
                margin: 5px 0;
            }
        }
    }

    .result-content {
        .result-grid {
            grid-template-columns: 1fr;
        }

        .result-item {
            padding: 20px;

            .result-icon {
                font-size: 30px;
                margin-right: 15px;
                padding: 10px;
            }

            .result-info {
                .result-value {
                    font-size: 20px;
                }
            }
        }
    }

    .info-content {
        .info-item {
            flex-direction: column;
            align-items: flex-start;

            .info-details {
                margin-top: 10px;
            }
        }
    }
}
</style>