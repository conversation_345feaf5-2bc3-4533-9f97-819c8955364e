import request from '@/utils/request'

// 查询福利包主列表
export function listPackage(query) {
  return request({
    url: '/system/package/list',
    method: 'get',
    params: query
  })
}

// 查询福利包主详细
export function getPackage(id) {
  return request({
    url: '/system/package/' + id,
    method: 'get'
  })
}

// 新增福利包主
export function addPackage(data) {
  return request({
    url: '/system/package',
    method: 'post',
    data: data
  })
}

// 修改福利包主
export function updatePackage(data) {
  return request({
    url: '/system/package',
    method: 'put',
    data: data
  })
}

// 删除福利包主
export function delPackage(id) {
  return request({
    url: '/system/package/' + id,
    method: 'delete'
  })
}
