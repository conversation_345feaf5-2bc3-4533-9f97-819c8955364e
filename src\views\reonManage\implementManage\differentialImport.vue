<!-- 差异导入页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-row class="mb8">
                <el-form-item label="导入日期从:" prop="importDateStart">
                    <el-date-picker class="width220" v-model="obj.queryParams.importDateStart" type="date"
                        placeholder="请选择" clearable />
                </el-form-item>
                <el-form-item label="导入日期到:" prop="importDateEnd">
                    <el-date-picker class="width220" v-model="obj.queryParams.importDateEnd" type="date"
                        placeholder="请选择" clearable />
                </el-form-item>
                <el-form-item label="导入编号:" prop="importNo">
                    <el-input class="width220" v-model="obj.queryParams.importNo" placeholder="请输入导入编号" clearable />
                </el-form-item>
            </el-row>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleDownload">下载模版</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="导入编号" align="center" prop="importNo" />
            <el-table-column label="导入人" align="center" prop="creator" />
            <el-table-column label="导入时间" align="center" prop="createTime" />
            <el-table-column label="成功记录数" align="center" prop="successCount" />
            <el-table-column label="失败记录数" align="center" prop="failCount" />
            <el-table-column label="导入文件" align="center" prop="importFile" />
            <el-table-column label="处理状态" align="center" prop="processStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.processStatus === '1' ? 'success' : (scope.row.processStatus === '2' ? 'danger' : 'warning')">
                        {{ scope.row.processStatus === '1' ? '处理完成' : (scope.row.processStatus === '2' ? '处理失败' : '处理中')
                        }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="历史信息查询" align="center">
                <template #default="scope">
                    <el-button type="primary" text icon="View" @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" type="differentialImport" title="上传导入数据"
            :dialogForm="obj.dialogForm" />
        <!-- 历史信息查看 -->
        <el-dialog v-model="obj.dialogShowHistory" title="历史信息查看" width="40%" append-to-body>
            <el-button class="mb20" type="primary" @click="handleExport">导出数据</el-button>
            <el-table border :data="obj.tableDataHistory">
                <el-table-column label="导入编号" align="center" prop="importNo" />
                <el-table-column label="行号" align="center" prop="rowNo" />
                <el-table-column label="错误描述" align="center" prop="errorDescription" />
                <el-table-column label="提醒描述" align="center" prop="reminderDescription" />
                <el-table-column label="导入结果" align="center" prop="importResult" />
                <el-table-column label="导入信息" align="center" prop="importInfo" />
                <el-table-column label="创建人" align="center" prop="creator" />
                <el-table-column label="创建时间" align="center" prop="createTime" />
            </el-table>
        </el-dialog>
    </div>
</template>


<script setup name="DifferentialImport">
import { listScale } from "@/api/reonApi/scale";


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 处理状态选项
const processStatusOptions = [
    { value: '0', label: '处理中' },
    { value: '1', label: '处理完成' },
    { value: '2', label: '处理失败' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        importNo: 'IMP20230001',
        creator: '张三',
        createTime: '2023-01-15 10:30:00',
        successCount: 120,
        failCount: 0,
        importFile: '差异导入_202301.xlsx',
        processStatus: '1'
    },
    {
        id: 2,
        importNo: 'IMP20230002',
        creator: '李四',
        createTime: '2023-02-15 09:15:00',
        successCount: 85,
        failCount: 3,
        importFile: '差异导入_202302.xlsx',
        processStatus: '1'
    },
    {
        id: 3,
        importNo: 'IMP20230003',
        creator: '王五',
        createTime: '2023-03-15 11:45:00',
        successCount: 0,
        failCount: 0,
        importFile: '差异导入_202303.xlsx',
        processStatus: '0'
    }
];

// 表单验证规则
const rules = {
    remark: [
        { required: true, message: '请输入备注', trigger: 'blur' }
    ],
    file: [
        { required: true, message: '请选择要上传的文件', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateStart: null, // 导入日期从
        importDateEnd: null, // 导入日期到
        importNo: null, // 导入编号
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        remark: '', // 备注
        file: null // 文件
    }, // 导入表单
    dialogShow: false, // 导入弹窗
    ids: [], // 选中的id
    title: "", // 标题
})


/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.importDateStart) {
            const startDate = new Date(obj.queryParams.importDateStart).getTime();
            filteredData = filteredData.filter(item => new Date(item.createTime).getTime() >= startDate);
        }

        if (obj.queryParams.importDateEnd) {
            const endDate = new Date(obj.queryParams.importDateEnd).getTime();
            filteredData = filteredData.filter(item => new Date(item.createTime).getTime() <= endDate);
        }

        if (obj.queryParams.importNo) {
            filteredData = filteredData.filter(item => item.importNo.includes(obj.queryParams.importNo));
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 导入
function handleImport() {
    obj.dialogShow = true;
    obj.title = "上传导入数据";
    resetForm();
}

// 关闭导入
function handleClose() {
    obj.dialogShow = false;
    resetForm();
}


// 下载模版
function handleDownload() {
    // 实际项目中应该调用API进行下载模板
    proxy.$modal.msgSuccess('模板下载成功');

    // 模拟下载文件
    const fileName = '差异导入模板.xlsx';
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 订单详情
function handleDetail(row) {
    obj.dialogShowHistory = true;
}

// 重置表单
function resetForm() {
    obj.dialogForm = {
        remark: '', // 备注
        file: null // 文件
    };
    proxy.resetForm("dialogRef");
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.width420 {
    width: 420px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>