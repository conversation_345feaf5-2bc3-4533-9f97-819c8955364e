Math.easeInOutQuad = function (t, b, c, d) {
  t /= d / 2;
  if (t < 1) {
    return (c / 2) * t * t + b;
  }
  t--;
  return (-c / 2) * (t * (t - 2) - 1) + b;
};

// requestAnimationFrame for Smart Animating http://goo.gl/sx5sts
var requestAnimFrame = (function () {
  return (
    window.requestAnimationFrame ||
    window.webkitRequestAnimationFrame ||
    window.mozRequestAnimationFrame ||
    function (callback) {
      window.setTimeout(callback, 1000 / 60);
    }
  );
})();

/**
 * 将页面滚动到指定位置
 * @param {number} amount - 需要滚动到的位置
 */
function move(amount) {
  // 设置document.documentElement的scrollTop属性，使页面滚动到指定位置
  document.documentElement.scrollTop = amount;
  // 设置document.body.parentNode的scrollTop属性，使页面滚动到指定位置
  document.body.parentNode.scrollTop = amount;
  // 设置document.body的scrollTop属性，使页面滚动到指定位置
  document.body.scrollTop = amount;
}

/**
 * 获取当前页面的滚动位置
 * @returns {number} 当前页面的滚动位置
 */
function position() {
  // 返回document.documentElement的scrollTop属性，或者document.body.parentNode的scrollTop属性，或者document.body的scrollTop属性
  return (
    document.documentElement.scrollTop ||
    document.body.parentNode.scrollTop ||
    document.body.scrollTop
  );
}

/**
 * 将页面平滑滚动到指定位置
 * @param {number} to - 目标滚动位置
 * @param {number} duration - 滚动持续时间（毫秒），默认为500毫秒
 * @param {Function} callback - 滚动完成后的回调函数
 */
export function scrollTo(to, duration, callback) {
  // 获取当前滚动位置
  const start = position();
  // 计算滚动的距离
  const change = to - start;
  // 每次滚动的增量
  const increment = 20;
  // 当前时间
  let currentTime = 0;
  // 如果未指定持续时间，则默认为500毫秒
  duration = typeof duration === "undefined" ? 500 : duration;
  // 定义动画函数
  var animateScroll = function () {
    // 增加当前时间
    currentTime += increment;
    // 使用二次缓动函数计算当前滚动位置
    var val = Math.easeInOutQuad(currentTime, start, change, duration);
    // 移动页面到当前滚动位置
    move(val);
    // 如果当前时间小于持续时间，则继续请求动画帧
    if (currentTime < duration) {
      requestAnimFrame(animateScroll);
    } else {
      // 如果指定了回调函数且为函数类型，则在滚动完成后执行回调
      if (callback && typeof callback === "function") {
        callback();
      }
    }
  };
  // 开始动画
  animateScroll();
}
