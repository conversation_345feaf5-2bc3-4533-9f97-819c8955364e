<!-- 派遣员工查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="员工姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入员工姓名" />
            </el-form-item>
            <el-form-item label="证件编号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件编号" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="员工姓名" align="center" prop="employeeName" />
            <el-table-column label="证件类型" align="center" prop="idType" />
            <el-table-column label="证件编号" align="center" prop="idNumber" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="劳动报酬" align="center" prop="laborRemuneration" />
            <el-table-column label="社保基数" align="center" prop="socialInsuranceBase" />
            <el-table-column label="公积金基数" align="center" prop="housingFundBase" />
            <el-table-column label="入职时间" align="center" prop="entryDate" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        employeeName: null,
        idNumber: null,
        customerName: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeName: '员工姓名1',
                idType: '身份证',
                idNumber: '110101199001011234',
                customerName: '客户名称1',
                contractType: '合同类型1',
                laborRemuneration: 5000,
                socialInsuranceBase: 5000,
                housingFundBase: 5000,
                entryDate: '2023-01-01'
            },
            {
                id: 2,
                employeeName: '员工姓名2',
                idType: '身份证',
                idNumber: '110101199001011235',
                customerName: '客户名称2',
                contractType: '合同类型2',
                laborRemuneration: 6000,
                socialInsuranceBase: 6000,
                housingFundBase: 6000,
                entryDate: '2023-02-01'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>