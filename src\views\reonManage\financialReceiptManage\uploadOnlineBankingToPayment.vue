<!-- 财务-上传网银到款 -->
<template>
    <div class="app-container">
        <el-tabs type="border-card" style="height: calc(100vh - 130px);">
            <el-tab-pane label="查看网银记录">
                <!-- 查询条件 -->
                <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
                    <el-form-item label="分公司:" prop="province">
                        <el-input class="width220" v-model="obj.queryParams.province" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="到款客户:" prop="customerName">
                        <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="到款时间>=:" prop="paymentDateStart">
                        <el-date-picker class="width220" v-model="obj.queryParams.paymentDateStart" type="date"
                            placeholder="请选择日期" clearable />
                    </el-form-item>
                    <el-form-item label="到款时间<=:" prop="paymentDateEnd">
                        <el-date-picker class="width220" v-model="obj.queryParams.paymentDateEnd" type="date"
                            placeholder="请选择日期" clearable />
                    </el-form-item>
                    <el-form-item label="到款金额>=:" prop="paymentAmountStart">
                        <el-input class="width220" v-model="obj.queryParams.paymentAmountStart" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-form-item label="到款金额<=:" prop="paymentAmountEnd">
                        <el-input class="width220" v-model="obj.queryParams.paymentAmountEnd" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-form-item label="余额>=:" prop="balanceStart">
                        <el-input class="width220" v-model="obj.queryParams.balanceStart" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="余额<=:" prop="balanceEnd">
                        <el-input class="width220" v-model="obj.queryParams.balanceEnd" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="未核销金额>:" prop="unwrittenOffAmountStart">
                        <el-input class="width220" v-model="obj.queryParams.unwrittenOffAmountStart" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-form-item label="未核销金额<:" prop="unwrittenOffAmountEnd">
                        <el-input class="width220" v-model="obj.queryParams.unwrittenOffAmountEnd" placeholder="请输入"
                            clearable />
                    </el-form-item>
                    <el-form-item label="上传时间>=:" prop="uploadDateStart">
                        <el-date-picker class="width220" v-model="obj.queryParams.uploadDateStart" type="date"
                            placeholder="请选择日期" clearable />
                    </el-form-item>
                    <el-form-item label="上传时间<=:" prop="uploadDateEnd">
                        <el-date-picker class="width220" v-model="obj.queryParams.uploadDateEnd" type="date"
                            placeholder="请选择日期" clearable />
                    </el-form-item>
                    <el-form-item label="是否有效:" prop="isEffective">
                        <el-input class="width220" v-model="obj.queryParams.isEffective" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="冻结状态:" prop="freezeStatus">
                        <el-date-picker class="width220" v-model="obj.queryParams.sales" type="date" placeholder="请选择日期"
                            clearable />
                    </el-form-item>
                    <el-form-item label="到款公司是否为自有公司:" prop="isSelfCompany">
                        <el-date-picker class="width220" v-model="obj.queryParams.isSelfCompany" type="date"
                            placeholder="请选择日期" clearable />
                    </el-form-item>
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Check" @click="handleVerify">核销</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="EditPen" @click="handleSaveRemark">保存备注</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="Download" @click="handleExport">导出</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="warning" plain icon="Lock" @click="handleFreeze">冻结</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="info" plain icon="Unlock" @click="handleUnfreeze">解冻</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" plain icon="Delete" @click="handleInvalid">作废</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="CircleCheck" @click="handleValid">恢复有效</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="info" plain icon="List" @click="handleQueryOperationHistory">查询操作历史</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Document" @click="handleBatchVerify">批量核销</el-button>
                    </el-col>
                    <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
                <!-- 表格 -->
                <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="到款编号" align="center" prop="paymentId" />
                    <el-table-column label="到款客户" align="center" prop="customerName" />
                    <el-table-column label="到款时间" align="center" prop="paymentDate" />
                    <el-table-column label="到款金额" align="center" prop="paymentAmount" />
                    <el-table-column label="余额" align="center" prop="balance" />
                    <el-table-column label="已核销金额" align="center" prop="unwrittenOffAmount" />
                    <el-table-column label="核销人" align="center" prop="verifyPerson" />
                    <el-table-column label="分公司" align="center" prop="province" />
                    <el-table-column label="到款账号" align="center" prop="account" />
                    <el-table-column label="到款银行" align="center" prop="bank" />
                    <el-table-column label="交易时间" align="center" prop="transactionTime" />
                    <el-table-column label="上传时间" align="center" prop="uploadDate" />
                    <el-table-column label="到款备注" align="center" prop="paymentRemark" />
                    <el-table-column label="财务备注" align="center" prop="financialRemark" />
                    <el-table-column label="客服备注" align="center" prop="customerRemark" />
                    <el-table-column label="上传人" align="center" prop="uploadPerson" />
                    <el-table-column label="状态" align="center" prop="status" />
                    <el-table-column label="冻结状态" align="center" prop="freezeStatus" />
                </el-table>
                <!-- 分页 -->
                <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                    v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
            </el-tab-pane>
            <el-tab-pane label="上传网银记录">
                <!-- 查询条件 -->
                <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
                    <el-form-item label="分公司:" prop="province">
                        <el-select class="width220" v-model="obj.queryParams.province" placeholder="请选择" clearable>
                            <el-option v-for="item in provinces" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="上传人:" prop="uploadPerson">
                        <el-input class="width220" v-model="obj.queryParams.uploadPerson" placeholder="请输入" clearable />
                    </el-form-item>
                    <el-form-item label="上传开始时间:" prop="uploadDateStart">
                        <el-date-picker class="width220" v-model="obj.queryParams.uploadDateStart" type="date"
                            placeholder="请选择日期" clearable />
                    </el-form-item>
                    <el-form-item label="上传结束时间:" prop="uploadDateEnd">
                        <el-date-picker class="width220" v-model="obj.queryParams.uploadDateEnd" type="date"
                            placeholder="请选择日期" clearable />
                    </el-form-item>
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Download" @click="handleDownloadTemplate">模版下载</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="Upload" @click="handleUpload">上传</el-button>
                    </el-col>
                    <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
                <!-- 表格 -->
                <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="客户名称" align="center" prop="customerName" />
                    <el-table-column label="客户帐套" align="center" prop="customerAccount" />
                    <el-table-column label="账单类型" align="center" prop="billType" />
                    <el-table-column label="账单年月" align="center" prop="billMonth" />
                    <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
                    <el-table-column label="账单人数" align="center" prop="billPersonCount" />
                    <el-table-column label="应收金额" align="center" prop="receivableAmount" />
                    <el-table-column label="账单状态" align="center" prop="billStatus" />
                    <el-table-column label="首次生成时间" align="center" prop="firstGenerateTime" />
                    <el-table-column label="核销状态" align="center" prop="verifyStatus" />
                    <el-table-column label="开票状态" align="center" prop="invoiceStatus" />
                </el-table>
                <!-- 分页 -->
                <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                    v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
            </el-tab-pane>
        </el-tabs>
        <!-- 核销 -->
        <el-dialog v-model="obj.dialogShow" title="核销" width="60%" append-to-body draggable>
            <el-form :model="obj.form" ref="formRef" class="formHight" inline label-width="auto">
                <el-form-item label="客户:" prop="customerName">
                    <el-input class="width220" v-model="obj.form.customerName" />
                </el-form-item>
                <el-form-item label="收款方:" prop="customerAccount">
                    <el-select class="width220" v-model="obj.form.customerAccount" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户账套:" prop="customerAccount">
                    <el-select class="width220" v-model="obj.form.customerAccount" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="财务应收年月:" prop="financialReceivableMonth">
                    <el-date-picker class="width220" v-model="obj.form.financialReceivableMonth" type="date"
                        placeholder="请选择日期" clearable />
                </el-form-item>
                <el-divider content-position="left">到款记录</el-divider>
                <el-table :data="obj.tableData" border>
                    <el-table-column label="到款客户" align="center" prop="customerName" />
                    <el-table-column label="到款日期" align="center" prop="paymentDate" />
                    <el-table-column label="到款金额" align="center" prop="paymentAmount" />
                    <el-table-column label="到款银行" align="center" prop="bank" />
                    <el-table-column label="支付类型" align="center" prop="paymentType" />
                    <el-table-column label="支票号" align="center" prop="checkNumber" />
                    <el-table-column label="到款备注" align="center" prop="paymentRemark" />
                    <el-table-column label="余额" align="center" prop="balance" />
                </el-table>
                <el-divider content-position="left">应收核销</el-divider>
                <el-row class="mb8">
                    <el-button type="primary" plain icon="Check" @click="handleVerify">保存核销</el-button>
                    <el-button type="success" plain icon="Tickets" @click="handleVerify">保存开票</el-button>
                    <el-button type="info" plain icon="Document" @click="handleVerify">核销明细</el-button>
                </el-row>
                <el-table :data="obj.tableData" border @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="出账单分公司" align="center" prop="billProvince" />
                    <el-table-column label="客户名称" align="center" prop="customerName" />
                    <el-table-column label="客户编号" align="center" prop="customerAccount" />
                    <el-table-column label="客户账套" align="center" prop="customerAccount" />
                    <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
                    <el-table-column label="应收金额" align="center" prop="receivableAmount" />
                    <el-table-column label="已核销金额" align="center" prop="verifyAmount" />
                    <el-table-column label="审批中金额" align="center" prop="approvingAmount" />
                    <el-table-column label="调整金额" align="center" prop="adjustAmount" />
                    <el-table-column label="欠款金额" align="center" prop="arrearsAmount" />
                    <el-table-column label="开票状态" align="center" prop="invoiceStatus" />
                    <el-table-column label="核销状态" align="center" prop="verifyStatus" />
                    <el-table-column label="本次核销金额" align="center" prop="verifyAmount" />
                    <el-table-column label="差额" align="center" prop="differenceAmount" />
                    <el-table-column label="是否调整金额" align="center" prop="isAdjust" />
                </el-table>
            </el-form>
        </el-dialog>

        <!-- 上传 -->
        <el-dialog v-model="obj.dialogVisible" title="上传导入数据" width="30%" append-to-body draggable>
            <el-form :model="obj.form" ref="formRef" class="formHight" inline label-width="auto">
                <el-form-item label="上传人:" prop="uploadPerson">
                    <file-upload v-model="obj.form.uploadPerson" />
                </el-form-item>
                <el-form-item label="分公司:" prop="branch">
                    <el-select class="width220" v-model="obj.form.branch" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" plain icon="Upload" @click="handleUpload">上传</el-button>
                <el-button type="info" plain icon="Close" @click="obj.dialogVisible = false">取消</el-button>
            </template>
        </el-dialog>

    </div>
</template>

<script setup name="UploadOnlineBankingToPayment_financial">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";


const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数
    dialogVisible: false,//上传弹窗
    form: {
        uploadPerson: null,
        province: null,
    },//上传表单
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 核销
function handleVerify() {
    obj.dialogShow = true;
}

// 保存备注
function handleSaveRemark() {

}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 冻结
function handleFreeze() {

}

// 解冻
function handleUnfreeze() {

}

// 作废
function handleInvalid() {

}

// 恢复有效
function handleValid() {

}

// 查询操作历史日志
function handleQueryOperationHistory() {

}

// 批量核销
function handleBatchVerify() {

}

// 模版下载
function handleDownloadTemplate() {

}

// 上传 
function handleUpload() {
    obj.dialogVisible = true;
}



getList();
</script>
<style lang="scss" scoped></style>