<!-- 发票明细报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="发票开具公司:" prop="invoiceCompany">
                <el-select class="width220" v-model="obj.queryParams.invoiceCompany" placeholder="请选择公司" clearable>
                    <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="提票日期起:" prop="invoiceRequestDateStart" required>
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceRequestDateStart" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="开票日期起:" prop="invoiceDateStart" required>
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceDateStart" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="签约日期起始日期-起:" prop="signDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.signDateStart" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="销售所在公司:" prop="salesCompany">
                <el-select class="width220" v-model="obj.queryParams.salesCompany" placeholder="请选择公司" clearable>
                    <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="提票日期止:" prop="invoiceRequestDateEnd" required>
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceRequestDateEnd" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="开票日期止:" prop="invoiceDateEnd" required>
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceDateEnd" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="签约日期起始日期-止:" prop="signDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.signDateEnd" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="销售所在城市:" prop="salesCity">
                <el-select class="width220" v-model="obj.queryParams.salesCity" placeholder="请选择城市" clearable>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="产品类型:" prop="productType">
                <el-select class="width220" v-model="obj.queryParams.productType" placeholder="请选择产品类型" clearable>
                    <el-option v-for="item in productTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="签约部门:" prop="signDepartment">
                <el-select class="width220" v-model="obj.queryParams.signDepartment" placeholder="请选择部门" clearable>
                    <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row>
                <el-button icon="Refresh" @click="handleReset">重置</el-button>
                <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                <el-button type="primary" icon="Download" @click="handleSpecialExport">客服特殊导出</el-button>
            </el-row>
        </el-form>
    </div>
</template>

<script setup name="DetailedInvoiceStatement">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 公司选项
const companyOptions = ref([
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
]);

// 城市选项
const cityOptions = ref([
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
]);

// 产品类型选项
const productTypeOptions = ref([
    { value: '1', label: '社保' },
    { value: '2', label: '商保' },
    { value: '3', label: '公积金' },
    { value: '4', label: '人事代理' }
]);

// 部门选项
const departmentOptions = ref([
    { value: '1', label: '销售部' },
    { value: '2', label: '客服部' },
    { value: '3', label: '财务部' },
    { value: '4', label: '人力资源部' }
]);

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    ids: [], // 选中的ID数组
    single: true, // 是否单选
    multiple: true, // 是否多选
    tableData: [], // 表格数据
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        invoiceCompany: null,
        invoiceRequestDateStart: null,
        invoiceDateStart: null,
        signDateStart: null,
        salesCompany: null,
        invoiceRequestDateEnd: null,
        invoiceDateEnd: null,
        signDateEnd: null,
        salesCity: null,
        productType: null,
        customerName: null,
        signDepartment: null
    } // 查询表单
})

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 客服特殊导出按钮操作 */
function handleSpecialExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}
</script>
<style lang="scss" scoped></style>