<!-- 服务人数报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="客户名称:" prop="customerName  ">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="合同类型:" prop="contractType">
                <el-select class="width220" v-model="obj.queryParams.contractType" placeholder="请选择合同类型" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractNumber">
                <el-input class="width220" v-model="obj.queryParams.contractNumber" placeholder="请输入合同编号" clearable />
            </el-form-item>
            <el-form-item label="销售所在城市:" prop="salesCity">
                <el-select class="width220" v-model="obj.queryParams.salesCity" placeholder="请选择销售所在城市" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="集团名称:" prop="groupName">
                <el-select class="width220" v-model="obj.queryParams.groupName" placeholder="请选择集团名称" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="销售:" prop="sales">
                <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择销售" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectCustomer">
                <el-select class="width220" v-model="obj.queryParams.projectCustomer" placeholder="请选择项目客服" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="年月起:" prop="startMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.startMonth" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="年月止:" prop="endMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.endMonth" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-row class="mb8">
                <el-form-item>
                    <el-button type="primary" icon="Download" @click="handleExport">导出报表</el-button>
                </el-form-item>
            </el-row>
        </el-form>
    </div>
</template>

<script setup>
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


const obj = reactive({
    queryParams: {
        customerName: null,
        contractType: null,
        contractNumber: null,
        salesCity: null,
        groupName: null,
        sales: null,
        projectCustomer: null,
        startMonth: null,
        endMonth: null,
    },//查询表单

})


/** 导出明细按钮操作 */
function handleExport() {
    if (!obj.queryParams.startMonth || !obj.queryParams.endMonth) {
        proxy.$modal.msgWarning('请选择年月起和年月止');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        // 调用导出接口
        proxy.$modal.msgSuccess('导出成功');
    }).catch(() => { });
}

</script>
<style lang="scss" scoped></style>