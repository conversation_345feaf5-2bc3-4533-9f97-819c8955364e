<template>
    <el-table v-loading="props.loading" show-overflow-tooltip border :data="props.tableData"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="客户名称" align="center" prop="customerName" min-width="100" />
        <el-table-column label="合同编号" align="center" prop="contractNo" width="120" />
        <el-table-column label="扣缴义务人名称" align="center" prop="withholdingName" min-width="150" />
        <el-table-column label="扣缴义务人类型" align="center" width="120">
            <template #default="scope">
                {{ getWithholdingTypeName(scope.row.withholdingType) }}
            </template>
        </el-table-column>
        <el-table-column label="工资发放地" align="center" width="100">
            <template #default="scope">
                {{ getSalaryLocationName(scope.row.salaryLocation) }}
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="220">
            <template #default="scope">
                <el-button text type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button text type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </template>
        </el-table-column>
    </el-table>
</template>
<script setup>

/**
 * 获取扣缴义务人类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getWithholdingTypeName(typeId) {
    if (!typeId) return '-';

    const type = withholdingTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}

/**
 * 获取工资发放地名称
 * @param {string} locationId 地点ID
 * @returns {string} 地点名称
 */
function getSalaryLocationName(locationId) {
    if (!locationId) return '-';

    const location = salaryLocationOptions.find(item => item.value === locationId);
    return location ? location.label : '-';
}
// 扣缴义务人类型选项
const withholdingTypeOptions = [
    { value: '1', label: '企业' },
    { value: '2', label: '事业单位' },
    { value: '3', label: '个体工商户' },
    { value: '4', label: '其他组织' }
];

// 工资发放地选项
const salaryLocationOptions = [
    { value: '1', label: '北京市' },
    { value: '2', label: '上海市' },
    { value: '3', label: '广州市' },
    { value: '4', label: '深圳市' },
    { value: '5', label: '成都市' }
];

const props = defineProps({
    loading: {
        type: Boolean,
        default: false
    },
    tableData: {
        type: Array,
        default: () => []
    },
})
const emit = defineEmits(['handleSelectionChange'])
const handleSelectionChange = (selection) => {
    emit('handleSelectionChange', selection)
}

</script>

<style lang="scss" scoped></style>
