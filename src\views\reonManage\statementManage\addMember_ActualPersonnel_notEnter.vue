<!-- 增员未进入实做人员报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="报表年月:" prop="reportMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.reportMonth" type="month" placeholder="请选择年月"
                    clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="订单号:" prop="orderNumber">
                <el-input class="width220" v-model="obj.queryParams.orderNumber" placeholder="请输入订单号" clearable />
            </el-form-item>
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择城市" clearable>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否单立户:" prop="isSingleAccount">
                <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择是否单立户" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同类型:" prop="contractType">
                <el-select class="width220" v-model="obj.queryParams.contractType" placeholder="请选择合同类型" clearable>
                    <el-option v-for="item in contractTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" @click="handleQuery">查询</el-button>
                        <el-button type="primary" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row class="mb8">
            <el-col :span="24">
                <el-button type="primary" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" type="index" width="80" />
            <el-table-column label="城市名称" align="center" prop="cityName" />
            <el-table-column label="月份" align="center" prop="month" />
            <el-table-column label="订单编号" align="center" prop="orderNumber" />
            <el-table-column label="姓名" align="center" prop="employeeName" />
            <el-table-column label="证件号" align="center" prop="idNumber" />
            <el-table-column label="性别" align="center" prop="gender" />
            <el-table-column label="年龄" align="center" prop="age" />
            <el-table-column label="退休时间" align="center" prop="retirementTime" />
            <el-table-column label="联系电话" align="center" prop="phoneNumber" />
            <el-table-column label="申请入职时间" align="center" width="120" prop="applyEntryTime" />
            <el-table-column label="入职日期" align="center" prop="entryDate" />
            <el-table-column label="雇员状态" align="center" prop="employeeStatus">
                <template #default="scope">
                    <dict-tag :options="employeeStatusOptions" :value="scope.row.employeeStatus" />
                </template>
            </el-table-column>
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同类型" align="center" prop="contractType">
                <template #default="scope">
                    <dict-tag :options="contractTypeOptions" :value="scope.row.contractType" />
                </template>
            </el-table-column>
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="签约方抬头" align="center" width="120" prop="signingParty" />
            <el-table-column label="派单分公司" align="center" width="120" prop="dispatchCompany" />
            <el-table-column label="派单客服" align="center" prop="dispatchService" />
            <el-table-column label="接单方" align="center" prop="receiver" />
            <el-table-column label="接单方类型" align="center" width="120" prop="receiverType" />
            <el-table-column label="接单方客服" align="center" width="120" prop="receiverService" />
            <el-table-column label="是否单立户" align="center" width="120" prop="isSingleAccount">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isSingleAccount" />
                </template>
            </el-table-column>
            <el-table-column label="单立户名称" align="center" width="120" prop="singleAccountName" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="AddMember_ActualPersonnel_notEnter">

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 城市选项
const cityOptions = ref([
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
]);

// 合同类型选项
const contractTypeOptions = ref([
    { value: '1', label: '劳务派遣' },
    { value: '2', label: '劳务外包' },
    { value: '3', label: '劳务协作' }
]);

// 雇员状态选项
const employeeStatusOptions = ref([
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '待入职' },
    { value: '4', label: '未入职' }
]);

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    ids: [], // 选中的ID数组
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportMonth: null,
        customerName: null,
        employeeName: null,
        idNumber: null,
        orderNumber: null,
        city: null,
        isSingleAccount: null,
        contractType: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                cityName: '北京',
                month: '2023-05',
                orderNumber: 'DD20230001',
                employeeName: '张三',
                idNumber: '110101199001011234',
                gender: '男',
                age: 33,
                retirementTime: '2050-01-01',
                phoneNumber: '***********',
                applyEntryTime: '2023-04-15',
                entryDate: '2023-05-01',
                employeeStatus: '4',
                customerCode: 'KH20230001',
                customerName: '客户A',
                contractType: '1',
                contractCode: 'HT20230001',
                signingParty: '公司A',
                dispatchCompany: '分公司A',
                dispatchService: '客服1',
                receiver: '接单方A',
                receiverType: '公司',
                receiverService: '客服2',
                isSingleAccount: 'Y',
                singleAccountName: '单立户A'
            },
            {
                id: 2,
                cityName: '上海',
                month: '2023-06',
                orderNumber: 'DD20230002',
                employeeName: '李四',
                idNumber: '310101199002022345',
                gender: '女',
                age: 32,
                retirementTime: '2050-02-02',
                phoneNumber: '***********',
                applyEntryTime: '2023-05-15',
                entryDate: '2023-06-01',
                employeeStatus: '4',
                customerCode: 'KH20230002',
                customerName: '客户B',
                contractType: '2',
                contractCode: '**********',
                signingParty: '公司B',
                dispatchCompany: '分公司B',
                dispatchService: '客服3',
                receiver: '接单方B',
                receiverType: '个人',
                receiverService: '客服4',
                isSingleAccount: 'N',
                singleAccountName: ''
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框操作 */
function handleSelectionChange(selection) {
    obj.multiple = selection.length > 0;
    obj.single = selection.length === 1;
    obj.ids = selection.map(item => item.id);
}

/** 导出操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        // 调用导出接口
        proxy.$modal.msgSuccess('导出成功');
    }).catch(() => { });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>