<!-- 分配小合同 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-input class="width220" v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称"
                    clearable />
            </el-form-item>
            <el-form-item label="接单是否分配:" prop="isAcceptanceAssigned">
                <el-select class="width220" v-model="obj.queryParams.isAcceptanceAssigned" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="后道是否分配:" prop="isBackAssigned">
                <el-select class="width220" v-model="obj.queryParams.isBackAssigned" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单分公司:" prop="branchCompany">
                <el-select class="width220" v-model="obj.queryParams.branchCompany" placeholder="请选择" clearable>
                    <el-option label="北京分公司" value="北京分公司" />
                    <el-option label="上海分公司" value="上海分公司" />
                    <el-option label="广州分公司" value="广州分公司" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单客服:" prop="serviceManager">
                <el-input class="width220" v-model="obj.queryParams.serviceManager" placeholder="请输入接单客服" clearable />
            </el-form-item>
            <el-form-item label="是否单立户:" prop="isSingleAccount">
                <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain :disabled="obj.single" @click="handleAll">接单、后道分配</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain :disabled="obj.single" @click="handleAcceptance">接单分配</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain :disabled="obj.single" @click="handleBack">后道分配</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain :disabled="obj.single" @click="handleHistory">分配历史日志</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="派单分公司" align="center" prop="dispatchBranchCompany" />
            <el-table-column label="接单分公司" align="center" prop="branchCompany" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="是否单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="客服类型" align="center" prop="serviceType" />
            <el-table-column label="接单客服" align="center" prop="acceptanceServiceManager" />
            <el-table-column label="派单客服" align="center" prop="dispatchServiceManager" />
            <el-table-column label="后道客服" align="center" prop="backServiceManager" />
            <el-table-column label="生效日期" align="center" prop="effectiveDate" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 弹窗 -->
        <el-dialog v-model="obj.dialogShow" title="分配客服" width="35%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <el-form-item label="新客服:" prop="newServiceManager" v-if="obj.isAll || obj.isAcceptance">
                    <el-input class="width220" v-model="obj.dialogForm.newServiceManager" placeholder="请输入新客服"
                        clearable />
                </el-form-item>
                <el-form-item label="生效日期:" prop="effectiveDate" v-if="obj.isAll || obj.isAcceptance || obj.isBack">
                    <el-date-picker class="width220" v-model="obj.dialogForm.effectiveDate" placeholder="请选择生效日期"
                        clearable />
                </el-form-item>
                <el-form-item label="后道、接单是否相同:" prop="isSame" v-if="obj.isAll">
                    <el-select class="width220" v-model="obj.dialogForm.isSame" placeholder="请选择" clearable>
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="后道客服:" prop="backServiceManager"
                    v-if="obj.isBack || (obj.isAll && obj.dialogForm.isSame === '0')">
                    <el-input class="width220" v-model="obj.dialogForm.backServiceManager" placeholder="请输入后道客服"
                        clearable />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSave">保存</el-button>
            </template>
        </el-dialog>
        <!-- 分配历史日志 -->
        <DistributionHistoryLog v-model:dialogShow="obj.dialogShow2" :tableData="obj.tableData2"
            menuName="subcontract_small" />
    </div>
</template>

<script setup name="Subcontract_small">

import { listScale } from "@/api/reonApi/scale";
import DistributionHistoryLog from '@/views/reonManage/components/dialog/distributionHistoryLog.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    newServiceManager: [
        { required: true, message: '新客服不能为空', trigger: 'blur' }
    ],
    effectiveDate: [
        { required: true, message: '生效日期不能为空', trigger: 'change' }
    ],
    backServiceManager: [
        { required: true, message: '后道客服不能为空', trigger: 'blur' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        smallContractName: null,
        isAcceptanceAssigned: null,
        isBackAssigned: null,
        branchCompany: null,
        serviceManager: null,
        isSingleAccount: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    tableData2: [],//分配历史日志列表
    dialogForm: {},//分配表单
    dialogShow: false,//分配弹窗
    dialogShow2: false,//分配历史日志弹窗
    ids: [],//选中的id
    title: "",//标题
    isAll: false,//接单、后道分配
    isAcceptance: false,//接单分配
    isBack: false,//后道分配
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerName: '客户名称1',
                contractName: '合同名称1',
                dispatchBranchCompany: '派单分公司1',
                branchCompany: '接单分公司1',
                smallContractName: '小合同名称1',
                isSingleAccount: '是',
                serviceType: '客服类型1',
                acceptanceServiceManager: '接单客服1',
                dispatchServiceManager: '派单客服1',
                backServiceManager: '后道客服1',
                effectiveDate: '2023-01-01',
                createBy: '创建人1',
                createTime: '2023-01-01 10:00:00'
            },
            {
                id: 2,
                customerName: '客户名称2',
                contractName: '合同名称2',
                dispatchBranchCompany: '派单分公司2',
                branchCompany: '接单分公司2',
                smallContractName: '小合同名称2',
                isSingleAccount: '否',
                serviceType: '客服类型2',
                acceptanceServiceManager: '接单客服2',
                dispatchServiceManager: '派单客服2',
                backServiceManager: '后道客服2',
                effectiveDate: '2023-02-01',
                createBy: '创建人2',
                createTime: '2023-02-01 10:00:00'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 重置表单 */
function reset() {
    obj.dialogForm = {
        newServiceManager: '',
        effectiveDate: '',
        isSame: '1',
        backServiceManager: ''
    };
    obj.isAll = false;
    obj.isAcceptance = false;
    obj.isBack = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 接单、后道分配
function handleAll() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要分配的数据');
        return;
    }
    reset();
    obj.isAll = true;
    obj.dialogShow = true;
}

// 保存按钮操作
function handleSave() {
    proxy.$modal.confirm('确认要保存吗？').then(() => {
        proxy.$modal.msgSuccess('保存成功');
        obj.dialogShow = false;
    }).catch(() => { });
}

// 接单分配
function handleAcceptance() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要分配的数据');
        return;
    }
    reset();
    obj.isAcceptance = true;
    obj.dialogShow = true;
}

// 后道分配
function handleBack() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要分配的数据');
        return;
    }
    reset();
    obj.isBack = true;
    obj.dialogShow = true;
}

// 分配历史日志
function handleHistory() {
    obj.dialogShow2 = true;
    // 模拟分配历史日志数据
    obj.tableData2 = [
        {
            id: 1,
            customerName: '客户名称1',
            contractName: '合同名称1',
            smallContractName: '小合同名称1',
            serviceType: '接单客服',
            serviceManager: '接单客服1',
            effectiveDate: '2023-01-01',
            branchCompany: '北京分公司'
        },
        {
            id: 2,
            customerName: '客户名称2',
            contractName: '合同名称2',
            smallContractName: '小合同名称2',
            serviceType: '后道客服',
            serviceManager: '后道客服2',
            effectiveDate: '2023-02-01',
            branchCompany: '上海分公司'
        }
    ];
}

// 详情按钮操作
function handleDetail() {
    proxy.$modal.msgInfo('查看详情功能正在开发中');
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>