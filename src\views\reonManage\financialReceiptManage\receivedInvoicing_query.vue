<!--实收开票查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="大集团:" prop="groupName">
                <el-input class="width220" v-model="obj.queryParams.groupName" placeholder="请输入大集团名称" clearable />
            </el-form-item>
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="开票抬头:" prop="invoiceTitle">
                <el-select class="width220" v-model="obj.queryParams.invoiceTitle" placeholder="请选择开票抬头" clearable>
                    <el-option label="抬头1" value="1" />
                    <el-option label="抬头2" value="2" />
                    <el-option label="抬头3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户帐套:" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户帐套" clearable>
                    <el-option label="帐套1" value="1" />
                    <el-option label="帐套2" value="2" />
                    <el-option label="帐套3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="财务应收月起:" prop="financialReceivableMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.financialReceivableMonthStart" type="month"
                    placeholder="请选择开始月份" clearable />
            </el-form-item>
            <el-form-item label="财务应收月止:" prop="financialReceivableMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.financialReceivableMonthEnd" type="month"
                    placeholder="请选择结束月份" clearable />
            </el-form-item>
            <el-form-item label="发票金额>=:" prop="invoiceAmountMin">
                <el-input class="width220" v-model="obj.queryParams.invoiceAmountMin" placeholder="请输入最小金额" clearable />
            </el-form-item>
            <el-form-item label="发票金额<=:" prop="invoiceAmountMax">
                <el-input class="width220" v-model="obj.queryParams.invoiceAmountMax" placeholder="请输入最大金额" clearable />
            </el-form-item>
            <el-form-item label="提票起始日期:" prop="invoiceSubmitStartDate">
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceSubmitStartDate" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="提票截止日期:" prop="invoiceSubmitEndDate">
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceSubmitEndDate" type="date"
                    placeholder="请选择结束日期" clearable />
            </el-form-item>
            <el-form-item label="开票起始日期:" prop="invoiceStartDate">
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceStartDate" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="开票截止日期:" prop="invoiceEndDate">
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceEndDate" type="date"
                    placeholder="请选择结束日期" clearable />
            </el-form-item>
            <el-form-item label="开票编号:" prop="invoiceNumber">
                <el-input class="width220" v-model="obj.queryParams.invoiceNumber" placeholder="请输入开票编号" clearable />
            </el-form-item>
            <el-form-item label="发票状态:" prop="invoiceStatus">
                <el-select class="width220" v-model="obj.queryParams.invoiceStatus" placeholder="请选择发票状态" clearable>
                    <el-option label="未开票" value="0" />
                    <el-option label="已开票" value="1" />
                    <el-option label="开票中" value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="签单分公司:" prop="signBranch">
                <el-select class="width220" v-model="obj.queryParams.signBranch" placeholder="请选择签单分公司" clearable>
                    <el-option label="上海分公司" value="上海分公司" />
                    <el-option label="北京分公司" value="北京分公司" />
                    <el-option label="广州分公司" value="广州分公司" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" @click="handleExport">导出</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="warning" icon="Remove" @click="handleRedWriteOff">红冲</el-button>
                <el-button type="primary" icon="Printer" @click="handlePrintInvoiceApplication">打印开票申请单</el-button>
                <el-button type="info" icon="Tickets" @click="handlePrintInvoiceRecord">开票记录</el-button>
                <el-button type="success" icon="Upload" @click="handleImportRedInvoice">导入红牌单号</el-button>
            </el-col>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="签单分公司" align="center" prop="signBranch" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="开票抬头" align="center" prop="invoiceTitle" />
            <el-table-column label="发票金额" align="center" prop="invoiceAmount" />
            <el-table-column label="蓝票提票日期" align="center" prop="blueInvoiceSubmitDate" />
            <el-table-column label="蓝票开票日期" align="center" prop="blueInvoiceDate" />
            <el-table-column label="红票提票日期" align="center" prop="redInvoiceSubmitDate" />
            <el-table-column label="红票开票日期" align="center" prop="redInvoiceDate" />
            <el-table-column label="发票状态" align="center" prop="invoiceStatus" />
            <el-table-column label="开票备注" align="center" prop="invoiceRemark" />
            <el-table-column label="其他税收分类" align="center" prop="taxCategory" />
            <el-table-column label="开票编号" align="center" prop="invoiceNumber" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 开票记录 -->
        <el-dialog v-model="obj.showInvoiceRecord" :title="obj.title" width="60%">
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Download" @click="handleDownloadInvoice">下载发票</el-button>
                        <el-button type="primary" icon="Refresh" @click="handleRefreshInvoice">刷新</el-button>
                        <el-button type="primary" icon="RefreshRight" @click="handleReacquireInvoice">重新获取</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="签单分公司" align="center" prop="signBranch" />
                <el-table-column label="客户编号" align="center" prop="customerCode" />
                <el-table-column label="客户名称" align="center" prop="customerName" />
                <el-table-column label="发票金额" align="center" prop="invoiceAmount" />
                <el-table-column label="蓝票提票日期" align="center" sortable prop="blueInvoiceSubmitDate" />
                <el-table-column label="蓝票开票日期" align="center" prop="blueInvoiceDate" />
                <el-table-column label="红票提票日期" align="center" prop="redInvoiceSubmitDate" />
                <el-table-column label="红票开票日期" align="center" prop="redInvoiceDate" />
                <el-table-column label="发票状态" align="center" prop="invoiceStatus" />
                <el-table-column label="开票备注" align="center" prop="invoiceRemark" />
                <el-table-column label="其他税收分类" align="center" prop="taxCategory" />
                <el-table-column label="开票编号" align="center" prop="invoiceNumber" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="ReceivedInvoicing_query">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null,
        customerName: null,
        invoiceTitle: null,
        customerAccount: null,
        financialReceivableMonthStart: null,
        financialReceivableMonthEnd: null,
        invoiceAmountMin: null,
        invoiceAmountMax: null,
        invoiceSubmitStartDate: null,
        invoiceSubmitEndDate: null,
        invoiceStartDate: null,
        invoiceEndDate: null,
        invoiceNumber: null,
        invoiceStatus: null,
        signBranch: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
    showInvoiceRecord: false,//开票记录
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                signBranch: '上海分公司',
                customerCode: 'CUST001',
                customerName: '客户名称1',
                invoiceTitle: '抬头1',
                invoiceAmount: 10000.00,
                blueInvoiceSubmitDate: '2023-01-10',
                blueInvoiceDate: '2023-01-15',
                redInvoiceSubmitDate: '',
                redInvoiceDate: '',
                invoiceStatus: '已开票',
                invoiceRemark: '正常开票',
                taxCategory: '服务费',
                invoiceNumber: 'INV20230115001'
            },
            {
                id: 2,
                signBranch: '北京分公司',
                customerCode: 'CUST002',
                customerName: '客户名称2',
                invoiceTitle: '抬头2',
                invoiceAmount: 20000.00,
                blueInvoiceSubmitDate: '2023-02-10',
                blueInvoiceDate: '2023-02-15',
                redInvoiceSubmitDate: '2023-03-10',
                redInvoiceDate: '2023-03-15',
                invoiceStatus: '已红冲',
                invoiceRemark: '退款红冲',
                taxCategory: '咨询费',
                invoiceNumber: 'INV20230215001'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 红冲
function handleRedWriteOff() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要红冲的数据');
        return;
    }
    proxy.$modal.confirm('是否确认红冲选中的发票？').then(function () {
        // 这里可以调用API红冲发票
        proxy.$modal.msgSuccess('红冲操作成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 打印开票申请单
function handlePrintInvoiceApplication() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要打印开票申请单的数据');
        return;
    }
    proxy.$modal.msgSuccess('打印开票申请单成功');
}

// 开票记录
function handlePrintInvoiceRecord() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要查看开票记录的数据');
        return;
    }
    obj.showInvoiceRecord = true;
    obj.title = "查看开票明细";
}

// 导入红牌单号
function handleImportRedInvoice() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要导入红牌单号的数据');
        return;
    }
    proxy.$modal.msgSuccess('导入红牌单号成功');
}

// 下载发票
function handleDownloadInvoice() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要下载的发票');
        return;
    }
    proxy.$modal.msgSuccess('下载发票成功');
}

// 刷新
function handleRefreshInvoice() {
    getList();
    proxy.$modal.msgSuccess('刷新成功');
}

// 重新获取
function handleReacquireInvoice() {
    getList();
    proxy.$modal.msgSuccess('重新获取成功');
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>