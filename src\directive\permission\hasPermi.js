import useUserStore from "@/store/modules/user";

export default {
  /**
   * 当指令被挂载到元素上时执行的钩子函数
   * @param {HTMLElement} el - 指令所绑定的元素
   * @param {Object} binding - 一个对象，包含指令的相关信息
   * @param {VNode} vnode - Vue 编译生成的虚拟节点
   */
  mounted(el, binding, vnode) {
    // 从 binding 对象中解构出 value 属性
    const { value } = binding;
    // 定义一个表示所有权限的字符串
    const all_permission = "*:*:*";
    // 从用户存储中获取当前用户的权限列表
    const permissions = useUserStore().permissions;

    // 检查 value 是否是一个非空数组
    if (value && value instanceof Array && value.length > 0) {
      // 将 value 赋值给 permissionFlag 变量
      const permissionFlag = value;

      // 检查当前用户的权限列表中是否存在满足条件的权限
      const hasPermissions = permissions.some((permission) => {
        // 如果权限是通配符或者权限列表中包含该权限，则返回 true
        return (
          all_permission === permission || permissionFlag.includes(permission)
        );
      });

      // 如果用户没有权限，则移除该元素
      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      // 如果 value 不是一个有效的数组，则抛出错误
      throw new Error(`请设置操作权限标签值`);
    }
  },
};
