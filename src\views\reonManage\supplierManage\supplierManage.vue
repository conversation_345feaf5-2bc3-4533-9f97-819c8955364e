<!-- 供应商管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商/联系人:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="参保地省级:" prop="province">
                <el-select class="width220" v-model="obj.queryParams.province" placeholder="请选择" clearable
                    @change="handleProvinceChange">
                    <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="参保地市级:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择" clearable>
                    <el-option v-if="obj.queryParams.province" v-for="item in cityList" :key="item.code"
                        :label="item.city" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="客服负责人:" prop="username">
                <el-input class="width220" v-model="obj.queryParams.username" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>

            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Operation" @click="handleDistribution">社保供应商服务城市分配</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Open" @click="handleUpdate">启用</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="TurnOff" @click="handleUpdate">禁用</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商名称" align="center" prop="id" />
            <el-table-column label="供应商类型" align="center" prop="username">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.username" />
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="email">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.email" />
                </template>
            </el-table-column>
            <el-table-column label="联系人" align="center" prop="birthdate">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.birthdate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="联系方式" align="center" prop="isActive">
                <template #default="scope">
                    <dict-tag :options="sys_job_group" :value="scope.row.isActive" />
                </template>
            </el-table-column>
            <el-table-column label="邮箱" align="center" prop="birthdate">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.birthdate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="地址" align="center" prop="isActive">
                <template #default="scope">
                    <dict-tag :options="sys_job_group" :value="scope.row.isActive" />
                </template>
            </el-table-column>
            <el-table-column label="注册时间" align="center" prop="birthdate">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.birthdate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="采购负责人" align="center" prop="isActive">
                <template #default="scope">
                    <dict-tag :options="sys_job_group" :value="scope.row.isActive" />
                </template>
            </el-table-column>
            <el-table-column label="客服负责人" align="center" prop="birthdate">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.birthdate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"></el-button>
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="60%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="供应商名称：" prop="supplierName">
                    <el-input :disabled="obj.isDetail" class="width420" v-model="obj.dialogForm.supplierName"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="联系人：" prop="contactPerson">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.contactPerson"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="联系方式：" prop="contactPhone">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.contactPhone"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="邮箱：" prop="contactEmail">
                    <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.contactEmail"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="供应商地址：" prop="address">
                    <el-input :disabled="obj.isDetail" class="width420" v-model="obj.dialogForm.address"
                        placeholder="请输入" />
                </el-form-item>
                <!-- 下划线 -->
                <el-divider />
                <el-table :data="obj.dialogForm.supplierList" style="width: 100%">
                    <el-table-column label="省" prop="province" align="center">
                        <template #default="scope">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="scope.row.province"
                                placeholder="请选择" clearable @change="rowProvinceChange(scope.row, scope.$index)">
                                <el-option v-for="item in ChinaCitys" :key="item.code" :label="item.province"
                                    :value="item.code" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="市" prop="city" align="center">
                        <template #default="scope">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="scope.row.city"
                                placeholder="请选择" clearable>
                                <el-option v-for="item in rowCityList" :key="item.code" :label="item.city"
                                    :value="item.code" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否优选" prop="isActive" align="center">
                        <template #default="scope">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="scope.row.isActive"
                                placeholder="请选择" clearable>
                                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="是否启用" prop="enable" align="center">
                        <template #default="scope">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="scope.row.enable"
                                placeholder="请选择" clearable>
                                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label"
                                    :value="dict.value" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" v-if="!obj.isDetail">
                        <template #default="scope">
                            <el-button link type="primary" icon="Plus"
                                @click="addBtn(scope.row, scope.$index)"></el-button>
                            <el-button link type="primary" icon="Delete"
                                @click="delBtn(scope.row, scope.$index)"></el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 社保供应商服务城市分配 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="60%" append-to-body style="min-height: 500px;">
            <el-form :model="obj.dialogForm" label-width="auto">
                <el-row>
                    <el-col :span="12"></el-col>
                    <el-col :span="12">
                        <el-form-item label="供应商名称：">
                            <el-input readonly class="width220" v-model="obj.dialogForm.username" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-button class="marr20" type="primary" @click="addBtn">分配</el-button>
                    <el-input class="width220 marr20" v-model="obj.dialogForm.username" placeholder="请输入市名称" />
                    <el-button type="primary" @click="addBtn">检索</el-button>
                    <el-button type="primary" @click="addBtn">取消检索</el-button>
                </el-row>

                <el-table border :data="obj.dialogForm.supplierList" style="width: 100%;margin-top: 20px;"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="省" prop="province" align="center">
                        <template #default="scope">
                            <div>{{ scope.row.province }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="市" prop="city" align="center">
                        <template #default="scope">
                            <div>{{ scope.row.city }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="责任客服" prop="customer" align="center">
                        <template #default="scope">
                            <div>{{ scope.row.customer }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center">
                        <template #default="scope">
                            <el-button type="primary" @click="addBtn(scope.row, scope.$index)">分配</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                    v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup name="SupplierManage">
import { listInstance, delInstance } from '@/api/reonApi/instance';
import ChinaCitys from "@/utils/ChinaCitys.json";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        province: null,
        city: null,
        username: null,
    },//查询表单
    rules: {
        username: [
            { required: true, message: "2不能为空", trigger: "blur" }
        ],
        email: [
            { required: true, message: "3不能为空", trigger: "blur" }
        ],
    },
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            username: '张三',
            email: '1',
            birthdate: '2021-01-01',
            isActive: '1',
            supplierName: '北京科技有限公司',
            supplierType: '1',
            status: '1',
            contactPerson: '王经理',
            contactPhone: '13800138000',
            contactEmail: '<EMAIL>',
            address: '北京市海淀区中关村软件园',
            registerTime: '2021-01-01 10:00:00',
            purchaseManager: '李经理',
            serviceManager: '张经理'
        },
        {
            id: 2,
            username: '李四',
            email: '0',
            birthdate: '2021-02-01',
            isActive: '0',
            supplierName: '上海信息技术有限公司',
            supplierType: '2',
            status: '0',
            contactPerson: '张总监',
            contactPhone: '13900139000',
            contactEmail: '<EMAIL>',
            address: '上海市浦东新区张江高科技园区',
            registerTime: '2021-02-01 11:00:00',
            purchaseManager: '王经理',
            serviceManager: '赵经理'
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //新增、修改弹出框
    dialogShow2: false,//分配弹出框
    ids: [],//选中的id
    title: "",//标题
    isDetail: false,//是否查看
})

/** 列表 */
function getList() {
    obj.loading = true;
    listInstance(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}
const cityList = ref([]);//市列表
function handleProvinceChange(code) {
    console.log(code);
    obj.queryParams.city = null;
    cityList.value = ChinaCitys.filter(item => item.code == code)[0].citys;
}



// 表单重置
function reset() {
    obj.dialogForm = {
        supplierList: []
    };
    obj.isDetail = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    console.log(obj.queryParams);

    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}
/** 分配按钮 */
function handleDistribution() {
    obj.dialogShow2 = true;
}
/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增报价单";

    obj.dialogForm.supplierList.push({
        province: null,
        city: null,
        customer: null,
        enable: null,
    });
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || obj.ids
    getUsers(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改报价单";
    });
}
/** 详情按钮操作 */
function handleDetail(row) {
    obj.isDetail = true;
    obj.dialogShow = true;
    obj.title = "详情";
}
// 新增
function addBtn(row, index) {
    obj.dialogForm.supplierList.push({});
    rowCityList.value = [];
}

// 删除
function delBtn(row, index) {
    if (obj.dialogForm.supplierList.length > 1) {
        obj.dialogForm.supplierList.splice(index, 1);
        rowCityList.value = [];
    }
}
const rowCityList = ref([]);//市列表
// 省改变
function rowProvinceChange(row, index) {
    obj.dialogForm.supplierList[index].city = null;
    rowCityList.value = ChinaCitys.filter(item => item.code == row.province)[0].citys;
}
/** 提交按钮 */
function submitForm() {
    console.log(obj.dialogForm);
    return
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row?.id || obj.ids;
    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}


getList();
</script>
<style lang="scss" scoped></style>