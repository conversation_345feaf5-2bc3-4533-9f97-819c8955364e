<!-- 公司银行基础数据 -->
<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
      <el-form-item label="公司:" prop="companyName">
        <el-select class="width220" filterable v-model="obj.queryParams.companyName" placeholder="请选择公司" clearable>
          <el-option v-for="item in companyOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="银行名称:" prop="bankName">
        <el-input class="width220" v-model="obj.queryParams.bankName" placeholder="请输入银行名称" clearable
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="类型:" prop="accountType">
        <el-select class="width220" filterable v-model="obj.queryParams.accountType" placeholder="请选择类型" clearable>
          <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-row :gutter="10" class="mb8">
        <el-col :span="24">
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleEdit">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="obj.multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Plus" @click="handleAdd_special">新增公司特殊银行信息</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Edit" @click="handleEdit_special">修改公司特殊银行信息</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!-- 表格 -->
    <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="序号" type="index" align="center" width="80" />
      <el-table-column label="公司名称" align="center" prop="companyName" />
      <el-table-column label="银行" align="center" prop="bankName" />
      <el-table-column label="开户行" align="center" prop="openBank" />
      <el-table-column label="开户地" align="center" prop="openLocation" />
      <el-table-column label="银行账户" align="center" prop="bankAccount" />
      <el-table-column label="账户全称" align="center" prop="accountFullName" />
      <el-table-column label="类型" align="center" prop="accountType">
        <template #default="scope">
          <el-tag :type="scope.row.accountType === '1' ? 'success' : 'info'">
            {{ scope.row.accountType === '1' ? '基本户' : '一般户' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" align="center" prop="customerName" v-if="obj.isSpecial" />
    </el-table>
    <!-- 分页 -->
    <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
      v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    <!-- 新增 -->
    <el-dialog v-model="obj.dialogShow" :title="obj.title" width="25%">
      <el-form :model="obj.dialogForm" ref="formRef" label-width="auto">
        <el-form-item v-if="obj.isSpecial" label="客户名称:" prop="customerName">
          <el-select style="width: 100%;" v-model="obj.dialogForm.customerName" placeholder="请选择客户" clearable>
            <el-option v-for="item in customerOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="公司:" prop="companyName">
          <el-select style="width: 100%;" v-model="obj.dialogForm.companyName" placeholder="请选择公司" clearable>
            <el-option v-for="item in companyOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="账户全称:" prop="accountFullName">
          <el-input style="width: 100%;" v-model="obj.dialogForm.accountFullName" placeholder="请输入账户全称" />
        </el-form-item>
        <el-form-item label="银行:" prop="bankName">
          <el-select style="width: 100%;" v-model="obj.dialogForm.bankName" placeholder="请选择银行" clearable>
            <el-option v-for="item in bankOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="开户银行:" prop="openBank">
          <el-input style="width: 100%;" v-model="obj.dialogForm.openBank" placeholder="请输入开户银行" />
        </el-form-item>
        <el-form-item label="开户地:" prop="openLocation">
          <el-input style="width: 100%;" v-model="obj.dialogForm.openLocation" placeholder="请输入开户地" />
        </el-form-item>
        <el-form-item label="银行账号:" prop="bankAccount">
          <el-input style="width: 100%;" v-model="obj.dialogForm.bankAccount" placeholder="请输入银行账号" />
        </el-form-item>
        <el-form-item label="类型:" prop="accountType">
          <el-select style="width: 100%;" v-model="obj.dialogForm.accountType" placeholder="请选择类型" clearable>
            <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CorporateBankBasicData">
import { listImport } from "@/api/reonApi/import";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 公司选项
const companyOptions = [
  { value: '1', label: '北京总公司' },
  { value: '2', label: '上海分公司' },
  { value: '3', label: '广州分公司' },
  { value: '4', label: '深圳分公司' },
];

// 银行选项
const bankOptions = [
  { value: '1', label: '中国工商银行' },
  { value: '2', label: '中国农业银行' },
  { value: '3', label: '中国建设银行' },
  { value: '4', label: '中国银行' },
  { value: '5', label: '交通银行' },
];

// 账户类型选项
const accountTypeOptions = [
  { value: '1', label: '基本户' },
  { value: '2', label: '一般户' },
];

// 客户选项
const customerOptions = [
  { value: '1', label: '客户A' },
  { value: '2', label: '客户B' },
  { value: '3', label: '客户C' },
];

const obj = reactive({
  showSearch: true, //显示搜索条件
  loading: false, //加载中
  single: true, //是否单选
  multiple: true, //是否多选
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    companyName: null,
    bankName: null,
    accountType: null,
  }, //查询表单

  total: 0, //总条数
  tableData: [
    {
      id: 1,
      companyName: '北京总公司',
      bankName: '中国工商银行',
      openBank: '工商银行北京分行',
      openLocation: '北京市',
      bankAccount: '6222021234567890123',
      accountFullName: '北京总公司',
      accountType: '1',
      customerName: '客户A'
    },
    {
      id: 2,
      companyName: '上海分公司',
      bankName: '中国农业银行',
      openBank: '农业银行上海分行',
      openLocation: '上海市',
      bankAccount: '6228481234567890123',
      accountFullName: '上海分公司',
      accountType: '1',
      customerName: '客户B'
    },
    {
      id: 3,
      companyName: '广州分公司',
      bankName: '中国建设银行',
      openBank: '建设银行广州分行',
      openLocation: '广州市',
      bankAccount: '6227001234567890123',
      accountFullName: '广州分公司',
      accountType: '2',
      customerName: '客户C'
    }
  ], //列表
  dialogShow: false, //弹出框
  dialogForm: {}, //表单
  title: '',//标题

  isSpecial: false,//是否特殊
});

/** 列表 */
function getList() {
  obj.loading = true;
  listImport(obj.queryParams).then(response => {
    obj.total = response.total;
    obj.loading = false;
  });
}

/** 表单重置 */
function reset() {
  obj.dialogForm = {}
  obj.isDetails = false
  proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  obj.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
  // location.reload()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  obj.ids = selection.map((item) => item.id);
  obj.single = selection.length != 1;
  obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  obj.title = '新增'
  obj.dialogShow = true
}

/** 修改按钮操作 */
function handleEdit() {
  reset()
  obj.title = '修改'
  obj.dialogShow = true
}

/** 新增公司特殊银行信息按钮操作 */
function handleAdd_special() {
  reset()
  obj.isSpecial = true
  obj.title = '新增公司特殊银行信息'
  obj.dialogShow = true
}

/** 修改公司特殊银行信息按钮操作 */
function handleEdit_special() {
  reset()
  obj.isSpecial = true
  obj.title = '修改公司特殊银行信息'
  obj.dialogShow = true
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      if (obj.dialogForm.id != null) {
        updateClassify(obj.dialogForm).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          obj.dialogShow = false;
          getList();
        });
      } else {
        addClassify(obj.dialogForm).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          obj.dialogShow = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row?.id || obj.ids;
  if (!_ids || (_ids.length === 0 && !row)) {
    proxy.$modal.msgWarning('请选择要删除的数据');
    return;
  }
  proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
    return delClassify(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
    proxy.$modal.msgWarning("用户取消删除");
  });
}

getList();
</script>
<style lang="scss" scoped></style>
