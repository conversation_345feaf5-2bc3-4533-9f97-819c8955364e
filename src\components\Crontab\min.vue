<template>
    <el-form>
        <el-form-item>
            <el-radio v-model='radioValue' :value="1">
                分钟，允许的通配符[, - * /]
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="2">
                周期从
                <el-input-number v-model='cycle01' :min="0" :max="58" /> -
                <el-input-number v-model='cycle02' :min="cycle01 + 1" :max="59" /> 分钟
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="3">
                从
                <el-input-number v-model='average01' :min="0" :max="58" /> 分钟开始， 每
                <el-input-number v-model='average02' :min="1" :max="59 - average01" /> 分钟执行一次
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="4">
                指定
                <el-select clearable v-model="checkboxList" placeholder="可多选" multiple :multiple-limit="10">
                    <el-option v-for="item in 60" :key="item" :label="item - 1" :value="item - 1" />
                </el-select>
            </el-radio>
        </el-form-item>
    </el-form>
</template>
<script setup>
const emit = defineEmits(['update'])
const props = defineProps({
    cron: {
        type: Object,
        default: {
            second: "*",
            min: "*",
            hour: "*",
            day: "*",
            month: "*",
            week: "?",
            year: "",
        }
    },
    check: {
        type: Function,
        default: () => {
        }
    }
})
const radioValue = ref(1)
const cycle01 = ref(0)
const cycle02 = ref(1)
const average01 = ref(0)
const average02 = ref(1)
const checkboxList = ref([])
const checkCopy = ref([0])
const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, 0, 58)
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, 59)
    return cycle01.value + '-' + cycle02.value
})
const averageTotal = computed(() => {
    average01.value = props.check(average01.value, 0, 58)
    average02.value = props.check(average02.value, 1, 59 - average01.value)
    return average01.value + '/' + average02.value
})
const checkboxString = computed(() => {
    return checkboxList.value.join(',')
})
watch(() => props.cron.min, value => changeRadioValue(value))
watch([radioValue, cycleTotal, averageTotal, checkboxString], () => onRadioChange())
/**
 * 根据不同的输入值类型更新radioValue值
 * 此函数解释了如何根据输入字符串的不同格式来更新radioValue以及相关的周期或平均值
 * @param {string} value - 输入的字符串值，用于决定radioValue的值和更新其他相关数据
 */
function changeRadioValue(value) {
    // 当输入值为'*'时，设置radioValue为1
    if (value === '*') {
        radioValue.value = 1
    } else if (value.indexOf('-') > -1) {
        // 当输入值包含'-'时，表示这是一个范围值
        // 将输入值分割为起始和结束值，并设置相应的周期值
        const indexArr = value.split('-')
        cycle01.value = Number(indexArr[0])
        cycle02.value = Number(indexArr[1])
        radioValue.value = 2
    } else if (value.indexOf('/') > -1) {
        // 当输入值包含'/'时，表示这是一个平均值
        // 将输入值分割为分子和分母，并设置相应的平均值
        const indexArr = value.split('/')
        average01.value = Number(indexArr[0])
        average02.value = Number(indexArr[1])
        radioValue.value = 3
    } else {
        // 当输入值是逗号分隔的数字列表时
        // 将输入值转换为唯一的数字数组，并设置radioValue为4
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))]
        radioValue.value = 4
    }
}
/**
 * 当单选按钮值发生变化时调用此函数
 * 根据不同的单选按钮值执行相应的操作
 */
function onRadioChange() {
    // 根据单选按钮的值进行不同的操作
    switch (radioValue.value) {
        case 1:
            // 当单选按钮值为1时，调用emit函数更新数据，参数为'min', '*', 'min'
            emit('update', 'min', '*', 'min')
            break
        case 2:
            // 当单选按钮值为2时，调用emit函数更新数据，参数为'min', cycleTotal.value, 'min'
            emit('update', 'min', cycleTotal.value, 'min')
            break
        case 3:
            // 当单选按钮值为3时，调用emit函数更新数据，参数为'min', averageTotal.value, 'min'
            emit('update', 'min', averageTotal.value, 'min')
            break
        case 4:
            // 当单选按钮值为4时，根据复选框列表的长度决定如何更新数据
            if (checkboxList.value.length === 0) {
                // 如果复选框列表为空，则向列表中添加第一个复选框的值
                checkboxList.value.push(checkCopy.value[0])
            } else {
                // 如果复选框列表不为空，则将复选框列表的值赋给checkCopy
                checkCopy.value = checkboxList.value
            }
            // 调用emit函数更新数据，参数为'min', checkboxString.value, 'min'
            emit('update', 'min', checkboxString.value, 'min')
            break
    }
}
</script>

<style lang="scss" scoped>
.el-input-number--small,
.el-select,
.el-select--small {
    margin: 0 0.2rem;
}

.el-select,
.el-select--small {
    width: 19.8rem;
}
</style>