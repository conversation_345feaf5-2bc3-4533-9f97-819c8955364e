<!-- 非社保供应商管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称/联系人:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="供应商/联系人" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="负责客服:" prop="province">
                <el-select class="width220" v-model="obj.dialogForm.type" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="状态:" prop="city">
                <el-select class="width220" v-model="obj.dialogForm.type" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Open" @click="handleEnable">启用</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="TurnOff" @click="handleForbidden">禁用</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="SwitchButton" :disabled="obj.multiple"
                    @click="handleDelete">暂停</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商名称" align="center" prop="name" />
            <el-table-column label="供应商类型" align="center" prop="type">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.type" />
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column label="联系人" align="center" prop="contact">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.birthdate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="联系方式" align="center" prop="phone">
                <template #default="scope">
                    <dict-tag :options="sys_job_group" :value="scope.row.isActive" />
                </template>
            </el-table-column>
            <el-table-column label="邮箱" align="center" prop="email">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.birthdate, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="地址" align="center" prop="address">
                <template #default="scope">
                    <dict-tag :options="sys_job_group" :value="scope.row.isActive" />
                </template>
            </el-table-column>
            <el-table-column label="注册时间" align="center" prop="registerTime">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.registerTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="采购负责人" align="center" prop="purchaseManager">
                <template #default="scope">
                    <dict-tag :options="sys_job_group" :value="scope.row.purchaseManager" />
                </template>
            </el-table-column>
            <el-table-column label="客服负责人" align="center" prop="serviceManager">
                <template #default="scope">
                    <dict-tag :options="sys_job_group" :value="scope.row.serviceManager" />
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"></el-button>
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="20%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item label="供应商名称：" prop="name">
                    <el-input :disabled="obj.isDetail" style="width: 100%;" v-model="obj.dialogForm.name"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="供应商类型：" prop="type">
                    <el-select :disabled="obj.isDetail" style="width: 100%;" v-model="obj.dialogForm.type"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.code" :label="item.label" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="联系人：" prop="contact">
                    <el-input :disabled="obj.isDetail" style="width: 100%;" v-model="obj.dialogForm.contact"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="联系电话：" prop="phone">
                    <el-input :disabled="obj.isDetail" style="width: 100%;" v-model="obj.dialogForm.phone"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="供应商地址：" prop="address">
                    <el-input :disabled="obj.isDetail" style="width: 100%;" v-model="obj.dialogForm.address"
                        placeholder="请输入" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="NonSocialSecurity">

import { listInstance, delInstance } from '@/api/reonApi/instance';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');


const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        type: null,
        contact: null,
        phone: null,
        address: null,
    },//查询表单
    rules: {
        name: [
            { required: true, message: "供应商名称不能为空", trigger: "blur" }
        ],
        type: [
            { required: true, message: "供应商类型不能为空", trigger: "blur" }
        ],
        contact: [
            { required: true, message: "联系人不能为空", trigger: "blur" }
        ],
        phone: [
            { required: true, message: "联系电话不能为空", trigger: "blur" }
        ],
        address: [
            { required: true, message: "供应商地址不能为空", trigger: "blur" }
        ],
    },
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            name: '北京非社保科技有限公司',
            type: '1',
            status: '1',
            contact: '王经理',
            phone: '13800138000',
            email: '<EMAIL>',
            address: '北京市海淀区中关村软件园',
            registerTime: '2021-01-01 10:00:00',
            purchaseManager: '李经理',
            serviceManager: '张经理',
            username: '1',
            birthdate: '2021-01-01',
            isActive: '1'
        },
        {
            id: 2,
            name: '上海非社保信息技术有限公司',
            type: '2',
            status: '0',
            contact: '张总监',
            phone: '13900139000',
            email: '<EMAIL>',
            address: '上海市浦东新区张江高科技园区',
            registerTime: '2021-02-01 11:00:00',
            purchaseManager: '王经理',
            serviceManager: '赵经理',
            username: '0',
            birthdate: '2021-02-01',
            isActive: '0'
        },
        {
            id: 3,
            name: '广州非社保数字科技有限公司',
            type: '1',
            status: '1',
            contact: '刘总监',
            phone: '13700137000',
            email: '<EMAIL>',
            address: '广州市天河区珠江新城',
            registerTime: '2021-03-01 12:00:00',
            purchaseManager: '张经理',
            serviceManager: '王经理',
            username: '1',
            birthdate: '2021-03-01',
            isActive: '1'
        },
        {
            id: 4,
            name: '深圳非社保智能科技有限公司',
            type: '2',
            status: '0',
            contact: '陈总监',
            phone: '13600136000',
            email: '<EMAIL>',
            address: '深圳市南山区科技园',
            registerTime: '2021-04-01 13:00:00',
            purchaseManager: '赵经理',
            serviceManager: '刘经理',
            username: '0',
            birthdate: '2021-04-01',
            isActive: '0'
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //新增、修改弹出框
    ids: [],//选中的id
    title: "",//标题
    isDetail: false,//是否查看
})

/** 列表 */
function getList() {
    obj.loading = true;
    listInstance(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


// 表单重置
function reset() {
    obj.dialogForm = {};
    obj.isDetail = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    console.log(obj.queryParams);
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增报价单";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    if (row instanceof Event) {
        obj.tableData.forEach((i) => {
            if (i.id == obj.ids) {
                obj.dialogForm = proxy.lodash.cloneDeep(i);
            }
        });
    } else {
        obj.dialogForm = proxy.lodash.cloneDeep(row);
    }
    obj.dialogShow = true;
    return
    reset();
    const _id = row.id || obj.ids
    getUsers(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改报价单";
    });
}

/** 详情按钮操作 */
function handleDetail(row) {
    reset();
    obj.dialogShow = true;
    obj.isDetail = true;
    obj.title = "详情";
}

// 启用
function handleEnable() {
    proxy.$modal.confirm('是否确认启用？').then(function () {
        return delUsers(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("启用成功");
    }).catch(() => { });
}
// 禁用
function handleForbidden() {
    proxy.$modal.confirm('是否确认禁用？').then(function () {
        return delUsers(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("禁用成功");
    }).catch(() => { });
}


/** 提交按钮 */
function submitForm() {
    console.log(obj.dialogForm);
    return
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

getList();
</script>
<style lang="scss" scoped></style>