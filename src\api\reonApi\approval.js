import request from "@/utils/request";

// 查询供应商合同审批流程列表
export function listApproval(query) {
  return request({
    url: "/system/approval/list",
    method: "get",
    params: query,
  });
}

// 查询供应商合同审批流程详细
export function getApproval(id) {
  return request({
    url: "/system/approval/" + id,
    method: "get",
  });
}

// 新增供应商合同审批流程
export function addApproval(data) {
  return request({
    url: "/system/approval",
    method: "post",
    data: data,
  });
}

// 修改供应商合同审批流程
export function updateApproval(data) {
  return request({
    url: "/system/approval",
    method: "put",
    data: data,
  });
}

// 删除供应商合同审批流程
export function delApproval(id) {
  return request({
    url: "/system/approval/" + id,
    method: "delete",
  });
}
