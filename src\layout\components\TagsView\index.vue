<template>
  <div id="tags-view-container" class="tags-view-container">
    <scroll-pane ref="scrollPaneRef" class="tags-view-wrapper" @scroll="handleScroll">
      <router-link v-for="tag in visitedViews" :key="tag.path" :data-path="tag.path"
        :class="isActive(tag) ? 'active' : ''" :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        class="tags-view-item" :style="activeStyle(tag)" @click.middle="!isAffix(tag) ? closeSelectedTag(tag) : ''"
        @contextmenu.prevent="openMenu(tag, $event)">
        {{ tag.title }}
        <span v-if="!isAffix(tag)" @click.prevent.stop="closeSelectedTag(tag)">
          <close class="el-icon-close" style="width: 1em; height: 1em;vertical-align: middle;" />
        </span>
      </router-link>
    </scroll-pane>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">
        <refresh-right style="width: 1em; height: 1em;" /> 刷新页面
      </li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">
        <close style="width: 1em; height: 1em;" /> 关闭当前
      </li>
      <li @click="closeOthersTags">
        <circle-close style="width: 1em; height: 1em;" /> 关闭其他
      </li>
      <li v-if="!isFirstView()" @click="closeLeftTags">
        <back style="width: 1em; height: 1em;" /> 关闭左侧
      </li>
      <li v-if="!isLastView()" @click="closeRightTags">
        <right style="width: 1em; height: 1em;" /> 关闭右侧
      </li>
      <li @click="closeAllTags(selectedTag)">
        <circle-close style="width: 1em; height: 1em;" /> 全部关闭
      </li>
    </ul>
  </div>
</template>

<script setup>
import ScrollPane from './ScrollPane'
import { getNormalPath } from '@/utils/ruoyi'
import useTagsViewStore from '@/store/modules/tagsView'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

const visible = ref(false);
const top = ref(0);
const left = ref(0);
const selectedTag = ref({});
const affixTags = ref([]);
const scrollPaneRef = ref(null);

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const visitedViews = computed(() => useTagsViewStore().visitedViews);
const routes = computed(() => usePermissionStore().routes);
const theme = computed(() => useSettingsStore().theme);

watch(route, () => {
  addTags()
  moveToCurrentTag()
})

watch(visible, (value) => {
  if (value) {
    document.body.addEventListener('click', closeMenu)
  } else {
    document.body.removeEventListener('click', closeMenu)
  }
})

onMounted(() => {
  initTags()
  addTags()
})

/**
 * 检查给定路由是否为当前活动路由
 * 
 * @param {Object} r - 待检查的路由对象，包含路径信息
 * @returns {Boolean} - 如果给定路由的路径与当前路由的路径匹配，则返回true，表示该路由是活动的；否则返回false
 */
function isActive(r) {
  return r.path === route.path
}

/**
 * 根据标签的活动状态返回相应的样式
 * 
 * 此函数用于根据传入的标签对象判断其是否处于活动状态，如果是，则返回特定的样式；
 * 否则，返回一个空样式对象此函数主要用于在界面上动态改变标签的外观，
 * 以反映其是否被激活或选中
 * 
 * @param {Object} tag - 标签对象，包含标签的相关信息和状态
 * @returns {Object} 样式对象如果标签处于活动状态，返回包含背景色和边框色的样式对象；
 * 否则，返回一个空的样式对象
 */
function activeStyle(tag) {
  // 检查标签是否处于活动状态，如果不是，直接返回一个空的样式对象
  if (!isActive(tag)) return {};

  // 返回包含活动状态下标签的背景色和边框色的样式对象
  return {
    "background-color": theme.value,
    "border-color": theme.value
  };
}

/**
 * 判断标签是否为固定标签
 * 
 * 此函数通过检查标签的元数据中是否包含 affix 属性，来确定标签是否应被固定
 * 它用于在某些场景下需要区分标签是否具有固定性质，以便进行相应的处理
 * 
 * @param {Object} tag - 待检查的标签对象
 * @returns {Boolean} 如果标签为固定标签，则返回 true；否则返回 false
 */
function isAffix(tag) {
  return tag.meta && tag.meta.affix
}

/**
 * 判断当前视图是否是首次访问的视图
 * 
 * 此函数尝试通过比较当前选中的标签的完整路径来确定当前视图是否是首页
 * 或者是否与访问过的视图列表中的第二个视图的完整路径相匹配
 * 这种方法用于在某些特定场景下，如用户导航或页面加载时，判断用户是否处于初始访问状态
 * 
 * @returns {boolean} 如果当前视图是首次访问的视图，则返回true；否则返回false
 *                    如果在执行过程中捕获到错误，也会返回false
 */
function isFirstView() {
  try {
    // 检查当前选中的标签是否指向首页，或者与访问过的第二个视图路径匹配
    return selectedTag.value.fullPath === '/index' || selectedTag.value.fullPath === visitedViews.value[1].fullPath
  } catch (err) {
    // 在执行路径比较时，如果发生错误（例如访问不存在的属性），则捕获错误并返回false
    return false
  }
}

/**
 * 判断当前选中的标签是否为最后一个访问的视图
 * 
 * 此函数尝试比较当前选中的标签的完整路径(selectedTag.value.fullPath)
 * 与最后一个访问的视图的完整路径(visitedViews.value[visitedViews.value.length - 1].fullPath)
 * 如果相等，则返回true，表示当前选中的标签是最后一个访问的视图
 * 如果不相等或在比较过程中出现错误，则捕获错误并返回false
 * 
 * @returns {boolean} 如果当前选中的标签是最后一个访问的视图，则返回true；否则返回false
 */
function isLastView() {
  try {
    return selectedTag.value.fullPath === visitedViews.value[visitedViews.value.length - 1].fullPath
  } catch (err) {
    return false
  }
}

/**
 * 过滤出需要固定在标签页中的路由
 * @param {Array} routes - 路由配置数组
 * @param {string} basePath - 基础路径，默认为空字符串
 * @returns {Array} - 返回过滤后的标签页数组
 */
function filterAffixTags(routes, basePath = '') {
  let tags = []
  // 遍历路由配置数组
  routes.forEach(route => {
    // 检查路由是否具有meta属性且设置为affix，以确定是否需要固定在标签页中
    if (route.meta && route.meta.affix) {
      // 拼接基础路径和当前路由路径，并规范化路径
      const tagPath = getNormalPath(basePath + '/' + route.path)
      // 将符合条件的路由信息添加到标签页数组中
      tags.push({
        fullPath: tagPath,
        path: tagPath,
        name: route.name,
        meta: { ...route.meta }
      })
    }
    // 如果当前路由有子路由，则递归调用filterAffixTags函数处理子路由
    if (route.children) {
      const tempTags = filterAffixTags(route.children, route.path)
      // 如果子路由中存在需要固定的标签页，则将其合并到当前标签页数组中
      if (tempTags.length >= 1) {
        tags = [...tags, ...tempTags]
      }
    }
  })
  // 返回最终过滤后的标签页数组
  return tags
}

/**
 * 初始化标签视图
 * 该函数用于初始化标签视图，确保固定标签（affix tags）在页面或组件加载时正确设置。
 */
function initTags() {
  // 过滤出需要固定的标签
  const res = filterAffixTags(routes.value);

  // 将过滤后的标签赋值给 affixTags
  affixTags.value = res;

  // 遍历过滤后的标签列表
  for (const tag of res) {
    // 如果标签有名称，则将其添加到已访问的视图列表中
    if (tag.name) {
      useTagsViewStore().addVisitedView(tag)
    }
  }
}

/**
 * 添加标签函数
 * 
 * 此函数用于根据当前路由信息添加标签视图到缓存中如果路由包含特定的iframe链接，
 * 它还会添加iframe视图此函数确保只有具有特定名称和可能的iframe链接的路由
 * 会被添加到标签视图存储中
 * 
 * @returns {boolean} 总是返回false，表示函数执行完毕
 */
function addTags() {
  // 获取当前路由的名称
  const { name } = route
  // 检查路由名称是否存在
  if (name) {
    // 使用TagsViewStore添加当前视图到缓存中
    useTagsViewStore().addView(route)
    // 检查路由元数据中是否包含iframe链接信息
    if (route.meta.link) {
      // 使用TagsViewStore添加iframe视图到缓存中
      useTagsViewStore().addIframeView(route);
    }
  }
  // 函数执行完毕，返回false
  return false
}

/**
 * 移动到当前标签页的位置
 * 
 * 该函数在路由变更后执行，目的是在标签页列表中滚动到当前激活的标签页
 * 它首先等待DOM更新完成，然后遍历所有访问过的视图，找到匹配当前路由路径的视图
 * 如果找到匹配项，它将调用scrollPaneRef的moveToTarget方法来滚动到该视图
 * 如果视图的完整路径与当前路由的完整路径不匹配，表明有查询参数或哈希值的差异，
 * 此时更新该视图的访问记录以反映最新的路由状态
 */
function moveToCurrentTag() {
  nextTick(() => {
    for (const r of visitedViews.value) {
      if (r.path === route.path) {
        scrollPaneRef.value.moveToTarget(r);
        if (r.fullPath !== route.fullPath) {
          useTagsViewStore().updateVisitedView(route)
        }
      }
    }
  })
}

/**
 * 刷新选定的标签页
 * 此函数用于刷新当前视图，如果当前视图为iframe形式，则将其从标签视图存储中删除
 * 
 * @param {Object} view - 当前视图对象，用于刷新页面
 */
function refreshSelectedTag(view) {
  // 刷新当前视图页面
  proxy.$tab.refreshPage(view);

  // 如果当前视图是通过链接打开的，则删除该视图的iframe版本
  if (route.meta.link) {
    useTagsViewStore().delIframeView(route);
  }
}

/**
 * 关闭选中的标签页
 * 
 * 此函数通过调用 `proxy.$tab.closePage` 方法关闭指定的视图标签页，并在当前视图被关闭时，
 * 自动导航到最近访问的视图
 * 
 * @param {Object} view - 要关闭的视图对象
 */
function closeSelectedTag(view) {
  // 关闭指定的视图标签页，并获取更新后的访问视图列表
  proxy.$tab.closePage(view).then(({ visitedViews }) => {
    // 如果当前关闭的视图是激活状态，则导航到最近访问的视图
    if (isActive(view)) {
      toLastView(visitedViews, view)
    }
  })
}

/**
 * 关闭右侧标签页
 * 
 * 此函数通过调用 `closeRightPage` 方法来关闭当前选中标签右侧的所有标签页
 * 如果关闭后剩余的标签页中没有当前路由对应的标签，則跳转到最后一个访问的视图
 */
function closeRightTags() {
  // 关闭右侧标签页并获取更新后的访问视图列表
  proxy.$tab.closeRightPage(selectedTag.value).then(visitedViews => {
    // 检查更新后的访问视图列表中是否存在当前路由对应的标签
    if (!visitedViews.find(i => i.fullPath === route.fullPath)) {
      // 如果不存在，则跳转到最后一个访问的视图
      toLastView(visitedViews)
    }
  })
}

/**
 * 关闭左侧标签页
 * 
 * 此函数通过调用 `closeLeftPage` 方法来关闭当前选中标签页左侧的所有标签页
 * 如果关闭标签页后，当前路由不再存在于剩余的访问视图中，则调用 `toLastView` 方法跳转到最后一个视图
 */
function closeLeftTags() {
  // 关闭左侧的所有标签页，并在成功后处理剩余的访问视图
  proxy.$tab.closeLeftPage(selectedTag.value).then(visitedViews => {
    // 检查剩余的访问视图中是否包含当前路由
    if (!visitedViews.find(i => i.fullPath === route.fullPath)) {
      // 如果不包含，则跳转到最后一个视图
      toLastView(visitedViews)
    }
  })
}

/**
 * 关闭其他标签页
 * 此函数首先导航到当前选中的标签页，然后关闭所有其他标签页，最后将视口移动到当前标签页
 * 它利用了路由导航和标签页管理的功能
 */
function closeOthersTags() {
  // 导航到当前选中的标签页，忽略任何导航错误
  router.push(selectedTag.value).catch(() => { });

  // 关闭其他标签页，这是一个异步操作
  proxy.$tab.closeOtherPage(selectedTag.value).then(() => {
    // 关闭其他标签页后，移动视口到当前标签页
    moveToCurrentTag()
  })
}

/**
 * 关闭所有标签页
 * 此函数通过调用`proxy.$tab.closeAllPage`方法关闭所有标签页，并根据访问记录重新定向到最近访问的标签页
 * 如果当前路由是固定标签，函数将不会执行任何操作
 * 
 * @param {Object} view - 当前视图对象，用于获取路由信息
 */
function closeAllTags(view) {
  // 关闭所有标签页并获取更新后的访问视图列表
  proxy.$tab.closeAllPage().then(({ visitedViews }) => {
    // 检查当前路由是否为固定标签
    if (affixTags.value.some(tag => tag.path === route.path)) {
      return
    }
    // 调用函数跳转到最后一个访问的视图
    toLastView(visitedViews, view)
  })
}

/**
 * 导航到最近访问的视图
 * @param {Array} visitedViews - 用户访问过的视图列表
 * @param {Object} view - 当前视图对象
 */
function toLastView(visitedViews, view) {
  // 获取最新访问的视图
  const latestView = visitedViews.slice(-1)[0]
  // 如果存在最新访问的视图，则导航到该视图
  if (latestView) {
    router.push(latestView.fullPath)
  } else {
    // 如果不存在最新访问的视图，且当前视图为'Dashboard'，则重定向到特定路径
    if (view.name === 'Dashboard') {
      router.replace({ path: '/redirect' + view.fullPath })
    } else {
      // 否则，导航到根路径
      router.push('/')
    }
  }
}

/**
 * 打开菜单
 * @param {string} tag - 标签名称
 * @param {Object} e - 事件对象
 */
function openMenu(tag, e) {
  // 定义菜单最小宽度
  const menuMinWidth = 105
  // 获取元素相对于视口的左边界距离
  const offsetLeft = proxy.$el.getBoundingClientRect().left
  // 获取元素的宽度
  const offsetWidth = proxy.$el.offsetWidth
  // 计算菜单可以达到的最大左边位置
  const maxLeft = offsetWidth - menuMinWidth
  // 计算鼠标点击位置距离元素左边界的距离
  const l = e.clientX - offsetLeft + 15

  // 如果计算的左边位置大于最大左边位置，则设置为最大左边位置，否则设置为计算的左边位置
  if (l > maxLeft) {
    left.value = maxLeft
  } else {
    left.value = l
  }

  // 设置菜单的顶部位置
  top.value = e.clientY
  // 设置菜单可见
  visible.value = true
  // 设置选中的标签
  selectedTag.value = tag
}

function closeMenu() {
  visible.value = false
}

function handleScroll() {
  closeMenu()
}
</script>

<style lang='scss' scoped>
.tags-view-container {
  height: 34px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);

  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      border: 1px solid #d8dce5;
      color: #495060;
      background: #fff;
      padding: 0 8px;
      font-size: 12px;
      margin-left: 5px;
      margin-top: 4px;

      &:first-of-type {
        margin-left: 15px;
      }

      &:last-of-type {
        margin-right: 15px;
      }

      &.active {
        background-color: #42b983;
        color: #fff;
        border-color: #42b983;

        &::before {
          content: "";
          background: #fff;
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: relative;
          margin-right: 5px;
        }
      }
    }
  }

  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }
}
</style>

<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .el-icon-close {
      width: 16px;
      height: 16px;
      vertical-align: 2px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;

      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -3px;
      }

      &:hover {
        background-color: #b4bccc;
        color: #fff;
        width: 12px !important;
        height: 12px !important;
      }
    }
  }
}
</style>