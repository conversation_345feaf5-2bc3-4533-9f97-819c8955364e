<!-- 接单确认员工离职 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="160px">
            <el-form-item label="接单客服:" prop="receiverService">
                <el-select class="width220" v-model="obj.queryParams.receiverService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方:" prop="orderReceiver">
                <el-select class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请选择" clearable>
                    <el-option v-for="item in receiverList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单方:" prop="orderSender">
                <el-select class="width220" v-model="obj.queryParams.orderSender" placeholder="请选择" clearable>
                    <el-option v-for="item in senderList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单方客服:" prop="senderService">
                <el-select class="width220" v-model="obj.queryParams.senderService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-select class="width220" v-model="obj.queryParams.smallContractName" placeholder="请选择" clearable>
                    <el-option v-for="item in smallContractList" :key="item.code" :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="人员分布:" prop="personnelDistribution">
                <el-select class="width220" v-model="obj.queryParams.personnelDistribution" placeholder="请选择" clearable>
                    <el-option v-for="item in distributionList" :key="item.code" :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请离职日期(起):" prop="applyLeaveDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.applyLeaveDateStart" type="date"
                    placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="申请离职日期(止):" prop="applyLeaveDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.applyLeaveDateEnd" type="date"
                    placeholder="请选择日期" />
            </el-form-item>
            <el-form-item label="小合同编号:" prop="smallContractCode">
                <el-input class="width220" v-model="obj.queryParams.smallContractCode" placeholder="请输入小合同编号" />
            </el-form-item>
            <el-form-item label="是否单立户:" prop="isSingleAccount">
                <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleDetail">订单详情</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleConfirm">进入确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleBatchConfirm">批量确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleReject">离职驳回</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleSave">保存</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员编号" align="center" prop="employeeCode" />
            <el-table-column label="订单编号" align="center" prop="orderCode" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="人员分布" align="center" prop="personnelDistribution" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="是否单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="派单方" align="center" prop="orderSender" />
            <el-table-column label="派单方客服" align="center" prop="senderService" />
            <el-table-column label="接单方" align="center" prop="orderReceiver" />
            <el-table-column label="接单方客服" align="center" prop="receiverService" />
            <el-table-column label="入职日期" align="center" prop="entryDate" />
            <el-table-column label="申请入职时间" align="center" prop="applyEntryDate" />
            <el-table-column label="入离职状态" align="center" prop="employmentStatus" />
            <el-table-column label="离职确认状态" align="center" prop="leaveConfirmStatus" />
            <el-table-column label="离职日期" align="center" prop="leaveDate" />
            <el-table-column label="申请离职时间" align="center" prop="applyLeaveDate" />
            <el-table-column label="离职原因" align="center" prop="leaveReason" />
            <el-table-column label="联系电话" align="center" prop="telephone" />
            <el-table-column label="手机号" align="center" prop="mobile" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 订单详情 -->
        <order-details v-model:dialogShow="obj.dialogShow" :dialogForm="obj.dialogForm" :title="obj.title"
            isDetail="true" />



        <!-- 进入确认 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="60%" append-to-body draggable>
            <el-form :model="obj.dialogForm" inline label-width="auto">
                <el-form-item label="雇员编号" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="雇员姓名" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="客户编号" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="客户名称" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="小合同" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="证件编号" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="派单方" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="接单方" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="派单方客服" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="接单方客服" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="订单状态" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="是否离职外呼" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="入职日期" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="申报入职时间" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="离职日期" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="申报离职日期" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="申报离职人" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="离职原因" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="减员原因" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="减员详细原因" prop="typeCode">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>

                <el-form-item label="离职备注" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="离职确认过程" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="变更确认过程" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="接单备注" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="变更备注" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入" />
                </el-form-item>
                <border-box title="产品">
                    <el-table :data="obj.dialogForm.productList" border>
                        <el-table-column label="序号" prop="id" />
                        <el-table-column label="产品名称" prop="name" />
                        <el-table-column label="社保公积金" prop="code" />
                        <el-table-column label="收费开始月" prop="type" />
                        <el-table-column label="收费截止月" prop="status" />
                    </el-table>
                </border-box>
                <el-form-item label="备注" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入内容" />
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";
import orderDetails from '../components/dialog/orderDetails.vue';
import BorderBox from '../components/borderBox.vue';

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 客服列表
const serviceList = ref([]);
// 派单方列表
const senderList = ref([]);
// 接单方列表
const receiverList = ref([]);
// 小合同列表
const smallContractList = ref([]);
// 人员分布列表
const distributionList = ref([]);

// 表单验证规则
const rules = {
    // 可以根据需要添加验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        receiverService: null,
        orderReceiver: null,
        orderSender: null,
        senderService: null,
        smallContractName: null,
        personnelDistribution: null,
        applyLeaveDateStart: null,
        applyLeaveDateEnd: null,
        smallContractCode: null,
        isSingleAccount: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {
        productList: []
    },//导入表单
    dialogShow: false,//导入弹窗
    dialogShow2: false,//导入弹窗
    bindSave: false,//绑定保存
    ids: [],//选中的id
    title: "",//标题
})

/** 获取客服列表 */
function getServiceList() {
    // 这里可以调用API获取客服列表
    serviceList.value = [
        { code: '1', name: '客服1' },
        { code: '2', name: '客服2' },
        { code: '3', name: '客服3' }
    ];
}

/** 获取派单方列表 */
function getSenderList() {
    // 这里可以调用API获取派单方列表
    senderList.value = [
        { code: '1', name: '派单方1' },
        { code: '2', name: '派单方2' },
        { code: '3', name: '派单方3' }
    ];
}

/** 获取接单方列表 */
function getReceiverList() {
    // 这里可以调用API获取接单方列表
    receiverList.value = [
        { code: '1', name: '接单方1' },
        { code: '2', name: '接单方2' },
        { code: '3', name: '接单方3' }
    ];
}

/** 获取小合同列表 */
function getSmallContractList() {
    // 这里可以调用API获取小合同列表
    smallContractList.value = [
        { code: '1', name: '小合同1' },
        { code: '2', name: '小合同2' },
        { code: '3', name: '小合同3' }
    ];
}

/** 获取人员分布列表 */
function getDistributionList() {
    // 这里可以调用API获取人员分布列表
    distributionList.value = [
        { code: '1', name: '分布1' },
        { code: '2', name: '分布2' },
        { code: '3', name: '分布3' }
    ];
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeCode: 'YG20230001',
                orderCode: 'DD20230001',
                employeeName: '雇员姓名1',
                idNumber: '110101199001011234',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                personnelDistribution: '分布1',
                smallContractName: '小合同名称1',
                isSingleAccount: 'Y',
                orderSender: '派单方1',
                senderService: '客服1',
                orderReceiver: '接单方1',
                receiverService: '客服2',
                entryDate: '2023-01-01',
                applyEntryDate: '2022-12-15',
                employmentStatus: '在职',
                leaveConfirmStatus: '待确认',
                leaveDate: '2023-06-01',
                applyLeaveDate: '2023-05-15',
                leaveReason: '个人原因',
                telephone: '010-********',
                mobile: '***********'
            },
            {
                id: 2,
                employeeCode: 'YG20230002',
                orderCode: 'DD20230002',
                employeeName: '雇员姓名2',
                idNumber: '110101199001011235',
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                personnelDistribution: '分布2',
                smallContractName: '小合同名称2',
                isSingleAccount: 'N',
                orderSender: '派单方2',
                senderService: '客服2',
                orderReceiver: '接单方2',
                receiverService: '客服3',
                entryDate: '2023-02-01',
                applyEntryDate: '2023-01-15',
                employmentStatus: '在职',
                leaveConfirmStatus: '待确认',
                leaveDate: '2023-07-01',
                applyLeaveDate: '2023-06-15',
                leaveReason: '公司原因',
                telephone: '010-********',
                mobile: '***********'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 订单详情
function handleDetail(row) {
    obj.dialogShow = true;
    obj.title = '订单详情';
    if (row) {
        // 如果是从表格行点击进入，可以加载该行的数据
        obj.dialogForm = { ...row };
    }
}

// 关闭订单详情
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {
        productList: []
    };
}

// 进入确认
function handleConfirm() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要确认的订单');
        return;
    }
    obj.dialogShow2 = true;
    obj.title = '离职确认';
    // 加载选中行的数据
    const selectedRow = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedRow) {
        obj.dialogForm = {
            ...selectedRow,
            productList: [
                {
                    id: 1,
                    name: '产品1',
                    code: '社保',
                    type: '2023-01',
                    status: '2023-12'
                },
                {
                    id: 2,
                    name: '产品2',
                    code: '公积金',
                    type: '2023-01',
                    status: '2023-12'
                }
            ]
        };
    }
}

// 批量确认
function handleBatchConfirm() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要批量确认的订单');
        return;
    }
    proxy.$modal.confirm('是否确认批量确认选中的订单？').then(function () {
        // 这里可以调用API进行批量确认操作
        proxy.$modal.msgSuccess("批量确认成功!");
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消操作");
    });
}

// 驳回
function handleReject() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要驳回的订单');
        return;
    }
    proxy.$modal.prompt('请输入驳回原因', '驳回原因').then(({ value }) => {
        if (value) {
            // 这里可以调用API进行驳回操作
            proxy.$modal.msgSuccess("驳回成功!");
            getList();
        } else {
            proxy.$modal.msgError('请输入驳回原因');
        }
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消操作");
    });
}

// 保存
function handleSave() {
    proxy.$modal.confirm('是否确认保存？').then(function () {
        // 这里可以调用API进行保存操作
        proxy.$modal.msgSuccess("保存成功!");
        obj.dialogShow2 = false;
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消操作");
    });
}

// 初始化数据
getServiceList();
getSenderList();
getReceiverList();
getSmallContractList();
getDistributionList();
getList();
</script>
<style lang="scss" scoped></style>