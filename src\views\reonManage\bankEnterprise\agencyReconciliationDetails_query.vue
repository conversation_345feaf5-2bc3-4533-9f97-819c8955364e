<!-- 查询代发对账明细 -->
<template>
    <div class="app-container">
        <el-tabs type="border-card">
            <el-tab-pane label="代发对账明细查询">
                <!-- 查询条件 -->
                <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
                    <el-form-item label="付款账户:" prop="paymentAccount">
                        <el-input class="width220" v-model="obj.queryParams.paymentAccount" placeholder="请输入付款账户"
                            clearable @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="开始日期:" prop="startDate">
                        <el-date-picker class="width220" v-model="obj.queryParams.startDate" type="date"
                            placeholder="请选择开始日期" clearable />
                    </el-form-item>
                    <el-form-item label="结束日期:" prop="endDate">
                        <el-date-picker class="width220" v-model="obj.queryParams.endDate" type="date"
                            placeholder="请选择结束日期" clearable />
                    </el-form-item>
                    <el-form-item label="业务类型:" prop="businessType">
                        <el-select class="width220" v-model="obj.queryParams.businessType" placeholder="请选择业务类型"
                            clearable>
                            <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="业务模式:" prop="businessMode">
                        <el-select class="width220" v-model="obj.queryParams.businessMode" placeholder="请选择业务模式"
                            clearable>
                            <el-option v-for="item in businessModeOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="收方户名:" prop="receiverName">
                        <el-input class="width220" v-model="obj.queryParams.receiverName" placeholder="请输入收方户名"
                            clearable @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="业务参考号:" prop="businessReferenceNo">
                        <el-input class="width220" v-model="obj.queryParams.businessReferenceNo" placeholder="请输入业务参考号"
                            clearable @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="最小金额:" prop="minAmount">
                        <el-input class="width220" v-model="obj.queryParams.minAmount" placeholder="请输入最小金额" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="最大金额:" prop="maxAmount">
                        <el-input class="width220" v-model="obj.queryParams.maxAmount" placeholder="请输入最大金额" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="打印模式:" prop="printMode">
                        <el-select class="width220" v-model="obj.queryParams.printMode" placeholder="请选择打印模式" clearable>
                            <el-option v-for="item in printModeOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <!-- 表格 -->
                <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="查询标记" align="center" prop="queryMark" />
                    <el-table-column label="打印任务编号" align="center" prop="printTaskNo" />
                    <el-table-column label="打印编号" align="center" prop="printNo" />
                    <el-table-column label="总笔数" align="center" prop="totalCount" />
                    <el-table-column label="付款账户" align="center" prop="paymentAccount" />
                    <el-table-column label="业务类型" align="center" prop="businessType" />
                    <el-table-column label="业务模式" align="center" prop="businessMode" />
                    <el-table-column label="交易日期" align="center" prop="transactionDate" />
                    <el-table-column label="打印时间" align="center" prop="printTime" width="180" />
                </el-table>
                <!-- 分页 -->
                <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                    v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
            </el-tab-pane>
            <el-tab-pane label="代发对账明细下载">
                <!-- 查询条件 -->
                <el-form @submit.prevent :model="obj.queryParams" ref="queryRef" inline label-width="auto">
                    <el-form-item label="打印ID:" prop="printId">
                        <el-input class="width220" v-model="obj.queryParams.printId" placeholder="请输入打印ID" clearable
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
                <!-- 表格 -->
                <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="打印编号" align="center" prop="queryMark" />
                    <el-table-column label="查询标记" align="center" prop="printTaskNo" />
                    <el-table-column label="下载路径" align="center" prop="printNo" />
                    <el-table-column label="创建人" align="center" prop="totalCount" />
                </el-table>
                <!-- 分页 -->
                <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                    v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup name="AgencyReconciliationDetails_query">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 业务类型选项
const businessTypeOptions = [
    { value: '1', label: '代发工资' },
    { value: '2', label: '其他' }
];

// 业务模式选项
const businessModeOptions = [
    { value: '1', label: '单笔代发' },
    { value: '2', label: '批量代发' }
];

// 打印模式选项
const printModeOptions = [
    { value: '1', label: '逐笔打印' },
    { value: '2', label: '批量打印' }
];



const obj = reactive({
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        paymentAccount: null,
        startDate: null,
        endDate: null,
        businessType: null,
        businessMode: null,
        receiverName: null,
        businessReferenceNo: null,
        minAmount: null,
        maxAmount: null,
        printMode: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                queryMark: 'QM001',
                printTaskNo: 'PT20230501001',
                printNo: 'P001',
                totalCount: 100,
                paymentAccount: '****************',
                businessType: '工资代发',
                businessMode: '批量代发',
                transactionDate: '2023-05-15',
                printTime: '2023-05-15 10:30:00'
            },
            {
                id: 2,
                queryMark: 'QM002',
                printTaskNo: 'PT20230502001',
                printNo: 'P002',
                totalCount: 50,
                paymentAccount: '****************',
                businessType: '报销代发',
                businessMode: '单笔代发',
                transactionDate: '2023-05-16',
                printTime: '2023-05-16 14:20:00'
            },
            {
                id: 3,
                queryMark: 'QM003',
                printTaskNo: 'PT20230503001',
                printNo: 'P003',
                totalCount: 75,
                paymentAccount: '****************',
                businessType: '税收代发',
                businessMode: '批量代发',
                transactionDate: '2023-05-17',
                printTime: '2023-05-17 09:15:00'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}





/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}


getList();
</script>
<style lang="scss" scoped></style>