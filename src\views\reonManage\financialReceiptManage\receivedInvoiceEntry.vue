<!-- 实收开票录入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerName" required>
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户付款方:" prop="customerPayer">
                <el-select class="width220" v-model="obj.queryParams.customerPayer" placeholder="请选择客户付款方" clearable>
                    <el-option label="付款方1" value="1" />
                    <el-option label="付款方2" value="2" />
                    <el-option label="付款方3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户帐套:" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户帐套" clearable>
                    <el-option label="帐套1" value="1" />
                    <el-option label="帐套2" value="2" />
                    <el-option label="帐套3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="财务应收年月(起):" prop="financialReceivableMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.financialReceivableMonthStart" type="month"
                    placeholder="请选择开始年月" clearable />
            </el-form-item>
            <el-form-item label="财务应收年月(止):" prop="financialReceivableMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.financialReceivableMonthEnd" type="month"
                    placeholder="请选择结束年月" clearable />
            </el-form-item>
            <el-form-item label="应收金额>=:" prop="receivableAmountMin">
                <el-input class="width220" v-model="obj.queryParams.receivableAmountMin" placeholder="请输入最小金额"
                    clearable />
            </el-form-item>
            <el-form-item label="应收金额<=:" prop="receivableAmountMax">
                <el-input class="width220" v-model="obj.queryParams.receivableAmountMax" placeholder="请输入最大金额"
                    clearable />
            </el-form-item>
            <el-form-item label="开票状态:" prop="invoiceStatus">
                <el-select class="width220" v-model="obj.queryParams.invoiceStatus" placeholder="请选择开票状态" clearable>
                    <el-option label="未开票" value="0" />
                    <el-option label="已开票" value="1" />
                    <el-option label="开票中" value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="核销状态:" prop="writeOffStatus">
                <el-select class="width220" v-model="obj.queryParams.writeOffStatus" placeholder="请选择核销状态" clearable>
                    <el-option label="未核销" value="0" />
                    <el-option label="已核销" value="1" />
                    <el-option label="核销中" value="2" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户帐套" align="center" prop="customerAccount" />
            <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
            <el-table-column label="应收金额" align="center" prop="receivableAmount" />
            <el-table-column label="开票状态" align="center" prop="invoiceStatus" />
            <el-table-column label="核销状态" align="center" prop="writeOffStatus" />
            <el-table-column label="未开票金额" align="center" prop="uninvoicedAmount" />
            <el-table-column label="已开票金额" align="center" prop="invoicedAmount" />
            <el-table-column label="审批中金额" align="center" prop="approvalAmount" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <div>
            <div class="flex-between-center">
                <div style="width: 59%;">
                    <el-divider content-position="left">已核销记录</el-divider>
                    <el-table border :data="obj.tableData">
                        <el-table-column label="到款日期" align="center" prop="paymentDate" />
                        <el-table-column label="到款金额" align="center" prop="paymentAmount" />
                        <el-table-column label="到款银行" align="center" prop="paymentBank" />
                        <el-table-column label="核销金额" align="center" prop="writeOffAmount" />
                        <el-table-column label="小额调整" align="center" prop="smallAdjustment" />
                        <el-table-column label="核销日期" align="center" prop="writeOffDate" />
                        <el-table-column label="审批状态" align="center" prop="writeOffStatus" />
                        <el-table-column label="核销人" align="center" prop="writeOffPerson" />
                        <el-table-column label="已开票金额" align="center" prop="invoicedAmount" />
                        <el-table-column label="审批中金额" align="center" prop="approvalAmount" />
                    </el-table>
                </div>
                <div style="width: 39%;">
                    <el-divider content-position="left">已开票记录</el-divider>
                    <el-table border :data="obj.tableData">
                        <el-table-column label="开票时间" align="center" prop="invoiceDate" />
                        <el-table-column label="开票编号" align="center" prop="invoiceNo" />
                        <el-table-column label="到款银行" align="center" prop="paymentBank" />
                        <el-table-column label="开票金额" align="center" prop="invoiceAmount" />
                        <el-table-column label="发票状态" align="center" prop="invoiceStatus" />
                        <el-table-column label="备注" align="center" prop="invoiceRemark" />
                    </el-table>
                </div>
            </div>
            <div class="mt20">
                <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
                    <el-row>
                        <el-form-item label="开票分公司:" prop="invoiceBranch">
                            <el-select class="width220" v-model="obj.invoiceForm.invoiceBranch" placeholder="请选择开票分公司"
                                clearable>
                                <el-option label="上海分公司" value="上海分公司" />
                                <el-option label="北京分公司" value="北京分公司" />
                                <el-option label="广州分公司" value="广州分公司" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="开票金额:" prop="invoiceAmount">
                            <el-input class="width220" v-model="obj.invoiceForm.invoiceAmount" placeholder="请输入开票金额"
                                clearable />
                        </el-form-item>
                        <el-form-item label="调整金额:" prop="adjustmentAmount">
                            <el-input class="width220" v-model="obj.invoiceForm.adjustmentAmount" placeholder="请输入调整金额"
                                clearable />
                        </el-form-item>
                        <el-form-item label="是否小额调整:" prop="isSmallAdjustment">
                            <el-select class="width220" v-model="obj.invoiceForm.isSmallAdjustment"
                                placeholder="请选择是否小额调整" clearable>
                                <el-option label="是" value="1" />
                                <el-option label="否" value="0" />
                            </el-select>
                        </el-form-item>
                    </el-row>
                    <el-row>
                        <el-form-item label="备注:" prop="remark">
                            <el-input class="width420" type="textarea" v-model="obj.invoiceForm.remark"
                                placeholder="请输入备注" clearable />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Tickets" @click="handleInvoiceDetail">开票明细</el-button>
                        </el-form-item>
                    </el-row>
                </el-form>
            </div>

            <InvoiceDetails v-model:dialogShow="obj.dialogShow" title="开票明细" :dialogForm="obj.dialogForm"
                :serviceFeeTableData="obj.serviceFeeTableData" :collectionTableData="obj.collectionTableData"
                :onceTableData="obj.onceTableData" @issueInvoice="handleIssueInvoice" />
        </div>
    </div>
</template>

<script setup name="ReceivedInvoiceEntry">
import InvoiceDetails from "../components/dialog/invoiceDetails.vue";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        customerPayer: null,
        customerAccount: null,
        financialReceivableMonthStart: null,
        financialReceivableMonthEnd: null,
        receivableAmountMin: null,
        receivableAmountMax: null,
        invoiceStatus: null,
        writeOffStatus: null
    },//查询表单
    invoiceForm: {
        invoiceBranch: null,
        invoiceAmount: null,
        adjustmentAmount: null,
        isSmallAdjustment: '0',
        remark: null
    },//开票表单
    total: 0,//总条数
    tableData: [],//列表
    dialogShow: false,//开票明细弹窗
    dialogForm: {},//开票明细表单
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerCode: 'CUST001',
                customerName: '客户名称1',
                customerAccount: '帐套001',
                financialReceivableMonth: '2023-01',
                receivableAmount: 10000.00,
                invoiceStatus: '未开票',
                writeOffStatus: '未核销',
                uninvoicedAmount: 10000.00,
                invoicedAmount: 0.00,
                approvalAmount: 0.00
            },
            {
                id: 2,
                customerCode: 'CUST002',
                customerName: '客户名称2',
                customerAccount: '帐套002',
                financialReceivableMonth: '2023-02',
                receivableAmount: 20000.00,
                invoiceStatus: '已开票',
                writeOffStatus: '已核销',
                uninvoicedAmount: 0.00,
                invoicedAmount: 20000.00,
                approvalAmount: 0.00
            }
        ];
        obj.total = 3;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 开票明细
function handleInvoiceDetail() {
    obj.dialogShow = true;
}

/** 开票 */
function handleIssueInvoice() {

}

// 初始化数据
getList();

</script>
<style lang="scss" scoped></style>