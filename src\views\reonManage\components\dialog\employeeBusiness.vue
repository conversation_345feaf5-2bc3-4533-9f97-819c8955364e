<template>
    <div>
        <el-dialog v-model="dialogShow" :title="title" width="60%" append-to-body draggable @close="handleClose">
            <el-form :model="dialogForm" ref="dialogRef" :rules="rules" inline label-width="auto">
                <el-form-item label="订单编号:" prop="orderNo">
                    <el-input class="width220" v-model="dialogForm.orderNo" placeholder="请输入订单编号"
                        :disabled="props.isEdit || !props.isEdit && title === '查看详情'" />
                </el-form-item>
                <el-form-item label="员工姓名:" prop="employeeName">
                    <el-input class="width220" v-model="dialogForm.employeeName" placeholder="请输入员工姓名"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="证件类型:" prop="idType">
                    <el-select class="width220" v-model="dialogForm.idType" placeholder="请选择"
                        :disabled="title === '查看详情'">
                        <el-option v-for="item in idTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="证件号码:" prop="idNumber">
                    <el-input class="width220" v-model="dialogForm.idNumber" placeholder="请输入证件号码"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="入离职状态:" prop="employmentStatus">
                    <el-select class="width220" v-model="dialogForm.employmentStatus" placeholder="请选择"
                        :disabled="title === '查看详情'">
                        <el-option v-for="item in employmentStatusOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户编号:" prop="customerNo">
                    <el-input class="width220" v-model="dialogForm.customerNo" placeholder="请输入客户编号"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="客户名称:" prop="customerName">
                    <el-input class="width220" v-model="dialogForm.customerName" placeholder="请输入客户名称"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="合同类型:" prop="contractType">
                    <el-select class="width220" v-model="dialogForm.contractType" placeholder="请选择"
                        :disabled="title === '查看详情'">
                        <el-option v-for="item in contractTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="小合同编号:" prop="smallContractNo">
                    <el-input class="width220" v-model="dialogForm.smallContractNo" placeholder="请输入小合同编号"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="小合同名称:" prop="smallContractName">
                    <el-input class="width220" v-model="dialogForm.smallContractName" placeholder="请输入小合同名称"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="接单方:" prop="orderReceiver">
                    <el-select class="width220" v-model="dialogForm.orderReceiver" placeholder="请选择"
                        :disabled="title === '查看详情'">
                        <el-option v-for="item in orderReceiverOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="接单客服:" prop="receiverService">
                    <el-input class="width220" v-model="dialogForm.receiverService" placeholder="请输入接单客服"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="城市:" prop="city">
                    <el-select class="width220" v-model="dialogForm.city" placeholder="请选择"
                        :disabled="title === '查看详情'">
                        <el-option v-for="item in cityOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="业务发生日期:" prop="businessDate">
                    <el-date-picker class="width220" v-model="dialogForm.businessDate" type="date" placeholder="请选择日期"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="票据金额:" prop="invoiceAmount">
                    <el-input class="width220" v-model="dialogForm.invoiceAmount" :step="100" placeholder="请输入票据金额"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="报销金额:" prop="reimbursementAmount">
                    <el-input class="width220" v-model="dialogForm.reimbursementAmount" :min="0" placeholder="请输入报销金额"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="业务大类:" prop="businessCategory">
                    <el-select class="width220" v-model="dialogForm.businessCategory" placeholder="请选择"
                        :disabled="title === '查看详情'">
                        <el-option v-for="item in businessCategoryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="业务小类:" prop="businessSubcategory">
                    <el-select class="width220" v-model="dialogForm.businessSubcategory" placeholder="请选择"
                        :disabled="title === '查看详情'">
                        <el-option v-for="item in businessSubcategoryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="具体业务:" prop="specificBusiness">
                    <el-select class="width220" v-model="dialogForm.specificBusiness" placeholder="请选择"
                        :disabled="title === '查看详情'">
                        <el-option v-for="item in specificBusinessOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否需要支付:" prop="needPayment">
                    <el-select class="width220" v-model="dialogForm.needPayment" placeholder="请选择"
                        :disabled="title === '查看详情'">
                        <el-option v-for="item in needPaymentOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="银行账户:" prop="bankAccount">
                    <el-input class="width220" v-model="dialogForm.bankAccount" placeholder="请输入银行账户"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="账户名:" prop="accountName">
                    <el-input class="width220" v-model="dialogForm.accountName" placeholder="请输入账户名"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <el-form-item label="开户行:" prop="bankName">
                    <el-input class="width220" v-model="dialogForm.bankName" placeholder="请输入开户行"
                        :disabled="title === '查看详情'" />
                </el-form-item>
                <border-box title="文件上传">
                    <file-upload v-model="dialogForm.fileList" />
                </border-box>
                <border-box :title="title == '新增信息' ? '备注' : title == '修改信息' ? '修改备注' : ''">
                    <el-input type="textarea" :rows="3" v-model="dialogForm.remark" placeholder="请输入备注" />
                </border-box>
                <div v-if="title !== '新增信息' && title !== '修改信息'">
                    <border-box title="操作记录">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.operationRecord" placeholder="请输入备注" />
                    </border-box>
                    <border-box title="新增备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.addRemark" placeholder="请输入备注" />
                    </border-box>
                    <border-box title="修改备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.modifyRemark" placeholder="请输入备注" />
                    </border-box>
                    <border-box title="接收备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.receiveRemark" placeholder="请输入备注" />
                    </border-box>
                    <border-box title="驳回备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.rejectRemark" placeholder="请输入备注" />
                    </border-box>
                    <border-box title="提交备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.submitRemark" placeholder="请输入备注" />
                    </border-box>
                    <border-box title="办理备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.processRemark" placeholder="请输入备注" />
                    </border-box>
                    <border-box title="退回备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.returnRemark" placeholder="请输入备注" />
                    </border-box>
                    <border-box title="办理成功备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.processSuccessRemark"
                            placeholder="请输入备注" />
                    </border-box>
                    <border-box title="办理失败备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.processFailRemark"
                            placeholder="请输入备注" />
                    </border-box>
                    <border-box title="取消办理备注">
                        <el-input type="textarea" :rows="3" v-model="dialogForm.cancelProcessRemark"
                            placeholder="请输入备注" />
                    </border-box>
                </div>
            </el-form>
            <template #footer>
                <el-button v-if="title !== '查看详情'" type="primary" @click="handleSave">保存</el-button>
                <el-button v-if="title !== '查看详情'" type="primary" @click="handleSubmit">立即提交</el-button>
                <el-button v-if="title !== '新增信息'" @click="dialogShow = false">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
// 证件类型选项
const idTypeOptions = [
    { value: '1', label: '身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '军官证' }
];

// 入离职状态选项
const employmentStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '停薪' }
];

// 合同类型选项
const contractTypeOptions = [
    { value: '1', label: '标准合同' },
    { value: '2', label: '非标准合同' }
];
// 接单方选项
const orderReceiverOptions = [
    { value: '1', label: '分公司A' },
    { value: '2', label: '分公司B' },
    { value: '3', label: '分公司C' }
];

// 城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' }
];

// 业务大类选项
const businessCategoryOptions = [
    { value: '1', label: '社保类' },
    { value: '2', label: '公积金类' },
    { value: '3', label: '人事类' }
];

// 业务小类选项
const businessSubcategoryOptions = [
    { value: '1', label: '养老保险' },
    { value: '2', label: '医疗保险' },
    { value: '3', label: '失业保险' },
    { value: '4', label: '公积金' },
    { value: '5', label: '入职办理' },
    { value: '6', label: '离职办理' }
];

// 具体业务选项
const specificBusinessOptions = [
    { value: '1', label: '养老保险变更' },
    { value: '2', label: '医疗保险变更' },
    { value: '3', label: '失业保险变更' },
    { value: '4', label: '公积金变更' },
    { value: '5', label: '入职办理' },
    { value: '6', label: '离职办理' }
];

// 是否需要支付选项
const needPaymentOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' }
];



const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    dialogForm: {
        type: Object,
        default: () => ({})
    },
    isEdit: {
        type: Boolean,
        default: false
    }
})
const rules = ref({
    orderNo: [{ required: true, message: '请输入订单编号', trigger: 'blur' }],
    employeeName: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
    idType: [{ required: true, message: '请选择证件类型', trigger: 'blur' }],
    idNumber: [{ required: true, message: '请输入证件号码', trigger: 'blur' }],

})

const dialogShow = ref(props.dialogShow);
const dialogForm = ref(props.dialogForm);
const title = ref(props.title);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});

watch(() => props.title, (newVal) => {
    title.value = newVal;
});

const emit = defineEmits(['update:dialogShow', 'close']);

/**
 * 关闭对话框
 */
const handleClose = () => {
    dialogShow.value = false;
    emit('close');
};

/**
 * 保存
 */
const handleSave = () => {
    console.log('保存');
};

/**
 * 提交
 */
const handleSubmit = () => {
    console.log('提交');
    console.log(dialogForm.value);
};

</script>
<style lang="scss" scoped></style>