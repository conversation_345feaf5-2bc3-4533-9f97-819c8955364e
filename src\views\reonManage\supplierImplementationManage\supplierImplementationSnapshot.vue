<!-- 供应商实做快照管理 -->

<template>
    <div class="app-container">
        <el-tabs style="min-height: calc(100vh - 130px);" type="border-card">
            <el-tab-pane label="非年缴快照">
                <!-- 查询条件 -->
                <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
                    <el-form-item label="订单号:" prop="orderNo">
                        <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入订单号" />
                    </el-form-item>
                    <el-form-item label="报表年月:" prop="reportMonth">
                        <el-input class="width220" v-model="obj.queryParams.reportMonth" placeholder="请输入报表年月" />
                    </el-form-item>
                    <el-form-item label="导入编号:" prop="importCode">
                        <el-input class="width220" v-model="obj.queryParams.importCode" placeholder="请输入导入编号" />
                    </el-form-item>
                    <el-form-item label="导入人:" prop="importUser">
                        <el-input class="width220" v-model="obj.queryParams.importUser" placeholder="请输入导入人" />
                    </el-form-item>
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="success" plain :disabled="obj.single" icon="edit"
                            @click="handleUpdate('no')">修改</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" plain :disabled="obj.single"
                            @click="handleClearAll">清除当前月全部差异</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" plain :disabled="obj.single"
                            @click="handleClearProduct">清除某个产品当月差异</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" plain :disabled="obj.single"
                            @click="handleClearOrderAll">清除输入框的订单号的全部差异</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" plain :disabled="obj.single"
                            @click="handleClearOrder">清除输入框的订单号的全部单个差异</el-button>
                    </el-col>
                    <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
                <!-- 表格 -->
                <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.nonYearlyData"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="账单月" align="center" prop="billMonth" />
                    <el-table-column label="快照ID" align="center" prop="snapshotId" />
                    <el-table-column label="产品信息Code" align="center" prop="productInfoCode" />
                    <el-table-column label="服务费信息Code" align="center" prop="serviceFeeCode" />
                    <el-table-column label="订单编号" align="center" prop="orderNo" />
                    <el-table-column label="产品类型" align="center" prop="productType" />
                    <el-table-column label="比例编码" align="center" prop="ratioCode" />
                    <el-table-column label="收费起始月" align="center" prop="feeStartMonth" />
                    <el-table-column label="账单开始月" align="center" prop="billStartMonth" />
                    <el-table-column label="收费截止月" align="center" prop="feeEndMonth" />
                    <el-table-column label="企业基数" align="center" prop="companyBase" />
                    <el-table-column label="个人基数" align="center" prop="personalBase" />
                    <el-table-column label="企业金额" align="center" prop="companyAmount" />
                    <el-table-column label="个人金额" align="center" prop="personalAmount" />
                    <el-table-column label="创建人" align="center" prop="createUser" />
                    <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
                    <el-table-column label="修改人" align="center" prop="updateUser" />
                </el-table>
            </el-tab-pane>
            <el-tab-pane label="年缴快照">
                <!-- 查询条件 -->
                <el-form :model="obj.yearlyQueryParams" ref="yearlyQueryRef" inline v-show="obj.showSearch"
                    label-width="auto">
                    <el-form-item label="订单号:" prop="orderNo">
                        <el-input class="width220" v-model="obj.yearlyQueryParams.orderNo" placeholder="请输入订单号" />
                    </el-form-item>
                    <el-form-item label="报表年月:" prop="reportMonth">
                        <el-input class="width220" v-model="obj.yearlyQueryParams.reportMonth" placeholder="请输入报表年月" />
                    </el-form-item>
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="success" plain icon="edit" @click="handleUpdate('yes')">修改</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" plain icon="delete" @click="handleDelete">删除</el-button>
                    </el-col>
                    <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
                </el-row>
                <!-- 表格 -->
                <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.yearlyData"
                    @selection-change="handleYearlySelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="主键" align="center" prop="id" />
                    <el-table-column label="订单编号" align="center" prop="orderNo" />
                    <el-table-column label="产品类型" align="center" prop="productType" />
                    <el-table-column label="比例编码" align="center" prop="ratioCode" />
                    <el-table-column label="企业金额" align="center" prop="companyAmount" />
                    <el-table-column label="个人金额" align="center" prop="personalAmount" />
                    <el-table-column label="账单月" align="center" prop="billMonth" />
                    <el-table-column label="服务月" align="center" prop="serviceMonth" />
                    <el-table-column label="账单类型" align="center" prop="billType" />
                    <el-table-column label="所属年" align="center" prop="belongYear" />
                </el-table>
            </el-tab-pane>
        </el-tabs>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 非年缴快照-修改 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="60%" append-to-body>
            <el-form :model="obj.dialogForm" ref="dialogRef" inline label-width="auto">
                <el-form-item label="产品信息Code:" prop="productInfoCode">
                    <el-input class="width220" v-model="obj.dialogForm.productInfoCode" placeholder="请输入产品信息Code" />
                </el-form-item>
                <el-form-item label="服务费信息Code:" prop="serviceFeeCode">
                    <el-input class="width220" v-model="obj.dialogForm.serviceFeeCode" placeholder="请输入服务费信息Code" />
                </el-form-item>
                <el-form-item label="比例编码:" prop="ratioCode">
                    <el-input class="width220" v-model="obj.dialogForm.ratioCode" placeholder="请输入比例编码" />
                </el-form-item>
                <el-form-item label="收费起始月:" prop="feeStartMonth">
                    <el-input class="width220" v-model="obj.dialogForm.feeStartMonth" placeholder="请输入收费起始月" />
                </el-form-item>
                <el-form-item label="收费截止月:" prop="feeEndMonth">
                    <el-input class="width220" v-model="obj.dialogForm.feeEndMonth" placeholder="请输入收费截止月" />
                </el-form-item>
                <el-form-item label="账单起始月:" prop="billStartMonth">
                    <el-input class="width220" v-model="obj.dialogForm.billStartMonth" placeholder="请输入账单起始月" />
                </el-form-item>
                <el-form-item label="账单月:" prop="billMonth">
                    <el-input class="width220" v-model="obj.dialogForm.billMonth" placeholder="请输入账单月" />
                </el-form-item>
                <el-form-item label="企业基数:" prop="companyBase">
                    <el-input class="width220" v-model="obj.dialogForm.companyBase" placeholder="请输入企业基数" />
                </el-form-item>
                <el-form-item label="个人基数:" prop="personalBase">
                    <el-input class="width220" v-model="obj.dialogForm.personalBase" placeholder="请输入个人基数" />
                </el-form-item>
                <el-form-item label="企业金额:" prop="companyAmount">
                    <el-input class="width220" v-model="obj.dialogForm.companyAmount" placeholder="请输入企业金额" />
                </el-form-item>
                <el-form-item label="个人金额:" prop="personalAmount">
                    <el-input class="width220" v-model="obj.dialogForm.personalAmount" placeholder="请输入个人金额" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSaveNonYearly">修改</el-button>
                <el-button @click="closeDialog">关闭</el-button>
            </template>
        </el-dialog>
        <!-- 年缴快照-修改 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="60%" append-to-body>
            <el-form :model="obj.yearlyDialogForm" ref="yearlyDialogRef" inline label-width="auto">
                <el-form-item label="订单号:" prop="orderNo">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.orderNo" placeholder="请输入订单号" />
                </el-form-item>
                <el-form-item label="实做ID:" prop="implementationId">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.implementationId" placeholder="请输入实做ID" />
                </el-form-item>
                <el-form-item label="账单ID:" prop="billId">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.billId" placeholder="请输入账单ID" />
                </el-form-item>
                <el-form-item label="账单月:" prop="billMonth">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.billMonth" placeholder="请输入账单月" />
                </el-form-item>
                <el-form-item label="产品类型:" prop="productType">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.productType" placeholder="请输入产品类型" />
                </el-form-item>
                <el-form-item label="社保比例code:" prop="ratioCode">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.ratioCode" placeholder="请输入社保比例code" />
                </el-form-item>
                <el-form-item label="企业金额:" prop="companyAmount">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.companyAmount" placeholder="请输入企业金额" />
                </el-form-item>
                <el-form-item label="个人金额:" prop="personalAmount">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.personalAmount" placeholder="请输入个人金额" />
                </el-form-item>
                <el-form-item label="服务月:" prop="serviceMonth">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.serviceMonth" placeholder="请输入服务月" />
                </el-form-item>
                <el-form-item label="账单类型:" prop="billType">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.billType" placeholder="请输入账单类型" />
                </el-form-item>
                <el-form-item label="所属年:" prop="belongYear">
                    <el-input class="width220" v-model="obj.yearlyDialogForm.belongYear" placeholder="请输入所属年" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSaveYearly">修改</el-button>
                <el-button @click="closeYearlyDialog">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup name="SupplierImplementationSnapshot">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 当前激活的标签页
const activeTab = ref('nonYearly');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        reportMonth: null,
        importCode: null,
        importUser: null,
    },//非年缴查询表单
    yearlyQueryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        reportMonth: null,
    },//年缴查询表单
    total: 10,//总条数
    nonYearlyData: [],//非年缴列表
    yearlyData: [],//年缴列表
    dialogForm: {
        productInfoCode: '',
        serviceFeeCode: '',
        ratioCode: '',
        feeStartMonth: '',
        feeEndMonth: '',
        billStartMonth: '',
        billMonth: '',
        companyBase: '',
        personalBase: '',
        companyAmount: '',
        personalAmount: ''
    },//非年缴快照表单
    yearlyDialogForm: {
        orderNo: '',
        implementationId: '',
        billId: '',
        billMonth: '',
        productType: '',
        ratioCode: '',
        companyAmount: '',
        personalAmount: '',
        serviceMonth: '',
        billType: '',
        belongYear: ''
    },//年缴快照表单
    dialogShow: false,//非年缴弹窗
    dialogShow2: false,//年缴弹窗
    ids: [],//选中的id
    yearlyIds: [],//年缴选中的id
    title: "",//标题
})


/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        // 非年缴数据
        obj.nonYearlyData = [
            {
                id: 1,
                billMonth: '2023-05',
                snapshotId: 'SN001',
                productInfoCode: 'P001',
                serviceFeeCode: 'SF001',
                orderNo: 'ORD20230501',
                productType: '社保',
                ratioCode: 'RC001',
                feeStartMonth: '2023-01',
                billStartMonth: '2023-05',
                feeEndMonth: '2023-12',
                companyBase: 5000,
                personalBase: 3000,
                companyAmount: 500,
                personalAmount: 300,
                createUser: '张三',
                createTime: '2023-05-15 10:30:00',
                updateUser: '李四'
            },
            {
                id: 2,
                billMonth: '2023-05',
                snapshotId: 'SN002',
                productInfoCode: 'P002',
                serviceFeeCode: 'SF002',
                orderNo: 'ORD20230502',
                productType: '公积金',
                ratioCode: 'RC002',
                feeStartMonth: '2023-01',
                billStartMonth: '2023-05',
                feeEndMonth: '2023-12',
                companyBase: 6000,
                personalBase: 4000,
                companyAmount: 600,
                personalAmount: 400,
                createUser: '王五',
                createTime: '2023-05-16 14:20:00',
                updateUser: ''
            },
            {
                id: 3,
                billMonth: '2023-05',
                snapshotId: 'SN003',
                productInfoCode: 'P003',
                serviceFeeCode: 'SF003',
                orderNo: 'ORD20230503',
                productType: '养老保险',
                ratioCode: 'RC003',
                feeStartMonth: '2023-01',
                billStartMonth: '2023-05',
                feeEndMonth: '2023-12',
                companyBase: 7000,
                personalBase: 5000,
                companyAmount: 700,
                personalAmount: 500,
                createUser: '赵六',
                createTime: '2023-05-17 11:15:00',
                updateUser: ''
            }
        ];

        // 年缴数据
        obj.yearlyData = [
            {
                id: 1,
                orderNo: 'ORD20230601',
                productType: '社保',
                ratioCode: 'RC003',
                companyAmount: 6000,
                personalAmount: 4000,
                billMonth: '2023-06',
                serviceMonth: '2023-01',
                billType: '年缴',
                belongYear: '2023'
            },
            {
                id: 2,
                orderNo: 'ORD20230602',
                productType: '公积金',
                ratioCode: 'RC004',
                companyAmount: 7200,
                personalAmount: 4800,
                billMonth: '2023-06',
                serviceMonth: '2023-01',
                billType: '年缴',
                belongYear: '2023'
            }
        ];

        obj.total = obj.nonYearlyData.length + obj.yearlyData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 修改
function handleUpdate(type) {
    if (obj.ids.length === 0 && type === 'no') {
        proxy.$modal.msgError("请选择要修改的非年缴快照");
        return;
    }
    if (obj.yearlyIds.length === 0 && type === 'yes') {
        proxy.$modal.msgError("请选择要修改的年缴快照");
        return;
    }

    if (type === 'no') {
        // 获取选中的非年缴快照数据
        const selectedData = obj.nonYearlyData.find(item => item.id === obj.ids[0]);
        if (selectedData) {
            obj.dialogForm = { ...selectedData };
            obj.dialogShow = true;
            obj.title = "修改非年缴快照";
        }
    } else if (type === 'yes') {
        // 获取选中的年缴快照数据
        const selectedData = obj.yearlyData.find(item => item.id === obj.yearlyIds[0]);
        if (selectedData) {
            obj.yearlyDialogForm = { ...selectedData };
            obj.dialogShow2 = true;
            obj.title = "修改年缴快照";
        }
    }
}

// 关闭非年缴快照弹窗
function closeDialog() {
    obj.dialogShow = false;
    obj.dialogForm = {
        productInfoCode: '',
        serviceFeeCode: '',
        ratioCode: '',
        feeStartMonth: '',
        feeEndMonth: '',
        billStartMonth: '',
        billMonth: '',
        companyBase: '',
        personalBase: '',
        companyAmount: '',
        personalAmount: ''
    };
}

// 关闭年缴快照弹窗
function closeYearlyDialog() {
    obj.dialogShow2 = false;
    obj.yearlyDialogForm = {
        orderNo: '',
        implementationId: '',
        billId: '',
        billMonth: '',
        productType: '',
        ratioCode: '',
        companyAmount: '',
        personalAmount: '',
        serviceMonth: '',
        billType: '',
        belongYear: ''
    };
}

// 保存非年缴快照修改
function handleSaveNonYearly() {
    proxy.$modal.msgSuccess("修改成功");
    closeDialog();
    getList();
}

// 保存年缴快照修改
function handleSaveYearly() {
    proxy.$modal.msgSuccess("修改成功");
    closeYearlyDialog();
    getList();
}

// 删除年缴快照
function handleDelete() {
    if (obj.yearlyIds.length === 0) {
        proxy.$modal.msgError("请选择要删除的年缴快照");
        return;
    }
    proxy.$modal.confirm('是否确认删除所选年缴快照数据？').then(function () {
        proxy.$modal.msgSuccess("删除成功");
        getList();
    }).catch(() => { });
}

// 清除当前月全部差异
function handleClearAll() {
    if (!obj.queryParams.reportMonth) {
        proxy.$modal.msgError("请先输入报表年月");
        return;
    }
    proxy.$modal.confirm('是否确认清除' + obj.queryParams.reportMonth + '月全部差异？').then(function () {
        proxy.$modal.msgSuccess("清除成功");
        getList();
    }).catch(() => { });
}

// 清除某个产品当月差异
function handleClearProduct() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要清除差异的产品");
        return;
    }
    proxy.$modal.confirm('是否确认清除所选产品当月差异？').then(function () {
        proxy.$modal.msgSuccess("清除成功");
        getList();
    }).catch(() => { });
}

// 清除输入框的订单号的全部差异
function handleClearOrderAll() {
    if (!obj.queryParams.orderNo) {
        proxy.$modal.msgError("请先输入订单号");
        return;
    }
    proxy.$modal.confirm('是否确认清除订单号' + obj.queryParams.orderNo + '的全部差异？').then(function () {
        proxy.$modal.msgSuccess("清除成功");
        getList();
    }).catch(() => { });
}

// 清除输入框的订单号的全部单个差异
function handleClearOrder() {
    if (!obj.queryParams.orderNo || obj.ids.length === 0) {
        proxy.$modal.msgError("请输入订单号并选择要清除差异的数据");
        return;
    }
    proxy.$modal.confirm('是否确认清除订单号' + obj.queryParams.orderNo + '的选中差异？').then(function () {
        proxy.$modal.msgSuccess("清除成功");
        getList();
    }).catch(() => { });
}

// 选中非年缴数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

// 选中年缴数据变化
function handleYearlySelectionChange(selection) {
    obj.yearlyIds = selection.map(item => item.id);
}

getList();
</script>
<style lang="scss" scoped></style>