<!-- 订单汇总报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" readonly @click="handleClient" v-model="obj.queryParams.customerName"
                    placeholder="请选择客户" />
            </el-form-item>
            <el-form-item label="签单分公司:" prop="signingCompany">
                <el-select class="width220" v-model="obj.queryParams.signingCompany" placeholder="请选择" clearable>
                    <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button icon="Refresh" @click="handleReset">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>

            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="12">
                    <el-form-item style="font-family: '仿宋'">
                        <span style="font-size: 16px">总数:</span>
                        <span style="font-size: 26px; color: saddlebrown">{{ obj.total }}</span>
                    </el-form-item>
                </el-col>
                <el-col :span="12" style="text-align: right;">
                    <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                        cache-key="order-summary-report" />
                    <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                    <PrintButton print-id="print-table-area" print-title="订单汇总报表" icon="Printer" />
                </el-col>
            </el-row>
        </el-form>
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" border style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column typee="index" label="序号" width="80" align="center" />
            <el-table-column v-if="obj.selectedColumns.includes('orderNumber')" label="订单编号" align="center"
                prop="orderNumber" />
            <el-table-column v-if="obj.selectedColumns.includes('customerNumber')" label="客户编号" align="center"
                prop="customerNumber" />
            <el-table-column v-if="obj.selectedColumns.includes('customerName')" label="客户名称" align="center"
                prop="customerName" />
            <el-table-column v-if="obj.selectedColumns.includes('projectCustomer')" label="项目客服" align="center"
                prop="projectCustomer" />
            <el-table-column v-if="obj.selectedColumns.includes('orderCustomer')" label="接单客服" align="center"
                prop="orderCustomer" />
            <el-table-column v-if="obj.selectedColumns.includes('contractNumber')" label="合同编号" align="center"
                prop="contractNumber" />
            <el-table-column v-if="obj.selectedColumns.includes('contractName')" label="合同名称" align="center"
                prop="contractName" />
            <el-table-column v-if="obj.selectedColumns.includes('signingCompany')" label="签单公司" align="center"
                prop="signingCompany" />
            <el-table-column v-if="obj.selectedColumns.includes('employeeNumber')" label="员工编号" align="center"
                prop="employeeNumber" />
            <el-table-column v-if="obj.selectedColumns.includes('employeeName')" label="员工名称" align="center"
                prop="employeeName" />
            <el-table-column v-if="obj.selectedColumns.includes('orderDate')" label="订单日期" align="center"
                prop="orderDate" />
            <el-table-column v-if="obj.selectedColumns.includes('orderAmount')" label="订单金额" align="center"
                prop="orderAmount">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.orderAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('orderStatus')" label="订单状态" align="center"
                prop="orderStatus">
                <template #default="scope">
                    <dict-tag :options="orderStatusOptions" :value="scope.row.orderStatus" />
                </template>
            </el-table-column>
        </el-table>

        <!-- 打印区域 -->
        <div id="print-table-area" class="print-area">
            <table class="print-table">
                <thead>
                    <tr>
                        <th v-if="obj.selectedColumns.includes('orderNumber')">订单编号</th>
                        <th v-if="obj.selectedColumns.includes('customerNumber')">客户编号</th>
                        <th v-if="obj.selectedColumns.includes('customerName')">客户名称</th>
                        <th v-if="obj.selectedColumns.includes('projectCustomer')">项目客服</th>
                        <th v-if="obj.selectedColumns.includes('orderCustomer')">接单客服</th>
                        <th v-if="obj.selectedColumns.includes('contractNumber')">合同编号</th>
                        <th v-if="obj.selectedColumns.includes('contractName')">合同名称</th>
                        <th v-if="obj.selectedColumns.includes('signingCompany')">签单公司</th>
                        <th v-if="obj.selectedColumns.includes('employeeNumber')">员工编号</th>
                        <th v-if="obj.selectedColumns.includes('employeeName')">员工名称</th>
                        <th v-if="obj.selectedColumns.includes('orderDate')">订单日期</th>
                        <th v-if="obj.selectedColumns.includes('orderAmount')">订单金额</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in obj.tableData" :key="index">
                        <td v-if="obj.selectedColumns.includes('orderNumber')">{{ item.orderNumber }}</td>
                        <td v-if="obj.selectedColumns.includes('customerNumber')">{{ item.customerNumber }}</td>
                        <td v-if="obj.selectedColumns.includes('customerName')">{{ item.customerName }}</td>
                        <td v-if="obj.selectedColumns.includes('projectCustomer')">{{ item.projectCustomer }}</td>
                        <td v-if="obj.selectedColumns.includes('orderCustomer')">{{ item.orderCustomer }}</td>
                        <td v-if="obj.selectedColumns.includes('contractNumber')">{{ item.contractNumber }}</td>
                        <td v-if="obj.selectedColumns.includes('contractName')">{{ item.contractName }}</td>
                        <td v-if="obj.selectedColumns.includes('signingCompany')">{{ item.signingCompany }}</td>
                        <td v-if="obj.selectedColumns.includes('employeeNumber')">{{ item.employeeNumber }}</td>
                        <td v-if="obj.selectedColumns.includes('employeeName')">{{ item.employeeName }}</td>
                        <td v-if="obj.selectedColumns.includes('orderDate')">{{ item.orderDate }}</td>
                        <td v-if="obj.selectedColumns.includes('orderAmount')">{{ item.orderAmount }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 客户选择对话框 -->
        <client v-model:show="clientShow" @select="handleSelect" />
    </div>
</template>

<script setup name="OrderSummaryReport">

import { listScale } from "@/api/reonApi/scale";
import client from "@/views/reonManage/components/client.vue";
import ColumnFilter from "@/views/reonManage/components/columnFilter.vue";
import PrintButton from '@/components/PrintButton.vue';


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 签单公司选项
const companyOptions = [
    { value: '1', label: '北京分公司' },
    { value: '2', label: '上海分公司' },
    { value: '3', label: '广州分公司' },
    { value: '4', label: '深圳分公司' },
    { value: '5', label: '杭州分公司' }
];

// 订单状态选项
const orderStatusOptions = [
    { value: '0', label: '待处理', elTagType: 'info' },
    { value: '1', label: '处理中', elTagType: 'warning' },
    { value: '2', label: '已完成', elTagType: 'success' },
    { value: '3', label: '已取消', elTagType: 'danger' }
];

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    ids: [], // 选中id
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        customerId: null, // 客户ID，用于接口传参
        signingCompany: null,
        orderDateRange: null
    }, // 查询表单
    tableData: [], // 表格数据
    selectedColumns: ['orderNumber', 'customerNumber', 'customerName', 'projectCustomer', 'orderCustomer', 'contractNumber',
        'contractName', 'signingCompany', 'employeeNumber', 'employeeName', 'orderDate', 'orderAmount', 'orderStatus'], // 选中列
    columnOptions: [
        { label: '订单编号', prop: 'orderNumber' },
        { label: '客户编号', prop: 'customerNumber' },
        { label: '客户名称', prop: 'customerName' },
        { label: '项目客服', prop: 'projectCustomer' },
        { label: '接单客服', prop: 'orderCustomer' },
        { label: '合同编号', prop: 'contractNumber' },
        { label: '合同名称', prop: 'contractName' },
        { label: '签单公司', prop: 'signingCompany' },
        { label: '员工编号', prop: 'employeeNumber' },
        { label: '员工名称', prop: 'employeeName' },
        { label: '订单日期', prop: 'orderDate' },
        { label: '订单金额', prop: 'orderAmount' },
        { label: '订单状态', prop: 'orderStatus' }
    ], // 列选项
});

const clientShow = ref(false);



/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 处理日期范围
    let params = { ...obj.queryParams };
    if (params.orderDateRange && params.orderDateRange.length === 2) {
        params.startDate = params.orderDateRange[0];
        params.endDate = params.orderDateRange[1];
        delete params.orderDateRange;
    }

    // 调用API获取数据
    listScale(params).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                orderNumber: 'DD20230001',
                customerNumber: 'KH20230001',
                customerName: '客户A',
                projectCustomer: '客服1',
                orderCustomer: '客服2',
                contractNumber: 'HT20230001',
                contractName: '合同1',
                signingCompany: '北京分公司',
                employeeNumber: 'YG20230001',
                employeeName: '张三',
                orderDate: '2023-05-10',
                orderAmount: 50000.00,
                orderStatus: '2'
            },
            {
                id: 2,
                orderNumber: 'DD20230002',
                customerNumber: 'KH20230002',
                customerName: '客户B',
                projectCustomer: '客服3',
                orderCustomer: '客服4',
                contractNumber: '**********',
                contractName: '合同2',
                signingCompany: '上海分公司',
                employeeNumber: '**********',
                employeeName: '李四',
                orderDate: '2023-06-15',
                orderAmount: 75000.00,
                orderStatus: '1'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {

    });
}

/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    obj.queryParams.customerName = null;
    obj.queryParams.customerId = null;
    handleSearch();
}

/** 客户选择 */
function handleClient() {
    clientShow.value = true;
}

/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 打印按钮操作 */
function handlePrint() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可打印');
        return;
    }
    proxy.$modal.confirm('确认打印所有数据吗？').then(() => {
        // 调用打印接口
        window.print();
        proxy.$modal.msgSuccess('已发送到打印机');
    }).catch(() => { });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

/** 选择客户 */
function handleSelect(row) {
    if (row) {
        obj.queryParams.customerName = row.name;
        obj.queryParams.customerId = row.id;
    } else {
        obj.queryParams.customerName = null;
        obj.queryParams.customerId = null;
    }
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>