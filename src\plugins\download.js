﻿import axios from "axios";
import { ElLoading, ElMessage } from "element-plus";
import { saveAs } from "file-saver";
import { getToken } from "@/utils/auth";
import errorCode from "@/utils/errorCode";
import { blobValidate } from "@/utils/ruoyi";

const baseURL = import.meta.env.VITE_APP_BASE_API;
let downloadLoadingInstance;

export default {
  /**
   * 根据文件名下载文件
   * @param {string} name - 文件名
   * @param {boolean} isDelete - 是否删除文件，默认为 true
   */
  name(name, isDelete = true) {
    // 构建下载文件的URL，包括文件名和是否删除文件的参数
    var url =
      baseURL +
      "/common/download?fileName=" +
      encodeURIComponent(name) +
      "&delete=" +
      isDelete;
    // 使用axios发送GET请求，设置响应类型为blob，用于处理二进制数据
    axios({
      method: "get",
      url: url,
      responseType: "blob",
      // 在请求头中添加Authorization字段，用于身份验证
      headers: { Authorization: "Bearer " + getToken() },
    }).then((res) => {
      // 验证响应数据是否为有效的blob对象
      const isBlob = blobValidate(res.data);
      if (isBlob) {
        // 创建一个新的blob对象，包含响应数据
        const blob = new Blob([res.data]);
        // 使用saveAs函数保存blob对象为文件，文件名从响应头中获取并解码
        this.saveAs(blob, decodeURIComponent(res.headers["download-filename"]));
      } else {
        // 如果响应数据不是有效的blob对象，则打印错误信息
        this.printErrMsg(res.data);
      }
    });
  },

  /**
   * 根据资源路径下载文件
   * @param {string} resource - 资源路径
   */
  resource(resource) {
    // 构建下载文件的URL，包括资源路径参数
    var url =
      baseURL +
      "/common/download/resource?resource=" +
      encodeURIComponent(resource);
    // 使用axios发送GET请求，设置响应类型为blob，用于处理二进制数据
    axios({
      method: "get",
      url: url,
      responseType: "blob",
      // 在请求头中添加Authorization字段，用于身份验证
      headers: { Authorization: "Bearer " + getToken() },
    }).then((res) => {
      // 验证响应数据是否为有效的blob对象
      const isBlob = blobValidate(res.data);
      if (isBlob) {
        // 创建一个新的blob对象，包含响应数据
        const blob = new Blob([res.data]);
        // 使用saveAs函数保存blob对象为文件，文件名从响应头中获取并解码
        this.saveAs(blob, decodeURIComponent(res.headers["download-filename"]));
      } else {
        // 如果响应数据不是有效的blob对象，则打印错误信息
        this.printErrMsg(res.data);
      }
    });
  },

  /**
   * 下载ZIP文件
   * @param {string} url - ZIP文件的URL
   * @param {string} name - 保存的文件名
   */
  zip(url, name) {
    // 构建ZIP文件的下载URL
    var url = baseURL + url;
    // 显示加载提示
    downloadLoadingInstance = ElLoading.service({
      text: "正在下载数据，请稍候",
      background: "rgba(0, 0, 0, 0.7)",
    });
    // 使用axios发送GET请求，设置响应类型为blob，用于处理二进制数据
    axios({
      method: "get",
      url: url,
      responseType: "blob",
      // 在请求头中添加Authorization字段，用于身份验证
      headers: { Authorization: "Bearer " + getToken() },
    })
      .then((res) => {
        // 验证响应数据是否为有效的blob对象
        const isBlob = blobValidate(res.data);
        if (isBlob) {
          // 创建一个新的blob对象，包含响应数据，并指定MIME类型为application/zip
          const blob = new Blob([res.data], { type: "application/zip" });
          // 使用saveAs函数保存blob对象为文件，文件名从参数中获取
          this.saveAs(blob, name);
        } else {
          // 如果响应数据不是有效的blob对象，则打印错误信息
          this.printErrMsg(res.data);
        }
        // 关闭加载提示
        downloadLoadingInstance.close();
      })
      .catch((r) => {
        // 打印错误信息
        console.error(r);
        // 显示错误提示
        ElMessage.error("下载文件出现错误，请联系管理员！");
        // 关闭加载提示
        downloadLoadingInstance.close();
      });
  },

  /**
   * 保存文件
   * @param {Blob} text - 文件内容
   * @param {string} name - 文件名
   * @param {Object} opts - 其他选项
   */
  saveAs(text, name, opts) {
    // 使用file-saver库的saveAs函数保存文件
    saveAs(text, name, opts);
  },

  /**
   * 打印错误信息
   * @param {Object} data - 错误数据
   */
  async printErrMsg(data) {
    // 将错误数据转换为文本格式
    const resText = await data.text();
    // 将文本格式的错误数据解析为JSON对象
    const rspObj = JSON.parse(resText);
    // 根据错误码获取错误信息，如果没有对应的错误码，则使用默认错误信息
    const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode["default"];
    // 显示错误信息
    ElMessage.error(errMsg);
  },
};
