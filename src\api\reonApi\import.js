import request from '@/utils/request'

// 查询社保基数导入记录列表
export function listImport(query) {
  return request({
    url: '/system/import/list',
    method: 'get',
    params: query
  })
}

// 查询社保基数导入记录详细
export function getImport(id) {
  return request({
    url: '/system/import/' + id,
    method: 'get'
  })
}

// 新增社保基数导入记录
export function addImport(data) {
  return request({
    url: '/system/import',
    method: 'post',
    data: data
  })
}

// 修改社保基数导入记录
export function updateImport(data) {
  return request({
    url: '/system/import',
    method: 'put',
    data: data
  })
}

// 删除社保基数导入记录
export function delImport(id) {
  return request({
    url: '/system/import/' + id,
    method: 'delete'
  })
}
