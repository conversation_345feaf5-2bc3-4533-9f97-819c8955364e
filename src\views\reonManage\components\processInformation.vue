<!-- 流程信息 -->
<template>
    <el-table :data="tableData || props.tableData" border>
        <el-table-column label="序号" type="index" align="center" width="60" />
        <el-table-column label="处理人" align="center" prop="handler" />
        <el-table-column label="处理类型" align="center" prop="handleType" />
        <el-table-column label="审批意见" align="center" prop="approvalOpinion" />
        <el-table-column label="审批时间" align="center" prop="approvalTime" />
    </el-table>
</template>
<script setup>

const tableData = ref([
    {
        handler: '处理人',
        handleType: '处理类型',
        approvalOpinion: '审批意见',
        approvalTime: '审批时间',
    },
    {
        handler: '处理人',
        handleType: '处理类型',
        approvalOpinion: '审批意见',
        approvalTime: '审批时间',
    }
]);

const props = defineProps({
    tableData: {
        type: Array,
        default: () => []
    }
})
</script>
<style lang="scss" scoped></style>
