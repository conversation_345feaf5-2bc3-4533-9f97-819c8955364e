<template>
    <el-table v-loading="props.loading" border :data="props.tableData" @selection-change="handleSelectionChange"
        :row-class-name="tableRowClassName">
        <el-table-column type="selection" width="55" />
        <el-table-column label="供应商名称" align="center" prop="supplier">
            <template #default="scope">
                {{supplierOptions.find(item => item.value === scope.row.supplier)?.label || '未知供应商'}}
            </template>
        </el-table-column>
        <el-table-column label="账单模板" align="center" prop="billTemplate">
            <template #default="scope">
                {{billTemplateOptions.find(item => item.value === scope.row.billTemplate)?.label || '未知模板'}}
            </template>
        </el-table-column>
        <el-table-column label="账单年月" align="center" prop="billMonth" />
        <el-table-column label="一级类别" align="center" prop="firstCategory">
            <template #default="scope">
                {{firstCategoryOptions.find(item => item.value === scope.row.firstCategory)?.label || '未知类别'}}
            </template>
        </el-table-column>
        <el-table-column label="二级类别" align="center" prop="secondCategory">
            <template #default="scope">
                {{secondCategoryOptions.find(item => item.value === scope.row.secondCategory)?.label || '未知类别'}}
            </template>
        </el-table-column>
        <el-table-column label="金额" align="center" prop="amount" />
        <el-table-column label="金额(不含税)" align="center" prop="amountWithoutTax" />
        <el-table-column label="增值税率(%)" align="center" prop="taxRate" />
        <el-table-column label="增值税" align="center" prop="tax" />
        <el-table-column label="总人次" align="center" prop="totalPeople" />
        <el-table-column label="一次性支持人员" align="center" prop="oneTimeSupportPeople" />
        <el-table-column label="报表锁定状态" align="center" prop="reportLockStatus">
            <template #default="scope">
                <el-tag :type="scope.row.reportLockStatus === '1' ? 'danger' : 'success'">
                    {{reportLockStatusOptions.find(item => item.value === scope.row.reportLockStatus)?.label ||
                        '未知状态'}}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
            <template #default="scope">
                <el-tag :type="scope.row.status === '2' ? 'danger' : 'success'">
                    {{statusOptions.find(item => item.value === scope.row.status)?.label || '未知状态'}}
                </el-tag>
            </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="提交人" align="center" prop="submitter" />
        <el-table-column label="提交时间" align="center" prop="submitTime" />
    </el-table>
</template>
<script setup>
const props = defineProps({
    loading: {
        type: Boolean,
        required: true,
        default: false
    },
    tableData: {
        type: Array,
        required: true,
        default: () => []
    },
    menuName: {
        type: String,
        required: true,
        default: ''
    }
})
/** 表格行样式 */
function tableRowClassName({ row, rowIndex }) {
    if (row.reportLockStatus === '1') {
        return 'red-row'
    } else if (row.reportLockStatus === '0') {
        return 'blue-row'
    } else if (row.reportLockStatus === '2') {
        return 'gray-row'
    }
    return '';
}
// 供应商选项
const supplierOptions = [
    { value: '1', label: '供应商A' },
    { value: '2', label: '供应商B' },
    { value: '3', label: '供应商C' }
];

// 账单模板选项
const billTemplateOptions = [
    { value: '1', label: '模板1' },
    { value: '2', label: '模板2' },
    { value: '3', label: '模板3' }
];

// 一级类别选项
const firstCategoryOptions = [
    { value: '1', label: '社保' },
    { value: '2', label: '公积金' },
    { value: '3', label: '其他' }
];

// 二级类别选项
const secondCategoryOptions = [
    { value: '1', label: '养老保险' },
    { value: '2', label: '医疗保险' },
    { value: '3', label: '失业保险' },
    { value: '4', label: '公积金' },
    { value: '5', label: '服务费' }
];

// 报表锁定状态选项
const reportLockStatusOptions = [
    { value: '1', label: '已锁定' },
    { value: '0', label: '未锁定' }
];

// 状态选项
const statusOptions = [
    { value: '1', label: '正常' },
    { value: '2', label: '已废除' }
];

const emit = defineEmits(['selectionChange'])
/** 选择行 */
const handleSelectionChange = (selection) => {
    emit('selectionChange', selection)
}

</script>
<style lang="scss" scoped>
:deep(.red-row) {
    color: #f00;
}

:deep(.blue-row) {
    color: #4186D5;
}

:deep(.gray-row) {
    color: #d3d3d3;
}
</style>