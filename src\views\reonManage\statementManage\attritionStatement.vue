<!-- 减员报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="报离职起始时间:" prop="startTime">
                <el-date-picker class="width220" v-model="obj.queryParams.startTime" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="报离职截止时间:" prop="endTime">
                <el-date-picker class="width220" v-model="obj.queryParams.endTime" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="客户名称" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="项目城市" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单城市:" prop="receiveCity">
                <el-select class="width220" v-model="obj.queryParams.receiveCity" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方客服:" prop="receiveService">
                <el-select class="width220" v-model="obj.queryParams.receiveService" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方" prop="receiveServiceName">
                <el-input class="width220" v-model="obj.queryParams.receiveServiceName" placeholder="请输入接单方"
                    clearable />
            </el-form-item>
            <el-form-item label="是否单立户" prop="singleAccount">
                <el-select class="width220" v-model="obj.queryParams.singleAccount" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-button type="primary" icon="Download" @click="handleExport">数据导出</el-button>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<script setup name="AttritionStatement">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单

})

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}


</script>
<style lang="scss" scoped></style>