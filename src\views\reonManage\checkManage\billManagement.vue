<!-- 账单管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="客户帐套:" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户帐套" clearable>
                    <el-option label="帐套A" value="帐套A" />
                    <el-option label="帐套B" value="帐套B" />
                    <el-option label="帐套C" value="帐套C" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择账单年月"
                    clearable />
            </el-form-item>
            <el-form-item label="是否锁定:" prop="isLocked">
                <el-select class="width220" v-model="obj.queryParams.isLocked" placeholder="请选择是否锁定" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-form-item label="约定账单生成日:" prop="agreedBillGenerationDay">
                <el-input class="width220" v-model="obj.queryParams.agreedBillGenerationDay" placeholder="请输入生成日"
                    clearable />
            </el-form-item>
            <el-form-item label="约定账单锁定日:" prop="agreedBillLockDay">
                <el-input class="width220" v-model="obj.queryParams.agreedBillLockDay" placeholder="请输入锁定日" clearable />
            </el-form-item>
            <el-form-item label="账单类型:" prop="billType">
                <el-select class="width220" v-model="obj.queryParams.billType" placeholder="请选择账单类型" clearable>
                    <el-option label="类型A" value="A" />
                    <el-option label="类型B" value="B" />
                    <el-option label="类型C" value="C" />
                </el-select>
            </el-form-item>
            <el-form-item label="大集团:" prop="largeGroup">
                <el-select class="width220" v-model="obj.queryParams.largeGroup" placeholder="请选择大集团" clearable>
                    <el-option label="集团A" value="A" />
                    <el-option label="集团B" value="B" />
                    <el-option label="集团C" value="C" />
                </el-select>
            </el-form-item>
            <el-form-item label="生成状态:" prop="generationStatus">
                <el-select class="width220" v-model="obj.queryParams.generationStatus" placeholder="请选择生成状态" clearable>
                    <el-option label="未生成" value="0" />
                    <el-option label="已生成" value="1" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商核验状态:" prop="supplierVerificationStatus">
                <el-select class="width220" v-model="obj.queryParams.supplierVerificationStatus" placeholder="请选择核验状态"
                    clearable>
                    <el-option label="未核验" value="0" />
                    <el-option label="已核验" value="1" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商工资账单生成状态:" prop="supplierSalaryBillStatus">
                <el-select class="width220" v-model="obj.queryParams.supplierSalaryBillStatus" placeholder="请选择生成状态"
                    clearable>
                    <el-option label="未生成" value="0" />
                    <el-option label="已生成" value="1" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="success" plain icon="Plus" @click="generateBill">生成账单</el-button>
                        <el-button type="warning" plain icon="Printer" @click="billPrint">账单打印</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Lock" @click="handleLock">锁定</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Unlock" @click="handleUnlock">解锁</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Document" @click="handleBillLog">查看账单日志</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Upload" @click="handlePush">推送</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Close" @click="handleCancelPush">取消推送</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户帐套" align="center" prop="customerAccount" />
            <el-table-column label="账单类型" align="center" prop="billType" />
            <el-table-column label="账单年月" align="center" prop="billMonth" />
            <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
            <el-table-column label="账单人数" align="center" prop="billPersonCount" />
            <el-table-column label="应收金额" align="center" prop="receivableAmount" />
            <el-table-column label="账单版本" align="center" prop="billVersion" />
            <el-table-column label="生成状态" align="center" prop="generationStatus" />
            <el-table-column label="今日已生成次数" align="center" prop="todayGenerationCount" />
            <el-table-column label="账单状态" align="center" prop="billStatus" />
            <el-table-column label="首次生成时间" align="center" prop="firstGenerationTime" />
            <el-table-column label="生成人" align="center" prop="creator" />
            <el-table-column label="失败原因" align="center">
                <template #default="scope">
                    <el-button type="info" plain icon="View" @click="handleImport">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 查看账单日志 -->
        <el-dialog v-model="obj.dialogShow" title="查看账单日志" width="50%" append-to-body>
            <el-table :data="obj.tableData" border>
                <el-table-column label="客户名称" align="center" prop="customerName" />
                <el-table-column label="客户帐套" align="center" prop="customerAccount" />
                <el-table-column label="账单年月" align="center" prop="billMonth" />
                <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
                <el-table-column label="应收金额" align="center" prop="receivableAmount" />
                <el-table-column label="生成人" align="center" prop="creator" />
                <el-table-column label="创建时间" align="center" prop="createTime" />
                <el-table-column label="生成状态" align="center" prop="generationStatus" />
            </el-table>
            <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        </el-dialog>
    </div>
</template>

<script setup name="BillManagement">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    customerName: [
        { required: false, message: '客户名称不能为空', trigger: 'blur' }
    ],
    contractName: [
        { required: false, message: '合同名称不能为空', trigger: 'blur' }
    ],
    billMonth: [
        { required: false, message: '账单年月不能为空', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        contractName: null,
        customerAccount: null,
        billMonth: null,
        isLocked: null,
        agreedBillGenerationDay: null,
        agreedBillLockDay: null,
        billType: null,
        largeGroup: null,
        generationStatus: null,
        supplierVerificationStatus: null,
        supplierSalaryBillStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {},//导入表单
    dialogShow: false,//新增弹窗
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerName: '客户名称1',
                customerAccount: '帐套A',
                billType: '类型A',
                billMonth: '2023-01',
                financialReceivableMonth: '2023-02',
                billPersonCount: '10',
                receivableAmount: '10000',
                billVersion: '1.0',
                generationStatus: '已生成',
                todayGenerationCount: '1',
                billStatus: '已锁定',
                firstGenerationTime: '2023-01-01 10:00:00',
                creator: '张三',
                createTime: '2023-01-01 10:00:00',
                failReason: '无'
            },
            {
                id: 2,
                customerName: '客户名称2',
                customerAccount: '帐套B',
                billType: '类型B',
                billMonth: '2023-02',
                financialReceivableMonth: '2023-03',
                billPersonCount: '20',
                receivableAmount: '20000',
                billVersion: '1.0',
                generationStatus: '已生成',
                todayGenerationCount: '0',
                billStatus: '已锁定',
                firstGenerationTime: '2023-02-01 10:00:00',
                creator: '李四',
                createTime: '2023-02-01 10:00:00',
                failReason: '无'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 锁定按钮操作 */
function handleLock() {
    proxy.$modal.msgSuccess('锁定成功');
}

/** 解锁按钮操作 */
function handleUnlock() {
    proxy.$modal.msgSuccess('解锁成功');
}

/** 查看账单日志按钮操作 */
function handleBillLog() {
    obj.dialogShow = true;
}

/** 推送按钮操作 */
function handlePush() {
    proxy.$modal.msgSuccess('推送成功');
}

/** 取消推送按钮操作 */
function handleCancelPush() {
    proxy.$modal.msgSuccess('取消推送成功');
}

/** 生成账单按钮操作 */
function generateBill() {
    proxy.$modal.msgSuccess('生成账单成功');
}

/** 账单打印按钮操作 */
function billPrint() {
    proxy.$modal.msgSuccess('账单打印成功');
}

/** 查看失败原因按钮操作 */
function handleImport() {
    proxy.$modal.msgSuccess('查看失败原因成功');
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>