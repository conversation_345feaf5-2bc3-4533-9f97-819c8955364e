<template>
    <div>
        <el-dialog v-model="dialogShow" :title="props.title" width="65%" append-to-body draggable @close="close">
            <el-form :model="props.dialogForm" class="formHight" ref="formRef" inline label-width="auto">
                <el-form-item label="证件号码" prop="idNumber">
                    <el-input class="width220" v-model="props.idNumber" placeholder="请输入证件号码" />
                </el-form-item>
                <el-form-item label="证件类型" prop="idType">
                    <el-select class="width220" v-model="props.idType" placeholder="请选择证件类型">
                        <el-option label="身份证" value="1" />
                        <el-option label="护照" value="2" />
                        <el-option label="其他" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="雇员姓名" prop="employeeName">
                    <el-input class="width220" v-model="props.employeeName" placeholder="请输入雇员姓名" />
                </el-form-item>
                <el-form-item v-if="showUniqueId" label="唯一号" prop="uniqueId">
                    <el-input class="width220" v-model="props.uniqueId" placeholder="请输入唯一号" />
                </el-form-item>

                <el-form-item label="客户编号" prop="customerCode">
                    <el-input class="width220" v-model="props.customerCode" placeholder="请输入客户编号" />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-input class="width220" v-model="props.customerName" placeholder="请输入客户名称" />
                </el-form-item>

                <el-form-item v-if="isServiceContract" label="合同编号" prop="contractCode">
                    <el-input class="width220" v-model="props.contractCode" placeholder="请输入合同编号" />
                </el-form-item>
                <el-form-item v-if="isServiceContract" label="合同名称" prop="contractName">
                    <el-input class="width220" v-model="props.contractName" placeholder="请输入合同名称" />
                </el-form-item>

                <el-form-item v-if="isEmployeeContractAndRenewal" label="性别" prop="gender">
                    <el-select class="width220" v-model="props.gender" placeholder="请选择性别">
                        <el-option label="男" value="1" />
                        <el-option label="女" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="isEmployeeContractAndRenewal" label="年龄" prop="age">
                    <el-input class="width220" v-model="props.age" placeholder="请输入年龄" />
                </el-form-item>

                <el-form-item label="正式工资" prop="formalSalary">
                    <el-input class="width220" v-model="props.formalSalary" placeholder="请输入正式工资" />
                </el-form-item>
                <el-form-item v-if="showEmployeeFields" label="是否外呼" prop="isOutbound">
                    <el-select class="width220" v-model="props.isOutbound" placeholder="请选择是否外呼">
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="签署状态" prop="signStatus">
                    <el-select class="width220" v-model="props.signStatus" placeholder="请选择签署状态">
                        <el-option label="待签署" value="0" />
                        <el-option label="已签署" value="1" />
                        <el-option label="已撤回" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="签署日期" prop="signDate">
                    <el-date-picker v-model="props.signDate" type="date" placeholder="请选择签署日期" />
                </el-form-item>
                <el-form-item label="工作制" prop="workSystem">
                    <el-select class="width220" v-model="props.workSystem" placeholder="请选择工作制">
                        <el-option label="标准工时制" value="1" />
                        <el-option label="综合计算工时制" value="2" />
                        <el-option label="不定时工作制" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同类别" prop="contractType">
                    <el-select class="width220" v-model="props.contractType" placeholder="请选择合同类别">
                        <el-option label="劳动合同" value="1" />
                        <el-option label="劳务派遣协议" value="2" />
                        <el-option label="实习协议" value="3" />
                    </el-select>
                </el-form-item>

                <el-form-item v-if="showEmployeeFields" label="派遣期限起" prop="dispatchStartDate">
                    <el-date-picker v-model="props.dispatchStartDate" type="date" placeholder="请选择派遣期限起" />
                </el-form-item>
                <el-form-item v-if="showEmployeeFields" label="派遣期限止" prop="dispatchEndDate">
                    <el-date-picker v-model="props.dispatchEndDate" type="date" placeholder="请选择派遣期限止" />
                </el-form-item>

                <el-form-item label="劳动合同起" prop="contractStartDate">
                    <el-date-picker v-model="props.contractStartDate" type="date" placeholder="请选择劳动合同起" />
                </el-form-item>
                <el-form-item label="劳动合同止" prop="contractEndDate">
                    <el-date-picker v-model="props.contractEndDate" type="date" placeholder="请选择劳动合同止" />
                </el-form-item>
                <!-- 重新签署开始时间 -->
                <el-form-item v-if="showLookFields" label="重新签署开始时间" prop="reSignStartDate">
                    <el-date-picker v-model="props.reSignStartDate" type="date" placeholder="请选择重新签署开始时间" />
                </el-form-item>

                <el-form-item v-if="showProbationFields" label="是否有试用期" prop="hasProbation">
                    <el-select class="width220" v-model="props.hasProbation" placeholder="请选择是否有试用期">
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item v-if="showProbationFields" label="试用时间起" prop="probationStartDate">
                    <el-date-picker v-model="props.probationStartDate" type="date" placeholder="请选择试用时间起" />
                </el-form-item>
                <el-form-item v-if="showProbationFields" label="试用期月数" prop="probationMonths">
                    <el-input class="width220" v-model="props.probationMonths" placeholder="请输入试用期月数" />
                </el-form-item>
                <el-form-item v-if="showProbationFields" label="试用时间止" prop="probationEndDate">
                    <el-date-picker v-model="props.probationEndDate" type="date" placeholder="请选择试用时间止" />
                </el-form-item>
                <el-form-item v-if="showProbationFields" label="试用期工资" prop="probationSalary">
                    <el-input class="width220" v-model="props.probationSalary" placeholder="请输入试用期工资" />
                </el-form-item>


                <el-form-item label="合同签订地" prop="contractSignPlace">
                    <el-input class="width220" v-model="props.contractSignPlace" placeholder="请输入合同签订地" />
                </el-form-item>
                <el-form-item label="合同版本地" prop="contractVersionPlace">
                    <el-input class="width220" v-model="props.contractVersionPlace" placeholder="请输入合同版本地" />
                </el-form-item>
                <el-form-item label="合同版本" prop="contractVersion">
                    <el-input class="width220" v-model="props.contractVersion" placeholder="请输入合同版本" />
                </el-form-item>
                <el-form-item label="合同原则" prop="contractPrinciple">
                    <el-input class="width220" v-model="props.contractPrinciple" placeholder="请输入合同原则" />
                </el-form-item>

                <el-form-item v-if="showEntryTimeField" label="录入时间" prop="entryTime">
                    <el-date-picker v-model="props.entryTime" type="datetime" placeholder="请选择录入时间" />
                </el-form-item>

                <!-- 续签中间时间 -->
                <el-form-item v-if="showLookFields" label="续签中间时间" prop="renewalTime">
                    <el-date-picker v-model="props.renewalTime" type="datetime" placeholder="请选择续签中间时间" />
                </el-form-item>

                <el-form-item label="用工单位" prop="employmentUnit">
                    <el-input class="width220" v-model="props.employmentUnit" placeholder="请输入用工单位" />
                </el-form-item>
                <el-form-item v-if="showWorkFields" label="工作地（省/市）" prop="workPlace">
                    <el-input class="width220" v-model="props.workPlace" placeholder="请输入工作地" />
                </el-form-item>
                <el-form-item v-if="showWorkFields" label="工作岗位" prop="workPosition">
                    <el-input class="width220" v-model="props.workPosition" placeholder="请输入工作岗位" />
                </el-form-item>
                <div v-if="isLook">
                    <div class="mt10 mb10">新增操作记录</div>
                    <el-input type="textarea" :rows="3" v-model="props.addRecord" placeholder="请输入内容" />
                    <div class="mt10 mb10">修改操作记录</div>
                    <el-input type="textarea" :rows="3" v-model="props.updateRecord" placeholder="请输入内容" />
                    <div class="mt10 mb10">续签操作记录</div>
                    <el-input type="textarea" :rows="3" v-model="props.renewRecord" placeholder="请输入内容" />
                    <div class="mt10 mb10">终止操作记录</div>
                    <el-input type="textarea" :rows="3" v-model="props.terminateRecord" placeholder="请输入内容" />
                    <div class="mt10 mb10">重新签署操作记录</div>
                    <el-input type="textarea" :rows="3" v-model="props.reSignRecord" placeholder="请输入内容" />
                </div>
                <el-divider content-position="left">文件上传</el-divider>
                <el-row class="mt20">
                    <el-col :span="24">
                        <file-upload @update:modelValue="uploadedSuccessfully" v-model="props.isActive" />
                    </el-col>
                </el-row>
                <el-divider content-position="left">备注</el-divider>
                <el-row justify="center">
                    <el-col :span="18">
                        <el-input type="textarea" :rows="3" v-model="props.remark" placeholder="请输入内容" />
                    </el-col>
                </el-row>
            </el-form>
            <template #footer v-if="isAdd || isUpdate || isRenewal">
                <el-button type="primary" @click="handleSubmit">保 存</el-button>
                <el-button v-if="!isRenewal" type="primary" @click="handleSubmit">提 交</el-button>
                <el-button @click="close">取 消</el-button>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    dialogForm: {
        type: Object,
        default: () => { }
    },
    isAdd: {
        type: Boolean,
        default: false
    },
    isUpdate: {
        type: Boolean,
        default: false
    },
    isLook: {
        type: Boolean,
        default: false
    },
    isRenewal: {
        type: Boolean,
        default: false
    },
    menuName: {
        type: String,
        default: ''
    }
})
// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});
const menuName = ref(props.menuName)
const isAdd = ref(props.isAdd)
const isUpdate = ref(props.isUpdate)
const isLook = ref(props.isLook)
const isRenewal = ref(props.isRenewal)
watch(() => props.menuName, (newVal) => {
    menuName.value = newVal
})
watch(() => props.isAdd, (newVal) => {
    isAdd.value = newVal
})
watch(() => props.isUpdate, (newVal) => {
    isUpdate.value = newVal
})
watch(() => props.isLook, (newVal) => {
    isLook.value = newVal
})
watch(() => props.isRenewal, (newVal) => {
    isRenewal.value = newVal
})

// 计算属性，优化条件判断
const isEmployeeContract = computed(() => menuName.value === 'employeeContract')
const isServiceContract = computed(() => menuName.value === 'serviceContract')
const isEmployeeContract1 = computed(() => menuName.value === 'employeeContract1')
const isEmployeeContractAndRenewal = computed(() => isEmployeeContract.value && isRenewal.value)
const isEmployeeContractAndLook = computed(() => isEmployeeContract.value && isLook.value)
const isEmployeeContractNotRenewal = computed(() => isEmployeeContract.value && !isRenewal.value)
const isServiceContractAndAdd = computed(() => isServiceContract.value && isAdd.value)

// 整合条件判断的计算属性
const showUniqueId = computed(() => isEmployeeContract.value || (isServiceContract.value && !isAdd.value) || isEmployeeContract1.value)
const showEmployeeFields = computed(() => isEmployeeContract.value || isEmployeeContract1.value)
const showLookFields = computed(() => isEmployeeContractAndLook.value || isEmployeeContract1.value)
const showProbationFields = computed(() => isEmployeeContractNotRenewal.value || isEmployeeContract1.value)
const showEntryTimeField = computed(() => isEmployeeContractNotRenewal.value || isServiceContractAndAdd.value || isEmployeeContract1.value)
const showWorkFields = computed(() => isEmployeeContractNotRenewal.value || isServiceContract.value || isEmployeeContract1.value)

const emit = defineEmits(['close', 'update:dialogShow'])
const close = () => {
    dialogShow.value = false;
    emit('close')
}
</script>