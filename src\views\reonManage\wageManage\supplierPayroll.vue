<!-- 计税月限制白名单 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractNo">
                <el-input class="width220" v-model="obj.queryParams.contractNo" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="导入人:" prop="importUser">
                <el-select class="width220" v-model="obj.queryParams.importUser" placeholder="请选择" clearable>
                    <el-option v-for="item in importUserOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入编号:" prop="importCode">
                <el-input class="width220" v-model="obj.queryParams.importCode" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询 </el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" icon="Upload" plain @click="handleImport">导入</el-button>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <WithholdingAgent :loading="obj.loading" :tableData="obj.tableData"
            @handleSelectionChange="handleSelectionChange" />
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增/编辑对话框 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="500px" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" label-width="120px">
                <el-form-item label="合同编号:" prop="contractNo">
                    <el-input v-model="obj.dialogForm.contractNo" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="扣缴义务人:" prop="withholdingAgent">
                    <el-select v-model="obj.dialogForm.withholdingAgent" placeholder="请选择" clearable>
                        <el-option v-for="item in withholdingAgentOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="扣缴义务人类型:" prop="withholdingAgentType">
                    <el-select v-model="obj.dialogForm.withholdingAgentType" placeholder="请选择" clearable>
                        <el-option v-for="item in withholdingAgentTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工资发放地:" prop="salaryPaymentPlace">
                    <el-input v-model="obj.dialogForm.salaryPaymentPlace" placeholder="请输入" clearable />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="SupplierPayroll">
import WithholdingAgent from '@/views/reonManage/components/table/withholdingAgent.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 导入人选项
const importUserOptions = [
    { value: '1', label: '管理员' },
    { value: '2', label: '操作员A' },
    { value: '3', label: '操作员B' }
];

// 扣缴义务人选项
const withholdingAgentOptions = [
    { value: '1', label: '企业1' },
    { value: '2', label: '企业2' },
    { value: '3', label: '企业3' }
];

// 扣缴义务人类型选项
const withholdingAgentTypeOptions = [
    { value: '1', label: '企业' },
    { value: '2', label: '个人' }
];

// 表单验证规则
const rules = {
    contractNo: [
        { required: true, message: '请输入合同编号', trigger: 'blur' }
    ],
    withholdingAgent: [
        { required: true, message: '请选择扣缴义务人', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null, // 客户名称
        contractNo: null, // 合同编号
        importUser: null, // 导入人
        importCode: null, // 导入编号
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 显示对话框
    title: '', // 对话框标题
    ids: [], // 选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                customerName: '某某科技有限公司',
                contractNo: 'CT20230501',
                withholdingName: '王五',
                withholdingAgentName: '企业1',
                withholdingAgentType: '1',
                salaryPaymentPlace: '北京'
            },
            {
                id: 2,
                customerName: '某某信息技术有限公司',
                contractNo: 'CT20230601',
                withholdingName: '李四',
                withholdingAgentName: '企业2',
                withholdingAgentType: '1',
                salaryPaymentPlace: '上海'
            },
            {
                id: 3,
                customerName: '某某软件有限公司',
                contractNo: '**********',
                withholdingName: '张三',
                withholdingAgentName: '企业3',
                withholdingAgentType: '2',
                salaryPaymentPlace: '广州'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}



// 表单重置
function resetForm() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}
// 导入
function handleImport() {
    proxy.$modal.msgSuccess('开始导入数据');
    // 实际项目中应该打开导入对话框
}

// 新增
function handleAdd() {
    obj.title = '新增供应商工资信息';
    resetForm();
    obj.dialogShow = true;
}

// 修改
function handleEdit(row) {
    obj.title = '修改供应商工资信息';
    obj.dialogForm = JSON.parse(JSON.stringify(row));
    obj.dialogShow = true;
}

// 删除
function handleDelete(row) {
    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

// 提交表单
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}

.dialog-footer {
    text-align: center;
    padding-top: 10px;
}
</style>