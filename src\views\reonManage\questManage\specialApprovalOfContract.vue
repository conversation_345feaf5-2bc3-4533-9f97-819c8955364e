<!-- 合同特殊审批流程 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="审批类型:" prop="approvalType">
                <el-select class="width220" v-model="obj.queryParams.approvalType" placeholder="请选择" clearable>
                    <el-option v-for="item in approvalTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="名称:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="审批状态:" prop="approvalStatus">
                <el-select class="width220" v-model="obj.queryParams.approvalStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in approvalStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="特殊审批名称" align="center" prop="name" />
            <el-table-column label="类型" align="center" prop="type" />
            <el-table-column label="审批状态" align="center" prop="status" />
            <el-table-column label="提交人" align="center" prop="submitter" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="操作" align="center" width="180">
                <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="SpecialApprovalOfContract">
import { listApproval } from '@/api/reonApi/approval';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 审批类型选项
const approvalTypeOptions = [
    { value: '1', label: '合同特殊审批' },
    { value: '2', label: '价格特殊审批' },
    { value: '3', label: '付款特殊审批' },
    { value: '4', label: '其他特殊审批' }
];

// 审批状态选项
const approvalStatusOptions = [
    { value: '1', label: '待审批' },
    { value: '2', label: '审批中' },
    { value: '3', label: '已审批' },
    { value: '4', label: '已驳回' },
    { value: '5', label: '已终止' }
];


const obj = reactive({
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        approvalType: null,
        name: null,
        approvalStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    listApproval(obj.queryParams).then(response => {
        obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 查看详情 */
function handleDetail(row) {
    proxy.$modal.msgInfo(`查看ID为${row.id}的详细信息`);
}


getList();
</script>
<style lang="scss" scoped></style>