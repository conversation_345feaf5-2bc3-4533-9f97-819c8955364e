<!-- 社保和最低工资维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="所属城市:" prop="cityCode">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="服务网点:" prop="servicePoint">
                <el-select class="width220" filterable v-model="obj.queryParams.servicePoint" placeholder="请选择服务网点"
                    clearable>
                    <el-option v-for="item in servicePointOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.single" @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="城市名称" align="center" prop="cityName" />
            <el-table-column label="服务网点" align="center" prop="servicePoint" />
            <el-table-column label="开始时间" align="center" prop="startDate" />
            <el-table-column label="结束时间" align="center" prop="endDate" />
            <el-table-column label="最低工资" align="center" prop="minimumWage" />
            <el-table-column label="年度市平均工资" align="center" prop="cityAverageSalary" />
            <el-table-column label="年度省平均工资" align="center" prop="provinceAverageSalary" />
            <el-table-column label="政策链接" align="center" prop="policyLink" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="修改人" align="center" prop="updateBy" />
            <el-table-column label="修改时间" align="center" prop="updateTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="55%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-row>
                    <el-form-item label="所属城市" prop="city">
                        <el-select :disabled="obj.isDetail" class="width220" filterable v-model="obj.dialogForm.city"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                                :value="item.code" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="服务网点" prop="type">
                        <el-select :disabled="obj.isDetail" class="width220" filterable v-model="obj.dialogForm.type"
                            placeholder="请选择" clearable>
                            <el-option v-for="item in sys_job_group" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-row>
                <el-row :gutter="10" class="mb8" v-if="!obj.isDetail">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Plus" @click="dialogAdd">新增</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" plain icon="Delete" @click="dialogDelete">删除</el-button>
                    </el-col>
                </el-row>
                <el-table :data="obj.dialogForm.tableData" border @selection-change="dialogSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column label="起始月" align="center" prop="id">
                        <template #default="scope">
                            <el-select :disabled="obj.isDetail" style="width: 100%;" v-model="scope.row.id"
                                placeholder="请选择">
                                <el-option v-for="item in 12" :key="item" :label="item" :value="item" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="截止月" align="center" prop="id">
                        <template #default="scope">
                            <el-select :disabled="obj.isDetail" style="width: 100%;" v-model="scope.row.id"
                                placeholder="请选择">
                                <el-option v-for="item in 12" :key="item" :label="item" :value="item" />
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="最低工资" align="center" prop="id">
                        <template #default="scope">
                            <el-input :disabled="obj.isDetail" style="width: 100%;" v-model="scope.row.id" />
                        </template>
                    </el-table-column>
                    <el-table-column label="年度省平均工资" align="center" prop="id">
                        <template #default="scope">
                            <el-input :disabled="obj.isDetail" style="width: 100%;" v-model="scope.row.id" />
                        </template>
                    </el-table-column>
                    <el-table-column label="年度市平均工资" align="center" prop="id">
                        <template #default="scope">
                            <el-input :disabled="obj.isDetail" style="width: 100%;" v-model="scope.row.id" />
                        </template>
                    </el-table-column>
                    <el-table-column label="政策链接" align="center" prop="id">
                        <template #default="scope">
                            <el-input :disabled="obj.isDetail" style="width: 100%;" v-model="scope.row.id" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center">
                        <template #default="scope">
                            <el-button link type="danger" icon="Delete"
                                @click="dialogDelete(scope.row, scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="SocialSecurity_minimumWage">

import { useAreaStore } from '@/store/modules/area'
import { listImport } from "@/api/reonApi/import";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据

// 服务网点选项
const servicePointOptions = [
    { value: '北京市总部', label: '北京市总部' },
    { value: '上海市总部', label: '上海市总部' },
    { value: '广州市总部', label: '广州市总部' },
    { value: '深圳市总部', label: '深圳市总部' },
    { value: '成都市总部', label: '成都市总部' },
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cityCode: null,
        servicePoint: null,
    },//查询表单
    rules: {
        city: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        type: [{ required: true, message: '请选择人员类型', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            cityName: '北京市',
            servicePoint: '北京市总部',
            startDate: '2023-01-01',
            endDate: '2023-12-31',
            minimumWage: '2600',
            cityAverageSalary: '12000',
            provinceAverageSalary: '10000',
            policyLink: 'https://example.com/policy',
            remark: '北京市最低工资标准',
            createBy: 'admin',
            createTime: '2023-01-01 10:00:00',
            updateBy: 'admin',
            updateTime: '2023-01-01 10:00:00'
        },
        {
            id: 2,
            cityName: '上海市',
            servicePoint: '上海市总部',
            startDate: '2023-01-01',
            endDate: '2023-12-31',
            minimumWage: '2590',
            cityAverageSalary: '13000',
            provinceAverageSalary: '11000',
            policyLink: 'https://example.com/policy',
            remark: '上海市最低工资标准',
            createBy: 'admin',
            createTime: '2023-01-01 10:00:00',
            updateBy: 'admin',
            updateTime: '2023-01-01 10:00:00'
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    dialogIds: [],//选中的id
    title: "",//标题
    isDetail: false,//是否详情
})

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {
        tableData: []
    };
    obj.isDetail = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.title = "新增最低工资维护信息";
    obj.dialogShow = true;
}
/** 查看按钮操作 */
function handleDetail(row) {
    console.log(row);
    obj.isDetail = true;
    obj.dialogForm = row;
    obj.title = "查看详细信息";
    obj.dialogShow = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = {
        ...row,
        tableData: [
            {
                id: 1,
                startMonth: 1,
                endMonth: 12,
                minimumWage: row.minimumWage,
                provinceAverageSalary: row.provinceAverageSalary,
                cityAverageSalary: row.cityAverageSalary,
                policyLink: row.policyLink
            }
        ]
    };
    obj.title = "修改最低工资信息";
    obj.dialogShow = true;
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 新增 */
function dialogAdd() {
    obj.dialogForm.tableData.push({})
}

/** 删除 */
function dialogDelete(row, index) {
    if (index >= 0) {
        obj.dialogForm.tableData.splice(index, 1)
    } else {
        obj.dialogForm.tableData = obj.dialogForm.tableData.filter(item => !obj.dialogIds.includes(item))
    }
}

/** 选择 */
function dialogSelectionChange(selection) {
    obj.dialogIds = selection.map(item => item);
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped></style>