import { defineStore } from "pinia";
import ChinaCitys from "@/utils/ChinaCitys.json";

export const useAreaStore = defineStore("area", () => {
  const provinces = ChinaCitys;

  /**
   * 根据省份代码获取城市列表
   *
   * 此函数通过查找给定省份代码对应的省份对象，来获取该省份下的所有城市列表
   * 如果找到了对应的省份，则返回该省份的城市列表；如果未找到，则返回一个空数组
   *
   * @param {string} provinceCode - 省份代码，用于查找对应的省份对象
   * @returns {Array} - 返回城市列表数组，如果未找到省份则返回空数组
   */
  const getCities = (provinceCode) => {
    // 查找与给定省份代码匹配的省份对象
    const province = provinces.find((p) => p.code === provinceCode);
    // 如果找到了省份对象，则返回其城市列表；否则返回空数组
    return province ? province.cities : [];
  };

  /**
   * 根据省份代码和城市代码获取对应的地区列表
   * 此函数首先根据省份代码找到对应的省份对象，然后在该省份对象中
   * 根据城市代码找到对应的城市对象，最后返回该城市对象中的地区列表
   * 如果未找到对应的省份或城市，则返回空数组
   *
   * @param {string} provinceCode - 省份代码
   * @param {string} cityCode - 城市代码
   * @returns {array} 地区列表，如果没有找到对应的省份或城市，则返回空数组
   */
  const getDistricts = (provinceCode, cityCode) => {
    // 根据省份代码找到对应的省份对象
    const province = provinces.find((p) => p.code === provinceCode);
    // 如果未找到对应的省份，则返回空数组
    if (!province) return [];
    // 在找到的省份对象中，根据城市代码找到对应的城市对象
    const city = province.cities.find((c) => c.code === cityCode);
    // 如果找到了对应的城市对象，则返回该对象中的地区列表，否则返回空数组
    return city ? city.districts : [];
  };

  return {
    provinces,
    getCities,
    getDistricts,
  };
});
