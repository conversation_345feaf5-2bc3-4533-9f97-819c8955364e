<template>
    <el-form>
        <el-form-item>
            <el-radio v-model='radioValue' :value="1">
                月，允许的通配符[, - * /]
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="2">
                周期从
                <el-input-number v-model='cycle01' :min="1" :max="11" /> -
                <el-input-number v-model='cycle02' :min="cycle01 + 1" :max="12" /> 月
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="3">
                从
                <el-input-number v-model='average01' :min="1" :max="11" /> 月开始，每
                <el-input-number v-model='average02' :min="1" :max="12 - average01" /> 月月执行一次
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="4">
                指定
                <el-select clearable v-model="checkboxList" placeholder="可多选" multiple :multiple-limit="8">
                    <el-option v-for="item in monthList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
            </el-radio>
        </el-form-item>
    </el-form>
</template>

<script setup>
const emit = defineEmits(['update'])
const props = defineProps({
    cron: {
        type: Object,
        default: {
            second: "*",
            min: "*",
            hour: "*",
            day: "*",
            month: "*",
            week: "?",
            year: "",
        }
    },
    check: {
        type: Function,
        default: () => {
        }
    }
})
const radioValue = ref(1)
const cycle01 = ref(1)
const cycle02 = ref(2)
const average01 = ref(1)
const average02 = ref(1)
const checkboxList = ref([])
const checkCopy = ref([1])
const monthList = ref([
    { key: 1, value: '一月' },
    { key: 2, value: '二月' },
    { key: 3, value: '三月' },
    { key: 4, value: '四月' },
    { key: 5, value: '五月' },
    { key: 6, value: '六月' },
    { key: 7, value: '七月' },
    { key: 8, value: '八月' },
    { key: 9, value: '九月' },
    { key: 10, value: '十月' },
    { key: 11, value: '十一月' },
    { key: 12, value: '十二月' }
])
const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, 1, 11)
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, 12)
    return cycle01.value + '-' + cycle02.value
})
const averageTotal = computed(() => {
    average01.value = props.check(average01.value, 1, 11)
    average02.value = props.check(average02.value, 1, 12 - average01.value)
    return average01.value + '/' + average02.value
})
const checkboxString = computed(() => {
    return checkboxList.value.join(',')
})
watch(() => props.cron.month, value => changeRadioValue(value))
watch([radioValue, cycleTotal, averageTotal, checkboxString], () => onRadioChange())
/**
 * 根据不同的输入值类型更新radioValue值
 * 此函数解释了如何根据输入字符串的不同格式来更新radioValue以及相关的周期或平均值
 * @param {string} value - 输入的字符串值，用于决定radioValue的值和更新其他相关数据
 */
function changeRadioValue(value) {
    // 当输入值为'*'时，设置radioValue为1
    if (value === '*') {
        radioValue.value = 1
    } else if (value.indexOf('-') > -1) {
        // 当输入值包含'-'时，表示这是一个范围值
        // 将输入值分割为起始和结束值，并设置相应的周期值
        const indexArr = value.split('-')
        cycle01.value = Number(indexArr[0])
        cycle02.value = Number(indexArr[1])
        radioValue.value = 2
    } else if (value.indexOf('/') > -1) {
        // 当输入值包含'/'时，表示这是一个平均值
        // 将输入值分割为分子和分母，并设置相应的平均值
        const indexArr = value.split('/')
        average01.value = Number(indexArr[0])
        average02.value = Number(indexArr[1])
        radioValue.value = 3
    } else {
        // 当输入值是逗号分隔的数字列表时
        // 将输入值转换为唯一的数字数组，并设置radioValue为4
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))]
        radioValue.value = 4
    }
}
/**
 * 处理单选按钮变化事件
 * 此函数根据单选按钮的值来决定如何更新数据
 * 它使用emit函数来触发更新事件，传递不同的参数根据用户选择
 */
function onRadioChange() {
    // 根据单选按钮的值执行不同的操作
    switch (radioValue.value) {
        case 1:
            // 当单选按钮值为1时，触发更新事件，传递通配符'*'
            emit('update', 'month', '*', 'month')
            break
        case 2:
            // 当单选按钮值为2时，触发更新事件，传递周期总数
            emit('update', 'month', cycleTotal.value, 'month')
            break
        case 3:
            // 当单选按钮值为3时，触发更新事件，传递平均值总数
            emit('update', 'month', averageTotal.value, 'month')
            break
        case 4:
            // 当单选按钮值为4时，处理多选框列表
            if (checkboxList.value.length === 0) {
                // 如果多选框列表为空，则添加第一个可用选项
                checkboxList.value.push(checkCopy.value[0])
            } else {
                // 如果列表不为空，则将当前选项复制给备份变量
                checkCopy.value = checkboxList.value
            }
            // 触发更新事件，传递多选框选项字符串
            emit('update', 'month', checkboxString.value, 'month')
            break
    }
}
</script>

<style lang="scss" scoped>
.el-input-number--small,
.el-select,
.el-select--small {
    margin: 0 0.2rem;
}

.el-select,
.el-select--small {
    width: 18.8rem;
}
</style>