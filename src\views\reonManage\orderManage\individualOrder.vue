<!-- 生成个人订单 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="客户编号:" prop="customerCode">
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入客户编号" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-input class="width220" v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称"
                    clearable />
            </el-form-item>
            <el-form-item label="接单方客服:" prop="orderService">
                <el-input class="width220" v-model="obj.queryParams.orderService" placeholder="请输入接单方客服" clearable />
            </el-form-item>
            <el-form-item label="接单方:" prop="orderReceiver">
                <el-input class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请输入接单方" clearable />
            </el-form-item>
            <el-form-item label="派单方:" prop="orderSender">
                <el-input class="width220" v-model="obj.queryParams.orderSender" placeholder="请输入派单方" clearable />
            </el-form-item>
            <el-form-item label="入离职状态:" prop="employmentStatus">
                <el-select class="width220" v-model="obj.queryParams.employmentStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in employmentStatusList" :key="item.code" :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="雇员状态:" prop="employeeStatus">
                <el-select class="width220" v-model="obj.queryParams.employeeStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in employeeStatusList" :key="item.code" :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="!obj.single" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="!obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Check" @click="handleSubmit">提交确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleProductAmount">产品金额修正</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail" :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员编号" align="center" prop="employeeCode" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="入职日期" align="center" prop="entryDate" />
            <el-table-column label="申请入职时间" align="center" prop="applyEntryDate" />
            <el-table-column label="增员状态" align="center" prop="employeeStatus" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" link icon="View" @click="handleDetail(scope.row)">详情</el-button>
                    <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                    <el-button type="success" link icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 导入 -->
        <order-contract-reserve-fund type="IndividualOrder" v-model:dialogShow="obj.dialogShow" :title="obj.title"
            :form="obj.dialogForm" :tableData="obj.tableData" :tableData_no="obj.tableData_no"
            :isDetail="obj.isDetail" />

    </div>
</template>

<script setup name="IndividualOrder">
import { listScale } from "@/api/reonApi/scale";
import OrderContractReserveFund from '@/views/reonManage/components/dialog/orderContractReserveFund.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 入离职状态列表
const employmentStatusList = ref([]);
// 雇员状态列表
const employeeStatusList = ref([]);

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null,
        employeeName: null,
        customerCode: null,
        customerName: null,
        smallContractName: null,
        orderService: null,
        orderReceiver: null,
        orderSender: null,
        employmentStatus: null,
        employeeStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    tableData_no: [],//非社保公积金列表
    dialogForm: {},//弹出框表单
    dialogShow: false,//弹出框
    ids: [],//选中的id
    title: "",//标题
    isDetail: false,//是否详情
    isEdit: false,//是否编辑
})

const tableRowClassName = ({ row }) => {
    if (row.id === 1) {
        return 'warning-row'
    } else if (row.id === 3) {
        return 'success-row'
    }
    return ''
}

/** 获取入离职状态列表 */
function getEmploymentStatusList() {
    // 这里可以调用API获取入离职状态列表
    employmentStatusList.value = [
        { code: '1', name: '在职' },
        { code: '2', name: '离职' },
        { code: '3', name: '待入职' }
    ];
}

/** 获取雇员状态列表 */
function getEmployeeStatusList() {
    // 这里可以调用API获取雇员状态列表
    employeeStatusList.value = [
        { code: '1', name: '正常' },
        { code: '2', name: '待审核' },
        { code: '3', name: '已挂起' },
        { code: '4', name: '已驳回' }
    ];
}

// 表单重置
function reset() {
    obj.dialogForm = {
        tableData: []
    };
    obj.isEdit = false;
    obj.isDetail = false;
    obj.activeTab = '0';
    proxy.resetForm("formRef");
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeCode: 'YG20230001',
                employeeName: '雇员姓名1',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                smallContractName: '小合同名称1',
                entryDate: '2023-01-01',
                applyEntryDate: '2022-12-15',
                employeeStatus: '正常'
            },
            {
                id: 2,
                employeeCode: '**********',
                employeeName: '雇员姓名2',
                customerCode: '**********',
                customerName: '客户名称2',
                smallContractName: '小合同名称2',
                entryDate: '2023-02-01',
                applyEntryDate: '2023-01-15',
                employeeStatus: '待审核'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = '新增个人订单';
}

/** 修改 */
function handleEdit() {
    reset();
    obj.isEdit = true;
    obj.dialogShow = true;
    // 加载选中行的数据
    const selectedRow = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedRow) {
        obj.dialogForm = { ...selectedRow };
    }
    obj.title = '修改个人订单';
}

/** 详情 */
function handleDetail(row) {
    reset();
    obj.isDetail = true;
    obj.dialogShow = true;
    if (row) {
        // 如果是从表格行点击进入，可以加载该行的数据
        obj.dialogForm = { ...row };
    }
    obj.title = '查看个人订单';
}

/** 关闭弹窗 */
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {};
    obj.isDetail = false;
}

/** 删除 */
function handleDelete() {
    proxy.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        // 这里可以调用API进行删除操作
        proxy.$modal.msgSuccess("删除成功!");
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 提交确认 */
function handleSubmit() {
    proxy.$modal.confirm('是否确认提交选中的订单？').then(function () {
        // 这里可以调用API进行提交操作
        proxy.$modal.msgSuccess("提交成功!");
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消提交");
    });
}

/** 产品金额修正 */
function handleProductAmount() {
    proxy.$modal.confirm('是否确认产品金额修正？').then(function () {
        // 这里可以调用API进行金额修正操作
        proxy.$modal.msgSuccess("产品金额修正成功!");
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消产品金额修正");
    });
}

// 初始化数据
getEmploymentStatusList();
getEmployeeStatusList();
getList();
</script>
<style lang="scss" scoped>
:deep(.warning-row) {
    color: red;
}
</style>