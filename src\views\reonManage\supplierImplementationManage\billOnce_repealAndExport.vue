<!-- 账单一次性废除与导出 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplier">
                <el-select class="width220" v-model="obj.queryParams.supplier" placeholder="请选择" clearable>
                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商账单模板:" prop="billTemplate">
                <el-select class="width220" v-model="obj.queryParams.billTemplate" placeholder="请选择" clearable>
                    <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报表年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择年月"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mt20 mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Delete" @click="handleRepeal">废除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table ref="refTable" v-loading="obj.loading" border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商名称" align="center" prop="supplier">
                <template #default="scope">
                    {{supplierOptions.find(item => item.value === scope.row.supplier)?.label || '未知供应商'}}
                </template>
            </el-table-column>
            <el-table-column label="账单模板" align="center" prop="billTemplate">
                <template #default="scope">
                    {{billTemplateOptions.find(item => item.value === scope.row.billTemplate)?.label || '未知模板'}}
                </template>
            </el-table-column>
            <el-table-column label="账单年月" align="center" prop="billMonth" />
            <el-table-column label="一级类别" align="center" prop="firstCategory">
                <template #default="scope">
                    {{firstCategoryOptions.find(item => item.value === scope.row.firstCategory)?.label || '未知类别'}}
                </template>
            </el-table-column>
            <el-table-column label="二级类别" align="center" prop="secondCategory">
                <template #default="scope">
                    {{secondCategoryOptions.find(item => item.value === scope.row.secondCategory)?.label || '未知类别'}}
                </template>
            </el-table-column>
            <el-table-column label="金额" align="center" prop="amount" />
            <el-table-column label="金额(不含税)" align="center" prop="amountWithoutTax" />
            <el-table-column label="增值税率(%)" align="center" prop="taxRate" />
            <el-table-column label="增值税" align="center" prop="tax" />
            <el-table-column label="总人次" align="center" prop="totalPeople" />
            <el-table-column label="一次性支持人员" align="center" prop="oneTimeSupportPeople" />
            <el-table-column label="报表锁定状态" align="center" prop="reportLockStatus">
                <template #default="scope">
                    <el-tag :type="scope.row.reportLockStatus === '1' ? 'danger' : 'success'">
                        {{reportLockStatusOptions.find(item => item.value === scope.row.reportLockStatus)?.label ||
                            '未知状态'}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <el-tag :type="scope.row.status === '2' ? 'danger' : 'success'">
                        {{statusOptions.find(item => item.value === scope.row.status)?.label || '未知状态'}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" />
        </el-table>
        <BillOneTime :tableData="obj.tableData" :loading="obj.loading" menuName="billOnce_repealAndExport"
            @selectionChange="handleSelectionChange" />
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 废除确认弹窗 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="55%" append-to-body>
            <el-form :model="obj.dialogForm" label-width="auto">
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入废除原因" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleRepealConfirm">确认</el-button>
                <el-button @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup name="BillOnce_repealAndExport">
import { listScale } from "@/api/reonApi/scale";
import BillOneTime from '@/views/reonManage/components/table/billOneTime.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 供应商选项
const supplierOptions = [
    { value: '1', label: '供应商A' },
    { value: '2', label: '供应商B' },
    { value: '3', label: '供应商C' }
];

// 账单模板选项
const billTemplateOptions = [
    { value: '1', label: '模板1' },
    { value: '2', label: '模板2' },
    { value: '3', label: '模板3' }
];

// 一级类别选项
const firstCategoryOptions = [
    { value: '1', label: '社保' },
    { value: '2', label: '公积金' },
    { value: '3', label: '其他' }
];

// 二级类别选项
const secondCategoryOptions = [
    { value: '1', label: '养老保险' },
    { value: '2', label: '医疗保险' },
    { value: '3', label: '失业保险' },
    { value: '4', label: '公积金' },
    { value: '5', label: '服务费' }
];

// 报表锁定状态选项
const reportLockStatusOptions = [
    { value: '1', label: '已锁定' },
    { value: '0', label: '未锁定' }
];

// 状态选项
const statusOptions = [
    { value: '1', label: '正常' },
    { value: '2', label: '已废除' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        supplier: '1',
        billTemplate: '1',
        billMonth: '2023-01',
        firstCategory: '1',
        secondCategory: '1',
        amount: 10000,
        amountWithoutTax: 9433.96,
        taxRate: 6,
        tax: 566.04,
        totalPeople: 100,
        oneTimeSupportPeople: 5,
        reportLockStatus: '1',
        status: '1',
        remark: '正常账单'
    },
    {
        id: 2,
        supplier: '2',
        billTemplate: '2',
        billMonth: '2023-02',
        firstCategory: '2',
        secondCategory: '4',
        amount: 8000,
        amountWithoutTax: 7547.17,
        taxRate: 6,
        tax: 452.83,
        totalPeople: 80,
        oneTimeSupportPeople: 0,
        reportLockStatus: '1',
        status: '1',
        remark: '正常账单'
    },
    {
        id: 3,
        supplier: '3',
        billTemplate: '3',
        billMonth: '2023-03',
        firstCategory: '3',
        secondCategory: '5',
        amount: 5000,
        amountWithoutTax: 4716.98,
        taxRate: 6,
        tax: 283.02,
        totalPeople: 50,
        oneTimeSupportPeople: 10,
        reportLockStatus: '0',
        status: '2',
        remark: '已废除账单'
    }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplier: null, // 供应商
        billTemplate: null, // 账单模板
        billMonth: null, // 报表年月
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        remark: '' // 备注
    }, // 废除表单
    dialogShow: false, // 废除确认弹窗
    ids: [], // 选中的id
    title: "废除确认" // 标题
});

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.supplier) {
            filteredData = filteredData.filter(item => item.supplier === obj.queryParams.supplier);
        }

        if (obj.queryParams.billTemplate) {
            filteredData = filteredData.filter(item => item.billTemplate === obj.queryParams.billTemplate);
        }

        if (obj.queryParams.billMonth) {
            const month = new Date(obj.queryParams.billMonth).toISOString().slice(0, 7);
            filteredData = filteredData.filter(item => item.billMonth === month);
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/**选择行 */
function clickRow(row) {
    proxy.$refs["refTable"].toggleRowSelection(row);
}

// 废除
function handleRepeal() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要废除的记录');
        return;
    }

    // 检查是否有已废除的记录
    const selectedRows = obj.tableData.filter(item => obj.ids.includes(item.id));
    const hasInvalidStatus = selectedRows.some(item => item.status === '2');

    if (hasInvalidStatus) {
        proxy.$modal.msgError('选中的记录中包含已废除的记录，请重新选择');
        return;
    }

    obj.dialogShow = true;
}

// 废除确认
function handleRepealConfirm() {
    if (!obj.dialogForm.remark) {
        proxy.$modal.msgError('请输入废除原因');
        return;
    }

    // 实际项目中应该调用API进行废除
    proxy.$modal.msgSuccess('废除成功');
    obj.dialogShow = false;

    // 更新本地数据
    obj.ids.forEach(id => {
        const index = obj.tableData.findIndex(item => item.id === id);
        if (index !== -1) {
            obj.tableData[index].status = '2';
            obj.tableData[index].remark = obj.dialogForm.remark;
        }
    });

    obj.dialogForm.remark = '';
    obj.ids = [];
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped></style>