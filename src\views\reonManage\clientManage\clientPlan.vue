<!-- 客户方案 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customer">
                <el-input class="width220" v-model="obj.queryParams.customer" placeholder="请输入客户" />
            </el-form-item>
            <el-form-item label="方案名称:" prop="solutionName">
                <el-input class="width220" v-model="obj.queryParams.solutionName" placeholder="请输入方案名称" />
            </el-form-item>
            <el-form-item label="方案编号:" prop="solutionCode">
                <el-input class="width220" v-model="obj.queryParams.solutionCode" placeholder="请输入方案编号" />
            </el-form-item>
            <el-form-item label="状态:" prop="status">
                <el-select class="width220" v-model="obj.queryParams.status" placeholder="请选择" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="方案类型:" prop="solutionType">
                <el-select class="width220" v-model="obj.queryParams.solutionType" placeholder="请选择" clearable>
                    <el-option v-for="item in solutionTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-select class="width220" filterable v-model="obj.queryParams.supplierName" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增客户方案</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single"
                    @click="handleUpdate">修改客户方案</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Hide" @click="handleHide">隐藏客户方案</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商名称" align="center" prop="supplierName" />
            <el-table-column label="方案名称" align="center" prop="solutionName" />
            <el-table-column label="方案编号" align="center" prop="solutionCode" />
            <el-table-column label="方案类型" align="center" prop="solutionType" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="方案成本价" align="center" prop="costPrice" />
            <el-table-column label="方案指导价" align="center" prop="guidePrice" />
            <el-table-column label="保险形式" align="center" prop="insuranceType" />
            <el-table-column label="付费方式" align="center" prop="paymentMethod" />
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
                        {{ scope.row.status === '1' ? '有效' : '无效' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="操作" align="center" width="150">
                <template #default="scope">
                    <el-button text size="small" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button text size="small" @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="65%" append-to-body draggable>
            <el-form class="form-container" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="客户" prop="customer" required>
                    <el-select class="width220" filterable v-model="obj.dialogForm.customer" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="方案编号" prop="solutionCode" required>
                    <el-input class="width220" v-model="obj.dialogForm.solutionCode" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="方案名称" prop="solutionName" required>
                    <el-input class="width220" v-model="obj.dialogForm.solutionName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="付费方式" prop="paymentMethod">
                    <el-select class="width220" v-model="obj.dialogForm.paymentMethod" placeholder="请选择" clearable>
                        <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="方案成本价" prop="costPrice" required>
                    <el-input class="width220" v-model="obj.dialogForm.costPrice" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="方案指导价" prop="guidePrice" required>
                    <el-input class="width220" v-model="obj.dialogForm.guidePrice" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="供应商" prop="supplier" required>
                    <el-select class="width220" filterable v-model="obj.dialogForm.supplier" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="保险形式" prop="insuranceType">
                    <el-select class="width220" v-model="obj.dialogForm.insuranceType" placeholder="请选择" clearable>
                        <el-option v-for="item in insuranceTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-divider content-position="left">供应商产品</el-divider>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="24">
                        <el-button type="primary" plain icon="Plus" @click="handleAddProduct">选择供应商产品</el-button>
                        <el-button type="danger" plain icon="Delete" :disabled="obj.productMultiple"
                            @click="handleDeleteProduct">删除</el-button>
                    </el-col>
                </el-row>
                <el-table :data="obj.productData" border @selection-change="handleProductSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" type="index" width="50" align="center" />
                    <el-table-column label="供应商" align="center" prop="supplierName" />
                    <el-table-column label="产品名称" align="center" prop="productName" />
                    <el-table-column label="产品类型" align="center" prop="productType" />
                    <el-table-column label="基准保额" align="center" prop="baseInsuranceAmount" />
                    <el-table-column label="基准月付成本" align="center" prop="baseMonthCost" />
                    <el-table-column label="基准年付成本" align="center" prop="baseYearCost" />
                    <el-table-column label="基准月付指导价" align="center" prop="baseMonthGuidePrice" />
                    <el-table-column label="基准年付指导价" align="center" prop="baseYearGuidePrice" />
                    <el-table-column label="是否为必选项" align="center" prop="isRequired">
                        <template #default="scope">
                            <el-tag :type="scope.row.isRequired === '1' ? 'danger' : 'info'">
                                {{ scope.row.isRequired === '1' ? '是' : '否' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="120">
                        <template #default="scope">
                            <el-button text size="small" @click="handleEditProduct(scope.row)">编辑</el-button>
                            <el-button text size="small" @click="handleViewProduct(scope.row)">查看</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ClientPlan">

import { listScale } from "@/api/reonApi/scale";

// 初始化常量和对象
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const formRef = ref(null)
const queryRef = ref(null)

// 选项数据
const statusOptions = ref([
    { value: '1', label: '有效' },
    { value: '0', label: '无效' }
])

const solutionTypeOptions = ref([
    { value: '1', label: '基础方案' },
    { value: '2', label: '标准方案' },
    { value: '3', label: '高端方案' }
])

const supplierOptions = ref([
    { value: '1', label: '供应商A' },
    { value: '2', label: '供应商B' },
    { value: '3', label: '供应商C' }
])

const customerOptions = ref([
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
])

const paymentMethodOptions = ref([
    { value: '1', label: '月付' },
    { value: '2', label: '季付' },
    { value: '3', label: '年付' }
])

const insuranceTypeOptions = ref([
    { value: '1', label: '商业保险' },
    { value: '2', label: '社会保险' },
    { value: '3', label: '意外保险' }
])

// 响应式数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    productMultiple: true, // 产品是否多选
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customer: null,
        solutionName: null,
        solutionCode: null,
        status: null,
        solutionType: null,
        supplierName: null
    }, // 查询表单
    rules: {
        customer: [{ required: true, message: '请选择客户', trigger: 'change' }],
        solutionCode: [{ required: true, message: '请输入方案编号', trigger: 'blur' }],
        solutionName: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
        costPrice: [{ required: true, message: '请输入方案成本价', trigger: 'blur' }],
        guidePrice: [{ required: true, message: '请输入方案指导价', trigger: 'blur' }],
        supplier: [{ required: true, message: '请选择供应商', trigger: 'change' }]
    },
    total: 0, // 总条数
    tableData: [], // 列表
    productData: [], // 产品列表
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    ids: [], // 选中的id
    productIds: [], // 选中的产品id
    title: "", // 标题
})

/**
 * 获取列表数据
 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        obj.tableData = response.rows || [];
        obj.total = response.total || 0;
        obj.loading = false;
    }).catch(error => {
        console.error("获取数据失败", error);
        obj.tableData = [
            {
                id: 1,
                customer: '客户A',
                solutionCode: '方案编号1',
                solutionName: '方案名称1',
                costPrice: 1000,
                guidePrice: 1500,
                supplier: '供应商A',
                status: '1',
                solutionType: '1',
                supplierName: '供应商A'
            },
            {
                id: 2,
                customer: '客户B',
                solutionCode: '方案编号2',
                solutionName: '方案名称2',
                costPrice: 1200,
                guidePrice: 1800,
                supplier: '供应商B',
                status: '1',
                solutionType: '2',
                supplierName: '供应商B'
            }
        ];
        obj.loading = false;
    });
}

/**
 * 表单重置
 */
function reset() {
    obj.dialogForm = {};
    obj.productData = [];
    if (formRef.value) {
        formRef.value.resetFields();
    }
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/**
 * 重置按钮操作
 */
function resetQuery() {
    if (queryRef.value) {
        queryRef.value.resetFields();
    }
    handleQuery();
}

/**
 * 多选框选中数据
 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = selection.length === 0;
}

/**
 * 产品多选框选中数据
 */
function handleProductSelectionChange(selection) {
    obj.productIds = selection.map(item => item.id);
    obj.productMultiple = selection.length === 0;
}

/**
 * 新增按钮操作
 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增客户方案";
}

/**
 * 查看按钮操作
 */
function handleDetail(row) {
    reset();
    if (row && row.id) {
        listScale(row.id).then(response => {
            obj.dialogForm = response.data || {};
            obj.productData = response.data.products || [];
            obj.dialogShow = true;
            obj.title = "方案详情";
        }).catch(error => {
            console.error("获取详情失败", error);
        });
    }
}

/**
 * 修改按钮操作
 */
function handleUpdate(row) {
    reset();
    const id = row?.id || obj.ids[0];
    if (id) {
        listScale(id).then(response => {
            obj.dialogForm = response.data || {};
            obj.productData = response.data.products || [];
            obj.dialogShow = true;
            obj.title = "修改客户方案";
        }).catch(error => {
            console.error("获取数据失败", error);
        });
    } else {
        proxy.$modal.msgError("请选择要修改的数据");
    }
}

/**
 * 隐藏按钮操作
 */
function handleHide() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要隐藏的数据");
        return;
    }
    proxy.$modal.confirm('是否确认隐藏所选中的数据?').then(() => {
        // 调用隐藏API
        proxy.$modal.msgSuccess("隐藏成功");
        getList();
    }).catch(() => { });
}

/**
 * 选择供应商产品
 */
function handleAddProduct() {
    // 弹出选择产品对话框的逻辑
    // 这里只是模拟添加一个产品
    obj.productData.push({
        id: Date.now(),
        supplierName: '供应商A',
        productName: '产品' + Math.floor(Math.random() * 100),
        productType: '类型' + Math.floor(Math.random() * 10),
        baseInsuranceAmount: (Math.random() * 100).toFixed(2),
        baseMonthCost: (Math.random() * 1000).toFixed(2),
        baseYearCost: (Math.random() * 10000).toFixed(2),
        baseMonthGuidePrice: (Math.random() * 1500).toFixed(2),
        baseYearGuidePrice: (Math.random() * 15000).toFixed(2),
        isRequired: Math.random() > 0.5 ? '1' : '0'
    });
}

/**
 * 删除产品
 */
function handleDeleteProduct() {
    if (obj.productIds.length === 0) {
        proxy.$modal.msgError("请选择要删除的产品");
        return;
    }
    proxy.$modal.confirm('是否确认删除所选产品?').then(() => {
        obj.productData = obj.productData.filter(item => !obj.productIds.includes(item.id));
        obj.productIds = [];
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

/**
 * 编辑产品
 */
function handleEditProduct(row) {
    // 弹出编辑产品对话框的逻辑
    console.log('编辑产品', row);
}

/**
 * 查看产品
 */
function handleViewProduct(row) {
    // 弹出查看产品对话框的逻辑
    console.log('查看产品', row);
}

/**
 * 提交表单
 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 页面加载时获取数据
getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.form-container {
    max-height: 500px;
    overflow-y: auto;
}

.dialog-footer {
    text-align: center;
}

.mb8 {
    margin-bottom: 8px;
}
</style>