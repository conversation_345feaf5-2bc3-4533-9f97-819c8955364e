<!-- 合同客户迁移 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="分公司:" prop="branchName">
                <el-input class="width220" v-model="obj.queryParams.branchName" placeholder="请输入分公司名称" />
            </el-form-item>
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" />
            </el-form-item>

            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" @click="handleAdd">分配</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" @click="handleDetail">分配历史日志</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" type="index" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="派单分公司" align="center" prop="branchName" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="客服类型" align="center" prop="serviceType" />
            <el-table-column label="接单客服经理" align="center" prop="receiverManager" />
            <el-table-column label="派单客服经理" align="center" prop="senderManager" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" plain @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 新增转移 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="70%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" label-width="auto">
                <el-divider content-position="left">上传劳动合同</el-divider>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="8">
                        <el-form-item label="上传劳动合同" prop="typeCode">
                            <FileUpload v-model="obj.dialogForm.contractFile" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleUploadContract">下载模版</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleUploadContract">上传/查看记录</el-button>
                    </el-col>
                </el-row>
                <el-divider content-position="left">转移设定条件</el-divider>
                <el-row class="mb8">
                    <el-col :span="6">
                        <el-form-item label="原小合同名称" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="原小合同编号" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-divider content-position="left">查询结果</el-divider>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="6">
                        <el-form-item label="姓名" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="证件号码" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="订单编号" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleUploadContract">定位</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleUploadContract">重置</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleUploadContract">确认</el-button>
                    </el-col>
                </el-row>
                <el-table :data="obj.dialogForm.tableData" style="width: 100%"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="是否存在未办理实做" align="center" prop="id" />
                    <el-table-column label="唯一号" align="center" prop="id" />
                    <el-table-column label="订单编号" align="center" prop="id" />
                    <el-table-column label="雇员姓名" align="center" prop="id" />
                    <el-table-column label="证件编号" align="center" prop="id" />
                    <el-table-column label="客户编号" align="center" prop="id" />
                    <el-table-column label="客户名称" align="center" prop="id" />
                    <el-table-column label="小合同编号" align="center" prop="id" />
                    <el-table-column label="小合同名称" align="center" prop="id" />
                    <el-table-column label="派单方" align="center" prop="id" />
                    <el-table-column label="接单方" align="center" prop="id" />
                    <el-table-column label="派单方客服" align="center" prop="id" />
                    <el-table-column label="雇员状态" align="center" prop="id" />
                    <el-table-column label="入离职状态" align="center" prop="id" />
                    <el-table-column label="变更状态" align="center" prop="id" />
                    <el-table-column label="入职日期" align="center" prop="id" />
                </el-table>
                <pagination :total="obj.total" v-model:page="obj.queryParams.pageNum"
                    v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

                <el-divider content-position="left">转移信息</el-divider>
                <el-row class="mb8">
                    <el-col :span="4">
                        <el-form-item label="转移名称" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="新小合同名称" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="新小合同编号" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="报价单" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="转移生效月" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-table :data="obj.dialogForm.tableData" style="width: 100%">
                    <el-table-column label="项目" align="center" prop="id" />
                    <el-table-column label="账单起始月" align="center" prop="id" />
                    <el-table-column label="金额" align="center" prop="id" />
                    <el-table-column label="金额(不含税)" align="center" prop="id" />
                    <el-table-column label="账单模板" align="center" prop="id" />
                    <el-table-column label="收费模板" align="center" prop="id" />
                </el-table>
                <el-row class="mb8">
                    <el-col :span="12">
                        <el-form-item label="备注" prop="typeCode">
                            <el-input type="textarea" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-divider content-position="left">选中订单</el-divider>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleDelete">删除</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleDelete">删除全部</el-button>
                    </el-col>
                </el-row>
                <el-table :data="obj.dialogForm.tableData" style="width: 100%"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="订单编号" align="center" prop="id" />
                    <el-table-column label="雇员姓名" align="center" prop="id" />
                    <el-table-column label="入职日期" align="center" prop="id" />
                    <el-table-column label="证件编号" align="center" prop="id" />
                    <el-table-column label="证件类型" align="center" prop="id" />
                    <el-table-column label="客户编号" align="center" prop="id" />
                    <el-table-column label="客户名称" align="center" prop="id" />
                    <el-table-column label="小合同编号" align="center" prop="id" />
                    <el-table-column label="小合同名称" align="center" prop="id" />
                    <el-table-column label="派单方" align="center" prop="id" />
                    <el-table-column label="接单方" align="center" prop="id" />
                    <el-table-column label="派单方客服" align="center" prop="id" />
                    <el-table-column label="接单方客服" align="center" prop="id" />
                </el-table>
            </el-form>

        </el-dialog>


        <!-- 详情 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="70%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" label-width="auto">
                <el-divider content-position="left">变更设定条件</el-divider>
                <el-row class="mb8">
                    <el-col :span="6">
                        <el-form-item label="转移名称" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="转移编号" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="原小合同编号" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="新小合同编号" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="mb8">
                    <el-col :span="6">
                        <el-form-item label="转移生效月" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="新报价单编号:" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="创建人" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="创建时间" prop="typeCode">
                            <el-input class="width220" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="mb8">
                    <el-col :span="12">
                        <el-form-item label="备注" prop="typeCode">
                            <el-input type="textarea" v-model="obj.dialogForm.sales" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-table :data="obj.dialogForm.tableData" style="width: 100%">
                    <el-table-column label="项目" align="center" prop="id" />
                    <el-table-column label="账单起始月" align="center" prop="id" />
                    <el-table-column label="金额" align="center" prop="id" />
                    <el-table-column label="金额(不含税)" align="center" prop="id" />
                    <el-table-column label="账单模板" align="center" prop="id" />
                    <el-table-column label="收费模板" align="center" prop="id" />
                </el-table>
                <el-divider content-position="left">变更雇员列表</el-divider>
                <el-table :data="obj.dialogForm.tableData" style="width: 100%"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" align="center" type="index" />
                    <el-table-column label="姓名" align="center" prop="id" />
                    <el-table-column label="证件号" align="center" prop="id" />
                    <el-table-column label="订单号" align="center" prop="id" />
                    <el-table-column label="原报价单编号" align="center" prop="id" />
                    <el-table-column label="原报价单名称" align="center" prop="id" />
                    <el-table-column label="新报价单编号" align="center" prop="id" />
                    <el-table-column label="新报价单名称" align="center" prop="id" />
                    <el-table-column label="原账单模板编号" align="center" prop="id" />
                    <el-table-column label="原账单模板名称" align="center" prop="id" />
                    <el-table-column label="新账单模板编号" align="center" prop="id" />
                    <el-table-column label="新账单模板名称" align="center" prop="id" />
                    <el-table-column label="原收费频率编号" align="center" prop="id" />
                    <el-table-column label="原收费频率名称" align="center" prop="id" />
                    <el-table-column label="新收费频率编号" align="center" prop="id" />
                    <el-table-column label="新收费频率名称" align="center" prop="id" />
                    <el-table-column label="状态" align="center" prop="id" />
                    <el-table-column label="失败原因" align="center" prop="id" />
                    <el-table-column label="操作" align="center">
                        <template #default="scope">
                            <el-button type="primary" text icon="View" @click="handleDetail(scope.row)">查看</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>

        </el-dialog>
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    // 可以根据需要添加验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        branchName: null,
        customerName: null,
        contractName: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogShow: false,//新增转移
    dialogShow2: false,//查看转移信息
    dialogForm: {
        contractFile: [],
        sales: '',
        tableData: []
    },//新增转移表单
    rules: {},//新增转移表单验证规则
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerName: '客户名称1',
                contractName: '合同名称1',
                branchName: '分公司1',
                smallContractName: '小合同名称1',
                serviceType: '客服类型1',
                receiverManager: '接单客服经理1',
                senderManager: '派单客服经理1'
            },
            {
                id: 2,
                customerName: '客户名称2',
                contractName: '合同名称2',
                branchName: '分公司2',
                smallContractName: '小合同名称2',
                serviceType: '客服类型2',
                receiverManager: '接单客服经理2',
                senderManager: '派单客服经理2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增转移 */
function handleAdd() {
    obj.dialogShow = true;
    obj.title = "新增转移";
    obj.dialogForm = {
        contractFile: [],
        sales: '',
        tableData: []
    };
}

/** 查看转移信息 */
function handleDetail(row) {
    obj.dialogShow2 = true;
    obj.title = "查看转移信息";
    if (row) {
        // 如果是从表格行点击进入，可以加载该行的数据
        obj.dialogForm = { ...row, tableData: [] };
    }
}

/** 上传合同 */
function handleUploadContract() {
    proxy.$modal.msgInfo('该功能正在开发中');
}

/** 删除操作 */
function handleDelete() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要删除的数据');
        return;
    }
    proxy.$modal.confirm('确认要删除选中的数据吗？').then(() => {
        // 这里可以调用API进行删除操作
        proxy.$modal.msgSuccess('删除成功');
    }).catch(() => { });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>