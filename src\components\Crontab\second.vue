<template>
    <el-form>
        <el-form-item>
            <el-radio v-model='radioValue' :value="1">
                秒，允许的通配符[, - * /]
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="2">
                周期从
                <el-input-number v-model='cycle01' :min="0" :max="58" /> -
                <el-input-number v-model='cycle02' :min="cycle01 + 1" :max="59" /> 秒
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="3">
                从
                <el-input-number v-model='average01' :min="0" :max="58" /> 秒开始，每
                <el-input-number v-model='average02' :min="1" :max="59 - average01" /> 秒执行一次
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="4">
                指定
                <el-select clearable v-model="checkboxList" placeholder="可多选" multiple :multiple-limit="10">
                    <el-option v-for="item in 60" :key="item" :label="item - 1" :value="item - 1" />
                </el-select>
            </el-radio>
        </el-form-item>
    </el-form>
</template>

<script setup>
const emit = defineEmits(['update'])
const props = defineProps({
    cron: {
        type: Object,
        default: {
            second: "*",
            min: "*",
            hour: "*",
            day: "*",
            month: "*",
            week: "?",
            year: "",
        }
    },
    check: {
        type: Function,
        default: () => {
        }
    }
})
const radioValue = ref(1)
const cycle01 = ref(0)
const cycle02 = ref(1)
const average01 = ref(0)
const average02 = ref(1)
const checkboxList = ref([])
const checkCopy = ref([0])
const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, 0, 58)
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, 59)
    return cycle01.value + '-' + cycle02.value
})
const averageTotal = computed(() => {
    average01.value = props.check(average01.value, 0, 58)
    average02.value = props.check(average02.value, 1, 59 - average01.value)
    return average01.value + '/' + average02.value
})
const checkboxString = computed(() => {
    return checkboxList.value.join(',')
})
watch(() => props.cron.second, value => changeRadioValue(value))
watch([radioValue, cycleTotal, averageTotal, checkboxString], () => onRadioChange())
/**
 * 根据不同的输入值类型更新radioValue的值
 * 此函数用于处理和更新单选框的值，根据输入值的类型（特定格式），来设置不同的radioValue值
 * @param {string} value - 输入的值，用于决定radioValue的值
 */
function changeRadioValue(value) {
    // 当输入值为'*'时，设置radioValue的值为1
    if (value === '*') {
        radioValue.value = 1
    } else if (value.indexOf('-') > -1) {
        // 当输入值包含'-'时，分割字符串并转换为数字，分别赋值给cycle01和cycle02，同时设置radioValue的值为2
        const indexArr = value.split('-')
        cycle01.value = Number(indexArr[0])
        cycle02.value = Number(indexArr[1])
        radioValue.value = 2
    } else if (value.indexOf('/') > -1) {
        // 当输入值包含'/'时，分割字符串并转换为数字，分别赋值给average01和average02，同时设置radioValue的值为3
        const indexArr = value.split('/')
        average01.value = Number(indexArr[0])
        average02.value = Number(indexArr[1])
        radioValue.value = 3
    } else {
        // 当输入值为以逗号分隔的数字字符串时，分割字符串并转换为数字数组，同时设置radioValue的值为4
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))]
        radioValue.value = 4
    }
}
// 单选按钮值变化时
function onRadioChange() {
    // 根据不同的单选按钮值触发相应的逻辑
    switch (radioValue.value) {
        case 1:
            // 当单选按钮值为1时，触发更新事件，传递'second'，'*'，'second'作为参数
            emit('update', 'second', '*', 'second')
            break
        case 2:
            // 当单选按钮值为2时，触发更新事件，传递'second'，cycleTotal的值，'second'作为参数
            emit('update', 'second', cycleTotal.value, 'second')
            break
        case 3:
            // 当单选按钮值为3时，触发更新事件，传递'second'，averageTotal的值，'second'作为参数
            emit('update', 'second', averageTotal.value, 'second')
            break
        case 4:
            // 当单选按钮值为4时，检查checkboxList是否为空，如果为空则添加第一个checkCopy的值，否则将checkboxList的值赋给checkCopy
            if (checkboxList.value.length === 0) {
                checkboxList.value.push(checkCopy.value[0])
            } else {
                checkCopy.value = checkboxList.value
            }
            // 触发更新事件，传递'second'，checkboxString的值，'second'作为参数
            emit('update', 'second', checkboxString.value, 'second')
            break
    }
}
</script>

<style lang="scss" scoped>
.el-input-number--small,
.el-select,
.el-select--small {
    margin: 0 0.2rem;
}

.el-select,
.el-select--small {
    width: 18.8rem;
}
</style>