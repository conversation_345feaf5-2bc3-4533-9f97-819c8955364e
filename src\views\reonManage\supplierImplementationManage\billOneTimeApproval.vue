<!-- 账单一次性审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplier">
                <el-select class="width220" v-model="obj.queryParams.supplier" placeholder="请选择" clearable>
                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商账单模板:" prop="billTemplate">
                <el-select class="width220" v-model="obj.queryParams.billTemplate" placeholder="请选择" clearable>
                    <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报表年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择年月"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Check" :disabled="obj.multiple"
                    @click="handleConfirm">通过</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Close" :disabled="obj.multiple"
                    @click="handleReject">驳回</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <BillOneTime :tableData="obj.tableData" :loading="obj.loading" menuName="billOneTimeApproval"
            @selectionChange="handleSelectionChange" />
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 审批驳回弹窗 -->
        <el-dialog v-model="obj.dialogShow2" :title="'审批驳回'" width="25%" append-to-body>
            <el-form :model="obj.dialogForm" label-width="auto">
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" :rows="3" v-model="obj.dialogForm.remark" placeholder="请输入驳回原因" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleRejectConfirm">确认</el-button>
                <el-button @click="obj.dialogShow2 = false">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="BillOneTimeApproval">
import { listScale } from "@/api/reonApi/scale";
import BillOneTime from '@/views/reonManage/components/table/billOneTime.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 供应商选项
const supplierOptions = [
    { value: '1', label: '供应商A' },
    { value: '2', label: '供应商B' },
    { value: '3', label: '供应商C' }
];

// 账单模板选项
const billTemplateOptions = [
    { value: '1', label: '模板1' },
    { value: '2', label: '模板2' },
    { value: '3', label: '模板3' }
];


// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        supplier: '1',
        billTemplate: '1',
        billMonth: '2023-01',
        firstCategory: '1',
        secondCategory: '1',
        amount: 10000,
        amountWithoutTax: 9433.96,
        taxRate: 6,
        tax: 566.04,
        totalPeople: 100,
        oneTimeSupportPeople: 5,
        reportLockStatus: '1',
        approvalStatus: '0',
        remark: '待审批账单',
        submitter: '张三',
        submitTime: '2023-01-15 10:30:45'
    },
    {
        id: 2,
        supplier: '2',
        billTemplate: '2',
        billMonth: '2023-02',
        firstCategory: '2',
        secondCategory: '4',
        amount: 8000,
        amountWithoutTax: 7547.17,
        taxRate: 6,
        tax: 452.83,
        totalPeople: 80,
        oneTimeSupportPeople: 0,
        reportLockStatus: '1',
        approvalStatus: '0',
        remark: '待审批账单',
        submitter: '李四',
        submitTime: '2023-02-20 14:25:30'
    },
    {
        id: 3,
        supplier: '3',
        billTemplate: '3',
        billMonth: '2023-03',
        firstCategory: '3',
        secondCategory: '5',
        amount: 5000,
        amountWithoutTax: 4716.98,
        taxRate: 6,
        tax: 283.02,
        totalPeople: 50,
        oneTimeSupportPeople: 10,
        reportLockStatus: '0',
        approvalStatus: '0',
        remark: '待审批账单',
        submitter: '王五',
        submitTime: '2023-03-10 09:15:20'
    }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplier: null, // 供应商
        billTemplate: null, // 账单模板
        billMonth: null, // 报表年月
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        remark: '', // 备注
        approvalStatus: '1' // 默认通过
    }, // 审批表单
    dialogShow: false, // 通过弹窗
    dialogShow2: false, // 驳回弹窗
    ids: [], // 选中的id
    title: "审批确认" // 标题
})

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.supplier) {
            filteredData = filteredData.filter(item => item.supplier === obj.queryParams.supplier);
        }

        if (obj.queryParams.billTemplate) {
            filteredData = filteredData.filter(item => item.billTemplate === obj.queryParams.billTemplate);
        }

        if (obj.queryParams.billMonth) {
            const month = new Date(obj.queryParams.billMonth).toISOString().slice(0, 7);
            filteredData = filteredData.filter(item => item.billMonth === month);
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 通过审批
function handleConfirm() {
    proxy.$modal.confirm('确认通过所有数据吗？').then(() => {
        proxy.$modal.msgSuccess('审批通过成功');
    }).catch(() => { });
}

// 驳回审批
function handleReject() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要驳回的记录');
        return;
    }

    // 检查是否有非待审批的记录
    const selectedRows = obj.tableData.filter(item => obj.ids.includes(item.id));
    const hasInvalidStatus = selectedRows.some(item => item.approvalStatus !== '0');

    if (hasInvalidStatus) {
        proxy.$modal.msgError('选中的记录中包含非待审批的记录，请重新选择');
        return;
    }

    obj.dialogForm.remark = '';
    obj.dialogForm.approvalStatus = '2';
    obj.dialogShow2 = true;
}

// 确认驳回
function handleRejectConfirm() {
    if (!obj.dialogForm.remark) {
        proxy.$modal.msgError('请输入驳回原因');
        return;
    }

    // 实际项目中应该调用API进行驳回
    proxy.$modal.msgSuccess('驳回成功');
    obj.dialogShow2 = false;

    // 更新本地数据
    obj.ids.forEach(id => {
        const index = obj.tableData.findIndex(item => item.id === id);
        if (index !== -1) {
            obj.tableData[index].approvalStatus = '2';
            obj.tableData[index].remark = obj.dialogForm.remark;
        }
    });

    obj.ids = [];
}

getList();
</script>
<style lang="scss" scoped></style>