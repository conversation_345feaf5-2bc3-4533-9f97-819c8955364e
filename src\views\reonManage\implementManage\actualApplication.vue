<!-- 实际申请 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-select class="width220" v-model="obj.queryParams.customer" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="单立户名称:" prop="singleAccountName">
                <el-select class="width220" v-model="obj.queryParams.singleAccountName" placeholder="请选择" clearable>
                    <el-option v-for="item in singleAccountOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable>
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="雇员状态:" prop="employeeStatus">
                <el-select class="width220" v-model="obj.queryParams.employeeStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in employeeStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单客服:" prop="orderReceiver">
                <el-input class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="福利起始月起:" prop="welfareStartMonthFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.welfareStartMonthFrom" type="month"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="福利起始月止:" prop="welfareStartMonthTo">
                <el-date-picker class="width220" v-model="obj.queryParams.welfareStartMonthTo" type="month"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="未办理状态:" prop="checkList">
                <el-checkbox-group v-model="obj.queryParams.checkList">
                    <el-checkbox label="待申请" />
                    <el-checkbox label="不需办理" />
                    <el-checkbox label="退回待申请" />
                </el-checkbox-group>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Search" @click="personalOrderBtn">查询个人订单</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="applyBtn">申请办理</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain @click="notApplyBtn">不需办理</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="exportDataBtn">导出数据</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="addWhiteListBtn">添加白名单</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="是否完全核销" align="center" width="120" prop="isFullyVerified" />
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.status === '在职' ? 'success' : (scope.row.status === '离职' ? 'danger' : 'warning')">
                        {{ scope.row.status }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="证件号码" align="center" width="140" prop="idNumber" />
            <el-table-column label="手机号码" align="center" width="120" prop="phoneNumber" />
            <el-table-column label="订单编号" align="center" prop="orderNo" />
            <el-table-column label="福利办理方" align="center" width="120" prop="welfareHandler" />
            <el-table-column label="福利包名称" align="center" width="120" prop="welfarePackageName" />
            <el-table-column label="福利包编号" align="center" width="120" prop="welfarePackageNo" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户编号" align="center" prop="customerNo" />
            <el-table-column label="服务区域类型" align="center" width="120" prop="serviceAreaType" />
            <el-table-column label="小合同" align="center" prop="smallContract" />
            <el-table-column label="签约方抬头" align="center" width="120" prop="signingPartyHeader" />
            <el-table-column label="派单方" align="center" prop="dispatchParty" />
            <el-table-column label="接单方" align="center" prop="receivingParty" />
            <el-table-column label="派单方客服" align="center" width="120" prop="dispatchPartyService" />
            <el-table-column label="接单方客服" align="center" width="120" prop="receivingPartyService" />
            <el-table-column label="社保工资" align="center" prop="socialSecuritySalary" />
            <el-table-column label="公积金工资" align="center" width="120" prop="housingFundSalary" />
            <el-table-column label="退回备注" align="center" prop="returnRemark" />
            <el-table-column label="入职日期" align="center" prop="entryDate" />
            <el-table-column label="福利起始月" align="center" width="120" prop="welfareStartMonth" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 查看个人订单 -->
        <order-contract-reserve-fund type="ActualApplication" v-model:dialogShow="obj.dialogShow" title="查看个人订单"
            :form="obj.dialogForm" :tableData="obj.tableData" :tableData_no="obj.tableData_no" :isDetail="true" />


        <!-- 申请办理/无需办理 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="20%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" label-width="auto">
                <el-form-item label="备注" prop="sales">
                    <el-input type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="cancelApply">取消</el-button>
                <el-button type="primary" @click="saveApply">保存</el-button>
            </template>
        </el-dialog>
        <!-- 添加白名单 -->
        <el-dialog v-model="obj.dialogShow3" :title="obj.title" width="50%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" label-width="auto">
                <el-form-item label="客户" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="上传文件" prop="sales">
                    <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
                </el-form-item>
            </el-form>
            <el-table :data="obj.dialogForm.formTable" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="客户Id" prop="sales" align="center" />
                <el-table-column label="客户名称" prop="sales" align="center" />
                <el-table-column label="福利办理方" prop="sales" width="120" align="center" />
                <el-table-column label="类型" prop="sales" align="center" />
                <el-table-column label="创建人" prop="sales" align="center" />
                <el-table-column label="创建时间" prop="sales" align="center" />
            </el-table>
            <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
            <template #footer>
                <el-button type="primary" @click="cancelWhiteList">取消</el-button>
                <el-button type="primary" @click="saveWhiteList">保存</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { listScale } from "@/api/reonApi/scale";
import OrderContractReserveFund from '@/views/reonManage/components/dialog/orderContractReserveFund.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 单立户选项
const singleAccountOptions = [
    { value: '1', label: '单立户A' },
    { value: '2', label: '单立户B' },
    { value: '3', label: '单立户C' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 雇员状态选项
const employeeStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '待入职' }
];

// 未办理状态选项
const checkList = ref([]);

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        employeeName: '张三',
        isFullyVerified: '是',
        status: '在职',
        idNumber: '110101199001011234',
        phoneNumber: '***********',
        orderNo: 'ORD20230001',
        welfareHandler: '公司A',
        welfarePackageName: '标准福利包',
        welfarePackageNo: 'WP001',
        customerName: '客户A',
        customerNo: 'CUS001',
        serviceAreaType: '一线城市',
        smallContract: '是',
        signingPartyHeader: '公司A',
        dispatchParty: '公司B',
        receivingParty: '公司C',
        dispatchPartyService: '张经理',
        receivingPartyService: '李经理',
        socialSecuritySalary: 10000,
        housingFundSalary: 8000,
        returnRemark: '',
        entryDate: '2023-01-01',
        welfareStartMonth: '2023-02'
    },
    {
        id: 2,
        employeeName: '李四',
        isFullyVerified: '否',
        status: '在职',
        idNumber: '110101199001021234',
        phoneNumber: '13900139000',
        orderNo: 'ORD20230002',
        welfareHandler: '公司B',
        welfarePackageName: '高级福利包',
        welfarePackageNo: 'WP002',
        customerName: '客户B',
        customerNo: 'CUS002',
        serviceAreaType: '二线城市',
        smallContract: '否',
        signingPartyHeader: '公司B',
        dispatchParty: '公司A',
        receivingParty: '公司C',
        dispatchPartyService: '王经理',
        receivingPartyService: '赵经理',
        socialSecuritySalary: 12000,
        housingFundSalary: 10000,
        returnRemark: '资料不完整',
        entryDate: '2023-02-01',
        welfareStartMonth: '2023-03'
    }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        singleAccountName: null, // 单立户名称
        welfarePackageName: null, // 福利包名称
        orderNo: null, // 订单编号
        employeeName: null, // 雇员姓名
        idNumber: null, // 证件号码
        employeeStatus: null, // 雇员状态
        orderReceiver: null, // 接单客服
        welfareStartMonthFrom: null, // 福利起始月起
        welfareStartMonthTo: null, // 福利起始月止
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 显示对话框
    dialogShow2: false, // 申请办理/无需办理对话框
    dialogShow3: false, // 添加白名单对话框
    title: '', // 对话框标题
    ids: [], // 选中的id
    isDetail: false, // 是否详情模式
    tableData_no: [] // 无需办理表格数据
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function resetForm() {
    obj.dialogForm = {
        remark: '',
        formTable: []
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

// 查询个人订单
function personalOrderBtn() {
    obj.dialogShow = true;
}
// 申请办理
function applyBtn() {
    obj.dialogShow2 = true;
    obj.title = '申请办理';
}

// 不需办理
function notApplyBtn() {
    obj.dialogShow2 = true;
    obj.title = '无需办理';
}

// 导出数据
function exportDataBtn() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 添加白名单
function addWhiteListBtn() {
    obj.dialogShow3 = true;
    obj.title = '实做申请白名单';
}


// 取消申请办理/无需办理
function cancelApply() {
    obj.dialogShow2 = false;
    resetForm();
}

// 保存申请办理/无需办理
function saveApply() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            // 实际项目中应该调用API进行提交
            proxy.$modal.msgSuccess('保存成功');
            obj.dialogShow2 = false;
            resetForm();
            getList(); // 刷新列表
        }
    });
}

// 取消添加白名单
function cancelWhiteList() {
    obj.dialogShow3 = false;
    resetForm();
}

// 保存添加白名单
function saveWhiteList() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            // 实际项目中应该调用API进行提交
            proxy.$modal.msgSuccess('保存成功');
            obj.dialogShow3 = false;
            resetForm();
            getList(); // 刷新列表
        }
    });
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>