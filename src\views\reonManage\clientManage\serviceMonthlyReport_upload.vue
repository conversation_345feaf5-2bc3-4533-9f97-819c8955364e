<!-- 服务月报上传 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="服务年月:" prop="serviceMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.serviceMonth" type="month"
                    placeholder="请选择服务年月" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName" required>
                <el-select class="width220" filterable v-model="obj.queryParams.customerName" placeholder="请选择客户"
                    clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>

            <el-row :gutter="10" class="mb8">
                <el-col :span="12">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增服务月报</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">修改服务月报</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="服务年月" align="center" prop="serviceMonth" />
            <el-table-column label="上传人" align="center" prop="uploadUser" />
            <el-table-column label="上传时间" align="center" prop="uploadTime" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="上传文件" align="center" prop="uploadFile">
                <template #default="scope">
                    <el-link type="primary" @click="handleDownload(scope.row)">{{
                        scope.row.uploadFile }}</el-link>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180">
                <template #default="scope">
                    <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="55%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="服务年月" prop="serviceMonth"
                    :rules="[{ required: true, message: '请选择服务年月', trigger: 'change' }]">
                    <el-date-picker class="width220" v-model="obj.dialogForm.serviceMonth" type="month"
                        placeholder="请选择服务年月" clearable />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName"
                    :rules="[{ required: true, message: '请选择客户名称', trigger: 'change' }]">
                    <el-select class="width220" filterable v-model="obj.dialogForm.customerName" placeholder="请选择客户"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-divider content-position="left">文件上传</el-divider>
                <el-form-item prop="fileList" :rules="[{ required: true, message: '请上传文件', trigger: 'change' }]">
                    <FileUpload v-model="obj.dialogForm.fileList" :limit="1" :fileSize="10"
                        :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
                </el-form-item>
                <el-divider content-position="left">备注</el-divider>
                <el-form-item prop="remark" style="width: 100%;">
                    <el-input type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入备注" :rows="3"
                        style="width: 100%;" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ServiceMonthlyReport_upload">

import { listScale } from "@/api/reonApi/scale";
import FileUpload from '@/components/FileUpload'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' },
    { value: '4', label: '客户D' },
    { value: '5', label: '客户E' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceMonth: null,
        customerName: null
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    ids: [], // 选中的id
    title: "", // 标题
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        obj.tableData = [
            {
                id: 1,
                customerName: '客户A',
                serviceMonth: '2023-05',
                uploadUser: '管理员',
                uploadTime: '2023-05-10 10:00:00',
                remark: '五月服务月报',
                uploadFile: '服务月报_客户A_2023年5月.pdf'
            },
            {
                id: 2,
                customerName: '客户B',
                serviceMonth: '2023-06',
                uploadUser: '管理员',
                uploadTime: '2023-06-15 14:30:00',
                remark: '六月服务月报',
                uploadFile: '服务月报_客户B_2023年6月.pdf'
            }
        ];
        obj.total = 2;
    }).catch(() => {

    });
}



/** 表单重置 */
function reset() {
    obj.dialogForm = {
        serviceMonth: null,
        customerName: null,
        fileList: [],
        remark: ''
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增服务月报";
}

/** 修改按钮操作 */
function handleEdit(row) {
    reset();
    console.log(obj.ids);

    const id = row.id || obj.ids[0];
    console.log(id);

    if (!id) {
        proxy.$modal.msgError('请选择要修改的数据');
        return;
    }
    // 获取详情数据，实际开发时可以调用接口
    const data = obj.tableData.find(item => item.id === id) || row;
    if (data) {
        obj.dialogForm = {
            id: data.id,
            customerName: data.customerName,
            serviceMonth: data.serviceMonth,
            remark: data.remark,
            fileList: [{
                name: data.uploadFile,
                url: data.uploadFile
            },
            {
                name: data.uploadFile,
                url: data.uploadFile
            },
            ]
        };
        obj.dialogShow = true;
        obj.title = "修改服务月报";
    }
}

/** 下载文件 */
function handleDownload(row) {
    // 调用下载文件接口
    proxy.$modal.msgSuccess('文件下载成功：' + row.uploadFile);
}

/** 添加服务月报 */
function addReport(reportData) {
    return new Promise((resolve) => {
        // 模拟数据，实际开发时可以调用接口
        console.log('添加服务月报数据:', reportData);
        setTimeout(() => {
            resolve({ code: 200, msg: '新增成功' });
        }, 300);
    });
}

/** 更新服务月报 */
function updateReport(reportData) {
    return new Promise((resolve) => {
        // 模拟数据，实际开发时可以调用接口
        console.log('更新服务月报数据:', reportData);
        setTimeout(() => {
            resolve({ code: 200, msg: '修改成功' });
        }, 300);
    });
}

/** 删除服务月报 */
function deleteReport(reportIds) {
    return new Promise((resolve) => {
        // 模拟数据，实际开发时可以调用接口
        console.log('删除服务月报ID:', reportIds);
        setTimeout(() => {
            resolve({ code: 200, msg: '删除成功' });
        }, 300);
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.fileList.length === 0) {
                proxy.$modal.msgError('请上传服务月报文件');
                return;
            }

            if (obj.dialogForm.id != null) {
                updateReport(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addReport(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped>
// 重写el-link的样式
.el-link.is-underline::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    bottom: 0;
    border-bottom: 1px solid #1890ff;
}
</style>