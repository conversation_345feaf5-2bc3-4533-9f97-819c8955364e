<!-- 商保绑定账单模板 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="合同编号:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.contractNumber" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="合同名称:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" plain @click="handleBindingBill">绑定账单模版</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="合同编号" align="center" prop="contractNumber" width="150" />
            <el-table-column label="合同名称" align="center" prop="contractName" width="100" />
            <el-table-column label="合同类型" align="center" prop="contractType" width="160" />
            <el-table-column label="报价单编号" align="center" prop="quoteNumber" min-width="150" />
            <el-table-column label="报价单名称" align="center" prop="quoteName" width="100" />
            <el-table-column label="客户编号" align="center" prop="customerNumber" width="150" />
            <el-table-column label="客户名称" align="center" prop="customerName" width="100" />
            <el-table-column label="销售" align="center" prop="sales" width="100" />
            <el-table-column label="负责客服" align="center" prop="customerService" width="160" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 绑定账单模版 -->
        <el-dialog v-model="bindingDialogVisible" title="绑定账单模版" width="40%">
            <el-button class="mb20" type="primary" @click="handleBinding">绑定</el-button>
            <el-table :data="billTemplateList" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="账单模板" prop="templateName" />
                <el-table-column label="收费频率" prop="chargeFrequency" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="CommercialInsuranceBindBillTemplate">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');


const bindingDialogVisible = ref(false); // 绑定账单模版弹窗
const billTemplateList = ref([]); // 账单模版列表
const selectedTemplateIds = ref([]); // 选中的模版id


const queryRef = ref(null); // 查询表单引用


const obj = reactive({
    loading: false,
    multiple: true, // 是否多选
    single: true, // 是否单选
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractNumber: null,
        contractName: null,
        customerName: null,
    },
    tableData: [],
    total: 0,
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟API调用
    setTimeout(() => {
        const mockData = [
            { id: 1, contractNumber: 'CON001', contractName: '合同A', contractType: '类型A', quoteNumber: 'Q001', quoteName: '报价单A', customerNumber: 'CUS001', customerName: '客户A', sales: '销售A', customerService: '客服A' },
            { id: 2, contractNumber: 'CON002', contractName: '合同B', contractType: '类型B', quoteNumber: 'Q002', quoteName: '报价单B', customerNumber: 'CUS002', customerName: '客户B', sales: '销售B', customerService: '客服B' },
            { id: 3, contractNumber: 'CON003', contractName: '合同C', contractType: '类型C', quoteNumber: 'Q003', quoteName: '报价单C', customerNumber: 'CUS003', customerName: '客户C', sales: '销售C', customerService: '客服C' },
        ];
        obj.tableData = mockData;
        obj.total = mockData.length;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (queryRef.value) {
        queryRef.value.resetFields();
    }
    handleQuery();
}

// 绑定账单模版
function handleBindingBill() {
    bindingDialogVisible.value = true;
    // 获取账单模版列表 - 模拟
    billTemplateList.value = [
        { id: 'TPL001', templateName: '标准月度账单', chargeFrequency: '月结' },
        { id: 'TPL002', templateName: '季度汇总账单', chargeFrequency: '季结' },
        { id: 'TPL003', templateName: '年度服务费账单', chargeFrequency: '年结' },
    ];
}

// 账单模版表格选择
function handleSelectionChange(selection) {
    selectedTemplateIds.value = selection.map(item => item.id);
}

// 绑定按钮
function handleBinding() {
    if (!selectedTemplateIds.value || selectedTemplateIds.value.length === 0) {
        proxy.$modal.msgWarning('请选择要绑定的账单模版');
        return;
    }
    // 此处添加绑定逻辑，例如调用API
    console.log('选中的模版ID:', selectedTemplateIds.value);
    proxy.$modal.msgSuccess('绑定成功');
    bindingDialogVisible.value = false;
}

getList();
</script>
<style lang="scss" scoped></style>