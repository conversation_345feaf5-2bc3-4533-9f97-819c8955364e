import tab from "./tab";
import auth from "./auth";
import cache from "./cache";
import modal from "./modal";
import download from "./download";

/**
 * 安装插件
 * 该函数用于在应用中安装一系列自定义插件，通过扩展应用的全局属性，使其具有额外的功能
 * @param {Object} app - 应用实例，通常是一个Vue或React等框架的应用实例
 */
export default function installPlugins(app) {
  // 页签操作
  // 提供方法或属性以操作应用中的页签，如切换、关闭等
  app.config.globalProperties.$tab = tab;
  // 认证对象
  // 提供认证相关的方法或属性，用于处理用户认证和授权逻辑
  app.config.globalProperties.$auth = auth;
  // 缓存对象
  // 提供缓存相关的操作方法，用于在客户端存储临时数据，提高应用性能
  app.config.globalProperties.$cache = cache;
  // 模态框对象
  // 提供模态框相关的操作方法，简化模态框的显示和隐藏逻辑
  app.config.globalProperties.$modal = modal;
  // 下载文件
  // 提供文件下载相关的功能，允许用户从应用中下载文件到本地
  app.config.globalProperties.$download = download;
}
