<template>
  <div v-if="!item.hidden">
    <template
      v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{ 'submenu-title-noDropdown': !isNest }">
          <svg-icon :icon-class="onlyOneChild.meta.icon || (item.meta && item.meta.icon)" />
          <template #title><span class="menu-title" style="font-size: 13px;"
              :title="hasTitle(onlyOneChild.meta.title)">{{
                onlyOneChild.meta.title }}</span></template>
        </el-menu-item>
      </app-link>
    </template>

    <el-sub-menu v-else ref="subMenu" :index="resolvePath(item.path)" teleported>
      <template v-if="item.meta" #title>
        <svg-icon :icon-class="item.meta && item.meta.icon" />
        <span class="menu-title" style="font-size: 13px;" :title="hasTitle(item.meta.title)">{{ item.meta.title
        }}</span>
      </template>

      <sidebar-item v-for="(child, index) in item.children" :key="child.path + index" :is-nest="true" :item="child"
        :base-path="resolvePath(child.path)" class="nest-menu" />
    </el-sub-menu>
  </div>
</template>

<script setup>
import { isExternal } from '@/utils/validate'
import AppLink from './Link'
import { getNormalPath } from '@/utils/ruoyi'

const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true
  },
  isNest: {
    type: Boolean,
    default: false
  },
  basePath: {
    type: String,
    default: ''
  }
})

const onlyOneChild = ref({});

/**
 * 判断当前路由项是否有且仅有一个未隐藏的子路由。
 * 
 * @param {Array} children - 当前路由项的子路由数组，默认为空数组。
 * @param {Object} parent - 当前路由项的对象。
 * @returns {Boolean} - 如果满足条件（仅有一个未隐藏子路由或没有子路由），返回 true；否则返回 false。
 */
function hasOneShowingChild(children = [], parent) {
  // 确保 children 参数为数组类型，若传入值为 null 或 undefined，则初始化为空数组。
  if (!children) {
    children = [];
  }

  // 筛选出未隐藏的子路由。
  const showingChildren = children.filter(item => {
    if (item.hidden) {
      return false; // 排除隐藏的子路由。
    } else {
      // 暂时存储唯一的未隐藏子路由（若存在）。
      onlyOneChild.value = item;
      return true; // 保留未隐藏的子路由。
    }
  });

  // 如果仅有一个未隐藏的子路由，则默认显示该子路由。
  if (showingChildren.length === 1) {
    return true;
  }

  // 如果没有任何未隐藏的子路由，则显示父路由。
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true };
    return true;
  }

  // 如果有多个未隐藏的子路由，则不满足条件，返回 false。
  return false;
}

/**
 * 解析路径
 * 该函数根据路由路径和查询参数，以及基础路径，返回一个解析后的路径对象
 * 如果路径是外部路径，则直接返回该路径
 * 如果基础路径是外部路径，则返回基础路径
 * 如果存在查询参数，则将其解析为对象，并与路径一起返回
 * 
 * @param {string} routePath - 路由路径
 * @param {string} routeQuery - 路由查询参数字符串
 * @returns {string | object} - 解析后的路径或路径对象
 */
function resolvePath(routePath, routeQuery) {
  // 检查路由路径是否为外部路径
  if (isExternal(routePath)) {
    return routePath
  }
  // 检查基础路径是否为外部路径
  if (isExternal(props.basePath)) {
    return props.basePath
  }
  // 如果存在查询参数，解析并返回路径对象
  if (routeQuery) {
    let query = JSON.parse(routeQuery);
    return { path: getNormalPath(props.basePath + '/' + routePath), query: query }
  }
  // 返回解析后的路径
  return getNormalPath(props.basePath + '/' + routePath)
}

/**
 * 检查标题是否满足特定长度条件
 * 
 * 此函数用于判断给定的标题是否至少包含5个字符如果满足条件，
 * 则返回原标题；如果不满足，则返回空字符串这一逻辑基于
 * 假设较短的标题可能不具有足够的信息量，因此不应被使用或显示
 * 
 * @param {string} title - 待检查的标题字符串
 * @returns {string} - 如果标题长度超过5个字符，则返回原标题；否则返回空字符串
 */
function hasTitle(title) {
  if (title.length > 5) {
    return title;
  } else {
    return "";
  }
}
</script>
