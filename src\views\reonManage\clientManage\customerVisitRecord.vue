<!-- 客户拜访记录 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" filterable v-model="obj.queryParams.customerName" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="拜访时间>:" prop="visitTime">
                <el-date-picker class="width220" v-model="obj.queryParams.visitTime" type="datetime"
                    placeholder="选择日期时间" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item label="拜访时间<:" prop="visitTime">
                <el-date-picker class="width220" v-model="obj.queryParams.visitTime" type="datetime"
                    placeholder="选择日期时间" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item label="拜访人:" prop="visitorName">
                <el-select class="width220" filterable v-model="obj.queryParams.visitorName" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in visitorOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增拜访记录</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single"
                    @click="handleEdit">修改拜访记录</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="拜访人" align="center" prop="visitorName" />
            <el-table-column label="拜访日期" align="center" prop="visitTime" />
            <el-table-column label="拜访主题" align="center" prop="visitTopic" />
            <el-table-column label="拜访备注" align="center" prop="remark" />
            <el-table-column label="上传文件" align="center" width="180">
                <template #default="scope">
                    <el-link type="primary" @click="handleFile(scope.row)">{{ scope.row.file }}</el-link>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="150">
                <template #default="scope">
                    <el-button text size="small" @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="55%" append-to-body draggable>
            <el-form class="form-container" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item label="客户名称" prop="customerName" required>
                    <el-select class="width220" filterable v-model="obj.dialogForm.customerName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="拜访时间" prop="visitTime" required>
                    <el-date-picker class="width220" v-model="obj.dialogForm.visitTime" type="datetime"
                        placeholder="选择日期时间" value-format="YYYY-MM-DD HH:mm:ss" />
                </el-form-item>
                <el-divider content-position="left">文件上传</el-divider>
                <el-form-item>
                    <file-upload></file-upload>
                </el-form-item>
                <el-divider content-position="left">拜访主题</el-divider>
                <el-form-item prop="visitTopic" required>
                    <el-input type="textarea" v-model="obj.dialogForm.visitTopic" placeholder="请输入拜访主题" :rows="3" />
                </el-form-item>
                <el-divider content-position="left">备注</el-divider>
                <el-form-item prop="remark">
                    <el-input type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入备注信息" :rows="3" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="obj.dialogShow = false">取 消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="CustomerVisitRecord">


// 初始化常量和对象
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const formRef = ref(null)
const queryRef = ref(null)

// 选项数据
const customerOptions = ref([
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
])

const visitorOptions = ref([
    { value: '1', label: '销售专员A' },
    { value: '2', label: '销售专员B' },
    { value: '3', label: '销售经理C' }
])

// 响应式数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        visitTimeRange: [],
        visitorName: null
    }, // 查询表单
    rules: {
        customerName: [{ required: true, message: '请选择客户', trigger: 'change' }],
        visitorName: [{ required: true, message: '请选择拜访人', trigger: 'change' }],
        visitTime: [{ required: true, message: '请选择拜访时间', trigger: 'change' }],
        visitTopic: [{ required: true, message: '请输入拜访主题', trigger: 'blur' }]
    },
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    ids: [], // 选中的id
    title: "", // 标题
    fileList: [] // 文件列表
})

/**
 * 获取列表数据
 */
function getList() {
    obj.loading = true;

    // 模拟数据
    setTimeout(() => {
        // 生成模拟数据
        const mockData = [];
        for (let i = 0; i < 10; i++) {
            mockData.push({
                id: i + 1,
                customerName: `客户${String.fromCharCode(65 + i % 3)}`,
                visitorName: `销售${String.fromCharCode(65 + i % 3)}`,
                visitTime: `2023-${String(i % 12 + 1).padStart(2, '0')}-${String(i % 28 + 1).padStart(2, '0')} 10:00:00`,
                visitTopic: `关于产品${i + 1}的销售讨论会议`,
                remark: `备注${i + 1}`,
                file: `文件${i + 1}`,
            });
        }

        // 应用查询条件过滤
        let filteredData = [...mockData];
        const params = obj.queryParams;

        if (params.customerName) {
            filteredData = filteredData.filter(item => item.customerName.includes(params.customerName));
        }

        if (params.visitorName) {
            filteredData = filteredData.filter(item => item.visitorName.includes(params.visitorName));
        }

        if (params.visitTimeRange && params.visitTimeRange.length === 2) {
            const beginTime = new Date(params.visitTimeRange[0]).getTime();
            const endTime = new Date(params.visitTimeRange[1]).getTime();
            filteredData = filteredData.filter(item => {
                const itemTime = new Date(item.visitTime).getTime();
                return itemTime >= beginTime && itemTime <= endTime;
            });
        }

        // 分页处理
        const start = (params.pageNum - 1) * params.pageSize;
        const end = start + params.pageSize;
        const pageData = filteredData.slice(start, end);

        obj.tableData = pageData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 500); // 模拟网络延迟
}

/**
 * 表单重置
 */
function reset() {
    obj.dialogForm = {};
    obj.fileList = [];
    if (formRef.value) {
        formRef.value.resetFields();
    }
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/**
 * 重置按钮操作
 */
function resetQuery() {
    if (queryRef.value) {
        queryRef.value.resetFields();
    }
    handleQuery();
}

/**
 * 多选框选中数据
 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = selection.length === 0;
}

/**
 * 新增按钮操作
 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增拜访记录";
}

/**
 * 查看按钮操作
 */
function handleView(row) {
    reset();
    if (row && row.id) {
        // 模拟获取详情数据
        setTimeout(() => {
            // 模拟详情数据
            const mockDetail = {
                id: row.id,
                customerName: row.customerName,
                visitorName: row.visitorName,
                visitTime: row.visitTime,
                visitTopic: row.visitTopic,
                remark: `这是关于${row.customerName}的拜访记录备注信息，包含了客户的需求和反馈。`,
                files: []
            };

            // 模拟文件列表
            if (row.fileCount > 0) {
                for (let i = 0; i < row.fileCount; i++) {
                    mockDetail.files.push({
                        name: `文件${i + 1}.pdf`,
                        url: '#'
                    });
                }
            }

            obj.dialogForm = mockDetail;
            obj.fileList = mockDetail.files;
            obj.dialogShow = true;
            obj.title = "拜访记录详情";
        }, 300);
    }
}

/**
 * 修改按钮操作
 */
function handleEdit(row) {
    reset();
    const id = row?.id || obj.ids[0];
    if (id) {
        // 模拟获取详情数据
        setTimeout(() => {
            // 如果是从表格行中获取数据
            const rowData = row || obj.tableData.find(item => item.id === id);
            if (rowData) {
                // 模拟详情数据
                const mockDetail = {
                    id: rowData.id,
                    customerName: rowData.customerName,
                    visitorName: rowData.visitorName,
                    visitTime: rowData.visitTime,
                    visitTopic: rowData.visitTopic,
                    remark: `这是关于${rowData.customerName}的拜访记录备注信息，包含了客户的需求和反馈。`,
                    files: []
                };

                // 模拟文件列表
                if (rowData.fileCount > 0) {
                    for (let i = 0; i < rowData.fileCount; i++) {
                        mockDetail.files.push({
                            name: `文件${i + 1}.pdf`,
                            url: '#'
                        });
                    }
                }

                obj.dialogForm = mockDetail;
                obj.fileList = mockDetail.files;
                obj.dialogShow = true;
                obj.title = "修改拜访记录";
            }
        }, 300);
    } else {
        proxy.$modal.msgError("请选择要修改的数据");
    }
}

/**
 * 文件变更处理
 */
function handleFileChange(file, fileList) {
    obj.fileList = fileList;
}

/**
 * 提交表单
 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 页面加载时获取数据
getList();
</script>
<style lang="scss" scoped>
// 重写el-link的样式
.el-link.is-underline::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    height: 0;
    bottom: 0;
    border-bottom: 1px solid #1890ff;
}
</style>