<!-- 商保减员确认 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryForm" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryForm.uniqueId" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="姓名:" prop="name">
                <el-input class="width220" v-model="obj.queryForm.name" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idCard">
                <el-input class="width220" v-model="obj.queryForm.idCard" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryForm.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractNo">
                <el-input class="width220" v-model="obj.queryForm.contractNo" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="方案编号:" prop="planNo">
                <el-input class="width220" v-model="obj.queryForm.planNo" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="收费起始月:" prop="chargeStartMonth">
                <el-date-picker class="width220" v-model="obj.queryForm.chargeStartMonth" type="month"
                    placeholder="请选择月份" clearable />
            </el-form-item>
            <el-form-item label="账单起始月:" prop="billStartMonth">
                <el-date-picker class="width220" v-model="obj.queryForm.billStartMonth" type="month" placeholder="请选择月份"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="openAttritionConfirmDialog">批量减员确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" @click="handleExport">导出当前的减员数据</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Back" @click="handleReject">驳回</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.listData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="证件号码" align="center" prop="idCard" width="180" />
            <el-table-column label="处理状态" align="center" prop="status" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同编号" align="center" prop="contractNo" />
            <el-table-column label="方案编号" align="center" prop="planNo" />
            <el-table-column label="产品类型" align="center" prop="productType" />
            <el-table-column label="收费起始月" align="center" prop="chargeStartMonth" />
            <el-table-column label="账单起始月" align="center" prop="billStartMonth" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryForm.pageNum"
            v-model:limit="obj.queryForm.pageSize" @pagination="getList" />

        <!-- 批量减员确认弹窗 (假设需要一个弹窗) -->
        <el-dialog v-model="obj.showAttritionConfirmDialog" title="批量减员确认" width="60%">
            <!-- 弹窗内容 -->
            <p>这里是批量减员确认的弹窗内容。</p>
            <el-table :data="obj.selectedItems" border>
                <el-table-column label="唯一号" align="center" prop="uniqueId" />
                <el-table-column label="姓名" align="center" prop="name" />
                <el-table-column label="证件号码" align="center" prop="idCard" width="180" />
                <el-table-column label="客户名称" align="center" prop="customerName" />
            </el-table>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="obj.showAttritionConfirmDialog = false">取消</el-button>
                    <el-button type="primary" @click="confirmAttrition">确认减员</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 驳回弹窗 (假设需要一个弹窗) -->
        <el-dialog v-model="obj.showRejectDialog" title="驳回确认" width="40%">
            <el-form label-width="80px">
                <el-form-item label="驳回原因">
                    <el-input type="textarea" v-model="obj.rejectReason" placeholder="请输入驳回原因"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="obj.showRejectDialog = false">取消</el-button>
                    <el-button type="primary" @click="confirmReject">确认驳回</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="CommercialInsuranceStaffReductionConfirmation">
import { useAreaStore } from '@/store/modules/area'
// import { getCommercialInsuranceAttritionList, confirmAttritionApi, rejectAttritionApi, exportAttritionDataApi } from '@/api/reonManage/commercialInsurance' // 假设的API路径

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();

const provinces = areaStore.provinces // 获取省份数据
const { sys_yes_no } = proxy.useDict('sys_yes_no'); // Simplified dict usage if only one needed

const queryRef = ref(null); // Added ref for form

const obj = reactive({
    showSearch: true,
    loading: false,
    queryForm: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null,
        name: null,
        idCard: null,
        customerName: null,
        contractNo: null,
        planNo: null,
        chargeStartMonth: null,
        billStartMonth: null,
    },
    total: 0,
    listData: [], // 表格数据
    ids: [], // 选中的id列表 (基于 uniqueId)
    names: [], // 选中的姓名列表 (或其他需要的信息)
    single: true, // 是否只选中一个
    multiple: true, // 是否可多选
    selectedItems: [], // 存储完整选中的行数据
    showAttritionConfirmDialog: false, // 控制批量减员确认弹窗
    showRejectDialog: false, // 控制驳回弹窗
    rejectReason: '', // 驳回原因
});

// 模拟表格数据
const generateMockData = (count) => {
    const data = [];
    const statuses = ['待处理', '已确认', '已驳回'];
    const productTypes = ['基础医疗', '补充医疗', '意外险'];
    for (let i = 1; i <= count; i++) {
        const id = 1000 + i;
        data.push({
            uniqueId: `UID${id}`,
            name: `姓名${i}`,
            idCard: `11010119900307${String(i).padStart(4, '0')}`,
            status: statuses[i % statuses.length],
            customerName: `客户公司${String.fromCharCode(65 + (i % 5))}`,
            contractNo: `CON${202300 + i}`,
            planNo: `PLN${i % 10 + 1}`,
            productType: productTypes[i % productTypes.length],
            chargeStartMonth: `2023-${String((i % 12) + 1).padStart(2, '0')}`,
            billStartMonth: `2023-${String(((i + 1) % 12) + 1).padStart(2, '0')}`,
            createTime: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toLocaleString(),
        });
    }
    return data;
};


/** 列表查询 */
function getList() {
    obj.loading = true;
    // 模拟 API 调用延时
    setTimeout(() => {
        // 在实际应用中，这里会调用 API: getCommercialInsuranceAttritionList(obj.queryForm).then(...)
        const mockData = generateMockData(obj.queryForm.pageSize); // 生成当前页的模拟数据
        const totalMockData = generateMockData(15); // 假设总共有 15 条数据用于模拟分页
        obj.listData = totalMockData.slice(
            (obj.queryForm.pageNum - 1) * obj.queryForm.pageSize,
            obj.queryForm.pageNum * obj.queryForm.pageSize
        );
        obj.total = totalMockData.length; // 更新总数以进行分页
        obj.loading = false;
    }, 500); // 500ms 延迟
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryForm.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (queryRef.value) {
        queryRef.value.resetFields();
    }
    handleQuery();
}

// 表格多选框选中数据
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.uniqueId);
    obj.names = selection.map(item => item.name); // 如果需要姓名
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
    obj.selectedItems = selection; // 保存完整选中项
}


// 打开批量减员确认弹窗
function openAttritionConfirmDialog() {
    if (obj.multiple) {
        proxy.$modal.msgWarning("请至少选择一条数据进行操作");
        return;
    }
    obj.showAttritionConfirmDialog = true;
}

// 确认减员操作 (模拟)
function confirmAttrition() {
    // 在实际应用中，这里会调用 API: confirmAttritionApi({ ids: obj.ids }).then(...)
    proxy.$modal.msgSuccess(`已提交 ${obj.ids.length} 条数据的减员确认请求`);
    obj.showAttritionConfirmDialog = false;
    getList(); // 刷新列表
}

// 导出按钮操作 (模拟)
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 驳回按钮操作
function handleReject() {
    if (obj.multiple) {
        proxy.$modal.msgWarning("请至少选择一条数据进行驳回");
        return;
    }
    obj.rejectReason = ''; // 清空上次的原因
    obj.showRejectDialog = true;
}

// 确认驳回操作 (模拟)
function confirmReject() {
    if (!obj.rejectReason) {
        proxy.$modal.msgError("请输入驳回原因");
        return;
    }
    // 在实际应用中，这里会调用 API: rejectAttritionApi({ ids: obj.ids, reason: obj.rejectReason }).then(...)
    proxy.$modal.msgSuccess(`已提交 ${obj.ids.length} 条数据的驳回请求，原因：${obj.rejectReason}`);
    obj.showRejectDialog = false;
    getList(); // 刷新列表
}


// 组件挂载后获取列表数据
onMounted(() => {
    getList();
});

</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}

.mb20 {
    margin-bottom: 20px;
}
</style>