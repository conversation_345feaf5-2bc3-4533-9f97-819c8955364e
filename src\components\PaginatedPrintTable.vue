<template>
  <div>
    <!-- 打印按钮 -->
    <el-button type="primary" plain icon="Printer" @click="handlePrint">
      分页打印
    </el-button>
    
    <!-- 隐藏的打印内容 -->
    <div :id="printId" style="display: none;">
      <div class="print-container">
        <!-- 打印标题 -->
        <div class="print-title">
          <h2>{{ printTitle }}</h2>
          <div class="print-date">打印时间：{{ currentDate }}</div>
        </div>
        
        <!-- 分页表格内容 -->
        <div v-for="(page, pageIndex) in paginatedData" :key="pageIndex" class="print-page">
          <!-- 表格 -->
          <table class="print-table">
            <!-- 表头 -->
            <thead>
              <tr>
                <th v-for="column in visibleColumns" :key="column.prop">
                  {{ column.label }}
                </th>
              </tr>
            </thead>
            <!-- 表体 -->
            <tbody>
              <tr v-for="row in page.data" :key="row.id">
                <td v-for="column in visibleColumns" :key="column.prop">
                  <template v-if="column.prop === 'processStatus'">
                    {{ getProcessStatusText(row[column.prop]) }}
                  </template>
                  <template v-else-if="column.prop === 'importFile'">
                    {{ row[column.prop] }}
                  </template>
                  <template v-else>
                    {{ row[column.prop] || '-' }}
                  </template>
                </td>
              </tr>
            </tbody>
          </table>
          
          <!-- 页脚信息 -->
          <div class="print-footer">
            <div class="page-info">
              第 {{ page.pageNumber }} 页，共 {{ totalPages }} 页
            </div>
            <div class="record-info">
              本页显示 {{ page.data.length }} 条记录，共 {{ totalRecords }} 条记录
            </div>
          </div>
          
          <!-- 分页符（除最后一页） -->
          <div v-if="pageIndex < paginatedData.length - 1" class="page-break"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  // 表格数据
  tableData: {
    type: Array,
    required: true,
    default: () => []
  },
  // 打印标题
  printTitle: {
    type: String,
    default: '数据列表'
  },
  // 每页显示行数
  rowsPerPage: {
    type: Number,
    default: 20
  },
  // 菜单名称（用于判断显示哪些列）
  menuName: {
    type: String,
    default: ''
  }
})

// 生成唯一的打印ID
const printId = ref(`print-table-${Date.now()}`)

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleString('zh-CN')
})

// 总记录数
const totalRecords = computed(() => props.tableData.length)

// 总页数
const totalPages = computed(() => Math.ceil(totalRecords.value / props.rowsPerPage))

// 定义表格列配置
const allColumns = [
  { prop: 'importNo', label: '导入编号' },
  { prop: 'creator', label: '导入人' },
  { prop: 'createTime', label: '导入时间' },
  { prop: 'remark', label: '备注' },
  { prop: 'successCount', label: '成功记录数' },
  { prop: 'failCount', label: '失败记录数' },
  { prop: 'remindCount', label: '提醒记录数' },
  { prop: 'importFile', label: '导入文件' },
  { prop: 'processStatus', label: '处理状态' },
  { prop: 'updateUser', label: '修改人' },
  { prop: 'updateTime', label: '修改时间' }
]

// 根据菜单名称过滤显示的列
const visibleColumns = computed(() => {
  if (props.menuName === 'batchSalaryIntroduction') {
    return allColumns // 显示所有列包括提醒记录数
  } else {
    return allColumns.filter(col => col.prop !== 'remindCount')
  }
})

// 分页数据
const paginatedData = computed(() => {
  const pages = []
  const data = props.tableData
  
  for (let i = 0; i < data.length; i += props.rowsPerPage) {
    const pageData = data.slice(i, i + props.rowsPerPage)
    pages.push({
      pageNumber: Math.floor(i / props.rowsPerPage) + 1,
      data: pageData
    })
  }
  
  return pages
})

// 处理状态文本转换
const getProcessStatusText = (status) => {
  switch (status) {
    case '1':
      return '处理完成'
    case '2':
      return '处理失败'
    default:
      return '处理中'
  }
}

// 打印样式
const printStyles = computed(() => {
  return `
    @media print {
      /* 基础打印设置 */
      * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
      }
      
      body {
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
      }
      
      #${printId.value} {
        display: block !important;
        background: white !important;
        color: black !important;
        font-family: Arial, "Microsoft YaHei", sans-serif !important;
        font-size: 12px !important;
        line-height: 1.4 !important;
      }
      
      .print-container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 15mm !important;
        box-sizing: border-box !important;
      }
      
      .print-title {
        text-align: center !important;
        margin-bottom: 20px !important;
        border-bottom: 2px solid #333 !important;
        padding-bottom: 10px !important;
      }
      
      .print-title h2 {
        margin: 0 0 10px 0 !important;
        font-size: 18px !important;
        font-weight: bold !important;
        color: #333 !important;
      }
      
      .print-date {
        font-size: 12px !important;
        color: #666 !important;
      }
      
      .print-page {
        width: 100% !important;
        margin-bottom: 0 !important;
      }
      
      .print-table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin-bottom: 20px !important;
        table-layout: auto !important;
      }
      
      .print-table th,
      .print-table td {
        border: 1px solid #333 !important;
        padding: 6px 4px !important;
        text-align: center !important;
        vertical-align: middle !important;
        font-size: 10px !important;
        line-height: 1.2 !important;
        word-wrap: break-word !important;
        word-break: break-all !important;
      }
      
      .print-table th {
        background-color: #f5f5f5 !important;
        font-weight: bold !important;
        color: #333 !important;
      }
      
      .print-table td {
        background-color: white !important;
        color: #333 !important;
      }
      
      .print-footer {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-top: 15px !important;
        padding-top: 10px !important;
        border-top: 1px solid #ccc !important;
        font-size: 10px !important;
        color: #666 !important;
      }
      
      .page-break {
        page-break-after: always !important;
        height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
      }
      
      /* 隐藏不需要打印的元素 */
      .no-print {
        display: none !important;
      }
    }
    
    @page {
      size: A4 landscape;
      margin: 10mm;
    }
  `
})

// 打印处理
const handlePrint = () => {
  if (props.tableData.length === 0) {
    ElMessage.warning('没有数据可以打印')
    return
  }
  
  // 创建样式元素
  const styleElement = document.createElement('style')
  styleElement.textContent = printStyles.value
  document.head.appendChild(styleElement)
  
  // 执行打印
  setTimeout(() => {
    const printContent = document.getElementById(printId.value)
    if (printContent) {
      // 显示打印内容
      printContent.style.display = 'block'
      
      // 打印
      window.print()
      
      // 打印完成后隐藏内容并清理样式
      setTimeout(() => {
        printContent.style.display = 'none'
        document.head.removeChild(styleElement)
      }, 100)
    }
  }, 100)
}

// 组件挂载时确保打印内容隐藏
onMounted(() => {
  const printElement = document.getElementById(printId.value)
  if (printElement) {
    printElement.style.display = 'none'
  }
})
</script>

<style scoped>
.print-container {
  display: none;
}

@media screen {
  .print-container {
    display: none !important;
  }
}
</style>
