<!-- 扣缴义务人信息表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="扣缴义务人编号:" prop="agentCode">
                <el-input class="width220" v-model="obj.queryParams.agentCode" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="扣缴义务人名称:" prop="agentName">
                <el-input class="width220" v-model="obj.queryParams.agentName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="扣缴义务人类型:" prop="agentType">
                <el-select class="width220" v-model="obj.queryParams.agentType" placeholder="请选择" clearable>
                    <el-option v-for="item in withholdingAgentTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资发放地:" prop="salaryPaymentPlace">
                <el-select class="width220" v-model="obj.queryParams.salaryPaymentPlace" placeholder="请选择" clearable>
                    <el-option v-for="item in salaryPaymentPlaceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="服务网点:" prop="servicePoint">
                <el-input class="width220" v-model="obj.queryParams.servicePoint" placeholder="请输入" clearable />
            </el-form-item>
            <el-row class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                <el-button type="warning" plain icon="Edit" @click="handleEdit">修改</el-button>
                <el-button type="info" plain icon="View" @click="handleView">查看</el-button>
                <el-button type="success" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="扣缴义务人编号" align="center" prop="agentCode" />
            <el-table-column label="扣缴义务人名称" align="center" prop="agentName" />
            <el-table-column label="服务网点" align="center" prop="servicePoint" />
            <el-table-column label="扣缴义务人类型" align="center" prop="agentType">
                <template #default="scope">
                    <el-tag :type="scope.row.agentType === '1' ? 'primary' : 'success'">
                        {{ scope.row.agentType === '1' ? '企业' : '个人' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="工资发放地" align="center" prop="salaryPaymentPlace" />
            <el-table-column label="工资发放开户行" align="center" prop="salaryPaymentBank" />
            <el-table-column label="是否建议引导同行卡发放" align="center" prop="isRecommendSameBank" />
            <el-table-column label="公转私限额" align="center" prop="publicTransferPrivateLimit" />
            <el-table-column label="发薪对接财务" align="center" prop="payrollConnectFinance" />
            <el-table-column label="邮件发送" align="center" prop="emailSend" />
            <el-table-column label="会计对接人" align="center" prop="accountantContact" />
            <el-table-column label="残障金比率" align="center" prop="disabilityInsuranceRate" />
            <el-table-column label="是否开通服务" align="center" prop="isOpenService" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="修改人" align="center" prop="updateBy" />
            <el-table-column label="修改时间" align="center" prop="updateTime" />
            <el-table-column label="操作" align="center" width="220">
                <template #default="scope">
                    <el-button type="primary" text icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button type="primary" text icon="View" @click="handleView(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增/编辑 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="80%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" :rules="rules" inline label-width="auto">
                <el-form-item label="扣缴义务人编号:" prop="agentCode">
                    <el-input class="width220" v-model="obj.dialogForm.agentCode" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="服务网点:" prop="servicePoint">
                    <el-input class="width220" v-model="obj.dialogForm.servicePoint" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="关联公司:" prop="relatedCompany">
                    <el-input class="width220" v-model="obj.dialogForm.relatedCompany" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="扣缴义务人名称:" prop="agentName">
                    <el-input class="width220" v-model="obj.dialogForm.agentName" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="工资发放地:" prop="salaryPaymentPlace">
                    <el-select class="width220" v-model="obj.dialogForm.salaryPaymentPlace" placeholder="请选择" clearable>
                        <el-option v-for="item in salaryPaymentPlaceOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-row>
                    <el-form-item label="备注:" prop="remark">
                        <el-input type="textarea" class="width420" :rows="3" v-model="obj.dialogForm.remark"
                            placeholder="请输入" clearable />
                    </el-form-item>
                </el-row>
            </el-form>
            <div style="display: flex; justify-content: space-between;">
                <div style="width: 49%;">
                    <el-row :gutter="10" class="mb20">
                        <el-col :span="24">
                            <el-button type="primary" icon="Plus" @click="handleAddHousingFund">新增</el-button>
                            <el-button type="danger" icon="Delete" @click="handleDeleteHousingFund">删除选中</el-button>
                        </el-col>
                    </el-row>
                    <el-table :data="housingFundData" border @selection-change="handleSelectionChangeHousingFund">
                        <el-table-column type="selection" width="55" />
                        <el-table-column label="序号" align="center" type="index" width="55" />
                        <el-table-column label="起始月" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.startMonth" type="month"
                                    placeholder="选择月" />
                            </template>
                        </el-table-column>
                        <el-table-column label="截止月" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.endMonth" type="month"
                                    placeholder="选择月" />
                            </template>
                        </el-table-column>
                        <el-table-column label="公积金超额计算金额" align="center" prop="amount" width="160">
                            <template #default="scope">
                                <el-input style="width: 100%;" v-model="scope.row.amount" placeholder="请输入" clearable />
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" width="140">
                            <template #default="scope">
                                <el-button type="primary" text icon="Plus"
                                    @click="handleAddHousingFund(scope.row)"></el-button>
                                <el-button type="primary" text icon="Delete"
                                    @click="handleDeleteHousingFund(scope.row)"></el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div style="width: 49%;">
                    <el-row :gutter="10" class="mb20">
                        <el-col :span="24">
                            <el-button type="primary" icon="Plus" @click="handleAddCompensation">新增</el-button>
                            <el-button type="danger" icon="Delete" @click="handleDeleteCompensation">删除选中</el-button>
                        </el-col>
                    </el-row>
                    <el-table :data="compensationData" border @selection-change="handleSelectionChangeCompensation">
                        <el-table-column type="selection" width="55" />
                        <el-table-column label="序号" align="center" type="index" width="55" />
                        <el-table-column label="起始月" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.startMonth" type="month"
                                    placeholder="选择月" />
                            </template>
                        </el-table-column>
                        <el-table-column label="截止月" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.endMonth" type="month"
                                    placeholder="选择月" />
                            </template>
                        </el-table-column>
                        <el-table-column label="经济补偿金超额计算金额" align="center" width="180">
                            <template #default="scope">
                                <el-input style="width: 100%;" v-model="scope.row.amount" placeholder="请输入" clearable />
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" width="140">
                            <template #default="scope">
                                <el-button type="primary" text icon="Plus"
                                    @click="handleAddCompensation(scope.row)"></el-button>
                                <el-button type="primary" text icon="Delete"
                                    @click="handleDeleteCompensation(scope.row)"></el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <template #footer>
                <el-button type="primary" @click="handleSubmit">立即提交</el-button>
                <el-button type="primary" @click="handleReset">重置</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="WithholdingAgent">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



// 扣缴义务人类型选项
const withholdingAgentTypeOptions = [
    { value: '1', label: '企业' },
    { value: '2', label: '个人' }
];

// 工资发放地选项
const salaryPaymentPlaceOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' }
];

// 表单验证规则
const rules = {
    agentCode: [
        { required: true, message: '请输入扣缴义务人编号', trigger: 'blur' }
    ],
    agentName: [
        { required: true, message: '请输入扣缴义务人名称', trigger: 'blur' }
    ],
    agentType: [
        { required: true, message: '请选择扣缴义务人类型', trigger: 'change' }
    ],
    salaryPaymentPlace: [
        { required: true, message: '请选择工资发放地', trigger: 'change' }
    ],
    servicePoint: [
        { required: false, message: '请输入服务网点', trigger: 'blur' }
    ],
    relatedCompany: [
        { required: false, message: '请输入关联公司', trigger: 'blur' }
    ],
    remark: [
        { required: false, message: '请输入备注', trigger: 'blur' }
    ]
};

// 公积金超额计算数据
const housingFundData = ref([]);

// 经济补偿金超额计算数据
const compensationData = ref([]);

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        agentCode: null, // 扣缴义务人编号
        agentName: null, // 扣缴义务人名称
        agentType: null, // 扣缴义务人类型
        salaryPaymentPlace: null, // 工资发放地
        servicePoint: null, // 服务网点
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {
        agentCode: '',
        servicePoint: '',
        relatedCompany: '',
        agentName: '',
        salaryPaymentPlace: '',
        remark: ''
    }, // 表单数据
    dialogShow: false, // 对话框显示
    ids: [], // 选中的id
    title: "", // 标题

    housingFundIndex: [], // 公积金选中的index
    compensationIndex: [], // 经济补偿金选中的index
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                agentCode: 'WA001',
                agentName: '某某科技有限公司',
                agentType: '1',
                salaryPaymentPlace: '北京',
                servicePoint: '北京总部',
                relatedCompany: '某某科技集团'
            },
            {
                id: 2,
                agentCode: 'WA002',
                agentName: '李四',
                agentType: '2',
                salaryPaymentPlace: '上海',
                servicePoint: '上海分部',
                relatedCompany: '某某信息技术有限公司'
            },
            {
                id: 3,
                agentCode: 'WA003',
                agentName: '某某软件有限公司',
                agentType: '1',
                salaryPaymentPlace: '广州',
                servicePoint: '广州分部',
                relatedCompany: '某某软件集团'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 新增 */
function handleAdd() {
    obj.dialogShow = true;
    obj.title = '新增扣缴义务人信息';
    resetForm();
    // 初始化公积金和经济补偿金数据
    housingFundData.value = [];
    compensationData.value = [];
}

/** 修改 */
function handleEdit(row) {
    obj.dialogShow = true;
    obj.title = '修改扣缴义务人信息';
    resetForm();
    // 模拟数据，实际项目中应该调用API获取详细信息
    if (row) {
        obj.dialogForm = {
            agentCode: row.agentCode,
            servicePoint: row.servicePoint,
            relatedCompany: row.relatedCompany,
            agentName: row.agentName,
            salaryPaymentPlace: row.salaryPaymentPlace,
            remark: ''
        };
    }

    // 模拟公积金超额计算数据
    housingFundData.value = [
        {
            id: 1,
            startMonth: '2023-01',
            endMonth: '2023-12',
            amount: 5000
        }
    ];

    // 模拟经济补偿金超额计算数据
    compensationData.value = [
        {
            id: 1,
            startMonth: '2023-01',
            endMonth: '2023-12',
            amount: 8000
        }
    ];
}

/** 查看 */
function handleView(row) {
    obj.dialogShow = true;
    obj.title = '查看扣缴义务人信息';
    resetForm();
    // 模拟数据，实际项目中应该调用API获取详细信息
    if (row) {
        obj.dialogForm = {
            agentCode: row.agentCode,
            servicePoint: row.servicePoint,
            relatedCompany: row.relatedCompany,
            agentName: row.agentName,
            salaryPaymentPlace: row.salaryPaymentPlace,
            remark: ''
        };
    }

    // 模拟公积金超额计算数据
    housingFundData.value = [
        {
            id: 1,
            startMonth: '2023-01',
            endMonth: '2023-12',
            amount: 5000
        }
    ];

    // 模拟经济补偿金超额计算数据
    compensationData.value = [
        {
            id: 1,
            startMonth: '2023-01',
            endMonth: '2023-12',
            amount: 8000
        }
    ];
}

/** 导出 */
function handleExport() {
    if (obj.tableData.length === 0) {
        proxy.$modal.msgError('没有数据可导出');
        return;
    }

    proxy.$modal.msgSuccess('导出成功');
    // 实际项目中应该调用API进行导出

    // 模拟下载文件
    const fileName = '扣缴义务人信息表.xlsx';
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}




// 表单重置
function resetForm() {
    obj.dialogForm = {
        agentCode: '',
        servicePoint: '',
        relatedCompany: '',
        agentName: '',
        salaryPaymentPlace: '',
        remark: ''
    };
    proxy.resetForm("formRef");
}

// 新增公积金超额计算数据
function handleAddHousingFund() {
    housingFundData.value.push({
        id: housingFundData.value.length + 1,
        startMonth: '',
        endMonth: '',
        amount: '',
        index: housingFundData.value.length
    });
}

// 选中公积金超额计算数据
function handleSelectionChangeHousingFund(selection) {
    if (selection) {
        obj.housingFundIndex = selection.map(item => item.index);
    }
}

// 删除公积金超额计算数据
function handleDeleteHousingFund(row) {

    // 判断row是不是event对象
    if (!(row instanceof Event)) {
        const index = housingFundData.value.findIndex(item => item.index === row.index);
        if (index !== -1) {
            housingFundData.value.splice(index, 1);
        }
    } else {
        for (let i = 0; i < obj.housingFundIndex.length; i++) {
            const index = housingFundData.value.findIndex(item => item.index === obj.housingFundIndex[i]);
            if (index !== -1) {
                housingFundData.value.splice(index, 1);
            }
        }
    }

}

// 新增经济补偿金超额计算数据
function handleAddCompensation() {
    compensationData.value.push({
        id: compensationData.value.length + 1,
        startMonth: '',
        endMonth: '',
        amount: '',
        index: compensationData.value.length
    });
}

// 选中经济补偿金超额计算数据
function handleSelectionChangeCompensation(selection) {
    console.log(selection);
    if (selection) {
        obj.compensationIndex = selection.map(item => item.index);
        console.log(obj.compensationIndex);
    }
}

// 删除经济补偿金超额计算数据
function handleDeleteCompensation(row) {

    // 判断row是不是event对象
    if (!(row instanceof Event)) {
        const index = compensationData.value.findIndex(item => item.index === row.index);
        if (index !== -1) {
            compensationData.value.splice(index, 1);
        }
    } else {
        for (let i = 0; i < obj.compensationIndex.length; i++) {
            const index = compensationData.value.findIndex(item => item.index === obj.compensationIndex[i]);
            if (index !== -1) {
                compensationData.value.splice(index, 1);
            }
        }
    }
}

// 提交表单
function handleSubmit() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            // 实际项目中应该调用API进行提交
            proxy.$modal.msgSuccess('提交成功');
            obj.dialogShow = false;
            getList(); // 刷新列表
        }
    });
}

// 重置按钮
function handleReset() {
    resetForm();
}

/** 选中数据改变 */
function handleSelectionChange(selection) {
    if (selection) {
        obj.ids = selection.map(item => item.id);
        obj.single = selection.length != 1;
        obj.multiple = !selection.length;
    }
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.width420 {
    width: 420px;
}

.mb8 {
    margin-bottom: 8px;
}

.mb20 {
    margin-bottom: 20px;
}
</style>