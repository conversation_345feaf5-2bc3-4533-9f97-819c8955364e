<!-- 收入数据报表恢复 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" v-show="obj.showSearch" inline label-width="auto">
            <el-tabs type="border-card">
                <el-tab-pane label="收入报表治理">
                    <border-box title="删除收入报表数据" style="width: 80%;margin: 20px auto;">
                        <el-form-item label="删除月份:" prop="typeCode">
                            <el-date-picker class="width220" v-model="obj.queryParams.sales" type="date"
                                placeholder="请选择日期" clearable />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleQuery">删除</el-button>
                        </el-form-item>
                    </border-box>
                    <border-box title="生成收入报表数据" style="width: 80%;margin: 20px auto;">
                        <el-form-item label="生成类型:" prop="typeCode">
                            <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="合同编号:" prop="typeCode">
                            <el-input class="width220" v-model="obj.queryParams.sales" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="账单模板:" prop="typeCode">
                            <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账单月:" prop="typeCode">
                            <el-date-picker class="width220" v-model="obj.queryParams.sales" type="date"
                                placeholder="请选择日期" clearable />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleQuery">提交</el-button>
                        </el-form-item>
                    </border-box>
                </el-tab-pane>
                <el-tab-pane label="账单数据治理">
                    <border-box title="重新生成Bill_Service_Num_Info数据" style="width: 80%;margin: 20px auto;">
                        <el-form-item label="合同编号:" prop="typeCode">
                            <el-input class="width220" v-model="obj.queryParams.sales" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="账单模板:" prop="typeCode">
                            <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账单月:" prop="typeCode">
                            <el-date-picker class="width220" v-model="obj.queryParams.sales" type="date"
                                placeholder="请选择日期" clearable />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleQuery">提交</el-button>
                        </el-form-item>
                    </border-box>
                    <border-box title="重新生成BILL_COST数据" style="width: 80%;margin: 20px auto;">
                        <el-form-item label="合同编号:" prop="typeCode">
                            <el-input class="width220" v-model="obj.queryParams.sales" placeholder="请输入" clearable />
                        </el-form-item>
                        <el-form-item label="账单模板:" prop="typeCode">
                            <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                                <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="账单月:" prop="typeCode">
                            <el-date-picker class="width220" v-model="obj.queryParams.sales" type="date"
                                placeholder="请选择日期" clearable />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleQuery">提交</el-button>
                        </el-form-item>
                    </border-box>
                </el-tab-pane>
            </el-tabs>
        </el-form>
    </div>
</template>

<script setup name="RevenueData_recover">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";


const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数
    ids: [],//选中id

    tableData: [],//列表
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框操作 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

getList();
</script>
<style lang="scss" scoped></style>