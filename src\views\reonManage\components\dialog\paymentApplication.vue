<template>
    <div>
        <!-- 对话框 -->
        <el-dialog v-model="dialogShow" :title="props.title" width="70%" append-to-body draggable @close="handleClose">
            <el-form class="formHight" ref="formRef" :model="props.form" :rules="rules" inline label-width="auto">
                <el-tabs type="border-card">
                    <el-tab-pane label="支付申请">
                        <!-- 支付类型 -->
                        <el-form-item label="支付类型" prop="paymentType">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.paymentType"
                                placeholder="请选择支付类型">
                                <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 福利办理方 -->
                        <el-form-item label="福利办理方" prop="welfareAgent"
                            v-if="props.menuName != 'wagePaymentsAreNotMatched' && props.menuName != 'releaseBatch' && props.menuName != 'wagePayment'">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.welfareAgent"
                                placeholder="请选择福利办理方">
                                <el-option v-for="item in welfareAgentOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 福利包名称 -->
                        <el-form-item label="福利包名称" prop="welfarePackageName"
                            v-if="props.menuName != 'wagePaymentsAreNotMatched' && props.menuName != 'releaseBatch' && props.menuName != 'wagePayment'">
                            <el-select :disabled="props.disabled" class="width220"
                                v-model="props.form.welfarePackageName" placeholder="请选择福利包名称">
                                <el-option v-for="item in welfarePackageNameOptions" :key="item.value"
                                    :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 报表年月 -->
                        <el-form-item label="报表年月" prop="reportMonth"
                            v-if="props.menuName != 'wagePaymentsAreNotMatched' && props.menuName != 'releaseBatch' && props.menuName != 'wagePayment'">
                            <el-date-picker :disabled="props.disabled" class="width220" v-model="props.form.reportMonth"
                                type="month" placeholder="请选择报表年月" value-format="YYYY-MM" />
                        </el-form-item>
                        <!-- 支付详细类型 -->
                        <el-form-item label="支付详细类型" prop="paymentDetailType"
                            v-if="props.menuName != 'wagePaymentsAreNotMatched' && props.menuName != 'releaseBatch' && props.menuName != 'wagePayment'">
                            <el-select :disabled="props.disabled" class="width220"
                                v-model="props.form.paymentDetailType" placeholder="请选择支付详细类型">
                                <el-option v-for="item in paymentDetailTypeOptions" :key="item.value"
                                    :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 支付抬头 -->
                        <el-form-item label="支付抬头" prop="paymentHeader"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.paymentHeader"
                                placeholder="请选择支付抬头">
                                <el-option v-for="item in paymentHeaderOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 支付方式 -->
                        <el-form-item label="支付方式" prop="paymentMethod">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.paymentMethod"
                                placeholder="请选择支付方式">
                                <el-option v-for="item in paymentMethodOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 详细支付方式 -->
                        <el-form-item label="详细支付方式" prop="detailPaymentMethod">
                            <el-select :disabled="props.disabled" class="width220"
                                v-model="props.form.detailPaymentMethod" placeholder="请选择详细支付方式">
                                <el-option v-for="item in detailPaymentMethodOptions" :key="item.value"
                                    :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 收款方 -->
                        <el-form-item label="收款方" prop="payee">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.payee"
                                placeholder="请选择收款方">
                                <el-option v-for="item in payeeOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 银行卡号选择 -->
                        <el-form-item label="银行卡号选择" prop="bankCardNo"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.bankCardNo"
                                placeholder="请选择银行卡号">
                                <el-option v-for="item in bankCardNoOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 收款银行 -->
                        <el-form-item label="收款银行" prop="bankName">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.bankName"
                                placeholder="请选择收款银行">
                                <el-option v-for="item in bankNameOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 收款银行开户行 -->
                        <el-form-item label="收款银行开户行" prop="bankBranch">
                            <el-input :disabled="props.disabled" class="width220" v-model="props.form.bankBranch"
                                placeholder="请输入收款银行开户行" />
                        </el-form-item>
                        <!-- 银行账号/支票号 -->
                        <el-form-item label="银行账号/支票号" prop="accountNo">
                            <el-input :disabled="props.disabled" class="width220" v-model="props.form.accountNo"
                                placeholder="请输入银行账号/支票号" />
                        </el-form-item>
                        <!-- 单据数量 -->
                        <el-form-item label="单据数量" prop="documentCount">
                            <el-input :disabled="props.disabled" class="width220" v-model="props.form.documentCount"
                                placeholder="请输入单据数量" />
                        </el-form-item>
                        <!-- 申请支付总额 -->
                        <el-form-item label="申请支付总额" prop="totalAmount">
                            <el-input :disabled="props.disabled" class="width220" v-model="props.form.totalAmount"
                                placeholder="请输入申请支付总额" />
                        </el-form-item>
                        <!-- 应付总额 -->
                        <el-form-item label="应付总额" prop="payableAmount">
                            <el-input :disabled="props.disabled" class="width220" v-model="props.form.payableAmount"
                                placeholder="请输入应付总额" />
                        </el-form-item>
                        <!-- 工资支付日期 -->
                        <el-form-item label="工资支付日期" prop="salaryPaymentDate"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <el-date-picker :disabled="props.disabled" v-model="props.form.salaryPaymentDate"
                                type="date" placeholder="请选择工资支付日期" value-format="YYYY-MM-DD" />
                        </el-form-item>
                        <!-- 最晚支付日期 -->
                        <el-form-item label="最晚支付日期" prop="latestPaymentDate"
                            v-if="props.menuName != 'wagePaymentsAreNotMatched'">
                            <el-date-picker :disabled="props.disabled" v-model="props.form.latestPaymentDate"
                                type="date" placeholder="请选择最晚支付日期" value-format="YYYY-MM-DD" />
                        </el-form-item>
                        <!-- 支付所属年月 -->
                        <el-form-item label="支付所属年月" prop="paymentMonth">
                            <el-date-picker :disabled="props.disabled" v-model="props.form.paymentMonth" type="month"
                                placeholder="请选择支付所属年月" value-format="YYYY-MM" />
                        </el-form-item>
                        <!-- 支付地 -->
                        <el-form-item label="支付地" prop="paymentPlace"
                            v-if="props.menuName != 'wagePaymentsAreNotMatched' && props.menuName != 'releaseBatch' && props.menuName != 'wagePayment'">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.paymentPlace"
                                placeholder="请选择支付地">
                                <el-option v-for="item in paymentPlaceOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 客户 -->
                        <el-form-item label="客户" prop="customer"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.customer"
                                placeholder="请选择客户">
                                <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 派单地 -->
                        <el-form-item label="派单地" prop="dispatchPlace"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.dispatchPlace"
                                placeholder="请选择派单地">
                                <el-option v-for="item in dispatchPlaceOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 派单地抬头 -->
                        <el-form-item label="派单地抬头" prop="dispatchHeader"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.dispatchHeader"
                                placeholder="请选择派单地抬头">
                                <el-option v-for="item in dispatchHeaderOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 是否退票重发 -->
                        <el-form-item label="是否退票重发" prop="isRefund"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <el-select :disabled="props.disabled" class="width220" v-model="props.form.isRefund"
                                placeholder="请选择是否退票重发">
                                <el-option v-for="item in isRefundOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <!-- 供应商发薪时间 -->
                        <el-form-item label="供应商发薪时间" prop="supplierPaymentTime"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'wagePaymentsAreNotMatched' && props.menuName != 'payment'">
                            <el-date-picker :disabled="props.disabled" v-model="props.form.supplierPaymentTime"
                                type="date" placeholder="请选择供应商发薪时间" value-format="YYYY-MM-DD" />
                        </el-form-item>
                        <!-- 支付内容(摘要) -->
                        <el-form-item label="支付内容(摘要)" prop="paymentContent"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <el-input :disabled="props.disabled" class="width220" v-model="props.form.paymentContent"
                                placeholder="请输入支付内容" />
                        </el-form-item>
                        <border-box title="发放批次到款数据"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <div class="mb8" v-if="props.menuName != 'payrollDetails'">
                                <el-button type="primary" @click="addRow">新增</el-button>
                                <el-button type="primary" @click="deleteRow">删除</el-button>
                            </div>
                            <el-table :data="expectedPaymentData" border @selection-change="handleSelectionChange">
                                <el-table-column type="selection" width="55" />
                                <el-table-column label="序号" align="center" prop="id" />
                                <el-table-column label="客户" align="center" prop="customer" />
                                <el-table-column label="到款(预计)日期" align="center" prop="expectedDate" />
                                <el-table-column label="到款金额" align="center" prop="amount" />
                                <el-table-column label="到款名称抬头" align="center" prop="paymentHeader" />
                            </el-table>
                            <div style="font-size: 16px;font-weight: 600;" class="mt20 mb10">核销到款数据</div>
                            <el-table :data="verifiedPaymentData" border>
                                <el-table-column label="序号" align="center" prop="id" />
                                <el-table-column label="客户" align="center" prop="customer" />
                                <el-table-column label="到款日期" align="center" prop="verifiedDate" />
                                <el-table-column label="到款金额" align="center" prop="amount" />
                                <el-table-column label="到款名称抬头" align="center" prop="paymentHeader" />
                            </el-table>
                        </border-box>
                        <el-form-item label="派单地支付目的" prop="dispatchPurpose"
                            v-if="props.menuName != 'actualPayment_query' && props.menuName != 'payment'">
                            <el-input :disabled="props.disabled" class="width420" type="textarea" :rows="3"
                                v-model="props.form.dispatchPurpose" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="付款用途" prop="paymentPurpose"
                            v-if="props.menuName != 'wagePaymentsAreNotMatched' && props.menuName != 'releaseBatch' && props.menuName != 'wagePayment'">
                            <el-input :disabled="props.disabled" class="width420" type="textarea" :rows="3"
                                v-model="props.form.paymentPurpose" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input :disabled="props.disabled" class="width420" type="textarea" :rows="3"
                                v-model="props.form.remark" placeholder="请输入内容" />
                        </el-form-item>
                        <div
                            v-if="props.menuName != 'wagePaymentsAreNotMatched' && props.menuName != 'releaseBatch' && props.menuName != 'wagePayment'">
                            <el-divider content-position="left">文件上传</el-divider>
                            <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="流程信息"
                        v-if="props.menuName != 'wagePaymentsAreNotMatched' && props.menuName != 'releaseBatch' && props.menuName != 'wagePayment'">
                        <ProcessInformation :tableData="approvalData" />
                    </el-tab-pane>
                </el-tabs>
            </el-form>
            <template #footer v-if="props.menuName != 'payrollDetails' && props.menuName != 'actualPayment_query'">
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">提交</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import borderBox from '../borderBox.vue';
import ProcessInformation from '../processInformation.vue';

const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    form: {
        type: Object,
        default: () => ({})
    },
    menuName: {
        type: String,
        default: ''
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

// 先定义emit
const emit = defineEmits(['close', 'update:dialogShow']);

// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});

const approvalData = ref([])
const paymentPlaceOptions = ref([])
const customerOptions = ref([])
const dispatchPlaceOptions = ref([])
const dispatchHeaderOptions = ref([])
const isRefundOptions = ref([])
const bankNameOptions = ref([])
const bankCardNoOptions = ref([])
const payeeOptions = ref([])
const paymentTypeOptions = ref([])
const paymentMethodOptions = ref([])
const detailPaymentMethodOptions = ref([])

/** 关闭弹窗 */
function handleClose() {
    dialogShow.value = false;
    emit('close')
}

function submitForm() {
    console.log(**********);
}
</script>
