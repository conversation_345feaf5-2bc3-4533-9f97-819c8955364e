<!-- 批量添加账单起始月 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateFrom" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateTo" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="导入编号:" prop="importNo">
                <el-select class="width220" v-model="obj.queryParams.importNo" placeholder="请选择" clearable>
                    <el-option v-for="item in importNoOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入人:" prop="importer">
                <el-select class="width220" v-model="obj.queryParams.importer" placeholder="请选择" clearable>
                    <el-option v-for="item in importerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleDownloadTemplate">下载模版</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="导入编号" align="center" prop="importNo" />
            <el-table-column label="导入人" align="center" prop="importer">
                <template #default="scope">
                    {{importerOptions.find(item => item.value === scope.row.importer)?.label || '未知'}}
                </template>
            </el-table-column>
            <el-table-column label="导入时间" align="center" prop="importTime" />
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="成功记录数" align="center" width="90" prop="successCount" />
            <el-table-column label="失败记录数" align="center" width="90" prop="failCount" />
            <el-table-column label="导入文件" align="center" prop="importFile">
                <template #default="scope">
                    <el-button type="primary" link @click="handleDownloadFile(scope.row)">下载</el-button>
                </template>
            </el-table-column>
            <el-table-column label="处理状态" align="center" prop="processStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.processStatus === '2' ? 'success' : (scope.row.processStatus === '3' ? 'danger' : 'warning')">
                        {{processStatusOptions.find(item => item.value === scope.row.processStatus)?.label || '未知状态'}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 导入弹窗 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm"
            :type="obj.importType" />
    </div>
</template>

<script setup>

import ImportForm from '@/views/reonManage/components/forms/import.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 导入人选项
const importerOptions = [
    { value: '1', label: '张三' },
    { value: '2', label: '李四' },
    { value: '3', label: '王五' }
];

// 导入编号选项
const importNoOptions = [
    { value: 'IMP20230001', label: 'IMP20230001' },
    { value: 'IMP20230002', label: 'IMP20230002' },
    { value: 'IMP20230003', label: 'IMP20230003' }
];

// 处理状态选项
const processStatusOptions = [
    { value: '1', label: '处理中' },
    { value: '2', label: '处理成功' },
    { value: '3', label: '处理失败' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        importNo: 'IMP20230001',
        importer: '1',
        importTime: '2023-01-15 10:30:45',
        remark: '批量添加账单起始月数据',
        successCount: 120,
        failCount: 5,
        importFile: '账单起始月数据20230115.xlsx',
        processStatus: '2',
        creator: '张三',
        createTime: '2023-01-15 10:30:45'
    },
    {
        id: 2,
        importNo: 'IMP20230002',
        importer: '2',
        importTime: '2023-02-20 14:25:30',
        remark: '批量添加账单起始月数据',
        successCount: 85,
        failCount: 0,
        importFile: '账单起始月数据20230220.xlsx',
        processStatus: '2',
        creator: '李四',
        createTime: '2023-02-20 14:25:30'
    },
    {
        id: 3,
        importNo: 'IMP20230003',
        importer: '3',
        importTime: '2023-03-10 09:15:20',
        remark: '批量添加账单起始月数据',
        successCount: 0,
        failCount: 15,
        importFile: '账单起始月数据20230310.xlsx',
        processStatus: '3',
        creator: '王五',
        createTime: '2023-03-10 09:15:20'
    }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateFrom: null, // 导入日期从
        importDateTo: null, // 导入日期到
        importNo: null, // 导入编号
        importer: null, // 导入人
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        remark: '', // 备注
        file: null // 文件
    }, // 导入表单
    dialogShow: false, // 导入弹窗
    ids: [], // 选中的id
    title: "导入", // 标题
    importType: 'batchAddBillingStartMonth' // 导入类型
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据处理
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.importDateFrom) {
            const startDate = new Date(obj.queryParams.importDateFrom).getTime();
            filteredData = filteredData.filter(item => new Date(item.importTime).getTime() >= startDate);
        }

        if (obj.queryParams.importDateTo) {
            const endDate = new Date(obj.queryParams.importDateTo).getTime();
            filteredData = filteredData.filter(item => new Date(item.importTime).getTime() <= endDate);
        }

        if (obj.queryParams.importNo) {
            filteredData = filteredData.filter(item => item.importNo === obj.queryParams.importNo);
        }

        if (obj.queryParams.importer) {
            filteredData = filteredData.filter(item => item.importer === obj.queryParams.importer);
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 导入
function handleImport() {
    obj.dialogShow = true;
}

// 关闭导入弹窗
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {
        remark: '',
        file: null
    };
}

// 下载模版
function handleDownloadTemplate() {
    // 模拟下载模板文件
    const fileName = '批量添加账单起始月模板.xlsx';
    const link = document.createElement('a');
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    proxy.$modal.msgSuccess('模板下载成功');
}

// 下载文件
function handleDownloadFile(row) {
    // 模拟下载文件
    const fileName = row.importFile;
    const link = document.createElement('a');
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    proxy.$modal.msgSuccess('文件下载成功');
}

getList();
</script>
<style lang="scss" scoped></style>