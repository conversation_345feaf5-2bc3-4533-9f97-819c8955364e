<!-- 工资抵扣项导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入编号:" prop="importCode">
                <el-input class="width220" v-model="obj.queryParams.importCode" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="税款所属年月>=:" prop="taxYearMonthFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.taxYearMonthFrom" type="month"
                    format="YYYY-MM" value-format="YYYY-MM" placeholder="请选择年月" clearable />
            </el-form-item>
            <el-form-item label="税款所属年月<=:" prop="taxYearMonthTo">
                <el-date-picker class="width220" v-model="obj.queryParams.taxYearMonthTo" type="month" format="YYYY-MM"
                    value-format="YYYY-MM" placeholder="请选择年月" clearable />
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="withholdingAgent">
                <el-input class="width220" v-model="obj.queryParams.withholdingAgent" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="importDeduction">导入专项扣除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="List" @click="viewImportRecord">查看导入记录</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="导入编号" align="center" prop="importCode" width="150" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
            <el-table-column label="修改时间" align="center" prop="updateTime" width="150" />
            <el-table-column label="工号" align="center" prop="employeeId" width="100" />
            <el-table-column label="姓名" align="center" prop="employeeName" width="100" />
            <el-table-column label="证件类型" align="center" width="150">
                <template #default="scope">
                    {{ getIdTypeName(scope.row.idType) }}
                </template>
            </el-table-column>
            <el-table-column label="证件号码" align="center" prop="idNumber" width="180" />
            <el-table-column label="扣缴义务人编号" align="center" prop="withholdingAgentCode" width="150" />
            <el-table-column label="扣缴义务人名称" align="center" prop="withholdingAgentName" width="180" />
            <el-table-column label="客户名称" align="center" prop="customerName" width="180" />
            <el-table-column label="所得期间起" align="center" prop="incomePeriodStart" width="100" />
            <el-table-column label="所得期间止" align="center" prop="incomePeriodEnd" width="100" />
            <el-table-column label="累计子女教育支出扣除" align="center" width="180">
                <template #default="scope">
                    {{ formatAmount(scope.row.childEducationDeduction) }}
                </template>
            </el-table-column>
            <el-table-column label="累计继续教育支出扣除" align="center" width="180">
                <template #default="scope">
                    {{ formatAmount(scope.row.continuingEducationDeduction) }}
                </template>
            </el-table-column>
            <el-table-column label="累计住房租金支出扣除" align="center" width="180">
                <template #default="scope">
                    {{ formatAmount(scope.row.housingRentDeduction) }}
                </template>
            </el-table-column>
            <el-table-column label="累计住房贷款利息支出扣除" align="center" width="200">
                <template #default="scope">
                    {{ formatAmount(scope.row.housingLoanInterestDeduction) }}
                </template>
            </el-table-column>
            <el-table-column label="累计赡养老人支出扣除" align="center" width="180">
                <template #default="scope">
                    {{ formatAmount(scope.row.elderlySupport) }}
                </template>
            </el-table-column>
            <el-table-column label="累计3岁以下婴幼儿照护" align="center" width="180">
                <template #default="scope">
                    {{ formatAmount(scope.row.childcareDeduction) }}
                </template>
            </el-table-column>
            <el-table-column label="累计个人养老金" align="center" width="150">
                <template #default="scope">
                    {{ formatAmount(scope.row.personalPensionDeduction) }}
                </template>
            </el-table-column>
            <el-table-column label="其它扣除" align="center" width="120">
                <template #default="scope">
                    {{ formatAmount(scope.row.otherDeductions) }}
                </template>
            </el-table-column>
            <el-table-column label="累计基本减除费用" align="center" width="150">
                <template #default="scope">
                    {{ formatAmount(scope.row.basicDeduction) }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 导入对话框 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" :title="obj.title" type="deductionsFromWages"
            @submit="submit" />


        <!-- 查看导入记录 -->
        <el-dialog v-model="obj.recordDialog.visible" :title="obj.recordDialog.title" width="60%" append-to-body>
            <el-form class="formHight" ref="recordFormRef" :model="obj.recordDialog.queryParams" inline
                label-width="auto">
                <el-form-item label="导入编号" prop="importCode">
                    <el-input class="width220" v-model="obj.recordDialog.queryParams.importCode" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="扣缴义务人" prop="withholdingAgent">
                    <el-input class="width220" v-model="obj.recordDialog.queryParams.withholdingAgent"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开始日期" prop="startDate">
                    <el-date-picker class="width220" v-model="obj.recordDialog.queryParams.startDate" type="date"
                        format="YYYY-MM-DD" value-format="YYYY-MM-DD" placeholder="请选择日期" clearable />
                </el-form-item>
                <el-form-item label="结束日期" prop="endDate">
                    <el-date-picker class="width220" v-model="obj.recordDialog.queryParams.endDate" type="date"
                        format="YYYY-MM-DD" value-format="YYYY-MM-DD" placeholder="请选择日期" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
            <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.recordDialog.tableData">
                <el-table-column type="index" align="center" />
                <el-table-column label="导入编号" align="center" prop="importCode" width="150" />
                <el-table-column label="导入人" align="center" prop="importer" width="100" />
                <el-table-column label="导入时间" align="center" prop="importTime" width="150" />
                <el-table-column label="备注" align="center" prop="remark" width="150" />
                <el-table-column label="成功记录数" align="center" prop="successCount" width="100" />
                <el-table-column label="失败记录数" align="center" prop="failCount" width="100" />
                <el-table-column label="导入文件" align="center" width="300">
                    <template #default="scope">
                        <el-button text icon="Download" @click="handleDownload(scope.row)">
                            {{ scope.row.importFile }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column label="处理状态" align="center" width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.status === '1'" type="success">成功</el-tag>
                        <el-tag v-else-if="scope.row.status === '2'" type="danger">失败</el-tag>
                        <el-tag v-else type="info">处理中</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="创建人" align="center" prop="creator" width="100" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
                <el-table-column label="修改人" align="center" prop="modifier" width="100" />
                <el-table-column label="修改时间" align="center" prop="modifyTime" width="150" />
                <el-table-column label="入职提醒" align="center" prop="entryReminder" width="100" />
                <el-table-column label="历史信息查看" align="center" fixed="right" width="120">
                    <template #default="scope">
                        <el-button text icon="Search" @click="handleDetail(scope.row)"></el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination v-show="obj.recordDialog.total > 0" :total="obj.recordDialog.total"
                v-model:page="obj.recordDialog.queryParams.pageNum"
                v-model:limit="obj.recordDialog.queryParams.pageSize" @pagination="getList" />
        </el-dialog>
    </div>
</template>

<script setup>
import UploadFile from '@/views/reonManage/components/dialog/uploadFile.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 证件类型选项
const idTypeOptions = [
    { value: '1', label: '居民身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '港澳居民来往内地通行证' },
    { value: '4', label: '台湾同胞来往内地通行证' },
    { value: '5', label: '外国人永久居留证' },
    { value: '6', label: '其他' }
];

// 处理状态选项
const statusOptions = [
    { value: '0', label: '处理中' },
    { value: '1', label: '成功' },
    { value: '2', label: '失败' }
];

// 工资扣除项数据
const obj = reactive({
    showSearch: true, // 显示搜索
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importCode: '', // 导入编号
        employeeName: '', // 姓名
        idNumber: '', // 证件号
        taxYearMonthFrom: '', // 税款所属年月从
        taxYearMonthTo: '', // 税款所属年月到
        withholdingAgent: '', // 扣缴义务人
        customerName: '', // 客户名称
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            importCode: 'IMP20230901001',
            createTime: '2023-09-01 10:00:00',
            updateTime: '2023-09-01 10:05:00',
            employeeId: 'EMP001',
            employeeName: '张三',
            idType: '1',
            idNumber: '110101199001011234',
            withholdingAgentCode: 'WAC001',
            withholdingAgentName: '北京科技有限公司',
            customerName: '北京科技有限公司',
            incomePeriodStart: '2023-01',
            incomePeriodEnd: '2023-09',
            childEducationDeduction: 1000.00,
            continuingEducationDeduction: 400.00,
            housingRentDeduction: 1500.00,
            housingLoanInterestDeduction: 1000.00,
            elderlySupport: 2000.00,
            childcareDeduction: 1000.00,
            personalPensionDeduction: 1000.00,
            otherDeductions: 0.00,
            basicDeduction: 5000.00
        },
        {
            id: 2,
            importCode: 'IMP20230901002',
            createTime: '2023-09-01 11:00:00',
            updateTime: '2023-09-01 11:05:00',
            employeeId: 'EMP002',
            employeeName: '李四',
            idType: '1',
            idNumber: '310101199002022345',
            withholdingAgentCode: 'WAC002',
            withholdingAgentName: '上海贸易有限公司',
            customerName: '上海贸易有限公司',
            incomePeriodStart: '2023-01',
            incomePeriodEnd: '2023-09',
            childEducationDeduction: 2000.00,
            continuingEducationDeduction: 0.00,
            housingRentDeduction: 1500.00,
            housingLoanInterestDeduction: 0.00,
            elderlySupport: 2000.00,
            childcareDeduction: 0.00,
            personalPensionDeduction: 1000.00,
            otherDeductions: 0.00,
            basicDeduction: 5000.00
        },
        {
            id: 3,
            importCode: 'IMP20230901003',
            createTime: '2023-09-01 14:00:00',
            updateTime: '2023-09-01 14:05:00',
            employeeId: 'EMP003',
            employeeName: '王五',
            idType: '1',
            idNumber: '440101199003033456',
            withholdingAgentCode: 'WAC003',
            withholdingAgentName: '广州电子有限公司',
            customerName: '广州电子有限公司',
            incomePeriodStart: '2023-01',
            incomePeriodEnd: '2023-09',
            childEducationDeduction: 1000.00,
            continuingEducationDeduction: 400.00,
            housingRentDeduction: 0.00,
            housingLoanInterestDeduction: 1000.00,
            elderlySupport: 0.00,
            childcareDeduction: 1000.00,
            personalPensionDeduction: 1000.00,
            otherDeductions: 500.00,
            basicDeduction: 5000.00
        }
    ],

    // 导入对话框相关数据
    importDialog: {
        visible: false,
        title: '',
        form: {
            withholdingAgent: '',
            remark: '',
            fileList: []
        },
        rules: {
            withholdingAgent: [
                { required: true, message: '请输入扣缴义务人', trigger: 'blur' }
            ],
            fileList: [
                { required: true, message: '请上传文件', trigger: 'change' }
            ]
        }
    },

    // 导入记录对话框相关数据
    recordDialog: {
        visible: false,
        title: '',
        queryParams: {
            pageNum: 1,
            pageSize: 10,
            importCode: '',
            withholdingAgent: '',
            startDate: '',
            endDate: ''
        },
        total: 0,
        tableData: [
            {
                id: 1,
                importCode: 'IMP20230901001',
                importer: '张三',
                importTime: '2023-09-01 10:00:00',
                remark: '专项扣除导入',
                successCount: 100,
                failCount: 5,
                importFile: '专项扣除数据20230901.xlsx',
                status: '1',
                creator: '张三',
                createTime: '2023-09-01 09:50:00',
                modifier: '张三',
                modifyTime: '2023-09-01 10:05:00',
                entryReminder: '已提醒'
            },
            {
                id: 2,
                importCode: 'IMP20230902001',
                importer: '李四',
                importTime: '2023-09-02 11:00:00',
                remark: '新入职员工专项扣除',
                successCount: 50,
                failCount: 2,
                importFile: '新入职员工专项扣除数据20230902.xlsx',
                status: '1',
                creator: '李四',
                createTime: '2023-09-02 10:50:00',
                modifier: '李四',
                modifyTime: '2023-09-02 11:05:00',
                entryReminder: '未提醒'
            }
        ]
    }
});

/**
 * 获取证件类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getIdTypeName(typeId) {
    if (!typeId) return '-';

    const type = idTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}



/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.importCode) {
            filteredData = filteredData.filter(item =>
                item.importCode.includes(obj.queryParams.importCode)
            );
        }

        if (obj.queryParams.employeeName) {
            filteredData = filteredData.filter(item =>
                item.employeeName.includes(obj.queryParams.employeeName)
            );
        }

        if (obj.queryParams.idNumber) {
            filteredData = filteredData.filter(item =>
                item.idNumber.includes(obj.queryParams.idNumber)
            );
        }

        if (obj.queryParams.taxYearMonthFrom) {
            filteredData = filteredData.filter(item =>
                item.incomePeriodStart >= obj.queryParams.taxYearMonthFrom
            );
        }

        if (obj.queryParams.taxYearMonthTo) {
            filteredData = filteredData.filter(item =>
                item.incomePeriodEnd <= obj.queryParams.taxYearMonthTo
            );
        }

        if (obj.queryParams.withholdingAgent) {
            filteredData = filteredData.filter(item =>
                item.withholdingAgentName.includes(obj.queryParams.withholdingAgent)
            );
        }

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item =>
                item.customerName.includes(obj.queryParams.customerName)
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 导入专项扣除 */
function importDeduction() {
    obj.dialogShow = true;
    obj.title = '上传导入数据';
    obj.importDialog.form = {
        withholdingAgent: '',
        remark: '',
        fileList: []
    };
}

/** 查看导入记录 */
function viewImportRecord() {
    obj.recordDialog.visible = true;
    obj.recordDialog.title = '查看导入记录';
}


/** 查看详情 */
function handleDetail(row) {
    proxy.$modal.msgInfo(`查看导入编号：${row.importCode} 的详细信息`);
}

/** 下载文件 */
function handleDownload(row) {
    // 在实际应用中，这里应该是一个请求后端接口下载文件的逻辑
    // 这里使用模拟数据演示
    proxy.$modal.msgSuccess(`正在下载文件：${row.importFile}`);

    // 模拟下载过程
    setTimeout(() => {
        proxy.$modal.msgSuccess(`文件 ${row.importFile} 下载成功`);
    }, 1000);
}

// 初始化加载数据
onMounted(() => {
    getList();
});
</script>
<style lang="scss" scoped></style>