<!-- 收入月度报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="报表年月:" prop="month">
                <el-date-picker class="width220" v-model="obj.queryParams.month" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="签约方抬头:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="销售所在公司:" prop="salesCompany">
                <el-select class="width220" v-model="obj.queryParams.salesCompany" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Download" @click="handleExport">账单打印</el-button>
                <el-button icon="Refresh" @click="handleReset">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup name="MonthlyIncomeStatement">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


const obj = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单

})

/** 重置按钮操作 */
function handleReset() {
    obj.queryParams = {};
}

/** 导出明细按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

</script>
<style lang="scss" scoped></style>