<!-- 银企直连-查询-工资支付-->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">

            <el-form-item label="出款公司名称:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="收款方:" prop="payee">
                <el-input class="width220" v-model="obj.queryParams.payee" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="支付类型:" prop="paymentType">
                <el-select class="width220" v-model="obj.queryParams.paymentType" placeholder="请选择" clearable>
                    <el-option v-for="item in paymentTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请人:" prop="applicant">
                <el-input class="width220" v-model="obj.queryParams.applicant" placeholder="请输入" clearable />
            </el-form-item>

            <el-form-item label="支付方式:" prop="paymentMethod">
                <el-input class="width220" v-model="obj.queryParams.paymentMethod" placeholder="请输入" clearable />
            </el-form-item>

            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>

            <el-form-item label="申请支付时间>=:" prop="applyTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeStart" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>

            <el-form-item label="申请支付时间<=:" prop="applyTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeEnd" type="date" placeholder="请选择日期"
                    clearable value-format="YYYY-MM-DD" />
            </el-form-item>

            <el-form-item label="工资支付日期:" prop="salaryPaymentDate">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryPaymentDate" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="制单状态:" prop="orderStatus">
                <el-select class="width220" v-model="obj.queryParams.orderStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in orderStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="审批状态:" prop="approvalStatus">
                <el-select class="width220" v-model="obj.queryParams.approvalStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in approvalStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="是否退票重发:" prop="isRefund">
                <el-select class="width220" v-model="obj.queryParams.isRefund" placeholder="请选择" clearable>
                    <el-option v-for="item in isRefundOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资复核日期:" prop="salaryReviewDate">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryReviewDate" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="自有工资复核日期:" prop="ownSalaryReviewDate">
                <el-date-picker class="width220" v-model="obj.queryParams.ownSalaryReviewDate" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="供应商发薪时间:" prop="supplierPaymentTime">
                <el-date-picker class="width220" v-model="obj.queryParams.supplierPaymentTime" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="支付关联抬头:" prop="paymentHeader">
                <el-input class="width220" v-model="obj.queryParams.paymentHeader" placeholder="请输入" clearable />
            </el-form-item>


            <el-form-item label="工资支付日期起:" prop="salaryPaymentDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryPaymentDateStart" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>


            <el-form-item label="工资支付日期止:" prop="salaryPaymentDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryPaymentDateEnd" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>


            <el-form-item label="派单地打印状态:" prop="dispatchPrintStatus">
                <el-select class="width220" v-model="obj.queryParams.dispatchPrintStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in printStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单地打印状态:" prop="receivePrintStatus">
                <el-select class="width220" v-model="obj.queryParams.receivePrintStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in printStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectService">
                <el-input class="width220" v-model="obj.queryParams.projectService" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="薪资客服:" prop="salaryService">
                <el-input class="width220" v-model="obj.queryParams.salaryService" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="时间审批类型:" prop="timeApprovalType">
                <el-select class="width220" v-model="obj.queryParams.timeApprovalType" placeholder="请选择" clearable>
                    <el-option v-for="item in timeApprovalTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleExport">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Printer" @click="handlePrint">打印银企直练出款汇总文件</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleDownload">下载出款明细文件</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleSetDispatchNonBankFile">设置成派单地非银企</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleSetReceiveNonBankFile">设置成接单地非银企</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleEditSalaryPaymentDate">编辑工资支付日期</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="wagePayment_directConnection" />
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column v-if="obj.selectedColumns.includes('companyName')" label="出款公司名称" align="center" sortable
                prop="companyName" />
            <el-table-column v-if="obj.selectedColumns.includes('customerName')" label="客户名称" align="center"
                prop="customerName" />
            <el-table-column v-if="obj.selectedColumns.includes('payee')" label="收款方" align="center" prop="payee" />
            <el-table-column v-if="obj.selectedColumns.includes('salaryPaymentDate')" label="工资支付日期" align="center"
                prop="salaryPaymentDate" />
            <el-table-column v-if="obj.selectedColumns.includes('totalAmount')" label="总计" align="center"
                prop="totalAmount" />
            <el-table-column v-if="obj.selectedColumns.includes('productPlan')" label="产品方案" align="center"
                prop="productPlan" />
            <el-table-column v-if="obj.selectedColumns.includes('orderStatus')" label="制单状态" align="center"
                prop="orderStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.orderStatus === '1' ? 'success' : scope.row.orderStatus === '2' ? 'warning' : 'info'">
                        {{ getOrderStatusName(scope.row.orderStatus) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('approvalStatus')" label="审核状态" align="center"
                prop="approvalStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.approvalStatus === '2' ? 'success' : scope.row.approvalStatus === '3' ? 'danger' : scope.row.approvalStatus === '1' ? 'warning' : 'info'">
                        {{ getApprovalStatusName(scope.row.approvalStatus) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('paymentMonth')" label="支付所属年月" align="center"
                prop="paymentMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('actualSalary')" label="实付工资款" align="center"
                prop="actualSalary" />
            <el-table-column v-if="obj.selectedColumns.includes('personalTax')" label="个税款" align="center"
                prop="personalTax" />
            <el-table-column v-if="obj.selectedColumns.includes('compensation')" label="补偿金" align="center"
                prop="compensation" />
            <el-table-column v-if="obj.selectedColumns.includes('compensationTax')" label="补偿金个税" align="center"
                prop="compensationTax" />
            <el-table-column v-if="obj.selectedColumns.includes('annualBonus')" label="年终奖" align="center"
                prop="annualBonus" />
            <el-table-column v-if="obj.selectedColumns.includes('annualBonusTax')" label="年终奖个税" align="center"
                prop="annualBonusTax" />
            <el-table-column v-if="obj.selectedColumns.includes('laborSalary')" label="劳务工资" align="center"
                prop="laborSalary" />
            <el-table-column v-if="obj.selectedColumns.includes('laborTax')" label="劳务个税工资" align="center"
                prop="laborTax" />
            <el-table-column v-if="obj.selectedColumns.includes('serviceFee')" label="服务费" align="center"
                prop="serviceFee" />
            <el-table-column v-if="obj.selectedColumns.includes('disabilityFund')" label="残障金" align="center"
                prop="disabilityFund" />
            <el-table-column v-if="obj.selectedColumns.includes('crossBankFee')" label="跨行手续费" align="center"
                prop="crossBankFee" />
            <el-table-column v-if="obj.selectedColumns.includes('unionFee')" label="工会费" align="center"
                prop="unionFee" />
            <el-table-column v-if="obj.selectedColumns.includes('totalTax')" label="税金合计" align="center"
                prop="totalTax" />
            <el-table-column v-if="obj.selectedColumns.includes('reviewTime')" label="复合时间" align="center"
                prop="reviewTime" />
            <el-table-column v-if="obj.selectedColumns.includes('ownSalaryReviewTime')" label="自有工资复合时间" align="center"
                prop="ownSalaryReviewTime" />
            <el-table-column v-if="obj.selectedColumns.includes('applicant')" label="申请人" align="center"
                prop="applicant" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentType')" label="支付类型" align="center"
                prop="paymentType">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.paymentType === '1' ? 'primary' : scope.row.paymentType === '2' ? 'success' : 'warning'">
                        {{ getPaymentTypeName(scope.row.paymentType) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('isRefund')" label="是否退票重发" align="center"
                prop="isRefund">
                <template #default="scope">
                    <el-tag :type="scope.row.isRefund === '1' ? 'warning' : 'info'">
                        {{ scope.row.isRefund === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('supplierPaymentTime')" label="供应商发薪时间" align="center"
                prop="supplierPaymentTime" />
            <el-table-column v-if="obj.selectedColumns.includes('expectedPaymentData')" label="预计到款数据" align="center"
                prop="expectedPaymentData" />
            <el-table-column v-if="obj.selectedColumns.includes('verifiedPaymentData')" label="核销的到款数据" align="center"
                prop="verifiedPaymentData" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentId')" label="支付ID" align="center"
                prop="paymentId" />
            <el-table-column v-if="obj.selectedColumns.includes('dispatchPrintStatus')" label="派单地打印状态" align="center"
                prop="dispatchPrintStatus">
                <template #default="scope">
                    <el-tag :type="scope.row.dispatchPrintStatus === '1' ? 'success' : 'info'">
                        {{ scope.row.dispatchPrintStatus === '1' ? '已打印' : '未打印' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('receivePrintStatus')" label="接单地打印状态" align="center"
                prop="receivePrintStatus">
                <template #default="scope">
                    <el-tag :type="scope.row.receivePrintStatus === '1' ? 'success' : 'info'">
                        {{ scope.row.receivePrintStatus === '1' ? '已打印' : '未打印' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('approvalInfo')" label="审批信息" align="center"
                width="120">
                <template #default="scope">
                    <el-button text size="small" @click="handleApprovalInfo(scope.row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column label="查看人员明细" align="center" width="120">
                <template #default="scope">
                    <el-button text size="small" @click="handlePersonnelDetail(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" title="上传导入数据" type="wagePayment_directConnection"
            :dialogForm="obj.dialogForm" :rules="rules" />
    </div>
</template>

<script setup name="WagePayment_directConnection">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



// 制单状态选项
const orderStatusOptions = [
    { value: '0', label: '未制单' },
    { value: '1', label: '已制单' },
    { value: '2', label: '制单中' }
];

// 审批状态选项
const approvalStatusOptions = [
    { value: '0', label: '未审批' },
    { value: '1', label: '审批中' },
    { value: '2', label: '已审批' },
    { value: '3', label: '已驳回' }
];

// 支付类型选项
const paymentTypeOptions = [
    { value: '1', label: '工资' },
    { value: '2', label: '年终奖' },
    { value: '3', label: '补偿金' }
];

// 是否退票重发选项
const isRefundOptions = [
    { value: '0', label: '否' },
    { value: '1', label: '是' }
];

// 打印状态选项
const printStatusOptions = [
    { value: '0', label: '未打印' },
    { value: '1', label: '已打印' }
];

// 时间审批类型选项
const timeApprovalTypeOptions = [
    { value: '1', label: '正常审批' },
    { value: '2', label: '加急审批' },
    { value: '3', label: '特急审批' }
];

// 文件列表
const fileList = ref([]);

// 表单验证规则
const rules = {
    remark: [
        { required: false, message: '请输入备注', trigger: 'blur' }
    ],
    fileList: [
        { required: true, message: '请上传文件', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null, // 出款公司名称
        payee: null, // 收款方
        paymentType: null, // 支付类型
        applicant: null, // 申请人
        paymentMethod: null, // 支付方式
        customerName: null, // 客户名称
        applyTimeStart: null, // 申请支付时间>=
        applyTimeEnd: null, // 申请支付时间<=
        salaryPaymentDate: null, // 工资支付日期
        orderStatus: null, // 制单状态
        approvalStatus: null, // 审批状态
        isRefund: null, // 是否退票重发
        salaryReviewDate: null, // 工资复核日期
        ownSalaryReviewDate: null, // 自有工资复核日期
        supplierPaymentTime: null, // 供应商发薪时间
        paymentHeader: null, // 支付关联抬头
        salaryPaymentDateStart: null, // 工资支付日期起
        salaryPaymentDateEnd: null, // 工资支付日期止
        dispatchPrintStatus: null, // 派单地打印状态
        receivePrintStatus: null, // 接单地打印状态
        projectService: null, // 项目客服
        salaryService: null, // 薪资客服
        timeApprovalType: null, // 时间审批类型
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {
        remark: '',
        fileList: []
    }, // 导入表单
    dialogShow: false, // 导入弹窗
    ids: [], // 选中的id
    title: "", // 标题

    columnOptions: [
        { label: '出款公司名称', prop: 'companyName' },
        { label: '客户名称', prop: 'customerName' },
        { label: '收款方', prop: 'payee' },
        { label: '工资支付日期', prop: 'salaryPaymentDate' },
        { label: '总计', prop: 'totalAmount' },
        { label: '产品方案', prop: 'productPlan' },
        { label: '制单状态', prop: 'orderStatus' },
        { label: '审核状态', prop: 'approvalStatus' },
        { label: '支付所属年月', prop: 'paymentMonth' },
        { label: '实付工资款', prop: 'actualSalary' },
        { label: '个税款', prop: 'personalTax' },
        { label: '补偿金', prop: 'compensation' },
        { label: '补偿金个税', prop: 'compensationTax' },
        { label: '年终奖', prop: 'annualBonus' },
        { label: '年终奖个税', prop: 'annualBonusTax' },
        { label: '劳务工资', prop: 'laborSalary' },
        { label: '劳务个税工资', prop: 'laborTax' },
        { label: '服务费', prop: 'serviceFee' },
        { label: '残障金', prop: 'disabilityFund' },
        { label: '跨行手续费', prop: 'crossBankFee' },
        { label: '工会费', prop: 'unionFee' },
        { label: '税金合计', prop: 'totalTax' },
        { label: '复合时间', prop: 'reviewTime' },
        { label: '自有工资复合时间', prop: 'ownSalaryReviewTime' },
        { label: '申请人', prop: 'applicant' },
        { label: '支付类型', prop: 'paymentType' },
        { label: '是否退票重发', prop: 'isRefund' },
        { label: '供应商发薪时间', prop: 'supplierPaymentTime' },
        { label: '预计到款数据', prop: 'expectedPaymentData' },
        { label: '核销的到款数据', prop: 'verifiedPaymentData' },
        { label: '支付ID', prop: 'paymentId' },
        { label: '派单地打印状态', prop: 'dispatchPrintStatus' },
        { label: '接单地打印状态', prop: 'receivePrintStatus' },
        { label: '审批信息', prop: 'approvalInfo' },
    ],
    selectedColumns: [
        'companyName', 'customerName', 'payee', 'salaryPaymentDate', 'totalAmount',
        'productPlan', 'orderStatus', 'approvalStatus', 'paymentMonth', 'actualSalary',
        'personalTax', 'compensation', 'compensationTax', 'annualBonus', 'annualBonusTax',
        'laborSalary', 'laborTax', 'serviceFee', 'disabilityFund', 'crossBankFee',
        'unionFee', 'totalTax', 'reviewTime', 'ownSalaryReviewTime', 'applicant',
        'paymentType', 'isRefund', 'supplierPaymentTime', 'expectedPaymentData',
        'verifiedPaymentData', 'paymentId', 'dispatchPrintStatus', 'receivePrintStatus',
        'approvalInfo'
    ]
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                companyName: '某某科技有限公司',
                customerName: '客户A',
                payee: '张三',
                salaryPaymentDate: '2023-05-15',
                totalAmount: 100000.00,
                productPlan: '标准方案',
                orderStatus: '1',
                approvalStatus: '2',
                paymentMonth: '2023-05',
                actualSalary: 85000.00,
                personalTax: 5000.00,
                compensation: 0.00,
                compensationTax: 0.00,
                annualBonus: 0.00,
                annualBonusTax: 0.00,
                laborSalary: 0.00,
                laborTax: 0.00,
                serviceFee: 8000.00,
                disabilityFund: 1000.00,
                crossBankFee: 500.00,
                unionFee: 500.00,
                totalTax: 15000.00,
                reviewTime: '2023-05-10 10:30:00',
                ownSalaryReviewTime: '2023-05-10 11:00:00',
                applicant: '李四',
                paymentType: '1',
                isRefund: '0',
                supplierPaymentTime: '2023-05-12',
                expectedPaymentData: '2023-05-16',
                verifiedPaymentData: '2023-05-16',
                paymentId: 'PAY20230515001',
                dispatchPrintStatus: '1',
                receivePrintStatus: '0',
                timeApprovalType: '1'
            },
            {
                id: 2,
                companyName: '某某信息技术有限公司',
                customerName: '客户B',
                payee: '李四',
                salaryPaymentDate: '2023-06-15',
                totalAmount: 120000.00,
                productPlan: '高级方案',
                orderStatus: '1',
                approvalStatus: '2',
                paymentMonth: '2023-06',
                actualSalary: 100000.00,
                personalTax: 6000.00,
                compensation: 0.00,
                compensationTax: 0.00,
                annualBonus: 0.00,
                annualBonusTax: 0.00,
                laborSalary: 0.00,
                laborTax: 0.00,
                serviceFee: 10000.00,
                disabilityFund: 2000.00,
                crossBankFee: 1000.00,
                unionFee: 1000.00,
                totalTax: 20000.00,
                reviewTime: '2023-06-10 10:30:00',
                ownSalaryReviewTime: '2023-06-10 11:00:00',
                applicant: '王五',
                paymentType: '1',
                isRefund: '0',
                supplierPaymentTime: '2023-06-12',
                expectedPaymentData: '2023-06-16',
                verifiedPaymentData: '2023-06-16',
                paymentId: 'PAY20230615001',
                dispatchPrintStatus: '1',
                receivePrintStatus: '1',
                timeApprovalType: '2'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 表单重置 */
function resetForm() {
    obj.dialogForm = {
        remark: ''
    };
    fileList.value = [];
    proxy.resetForm("formRef");
}

/** 查看审批信息 */
function handleApprovalInfo(row) {
    proxy.$modal.msgSuccess('查看审批信息：' + row.paymentId);
    // 实际项目中应该跳转到审批信息页面
}

/** 查看人员明细 */
function handlePersonnelDetail(row) {
    proxy.$modal.msgSuccess('查看人员明细：' + row.paymentId);
    // 实际项目中应该跳转到人员明细页面
}

/** 导出 */
function handleExport() {
    obj.dialogShow = true;
}

// 关闭导入
function handleClose() {
    obj.dialogShow = false;
}

/** 打印 */
function handlePrint() {
}

// 获取制单状态名称
function getOrderStatusName(status) {
    const statusMap = {
        '0': '未制单',
        '1': '已制单',
        '2': '制单中'
    };
    return statusMap[status] || '未知状态';
}

// 获取审批状态名称
function getApprovalStatusName(status) {
    const statusMap = {
        '0': '未审批',
        '1': '审批中',
        '2': '已审批',
        '3': '已驳回'
    };
    return statusMap[status] || '未知状态';
}

// 获取支付类型名称
function getPaymentTypeName(type) {
    const typeMap = {
        '1': '工资',
        '2': '年终奖',
        '3': '补偿金'
    };
    return typeMap[type] || '未知类型';
}

getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>