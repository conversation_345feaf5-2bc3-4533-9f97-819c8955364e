<!-- 客户拜访 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-row class="mb8">
                <el-form-item label="拜访日期:" prop="visitDateFrom">
                    <el-date-picker class="width220" v-model="obj.queryParams.visitDateFrom" type="date"
                        placeholder="请选择日期" value-format="YYYY-MM-DD" clearable />
                </el-form-item>
                <el-form-item label="客户姓名:" prop="customerName">
                    <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户姓名" clearable />
                </el-form-item>
                <el-form-item label="拜访结果:" prop="visitResult">
                    <el-select class="width220" v-model="obj.queryParams.visitResult" placeholder="请选择" clearable>
                        <el-option v-for="item in visitResultOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否意向客户:" prop="isIntention">
                    <el-select class="width220" v-model="obj.queryParams.isIntention" placeholder="请选择" clearable>
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">编辑</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" prop="id" width="60" />
            <el-table-column label="拜访日期" align="center" prop="visitDate" width="100" />
            <el-table-column label="拜访时间段" align="center" width="100">
                <template #default="scope">
                    {{ getVisitTimeLabel(scope.row.visitTime) }}
                </template>
            </el-table-column>
            <el-table-column label="是否返回公司" align="center" width="120">
                <template #default="scope">
                    <el-tag :type="scope.row.returnCompany === '1' ? 'success' : 'info'">
                        {{ scope.row.returnCompany === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="外出人" align="center" prop="visitor" width="80" />
            <el-table-column label="小组" align="center" prop="team" width="80" />
            <el-table-column label="陪同人" align="center" prop="companion" width="80" />
            <el-table-column label="是否意向客户" align="center" width="120">
                <template #default="scope">
                    <el-tag :type="scope.row.isIntention === '1' ? 'success' : 'info'">
                        {{ scope.row.isIntention === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="意向产品线" align="center" width="120">
                <template #default="scope">
                    {{ getProductLineLabel(scope.row.productLine) }}
                </template>
            </el-table-column>
            <el-table-column label="本年度累计拜访次数" align="center" prop="visitCount" width="150" />
            <el-table-column label="拜访目的" align="center" width="120">
                <template #default="scope">
                    {{ getVisitPurposeLabel(scope.row.visitPurpose) }}
                </template>
            </el-table-column>
            <el-table-column label="客户类型" align="center" width="100">
                <template #default="scope">
                    {{ getCustomerTypeLabel(scope.row.customerType) }}
                </template>
            </el-table-column>
            <el-table-column label="客户公司名称" align="center" prop="companyName" min-width="150" />
            <el-table-column label="客户姓名" align="center" prop="customerName" width="100" />
            <el-table-column label="客户职位" align="center" prop="position" width="120" />
            <el-table-column label="客户电话" align="center" prop="phone" width="120" />
            <el-table-column label="拜访结果" align="center" width="120">
                <template #default="scope">
                    <el-tag :type="getVisitResultType(scope.row.visitResult)">
                        {{ getVisitResultLabel(scope.row.visitResult) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120" fixed="right">
                <template #default="scope">
                    <el-button text type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 新增/编辑 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="65%" append-to-body draggable>
            <el-form :model="obj.dialogForm" ref="dialogRef" inline label-width="auto">
                <el-form-item label="拜访日期:" prop="visitDate">
                    <el-date-picker v-model="obj.dialogForm.visitDate" type="date" placeholder="选择日期" />
                </el-form-item>
                <el-form-item label="拜访时间段:" prop="visitTime">
                    <el-select class="width220" v-model="obj.dialogForm.visitTime" placeholder="请选择" clearable>
                        <el-option v-for="item in visitTimeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否返回公司:" prop="returnCompany">
                    <el-select class="width220" v-model="obj.dialogForm.returnCompany" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="外出人:" prop="visitor">
                    <el-input class="width220" v-model="obj.dialogForm.visitor" placeholder="请输入外出人" />
                </el-form-item>

                <el-form-item label="小组:" prop="team">
                    <el-input class="width220" v-model="obj.dialogForm.team" placeholder="请输入小组" />
                </el-form-item>

                <el-form-item label="陪同人:" prop="companion">
                    <el-input class="width220" v-model="obj.dialogForm.companion" placeholder="请输入陪同人" />
                </el-form-item>

                <el-form-item label="是否意向客户:" prop="isIntention">
                    <el-select class="width220" v-model="obj.dialogForm.isIntention" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="意向产品线:" prop="productLine">
                    <el-select class="width220" v-model="obj.dialogForm.productLine" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="本年度累计拜访次数:" prop="visitCount">
                    <el-input class="width220" v-model="obj.dialogForm.visitCount" placeholder="请输入本年度累计拜访次数" />
                </el-form-item>

                <el-form-item label="拜访目的:" prop="visitPurpose">
                    <el-select class="width220" v-model="obj.dialogForm.visitPurpose" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="客户类型:" prop="customerType">
                    <el-select class="width220" v-model="obj.dialogForm.customerType" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="客户公司名称:" prop="companyName">
                    <el-input class="width220" v-model="obj.dialogForm.companyName" placeholder="请输入客户公司名称" />
                </el-form-item>

                <el-form-item label="客户姓名:" prop="customerName">
                    <el-input class="width220" v-model="obj.dialogForm.customerName" placeholder="请输入客户姓名" />
                </el-form-item>

                <el-form-item label="客户职位:" prop="position">
                    <el-input class="width220" v-model="obj.dialogForm.position" placeholder="请输入客户职位" />
                </el-form-item>

                <el-form-item label="客户电话:" prop="phone">
                    <el-input class="width220" v-model="obj.dialogForm.phone" placeholder="请输入客户电话" />
                </el-form-item>

                <el-form-item label="客户性别:" prop="gender">
                    <el-select class="width220" v-model="obj.dialogForm.gender" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="拜访结果:" prop="visitResult">
                    <el-select class="width220" v-model="obj.dialogForm.visitResult" placeholder="请选择" clearable>
                        <el-option v-for="item in visitResultOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-col :span="24">
                    <el-form-item label="备注:" prop="remark">
                        <el-input class="width420" type="textarea" :rows="3" v-model="obj.dialogForm.remark"
                            placeholder="请输入备注" />
                    </el-form-item>
                </el-col>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button @click="handleCancel">取消</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup name="CustomerVisit">



const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



// 拜访时间段选项
const visitTimeOptions = [
    { value: '1', label: '上午' },
    { value: '2', label: '下午' },
    { value: '3', label: '全天' }
];

// 意向产品线选项
const productLineOptions = [
    { value: '1', label: '商业医疗险' },
    { value: '2', label: '团体寿险' },
    { value: '3', label: '团体重疾险' },
    { value: '4', label: '团体意外险' },
    { value: '5', label: '其他保险' }
];

// 拜访目的选项
const visitPurposeOptions = [
    { value: '1', label: '产品推广' },
    { value: '2', label: '客户维护' },
    { value: '3', label: '合同签署' },
    { value: '4', label: '客户投诉处理' },
    { value: '5', label: '其他' }
];

// 客户类型选项
const customerTypeOptions = [
    { value: '1', label: '新客户' },
    { value: '2', label: '老客户' },
    { value: '3', label: '潜在客户' },
    { value: '4', label: 'VIP客户' }
];

// 拜访结果选项
const visitResultOptions = [
    { value: '1', label: '非常成功' },
    { value: '2', label: '比较成功' },
    { value: '3', label: '一般' },
    { value: '4', label: '不成功' },
    { value: '5', label: '需要再次跟进' }
];

// 性别选项
const genderOptions = [
    { value: '1', label: '男' },
    { value: '2', label: '女' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        visitDateFrom: '', // 拜访日期开始
        visitDateTo: '', // 拜访日期结束
        customerName: '', // 客户姓名
        visitResult: '', // 拜访结果
        isIntention: '' // 是否意向客户
    }, // 查询表单
    total: 0, // 总条数

    tableData: [
        {
            id: 1,
            visitDate: '2023-05-01',
            visitTime: '1',
            returnCompany: '1',
            visitor: '张三',
            team: '销售A组',
            companion: '李四',
            isIntention: '1',
            productLine: '1',
            visitCount: 5,
            visitPurpose: '1',
            customerType: '2',
            companyName: '北京科技有限公司',
            customerName: '王经理',
            position: '人力资源总监',
            phone: '13800138000',
            gender: '1',
            visitResult: '1',
            remark: '客户对我们的产品非常满意，计划下周签署合同',
            createTime: '2023-05-01 10:00:00',
            creator: '张三'
        },
        {
            id: 2,
            visitDate: '2023-05-02',
            visitTime: '2',
            returnCompany: '0',
            visitor: '李四',
            team: '销售B组',
            companion: '王五',
            isIntention: '1',
            productLine: '2',
            visitCount: 3,
            visitPurpose: '2',
            customerType: '4',
            companyName: '上海贸易有限公司',
            customerName: '刘总',
            position: '财务总监',
            phone: '13900139000',
            gender: '1',
            visitResult: '2',
            remark: '客户对产品有一定兴趣，需要进一步了解详情',
            createTime: '2023-05-02 14:30:00',
            creator: '李四'
        }
    ], // 列表

    dialogForm: {
        id: '',
        visitDate: '',
        visitTime: '',
        returnCompany: '',
        visitor: '',
        team: '',
        companion: '',
        isIntention: '',
        productLine: '',
        visitCount: 0,
        visitPurpose: '',
        customerType: '',
        companyName: '',
        customerName: '',
        position: '',
        phone: '',
        gender: '',
        visitResult: '',
        remark: ''
    }, // 表单

    dialogShow: false, // 弹窗
    ids: [], // 选中的id
    title: "", // 标题
})


/**
 * 获取拜访时间段标签
 * @param {string} time 时间段值
 * @returns {string} 标签文本
 */
function getVisitTimeLabel(time) {
    const option = visitTimeOptions.find(item => item.value === time);
    return option ? option.label : '';
}

/**
 * 获取产品线标签
 * @param {string} line 产品线值
 * @returns {string} 标签文本
 */
function getProductLineLabel(line) {
    if (!line) return '';
    const option = productLineOptions.find(item => item.value === line);
    return option ? option.label : '';
}

/**
 * 获取拜访目的标签
 * @param {string} purpose 目的值
 * @returns {string} 标签文本
 */
function getVisitPurposeLabel(purpose) {
    const option = visitPurposeOptions.find(item => item.value === purpose);
    return option ? option.label : '';
}

/**
 * 获取客户类型标签
 * @param {string} type 类型值
 * @returns {string} 标签文本
 */
function getCustomerTypeLabel(type) {
    const option = customerTypeOptions.find(item => item.value === type);
    return option ? option.label : '';
}

/**
 * 获取拜访结果标签类型
 * @param {string} result 结果值
 * @returns {string} 标签类型
 */
function getVisitResultType(result) {
    switch (result) {
        case '1': return 'success';
        case '2': return 'success';
        case '3': return 'info';
        case '4': return 'danger';
        case '5': return 'warning';
        default: return '';
    }
}

/**
 * 获取拜访结果标签
 * @param {string} result 结果值
 * @returns {string} 标签文本
 */
function getVisitResultLabel(result) {
    const option = visitResultOptions.find(item => item.value === result);
    return option ? option.label : '';
}

/** 列表 */
function getList() {
    obj.loading = true;

    // 模拟数据已经定义在 obj.tableData 中
    setTimeout(() => {
        obj.loading = false;
        obj.total = obj.tableData.length;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 新增
function handleAdd() {
    // 重置表单
    obj.dialogForm = {
        id: '',
        visitDate: '',
        visitTime: '',
        returnCompany: '',
        visitor: '',
        team: '',
        companion: '',
        isIntention: '',
        productLine: '',
        visitCount: 0,
        visitPurpose: '',
        customerType: '',
        companyName: '',
        customerName: '',
        position: '',
        phone: '',
        gender: '',
        visitResult: '',
        remark: ''
    };

    obj.dialogShow = true;
    obj.title = "新增拜访客户信息";
}

// 编辑
function handleEdit(row) {
    const visitInfo = row || (obj.single ? null : obj.tableData.find(item => item.id === obj.ids[0]));
    if (!visitInfo) {
        proxy.$modal.msgInfo('请选择要编辑的记录');
        return;
    }

    // 复制数据到表单
    obj.dialogForm = JSON.parse(JSON.stringify(visitInfo));
    obj.dialogShow = true;
    obj.title = "编辑拜访客户信息";
}

// 详情
function handleDetail(row) {
    if (!row) {
        proxy.$modal.msgInfo('请选择要查看的记录');
        return;
    }
    obj.dialogShow = true
    obj.dialogForm = JSON.parse(JSON.stringify(row));
    obj.title = '查看'
}

// 保存表单
function handleSave() {
    if (!proxy.$refs["dialogRef"]) return;

    proxy.$refs["dialogRef"].validate(valid => {
        if (valid) {
            // 模拟保存操作
            const isNew = !obj.dialogForm.id;

            if (isNew) {
                // 新增记录
                const newVisit = {
                    ...obj.dialogForm,
                    id: obj.tableData.length + 1,
                    createTime: new Date().toLocaleString(),
                    creator: '当前用户'
                };

                obj.tableData.unshift(newVisit);
                proxy.$modal.msgSuccess('新增成功');
            } else {
                // 编辑记录
                const index = obj.tableData.findIndex(item => item.id === obj.dialogForm.id);
                if (index !== -1) {
                    obj.tableData[index] = { ...obj.tableData[index], ...obj.dialogForm };
                }
                proxy.$modal.msgSuccess('编辑成功');
            }

            obj.dialogShow = false;
            obj.total = obj.tableData.length;
        }
    });
}

// 取消操作
function handleCancel() {
    obj.dialogShow = false;
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 初始化
getList();
</script>
<style lang="scss" scoped></style>