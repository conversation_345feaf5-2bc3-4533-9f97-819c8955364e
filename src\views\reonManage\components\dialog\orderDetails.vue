<template>
    <div>
        <el-dialog v-model="dialogShow" :title="props.title" width="65%" append-to-body draggable @close="handleClose">
            <el-form class="formHight" ref="formRef" :model="formData" :rules="rules" inline label-width="auto">
                <el-tabs type="border-card">
                    <el-tab-pane label="订单基本信息">
                        <border-box title="订单信息">
                            <div class="fixed" v-if="props.isShow">
                                <el-row>
                                    <el-col :span="6">
                                        <el-form-item label="雇员编号" prop="employeeNo">
                                            <el-input :disabled="props.isDetail" class="width180"
                                                v-model="formData.employeeNo" placeholder="请输入" />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="雇员姓名" prop="employeeName">
                                            <el-input :disabled="props.isDetail" class="width180"
                                                v-model="formData.employeeName" placeholder="请输入" />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="客户编号" prop="customerNo">
                                            <el-input :disabled="props.isDetail" class="width180"
                                                v-model="formData.customerNo" placeholder="请输入" />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="客户名称" prop="customerName">
                                            <el-input :disabled="props.isDetail" class="width180"
                                                v-model="formData.customerName" placeholder="请输入" />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="6">
                                        <el-form-item label="证件类型" prop="idType">
                                            <el-select :disabled="props.isDetail" class="width180"
                                                v-model="formData.idType" placeholder="请选择">
                                                <el-option v-for="item in idTypeOptions" :key="item.value"
                                                    :label="item.label" :value="item.value" />
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="证件编号" prop="idNo">
                                            <el-input :disabled="props.isDetail" class="width180"
                                                v-model="formData.idNo" placeholder="请输入" />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="手机" prop="mobile">
                                            <el-input :disabled="props.isDetail" class="width180"
                                                v-model="formData.mobile" placeholder="请输入" />
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="6">
                                        <el-form-item label="联系电话" prop="phone">
                                            <el-input :disabled="props.isDetail" class="width180"
                                                v-model="formData.phone" placeholder="请输入" />
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </div>

                            <div :style="props.isShow ? { marginTop: '150px' } : {}">
                                <el-form-item label="入职状态" prop="employmentStatus">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.employmentStatus" placeholder="请选择">
                                        <el-option v-for="item in employmentStatusOptions" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="订单状态" prop="orderStatus">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.orderStatus" placeholder="请选择">
                                        <el-option v-for="item in orderStatusOptions" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="挂起原因" prop="suspendReason">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.suspendReason" placeholder="请选择">
                                        <el-option v-for="item in suspendReasonOptions" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="变更状态" prop="changeStatus">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.changeStatus" placeholder="请选择">
                                        <el-option v-for="item in changeStatusOptions" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="小合同" prop="subContract">
                                    <el-input :disabled="props.isDetail" class="width220" v-model="formData.subContract"
                                        placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="小合同编号" prop="subContractNo">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.subContractNo" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="派单方" prop="dispatchParty">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.dispatchParty" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="接单方" prop="receivingParty">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.receivingParty" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="派单方客服" prop="dispatchService">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.dispatchService" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="接单方客服" prop="receivingService">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.receivingService" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="是否有社保卡" prop="hasSocialCard">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.hasSocialCard" placeholder="请选择">
                                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="是否有统筹医疗" prop="hasUnifiedMedical">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.hasUnifiedMedical" placeholder="请选择">
                                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="申报入职时间" prop="declareEntryDate">
                                    <el-date-picker :disabled="props.isDetail" class="width220"
                                        v-model="formData.declareEntryDate" type="date" placeholder="请选择" />
                                </el-form-item>
                                <el-form-item label="申报入职人" prop="declareEntryPerson">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.declareEntryPerson" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="入职日期" prop="entryDate">
                                    <el-date-picker :disabled="props.isDetail" class="width220"
                                        v-model="formData.entryDate" type="date" placeholder="请选择" />
                                </el-form-item>
                                <el-form-item label="签约方抬头" prop="signingPartyHeader">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.signingPartyHeader" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="入职确认时间" prop="entryConfirmDate">
                                    <el-date-picker :disabled="props.isDetail" class="width220"
                                        v-model="formData.entryConfirmDate" type="date" placeholder="请选择" />
                                </el-form-item>
                                <el-form-item label="增员原因" prop="increaseReason">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.increaseReason" placeholder="请选择">
                                        <el-option v-for="item in increaseReasonOptions" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="是否外呼" prop="isOutboundCall">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.isOutboundCall" placeholder="请选择">
                                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="是否集中地投保" prop="isCentralizedInsurance">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.isCentralizedInsurance" placeholder="请选择">
                                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="是否存档" prop="isArchived">
                                    <el-select :disabled="props.isDetail" class="width220" v-model="formData.isArchived"
                                        placeholder="请选择">
                                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="实际工作地" prop="actualWorkplace">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.actualWorkplace" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="申报离职日期" prop="declareResignDate">
                                    <el-date-picker :disabled="props.isDetail" class="width220"
                                        v-model="formData.declareResignDate" type="date" placeholder="请选择" />
                                </el-form-item>
                                <el-form-item label="离职日期" prop="resignDate">
                                    <el-date-picker :disabled="props.isDetail" class="width220"
                                        v-model="formData.resignDate" type="date" placeholder="请选择" />
                                </el-form-item>
                                <el-form-item label="离职原因" prop="resignReason">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.resignReason" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="减员原因" prop="decreaseReason">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.decreaseReason" placeholder="请选择">
                                        <el-option v-for="item in decreaseReasonOptions" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="减员详细原因" prop="decreaseDetailReason">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.decreaseDetailReason" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="是否离职呼叫" prop="isResignCall">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.isResignCall" placeholder="请选择">
                                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>

                                <el-form-item label="未增反馈" prop="notIncreaseFeedback">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.notIncreaseFeedback" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="机动分类2" prop="flexibleCategory2">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.flexibleCategory2" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="机动分类3" prop="flexibleCategory3">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.flexibleCategory3" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="机动分类4" prop="flexibleCategory4">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.flexibleCategory4" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="机动分类5" prop="flexibleCategory5">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.flexibleCategory5" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="公积金账号" prop="providentFundAccount">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.providentFundAccount" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="社保工资" prop="socialInsuranceSalary">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.socialInsuranceSalary" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="公积金工资" prop="providentFundSalary">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.providentFundSalary" placeholder="请输入" />
                                </el-form-item>

                                <el-form-item label="人员分类" prop="personnelCategory">
                                    <el-input :disabled="props.isDetail" class="width220"
                                        v-model="formData.personnelCategory" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="是否连续转移标识" prop="continuousTransferFlag">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.continuousTransferFlag" placeholder="请选择">
                                        <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                                            :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="民族信息" prop="ethnicInfo">
                                    <el-input :disabled="props.isDetail" class="width220" v-model="formData.ethnicInfo"
                                        placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="合同类型" prop="contractType">
                                    <el-select :disabled="props.isDetail" class="width220"
                                        v-model="formData.contractType" placeholder="请选择">
                                        <el-option v-for="item in contractTypeOptions" :key="item.value"
                                            :label="item.label" :value="item.value" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="合同编号" prop="contractNo">
                                    <el-input :disabled="props.isDetail" class="width220" v-model="formData.contractNo"
                                        placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="入职备注" prop="entryRemark">
                                    <el-input :disabled="props.isDetail" class="width420" rows="3" :rows="3"
                                        type="textarea" v-model="formData.entryRemark" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="离职备注" prop="resignRemark">
                                    <el-input :disabled="props.isDetail" class="width420" rows="3" type="textarea"
                                        v-model="formData.resignRemark" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="入职过程" prop="entryProcess">
                                    <el-input :disabled="props.isDetail" class="width420" rows="3" type="textarea"
                                        v-model="formData.entryProcess" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="离职确认过程" prop="resignConfirmProcess">
                                    <el-input :disabled="props.isDetail" class="width420" rows="3" type="textarea"
                                        v-model="formData.resignConfirmProcess" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="变更确认过程" prop="changeConfirmProcess">
                                    <el-input :disabled="props.isDetail" class="width420" rows="3" type="textarea"
                                        v-model="formData.changeConfirmProcess" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="接单备注" prop="orderReceiveRemark">
                                    <el-input :disabled="props.isDetail" class="width420" rows="3" type="textarea"
                                        v-model="formData.orderReceiveRemark" placeholder="请输入" />
                                </el-form-item>
                                <el-form-item label="变更备注" prop="changeRemark">
                                    <el-input :disabled="props.isDetail" class="width420" rows="3" type="textarea"
                                        v-model="formData.changeRemark" placeholder="请输入" />
                                </el-form-item>
                            </div>
                        </border-box>
                        <!-- 劳动合同 -->
                        <border-box title="劳动合同">
                            <el-form-item label="是否需要签订" prop="needSign">
                                <el-select :disabled="props.isDetail" class="width220" v-model="formData.needSign"
                                    placeholder="请选择">
                                    <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="合同签订地" prop="contractSigningPlace">
                                <el-input :disabled="props.isDetail" class="width220"
                                    v-model="formData.contractSigningPlace" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="劳动合同类型" prop="laborContractType">
                                <el-select :disabled="props.isDetail" class="width220"
                                    v-model="formData.laborContractType" placeholder="请选择">
                                    <el-option v-for="item in laborContractTypeOptions" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="合同版本" prop="contractVersion">
                                <el-select :disabled="props.isDetail" class="width220"
                                    v-model="formData.contractVersion" placeholder="请选择">
                                    <el-option v-for="item in contractVersionOptions" :key="item.value"
                                        :label="item.label" :value="item.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="劳动合同起始时间" prop="laborContractStartDate">
                                <el-input :disabled="props.isDetail" class="width220"
                                    v-model="formData.laborContractStartDate" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="劳动合同结束时间" prop="laborContractEndDate">
                                <el-input :disabled="props.isDetail" class="width220"
                                    v-model="formData.laborContractEndDate" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="工作制" prop="workSystem">
                                <el-select :disabled="props.isDetail" class="width220" v-model="formData.workSystem"
                                    placeholder="请选择">
                                    <el-option label="入职" value="1" />
                                    <el-option label="离职" value="2" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="是否有试用期" prop="hasTrialPeriod">
                                <el-select :disabled="props.isDetail" class="width220" v-model="formData.hasTrialPeriod"
                                    placeholder="请选择">
                                    <el-option label="入职" value="1" />
                                    <el-option label="离职" value="2" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="试用期起始时间" prop="trialPeriodStartDate">
                                <el-input :disabled="props.isDetail" class="width220"
                                    v-model="formData.trialPeriodStartDate" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="试用期月数" prop="trialPeriodMonths">
                                <el-input :disabled="props.isDetail" class="width220"
                                    v-model="formData.trialPeriodMonths" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="试用工资" prop="trialSalary">
                                <el-input :disabled="props.isDetail" class="width220" v-model="formData.trialSalary"
                                    placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="派遣期限起" prop="dispatchPeriodStartDate">
                                <el-input :disabled="props.isDetail" class="width220"
                                    v-model="formData.dispatchPeriodStartDate" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="派遣期限止" prop="dispatchPeriodEndDate">
                                <el-input :disabled="props.isDetail" class="width220"
                                    v-model="formData.dispatchPeriodEndDate" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="正式工资" prop="regularSalary">
                                <el-input :disabled="props.isDetail" class="width220" v-model="formData.regularSalary"
                                    placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="合同原则" prop="contractPrinciple">
                                <el-input :disabled="props.isDetail" class="width220"
                                    v-model="formData.contractPrinciple" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="签署日期" prop="signingDate">
                                <el-input :disabled="props.isDetail" class="width220" v-model="formData.signingDate"
                                    placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="试用期结束时间" prop="trialPeriodEndDate">
                                <el-input :disabled="props.isDetail" class="width220"
                                    v-model="formData.trialPeriodEndDate" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="用工单位" prop="employer">
                                <el-input :disabled="props.isDetail" class="width220" v-model="formData.employer"
                                    placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="工作地(省/市)" prop="workPlace">
                                <el-input :disabled="props.isDetail" class="width220" v-model="formData.workPlace"
                                    placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="工作岗位" prop="workPosition">
                                <el-input :disabled="props.isDetail" class="width220" v-model="formData.workPosition"
                                    placeholder="请输入" />
                            </el-form-item>

                            <el-form-item label="备注" prop="remark">
                                <el-input :disabled="props.isDetail" class="width420" rows="3" type="textarea"
                                    v-model="formData.remark" placeholder="请输入" />
                            </el-form-item>
                        </border-box>
                        <!-- 账单模板变更信息 -->
                        <border-box title="账单模板变更信息">
                            <el-table :data="billTemplateChangeData" border>
                                <el-table-column label="变更编号" align="center" prop="changeNo" />
                                <el-table-column label="变更名称" align="center" prop="changeName" />
                                <el-table-column label="客户编号" align="center" prop="customerNo" />
                                <el-table-column label="客户名称" align="center" prop="customerName" />
                                <el-table-column label="原账单模板" align="center" prop="oldBillTemplate" />
                                <el-table-column label="原收费模板" align="center" prop="oldChargeTemplate" />
                                <el-table-column label="新账单模板" align="center" prop="newBillTemplate" />
                                <el-table-column label="新收费模板" align="center" prop="newChargeTemplate" />
                                <el-table-column label="创建人" align="center" prop="creator" />
                                <el-table-column label="创建时间" align="center" prop="createTime" />
                            </el-table>
                        </border-box>
                        <!-- 费用信息 -->
                        <border-box title="费用信息">
                            <el-table :data="costInfoData" border>
                                <el-table-column label="序号" type="index" align="center" />
                                <el-table-column label="产品名称" align="center" prop="productName" />
                                <el-table-column label="社保公积金" align="center" prop="securityType" />
                                <el-table-column label="金额" align="center" prop="amount" />
                                <el-table-column label="收费起始月" align="center" prop="chargeStartMonth" />
                                <el-table-column label="收费截止月" align="center" prop="chargeEndMonth" />
                                <el-table-column label="账单起始月" align="center" prop="billStartMonth" />
                                <el-table-column label="社保比例编号" align="center" prop="ratioCode" />
                                <el-table-column label="比例名称" align="center" prop="ratioName" />
                                <el-table-column label="企业比例" align="center" prop="companyRatio" />
                                <el-table-column label="个人比例" align="center" prop="personalRatio" />
                                <el-table-column label="企业附加" align="center" prop="companyExtra" />
                                <el-table-column label="个人附加" align="center" prop="personalExtra" />
                                <el-table-column label="企业基数" align="center" prop="companyBase" />
                                <el-table-column label="个人基数" align="center" prop="personalBase" />
                                <el-table-column label="企业金额" align="center" prop="companyAmount" />
                                <el-table-column label="个人金额" align="center" prop="personalAmount" />
                                <el-table-column label="收费模板" align="center" prop="chargeTemplate" />
                                <el-table-column label="账单模板" align="center" prop="billTemplate" />
                                <el-table-column label="备注" align="center" prop="remark" />
                            </el-table>
                        </border-box>
                        <!-- 非社保公积金 -->
                        <border-box title="非社保公积金">
                            <el-table :data="nonSocialSecurityData" border>
                                <el-table-column label="收费起始月" align="center" prop="chargeStartMonth" />
                                <el-table-column label="收费截止月" align="center" prop="chargeEndMonth" />
                                <el-table-column label="账单起始月" align="center" prop="billStartMonth" />
                                <el-table-column label="报价单" align="center" prop="quotation" />
                                <el-table-column label="合同" align="center" prop="contract" />
                                <el-table-column label="增值税" align="center" prop="vat" />
                                <el-table-column label="金额(不含税)" align="center" prop="amountExcludingTax" />
                                <el-table-column label="增值税率" align="center" prop="vatRate" />
                                <el-table-column label="服务比率" align="center" prop="serviceRatio" />
                                <el-table-column label="附加税率" align="center" prop="additionalTaxRate" />
                                <el-table-column label="备注" align="center" prop="remark" />
                            </el-table>
                        </border-box>
                    </el-tab-pane>
                    <el-tab-pane label="订单费用信息">
                        <el-form-item label="查询月起" prop="queryStartMonth">
                            <el-date-picker class="width220" v-model="formData.queryStartMonth" type="month"
                                placeholder="请选择" />
                        </el-form-item>
                        <el-form-item label="查询月止" prop="queryEndMonth">
                            <el-date-picker class="width220" v-model="formData.queryEndMonth" type="month"
                                placeholder="请选择" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleQuery">查询</el-button>
                            <el-button @click="resetQueryForm">重置</el-button>
                        </el-form-item>
                        <el-divider />
                        <el-button @click="previousPeriod">上个查询期间</el-button>
                        <el-button @click="nextPeriod">下个查询期间</el-button>
                    </el-tab-pane>
                </el-tabs>
            </el-form>
        </el-dialog>
    </div>
</template>
<script setup>


const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false,
    },
    dialogForm: {
        type: Object,
        default: () => ({}),
    },
    title: {
        type: String,
        default: '',
    },
    isDetail: {
        type: Boolean,
        default: false,
    },
    isShow: {
        type: Boolean,
        default: true,
    },
});

// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});
const emit = defineEmits(['close', 'update:dialogShow']);
const formRef = ref(null);

// 表单数据 - 从props初始化或使用默认值
const formData = reactive({
    // 基本信息
    employeeNo: props.dialogForm?.employeeNo || '',
    employeeName: props.dialogForm?.employeeName || '',
    customerNo: props.dialogForm?.customerNo || '',
    customerName: props.dialogForm?.customerName || '',
    idType: props.dialogForm?.idType || '1',
    idNo: props.dialogForm?.idNo || '',
    mobile: props.dialogForm?.mobile || '',
    phone: props.dialogForm?.phone || '',
    employmentStatus: props.dialogForm?.employmentStatus || '1',
    orderStatus: props.dialogForm?.orderStatus || '1',
    suspendReason: props.dialogForm?.suspendReason || '',
    changeStatus: props.dialogForm?.changeStatus || '',
    subContract: props.dialogForm?.subContract || '',
    subContractNo: props.dialogForm?.subContractNo || '',
    dispatchParty: props.dialogForm?.dispatchParty || '',
    receivingParty: props.dialogForm?.receivingParty || '',
    dispatchService: props.dialogForm?.dispatchService || '',
    receivingService: props.dialogForm?.receivingService || '',
    hasSocialCard: props.dialogForm?.hasSocialCard || '0',
    hasUnifiedMedical: props.dialogForm?.hasUnifiedMedical || '0',
    // 更多表单字段...
    needSign: props.dialogForm?.needSign || '1',
    // 查询参数
    queryStartMonth: props.dialogForm?.queryStartMonth || '',
    queryEndMonth: props.dialogForm?.queryEndMonth || '',
    declareEntryDate: props.dialogForm?.declareEntryDate || '',
    declareEntryPerson: props.dialogForm?.declareEntryPerson || '',
    entryDate: props.dialogForm?.entryDate || '',
    signingPartyHeader: props.dialogForm?.signingPartyHeader || '',
    entryConfirmDate: props.dialogForm?.entryConfirmDate || '',
    increaseReason: props.dialogForm?.increaseReason || '',
    isOutboundCall: props.dialogForm?.isOutboundCall || '0',
    isCentralizedInsurance: props.dialogForm?.isCentralizedInsurance || '0',
    isArchived: props.dialogForm?.isArchived || '0',
    actualWorkplace: props.dialogForm?.actualWorkplace || '',
    declareResignDate: props.dialogForm?.declareResignDate || '',
    resignDate: props.dialogForm?.resignDate || '',
    resignReason: props.dialogForm?.resignReason || '',
    decreaseReason: props.dialogForm?.decreaseReason || '',
    decreaseDetailReason: props.dialogForm?.decreaseDetailReason || '',
    isResignCall: props.dialogForm?.isResignCall || '0',
    notIncreaseFeedback: props.dialogForm?.notIncreaseFeedback || '',
    flexibleCategory2: props.dialogForm?.flexibleCategory2 || '',
    flexibleCategory3: props.dialogForm?.flexibleCategory3 || '',
    flexibleCategory4: props.dialogForm?.flexibleCategory4 || '',
    flexibleCategory5: props.dialogForm?.flexibleCategory5 || '',
    providentFundAccount: props.dialogForm?.providentFundAccount || '',
    socialInsuranceSalary: props.dialogForm?.socialInsuranceSalary || '',
    providentFundSalary: props.dialogForm?.providentFundSalary || '',
    personnelCategory: props.dialogForm?.personnelCategory || '',
    continuousTransferFlag: props.dialogForm?.continuousTransferFlag || '0',
    ethnicInfo: props.dialogForm?.ethnicInfo || '',
    contractType: props.dialogForm?.contractType || '',
    contractNo: props.dialogForm?.contractNo || '',
    entryRemark: props.dialogForm?.entryRemark || '',
    resignRemark: props.dialogForm?.resignRemark || '',
    entryProcess: props.dialogForm?.entryProcess || '',
    resignConfirmProcess: props.dialogForm?.resignConfirmProcess || '',
    changeConfirmProcess: props.dialogForm?.changeConfirmProcess || '',
    orderReceiveRemark: props.dialogForm?.orderReceiveRemark || '',
    changeRemark: props.dialogForm?.changeRemark || '',
    contractSigningPlace: props.dialogForm?.contractSigningPlace || '',
    laborContractType: props.dialogForm?.laborContractType || '',
    contractVersion: props.dialogForm?.contractVersion || '',
});

// 表单校验规则
const rules = {
    employeeNo: [{ required: true, message: '雇员编号不能为空', trigger: 'blur' }],
    employeeName: [{ required: true, message: '雇员姓名不能为空', trigger: 'blur' }],
    idNo: [{ required: true, message: '证件编号不能为空', trigger: 'blur' }],
    // 更多校验规则...
};

// 选项数据
const idTypeOptions = [
    { value: '1', label: '身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '港澳通行证' },
    { value: '4', label: '台胞证' },
    { value: '5', label: '其他' }
];

const employmentStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '待入职' }
];

const orderStatusOptions = [
    { value: '1', label: '待处理' },
    { value: '2', label: '处理中' },
    { value: '3', label: '已完成' },
    { value: '4', label: '已取消' },
    { value: '5', label: '已挂起' }
];

const yesNoOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' }
];

// 账单模板变更信息表格数据
const billTemplateChangeData = ref([
    {
        changeNo: 'BT20230001',
        changeName: '账单模板变更',
        customerNo: 'CUS20230001',
        customerName: '示例客户公司',
        oldBillTemplate: '社保标准账单模板',
        oldChargeTemplate: '社保标准收费模板',
        newBillTemplate: '社保高端账单模板',
        newChargeTemplate: '社保高端收费模板',
        creator: '管理员',
        createTime: '2023-01-15 10:30:45'
    },
    {
        changeNo: 'BT20230002',
        changeName: '账单收费变更',
        customerNo: 'CUS20230001',
        customerName: '示例客户公司',
        oldBillTemplate: '公积金标准账单模板',
        oldChargeTemplate: '公积金标准收费模板',
        newBillTemplate: '公积金高端账单模板',
        newChargeTemplate: '公积金高端收费模板',
        creator: '管理员',
        createTime: '2023-02-20 14:20:30'
    }
]);

// 费用信息表格数据
const costInfoData = ref([
    {
        id: 1,
        productName: '养老保险',
        securityType: '社保',
        amount: 2800,
        chargeStartMonth: '2023-01',
        chargeEndMonth: '2023-12',
        billStartMonth: '2023-01',
        ratioCode: 'YL001',
        ratioName: '养老比例标准',
        companyRatio: '20%',
        personalRatio: '8%',
        companyExtra: 0,
        personalExtra: 0,
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 2000,
        personalAmount: 800,
        chargeTemplate: '标准收费模板',
        billTemplate: '标准账单模板',
        remark: ''
    },
    {
        id: 2,
        productName: '医疗保险',
        securityType: '社保',
        amount: 800,
        chargeStartMonth: '2023-01',
        chargeEndMonth: '2023-12',
        billStartMonth: '2023-01',
        ratioCode: 'YL002',
        ratioName: '医疗比例标准',
        companyRatio: '6%',
        personalRatio: '2%',
        companyExtra: 0,
        personalExtra: 0,
        companyBase: 10000,
        personalBase: 10000,
        companyAmount: 600,
        personalAmount: 200,
        chargeTemplate: '标准收费模板',
        billTemplate: '标准账单模板',
        remark: ''
    }
]);

// 非社保公积金表格数据
const nonSocialSecurityData = ref([
    {
        id: 1,
        chargeStartMonth: '2023-01',
        chargeEndMonth: '2023-12',
        billStartMonth: '2023-01',
        quotation: 'QT20230001',
        contract: 'CT20230001',
        vat: 60,
        amountExcludingTax: 940,
        vatRate: '6%',
        serviceRatio: '10%',
        additionalTaxRate: '0%',
        remark: '商业保险'
    },
    {
        id: 2,
        chargeStartMonth: '2023-01',
        chargeEndMonth: '2023-12',
        billStartMonth: '2023-01',
        quotation: 'QT20230002',
        contract: 'CT20230002',
        vat: 48,
        amountExcludingTax: 752,
        vatRate: '6%',
        serviceRatio: '8%',
        additionalTaxRate: '0%',
        remark: '年金'
    }
]);

// 挂起原因选项
const suspendReasonOptions = [
    { value: '1', label: '材料不全' },
    { value: '2', label: '信息不完整' },
    { value: '3', label: '等待审核' },
    { value: '4', label: '其他原因' }
];

// 变更状态选项
const changeStatusOptions = [
    { value: '1', label: '未变更' },
    { value: '2', label: '变更中' },
    { value: '3', label: '已变更' }
];

// 增员原因选项
const increaseReasonOptions = [
    { value: '1', label: '新入职' },
    { value: '2', label: '转入' },
    { value: '3', label: '恢复' },
    { value: '4', label: '其他' }
];

// 减员原因选项
const decreaseReasonOptions = [
    { value: '1', label: '离职' },
    { value: '2', label: '转出' },
    { value: '3', label: '停缴' },
    { value: '4', label: '其他' }
];

// 合同类型选项
const contractTypeOptions = [
    { value: '1', label: '劳动合同' },
    { value: '2', label: '劳务合同' },
    { value: '3', label: '派遣合同' },
    { value: '4', label: '其他' }
];

// 劳动合同类型选项
const laborContractTypeOptions = [
    { value: '1', label: '固定期限' },
    { value: '2', label: '无固定期限' },
    { value: '3', label: '以完成一定工作为期限' },
    { value: '4', label: '其他' }
];

// 合同版本选项
const contractVersionOptions = [
    { value: '1', label: '2023版' },
    { value: '2', label: '2022版' },
    { value: '3', label: '2021版' },
    { value: '4', label: '其他' }
];

// 关闭弹窗
const handleClose = () => {
    dialogShow.value = false;
    emit('close');
};

// 查询方法
const handleQuery = () => {
    console.log('查询参数：', {
        queryStartMonth: formData.queryStartMonth,
        queryEndMonth: formData.queryEndMonth
    });
    // 此处可调用API进行查询
};

// 重置查询表单
const resetQueryForm = () => {
    formData.queryStartMonth = '';
    formData.queryEndMonth = '';
};

// 上一个查询期间
const previousPeriod = () => {
    if (formData.queryStartMonth && formData.queryEndMonth) {
        const startDate = new Date(formData.queryStartMonth);
        const endDate = new Date(formData.queryEndMonth);

        // 月份减1
        startDate.setMonth(startDate.getMonth() - 1);
        endDate.setMonth(endDate.getMonth() - 1);

        formData.queryStartMonth = startDate.toISOString().substring(0, 7);
        formData.queryEndMonth = endDate.toISOString().substring(0, 7);

        handleQuery();
    }
};

// 下一个查询期间
const nextPeriod = () => {
    if (formData.queryStartMonth && formData.queryEndMonth) {
        const startDate = new Date(formData.queryStartMonth);
        const endDate = new Date(formData.queryEndMonth);

        // 月份加1
        startDate.setMonth(startDate.getMonth() + 1);
        endDate.setMonth(endDate.getMonth() + 1);

        formData.queryStartMonth = startDate.toISOString().substring(0, 7);
        formData.queryEndMonth = endDate.toISOString().substring(0, 7);

        handleQuery();
    }
};

// 组件挂载时执行
onMounted(() => {
    // 如果是编辑模式且有传入数据，则初始化表单
    if (props.dialogForm) {
        // 深拷贝props.dialogForm到formData
        Object.assign(formData, props.dialogForm);
    }
});
</script>
<style scoped lang="scss">
.fixed {
    background: #2751A5;
    color: #fff;
    padding: 10px 0px;
    position: fixed;
    top: 200px;
    z-index: 99;
    width: 60%;
    left: 20%;

    :deep(.el-form-item__label) {
        color: #fff;
    }
}
</style>
