<!-- 增减追踪报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="集团名称:" prop="groupName">
                <el-select v-model="obj.queryParams.groupName" placeholder="请选择集团名称" clearable class="width220">
                    <el-option label="集团A" value="集团A" />
                    <el-option label="集团B" value="集团B" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item>
                <el-button icon="Refresh" @click="handleReset">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
                <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
            </el-form-item>
        </el-form>
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" border style="width: 100%">
            <el-table-column label="集团名称" align="center" prop="groupName" />
            <el-table-column label="客户编号" align="center" width="180" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同编号" align="center" width="140" prop="contractCode" />
            <el-table-column label="签单地" align="center" prop="signLocation" />
            <el-table-column label="签单分公司(派单分公司)" align="center" width="180" prop="signCompany" />
            <el-table-column label="签约方抬头 (合同我司抬头及收款公司)" align="center" width="260" prop="signingParty" />
            <el-table-column label="当月实际在保人数" align="center" width="140" prop="currentMonthInsured">
                <template #default="scope">
                    <span>{{ scope.row.currentMonthInsured }} 人</span>
                </template>
            </el-table-column>
            <el-table-column label="上月底在保人数" align="center" width="140" prop="lastMonthInsured">
                <template #default="scope">
                    <span>{{ scope.row.lastMonthInsured }} 人</span>
                </template>
            </el-table-column>
            <el-table-column label="对比上月在保人数结果" align="center" width="160" prop="compareLastMonth">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.compareLastMonth)">{{
                        formatPercentage(scope.row.compareLastMonth) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="当前月份-1对比结果" align="center" width="160" prop="compareMonth1">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.compareMonth1)">{{ formatPercentage(scope.row.compareMonth1)
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="当前月份-2对比结果" align="center" width="160" prop="compareMonth2">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.compareMonth2)">{{ formatPercentage(scope.row.compareMonth2)
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="当前月份-3对比结果" align="center" width="160" prop="compareMonth3">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.compareMonth3)">{{ formatPercentage(scope.row.compareMonth3)
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="当前月份-4对比结果" align="center" width="160" prop="compareMonth4">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.compareMonth4)">{{ formatPercentage(scope.row.compareMonth4)
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="当前月份-5对比结果" align="center" width="160" prop="compareMonth5">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.compareMonth5)">{{ formatPercentage(scope.row.compareMonth5)
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column label="是否前三个月连续减员大于30%" align="center" width="220" prop="isThreeMonthsConsecutiveDecrease">
                <template #default="scope">
                    <el-tag :type="scope.row.isThreeMonthsConsecutiveDecrease ? 'danger' : 'success'">
                        {{ scope.row.isThreeMonthsConsecutiveDecrease ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="半年内是否非连续3个月减员大于30%" align="center" width="260"
                prop="isHalfYearNonConsecutiveDecrease">
                <template #default="scope">
                    <el-tag :type="scope.row.isHalfYearNonConsecutiveDecrease ? 'danger' : 'success'">
                        {{ scope.row.isHalfYearNonConsecutiveDecrease ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="集团名称当月实际在保人数" align="center" width="200" prop="groupCurrentMonthInsured">
                <template #default="scope">
                    <span>{{ scope.row.groupCurrentMonthInsured }} 人</span>
                </template>
            </el-table-column>
            <el-table-column label="集团名称上月底在保人数" align="center" width="180" prop="groupLastMonthInsured">
                <template #default="scope">
                    <span>{{ scope.row.groupLastMonthInsured }} 人</span>
                </template>
            </el-table-column>
            <el-table-column label="集团名称对比上月在保人数结果" align="center" width="260" prop="groupCompareLastMonth">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.groupCompareLastMonth)">{{
                        formatPercentage(scope.row.groupCompareLastMonth) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="集团名称当前月份-1对比结果" align="center" width="220" prop="groupCompareMonth1">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.groupCompareMonth1)">{{
                        formatPercentage(scope.row.groupCompareMonth1) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="集团名称当前月份-2对比结果" align="center" width="220" prop="groupCompareMonth2">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.groupCompareMonth2)">{{
                        formatPercentage(scope.row.groupCompareMonth2) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="集团名称当前月份-3对比结果" align="center" width="220" prop="groupCompareMonth3">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.groupCompareMonth3)">{{
                        formatPercentage(scope.row.groupCompareMonth3) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="集团名称当前月份-4对比结果" align="center" width="220" prop="groupCompareMonth4">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.groupCompareMonth4)">{{
                        formatPercentage(scope.row.groupCompareMonth4) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="集团名称当前月份-5对比结果" align="center" width="220" prop="groupCompareMonth5">
                <template #default="scope">
                    <span :class="getCompareClass(scope.row.groupCompareMonth5)">{{
                        formatPercentage(scope.row.groupCompareMonth5) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="集团名称是否前三个月连续减员大于30%" align="center" width="260"
                prop="groupIsThreeMonthsConsecutiveDecrease">
                <template #default="scope">
                    <el-tag :type="scope.row.groupIsThreeMonthsConsecutiveDecrease ? 'danger' : 'success'">
                        {{ scope.row.groupIsThreeMonthsConsecutiveDecrease ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="集团名称半年内是否非连续3个月减员大于30%" align="center" width="300"
                prop="groupIsHalfYearNonConsecutiveDecrease">
                <template #default="scope">
                    <el-tag :type="scope.row.groupIsHalfYearNonConsecutiveDecrease ? 'danger' : 'success'">
                        {{ scope.row.groupIsHalfYearNonConsecutiveDecrease ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="AddSubtractTraceReport">

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    // 加载状态
    loading: false,
    // 总条数
    total: 0,
    // 查询参数
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null,
        customerName: null,
        reportMonth: null
    },
    // 表格数据
    tableData: []
})

/** 格式化百分比 */
function formatPercentage(value) {
    if (value === null || value === undefined) {
        return '0%';
    }
    return (value >= 0 ? '+' : '') + value + '%';
}

/** 获取比较结果的样式类 */
function getCompareClass(value) {
    if (value === null || value === undefined) {
        return '';
    }
    if (value > 0) {
        return 'text-success'; // 增长为绿色
    } else if (value < 0) {
        return 'text-danger'; // 减少为红色
    }
    return ''; // 不变为默认颜色
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                groupName: '集团A',
                customerCode: 'KH20230001',
                customerName: '客户A',
                contractCode: 'HT20230001',
                signLocation: '北京',
                signCompany: '分公司A',
                signingParty: '公司A',
                currentMonthInsured: 100,
                lastMonthInsured: 90,
                compareLastMonth: 11.11, // (100-90)/90*100
                compareMonth1: 5.26,
                compareMonth2: -10.53,
                compareMonth3: 15.79,
                compareMonth4: 0,
                compareMonth5: -5.26,
                isThreeMonthsConsecutiveDecrease: false,
                isHalfYearNonConsecutiveDecrease: false,
                groupCurrentMonthInsured: 500,
                groupLastMonthInsured: 480,
                groupCompareLastMonth: 4.17,
                groupCompareMonth1: 2.13,
                groupCompareMonth2: -4.26,
                groupCompareMonth3: 6.38,
                groupCompareMonth4: 0,
                groupCompareMonth5: -2.13,
                groupIsThreeMonthsConsecutiveDecrease: false,
                groupIsHalfYearNonConsecutiveDecrease: false
            },
            {
                id: 2,
                groupName: '集团B',
                customerCode: 'KH20230002',
                customerName: '客户B',
                contractCode: 'HT20230002',
                signLocation: '上海',
                signCompany: '分公司B',
                signingParty: '公司B',
                currentMonthInsured: 50,
                lastMonthInsured: 80,
                compareLastMonth: -37.5, // (50-80)/80*100
                compareMonth1: -33.33,
                compareMonth2: -40,
                compareMonth3: -35,
                compareMonth4: -20,
                compareMonth5: -10,
                isThreeMonthsConsecutiveDecrease: true,
                isHalfYearNonConsecutiveDecrease: true,
                groupCurrentMonthInsured: 300,
                groupLastMonthInsured: 400,
                groupCompareLastMonth: -25,
                groupCompareMonth1: -20,
                groupCompareMonth2: -30,
                groupCompareMonth3: -35,
                groupCompareMonth4: -15,
                groupCompareMonth5: -5,
                groupIsThreeMonthsConsecutiveDecrease: true,
                groupIsHalfYearNonConsecutiveDecrease: true
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 初始化数据
getList();
</script>

<style lang="scss" scoped>
.text-success {
    color: #67C23A;
}

.text-danger {
    color: #F56C6C;
}
</style>