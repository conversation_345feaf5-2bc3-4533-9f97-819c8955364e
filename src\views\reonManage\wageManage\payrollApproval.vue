<!-- 薪资审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="薪资类别名称:" prop="salaryCategory">
                <el-input class="width220" v-model="obj.queryParams.salaryCategory" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="薪资项目名称:" prop="salaryItemName">
                <el-input class="width220" v-model="obj.queryParams.salaryItemName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="SuccessFilled" @click="handleBatchPass">批量通过</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="CircleClose" :disabled="obj.single"
                    @click="handleBatchReject">批量驳回</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="info" plain icon="Printer" @click="handlePrint">打印</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="payroll-approval" />
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column v-if="obj.selectedColumns.includes('itemName')" label="薪资项目名称" align="center"
                prop="itemName">
                <template #default="scope">
                    <dict-tag :options="salaryItemOptions" :value="scope.row.itemName" />
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('englishName')" label="英文名称" align="center"
                prop="englishName" sortable />
            <el-table-column v-if="obj.selectedColumns.includes('isTaxable')" label="是否扣税" align="center"
                prop="isTaxable">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isTaxable" />
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('attribute')" label="增减属性" align="center"
                prop="attribute">
                <template #default="scope">
                    <dict-tag :options="attributeOptions" :value="scope.row.attribute" />
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('dataSource')" label="数据来源" align="center"
                prop="dataSource">
                <template #default="scope">
                    <dict-tag :options="dataSourceOptions" :value="scope.row.dataSource" />
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('isVisible')" label="是否显示" align="center"
                prop="isVisible">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isVisible" />
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('isShowInPayslip')" label="是否在薪资单显示" align="center"
                prop="isShowInPayslip">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isShowInPayslip" />
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('isText')" label="是否文本" align="center" prop="isText">
                <template #default="scope">
                    <dict-tag :options="yesNoOptions" :value="scope.row.isText" />
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>



const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 初始化字典
proxy.useDict();

// 薪资项目选项
const salaryItemOptions = ref([
    { value: '1', label: '基本工资' },
    { value: '2', label: '绩效工资' },
    { value: '3', label: '岗位津贴' },
    { value: '4', label: '交通补贴' },
    { value: '5', label: '餐补' }
]);

// 是否选项
const yesNoOptions = ref([
    { value: 'Y', label: '是' },
    { value: 'N', label: '否' }
]);

// 增减属性选项
const attributeOptions = ref([
    { value: '1', label: '增加' },
    { value: '2', label: '减少' }
]);

// 数据来源选项
const dataSourceOptions = ref([
    { value: '1', label: '系统录入' },
    { value: '2', label: '手工录入' },
    { value: '3', label: '自动计算' }
]);

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        itemName: '1', // 薪资项目名称
        englishName: 'Base Salary', // 英文名称
        isTaxable: 'Y', // 是否扣税
        attribute: '1', // 增减属性
        dataSource: '1', // 数据来源
        isVisible: 'Y', // 是否显示
        isShowInPayslip: 'Y', // 是否在薪资单显示
        isText: 'N' // 是否文本
    },
    {
        id: 2,
        itemName: '2', // 薪资项目名称
        englishName: 'Performance Bonus', // 英文名称
        isTaxable: 'Y', // 是否扣税
        attribute: '1', // 增减属性
        dataSource: '2', // 数据来源
        isVisible: 'Y', // 是否显示
        isShowInPayslip: 'Y', // 是否在薪资单显示
        isText: 'N' // 是否文本
    }
]

// 列配置选项
const columnOptions = [
    { prop: 'itemName', label: '薪资项目名称' },
    { prop: 'englishName', label: '英文名称' },
    { prop: 'isTaxable', label: '是否扣税' },
    { prop: 'attribute', label: '增减属性' },
    { prop: 'dataSource', label: '数据来源' },
    { prop: 'isVisible', label: '是否显示' },
    { prop: 'isShowInPayslip', label: '是否在薪资单显示' },
    { prop: 'isText', label: '是否文本' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        salaryCategory: null, // 薪资类别名称
        salaryItemName: null, // 薪资项目名称
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    ids: [], // 选中的id
    title: "", // 标题
    columnOptions: columnOptions, // 列配置选项
    selectedColumns: columnOptions.map(item => item.prop) // 默认选中所有列
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟异步请求
    setTimeout(() => {
        // 这里可以根据查询条件过滤数据
        const filteredData = mockTableData.filter(item => {
            const matchSalaryCategory = !obj.queryParams.salaryCategory ||
                item.itemName.includes(obj.queryParams.salaryCategory);
            const matchSalaryItemName = !obj.queryParams.salaryItemName ||
                item.itemName.includes(obj.queryParams.salaryItemName);
            return matchSalaryCategory && matchSalaryItemName;
        });

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}



// 批量通过
function handleBatchPass() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    proxy.$modal.msgSuccess('批量通过操作成功');
    // 这里可以添加实际的批量通过逻辑
}

// 批量驳回
function handleBatchReject() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要操作的数据');
        return;
    }
    proxy.$modal.msgSuccess('批量驳回操作成功');
    // 这里可以添加实际的批量驳回逻辑
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 导出功能
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 打印功能
function handlePrint() {
    if (obj.tableData.length === 0) {
        proxy.$modal.msgError('没有数据可打印');
        return;
    }

    proxy.$modal.msgSuccess('准备打印');
    // 实际项目中应该调用打印API或使用打印插件

    // 简单的打印当前页面
    setTimeout(() => {
        window.print();
    }, 300);
}

getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>