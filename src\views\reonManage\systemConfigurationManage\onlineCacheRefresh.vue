<!-- 线上缓存刷新 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" label-width="auto">
            <el-form-item label="刷新类型:" prop="typeCode">
                <el-select class="width420" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                    <el-option label="业务字典刷新" value="1" />
                    <el-option label="系统业务人员缓存刷新" value="2" />
                    <el-option label="省市缓存刷新" value="3" />
                </el-select>
                <el-button class="marl20" type="primary" icon="Refresh" @click="refresh">刷新</el-button>
            </el-form-item>
            <el-form-item label=" ">
                <el-input class="width420" type="textarea" v-model="obj.queryParams.sales" placeholder="请输入"
                    clearable />
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup name="OnlineCacheRefresh">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 新增
function handleAdd() {
    obj.dialogShow = true;
}

// 修改
function handleEdit() {
    obj.dialogShow = true;
}




getList();
</script>
<style lang="scss" scoped></style>