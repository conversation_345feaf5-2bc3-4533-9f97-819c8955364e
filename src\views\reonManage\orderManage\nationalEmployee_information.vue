<!-- 全国雇员信息查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="100px">
            <el-row class="mb8">
                <el-col :span="6">
                    <el-form-item label="唯一号:" prop="uniqueId">
                        <el-input v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="雇员姓名:" prop="employeeName">
                        <el-input v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="证件号码:" prop="idNumber">
                        <el-input v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="订单编号:" prop="orderCode">
                        <el-input v-model="obj.queryParams.orderCode" placeholder="请输入订单编号" clearable />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="订单编号" align="center" prop="orderCode" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件类型" align="center" prop="idType" />
            <el-table-column label="证件编号" align="center" prop="idNumber" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="派单方" align="center" prop="sender" />
            <el-table-column label="接单方" align="center" prop="receiver" />
            <el-table-column label="派单方客服" align="center" prop="senderService" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null,
        employeeName: null,
        idNumber: null,
        orderCode: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                uniqueId: 'WY20230001',
                orderCode: 'DD20230001',
                employeeName: '张三',
                idType: '身份证',
                idNumber: '110101199001011234',
                customerName: '客户名称1',
                smallContractName: '小合同名称1',
                sender: '派单方1',
                receiver: '接单方1',
                senderService: '派单方客服1'
            },
            {
                id: 2,
                uniqueId: 'WY20230002',
                orderCode: 'DD20230002',
                employeeName: '李四',
                idType: '身份证',
                idNumber: '110101199001011235',
                customerName: '客户名称2',
                smallContractName: '小合同名称2',
                sender: '派单方2',
                receiver: '接单方2',
                senderService: '派单方客服2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>