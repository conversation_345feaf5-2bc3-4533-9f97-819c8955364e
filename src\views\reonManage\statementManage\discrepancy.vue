<!-- 社保账单与实做报表差异报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="客户名称:" prop="customerName" required>
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType" required>
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择账户类型" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择城市" clearable>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方:" prop="receiver">
                <el-select class="width220" v-model="obj.queryParams.receiver" placeholder="请选择接单方" clearable>
                    <el-option v-for="item in receiverOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="证件号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号" clearable />
            </el-form-item>
            <el-form-item label="大区:" prop="region" required>
                <el-select class="width220" v-model="obj.queryParams.region" placeholder="请选择大区" clearable>
                    <el-option v-for="item in regionOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                <el-button icon="Refresh" @click="handleReset">重置</el-button>
            </el-row>
        </el-form>

    </div>
</template>

<script setup name="Discrepancy">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 账户类型选项
const accountTypeOptions = ref([
    { value: 'big', label: '大户' },
    { value: 'single', label: '单立户' }
]);

// 城市选项
const cityOptions = ref([
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
]);

// 接单方选项
const receiverOptions = ref([
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '个人' }
]);

// 大区选项
const regionOptions = ref([
    { value: '1', label: '华北区' },
    { value: '2', label: '华东区' },
    { value: '3', label: '华南区' },
    { value: '4', label: '西南区' },
    { value: '5', label: '西北区' }
]);

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    tableData: [], // 表格数据
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        accountType: null,
        city: null,
        receiver: null,
        idNumber: null,
        region: null,
        billMonth: null
    } // 查询表单
})


/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 导出明细按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        // 调用导出接口
        proxy.$modal.msgSuccess('导出成功');
    }).catch(() => { });
}

</script>

<style lang="scss" scoped></style>