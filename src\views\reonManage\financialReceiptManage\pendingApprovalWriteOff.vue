<!-- 待核销审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.sales" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="客户帐套" prop="typeCode">
                <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付类型:" prop="typeCode">
                <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="到款时间>=:" prop="typeCode">
                <el-date-picker class="width220" v-model="obj.queryParams.sales" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="到款时间<=:" prop="typeCode">
                <el-date-picker class="width220" v-model="obj.queryParams.sales" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="到款金额>=	:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.sales" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="到款金额<=:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.sales" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="核销时间>=:" prop="typeCode">
                <el-date-picker class="width220" v-model="obj.queryParams.sales" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="核销时间<=:" prop="typeCode">
                <el-date-picker class="width220" v-model="obj.queryParams.sales" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="核销金额>=:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.sales" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="核销金额<=:" prop="typeCode">
                <el-input class="width220" v-model="obj.queryParams.sales" placeholder="请输入" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" icon="Check" @click="handleQuery">通过</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" icon="Close" @click="resetQuery">驳回</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" type="index" width="80" />
            <el-table-column label="客户名称" align="center" prop="id" />
            <el-table-column label="客户帐套" align="center" prop="id" />
            <el-table-column label="应收金额" align="center" prop="id" />
            <el-table-column label="财务应收年月" align="center" prop="id" />
            <el-table-column label="核销状态" align="center" prop="id" />
            <el-table-column label="开票状态" align="center" prop="id" />
            <el-table-column label="核销金额" align="center" prop="id" />
            <el-table-column label="小额调整" align="center" prop="id" />
            <el-table-column label="核销日期" align="center" prop="id" />
            <el-table-column label="核销备注" align="center" prop="id" />
            <el-table-column label="审批状态" align="center" prop="id" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="PendingApprovalWriteOff">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";


const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数
    ids: [],//选中id

    tableData: [],//列表
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框操作 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

getList();
</script>
<style lang="scss" scoped></style>