<!-- 合同归档 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="合同编号:" prop="contractCode">
                <el-input class="width220" v-model="obj.queryParams.contractCode" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="销售:" prop="sales">
                <el-select class="width220" filterable v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                    <el-option v-for="item in salesList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="销售所属公司:" prop="salesCompany">
                <el-select class="width220" filterable v-model="obj.queryParams.salesCompany" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in companyList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同起始日:" prop="startDate">
                <el-date-picker class="width220" v-model="obj.queryParams.startDate" placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="合同结束日:" prop="endDate">
                <el-date-picker class="width220" v-model="obj.queryParams.endDate" placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="是否归档:" prop="isArchived">
                <el-select class="width220" filterable v-model="obj.queryParams.isArchived" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleConfirmFiling">确认归档</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleChangeToUnfiled">变更为未归档</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handlePrintFilingForm">打印合同归档表</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="是否归档" align="center" prop="isArchived">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isArchived" />
                </template>
            </el-table-column>
            <el-table-column label="流程类型" align="center" prop="processType" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="报价编号" align="center" prop="quoteCode" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="销售所在公司" align="center" prop="salesCompany" />
            <el-table-column label="派单分公司" align="center" prop="dispatchCompany" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";
const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 销售人员列表
const salesList = ref([]);
// 公司列表
const companyList = ref([]);

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractCode: null,
        contractName: null,
        customerName: null,
        sales: null,
        salesCompany: null,
        startDate: null,
        endDate: null,
        isArchived: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 获取销售人员列表 */
function getSalesList() {
    // 这里可以调用API获取销售人员列表
    salesList.value = [
        { code: '1', name: '销售1' },
        { code: '2', name: '销售2' },
        { code: '3', name: '销售3' }
    ];
}

/** 获取公司列表 */
function getCompanyList() {
    // 这里可以调用API获取公司列表
    companyList.value = [
        { code: '1', name: '公司1' },
        { code: '2', name: '公司2' },
        { code: '3', name: '公司3' }
    ];
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取合同归档列表
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                contractCode: 'HT20230001',
                contractName: '合同1',
                isArchived: 'Y',
                processType: '流程类型1',
                contractType: '合同类型1',
                quoteCode: 'BJ20230001',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                salesCompany: '销售公司1',
                dispatchCompany: '分公司1'
            },
            {
                id: 2,
                contractCode: 'HT20230002',
                contractName: '合同2',
                isArchived: 'N',
                processType: '流程类型2',
                contractType: '合同类型2',
                quoteCode: '**********',
                customerCode: '**********',
                customerName: '客户名称2',
                salesCompany: '销售公司2',
                dispatchCompany: '分公司2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {

    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 确认归档操作 */
function handleConfirmFiling() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要归档的合同');
        return;
    }
    proxy.$modal.confirm('确认要将选中的合同设置为已归档吗？').then(() => {
        // 这里可以调用API进行归档操作
        proxy.$modal.msgSuccess('归档成功');
        getList();
    }).catch(() => { });
}

/** 变更为未归档操作 */
function handleChangeToUnfiled() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要变更的合同');
        return;
    }
    proxy.$modal.confirm('确认要将选中的合同设置为未归档吗？').then(() => {
        // 这里可以调用API进行变更操作
        proxy.$modal.msgSuccess('变更成功');
        getList();
    }).catch(() => { });
}

/** 打印合同归档表操作 */
function handlePrintFilingForm() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要打印的合同');
        return;
    }
    // 这里可以调用API进行打印操作
    proxy.$modal.msgSuccess('打印成功');
}

// 初始化数据
getSalesList();
getCompanyList();
getList();
</script>
<style lang="scss" scoped></style>
