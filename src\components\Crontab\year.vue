<template>
    <el-form>
        <el-form-item>
            <el-radio :value="1" v-model='radioValue'>
                不填，允许的通配符[, - * /]
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio :value="2" v-model='radioValue'>
                每年
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio :value="3" v-model='radioValue'>
                周期从
                <el-input-number v-model='cycle01' :min='fullYear' :max="2098" /> -
                <el-input-number v-model='cycle02' :min="cycle01 ? cycle01 + 1 : fullYear + 1" :max="2099" />
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio :value="4" v-model='radioValue'>
                从
                <el-input-number v-model='average01' :min='fullYear' :max="2098" /> 年开始，每
                <el-input-number v-model='average02' :min="1" :max="2099 - average01 || fullYear" /> 年执行一次
            </el-radio>

        </el-form-item>

        <el-form-item>
            <el-radio :value="5" v-model='radioValue'>
                指定
                <el-select clearable v-model="checkboxList" placeholder="可多选" multiple :multiple-limit="8">
                    <el-option v-for="item in 9" :key="item" :value="item - 1 + fullYear"
                        :label="item - 1 + fullYear" />
                </el-select>
            </el-radio>
        </el-form-item>
    </el-form>
</template>

<script setup>
const emit = defineEmits(['update'])
const props = defineProps({
    cron: {
        type: Object,
        default: {
            second: "*",
            min: "*",
            hour: "*",
            day: "*",
            month: "*",
            week: "?",
            year: ""
        }
    },
    check: {
        type: Function,
        default: () => {
        }
    }
})
const fullYear = ref(0)
const maxFullYear = ref(0)
const radioValue = ref(1)
const cycle01 = ref(0)
const cycle02 = ref(0)
const average01 = ref(0)
const average02 = ref(1)
const checkboxList = ref([])
const checkCopy = ref([])
const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, fullYear.value, maxFullYear.value - 1)
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, maxFullYear.value)
    return cycle01.value + '-' + cycle02.value
})
const averageTotal = computed(() => {
    average01.value = props.check(average01.value, fullYear.value, maxFullYear.value - 1)
    average02.value = props.check(average02.value, 1, 10)
    return average01.value + '/' + average02.value
})
const checkboxString = computed(() => {
    return checkboxList.value.join(',')
})
watch(() => props.cron.year, value => changeRadioValue(value))
watch([radioValue, cycleTotal, averageTotal, checkboxString], () => onRadioChange())
/**
 * 根据不同的输入值类型更改单选按钮的值
 * 此函数处理多种格式的输入值，以确定正确的单选按钮选项和相关值
 * @param {string} value - 输入值，用于决定单选按钮的状态和其他相关数据
 */
function changeRadioValue(value) {
    // 当输入值为空时，将单选按钮值设置为1
    if (value === '') {
        radioValue.value = 1
        // 当输入值为"*"时，将单选按钮值设置为2
    } else if (value === "*") {
        radioValue.value = 2
        // 当输入值包含"-"时，表示一个范围，将单选按钮值设置为3，并记录范围值
    } else if (value.indexOf("-") > -1) {
        const indexArr = value.split('-')
        cycle01.value = Number(indexArr[0])
        cycle02.value = Number(indexArr[1])
        radioValue.value = 3
        // 当输入值包含"/"时，表示一个比率，将单选按钮值设置为4，并记录比率值
    } else if (value.indexOf("/") > -1) {
        const indexArr = value.split('/')
        average01.value = Number(indexArr[1])
        average02.value = Number(indexArr[0])
        radioValue.value = 4
        // 当输入值为逗号分隔的数字列表时，将单选按钮值设置为5，并记录列表值
    } else {
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))]
        radioValue.value = 5
    }
}
/**
 * 处理单选按钮变化事件
 * 此函数根据单选按钮的值来决定如何更新数据
 * 它使用emit函数来触发更新事件，传递不同的参数根据用户的选择
 */
function onRadioChange() {
    // 根据单选按钮的值执行不同的操作
    switch (radioValue.value) {
        case 1:
            // 当单选按钮值为1时，触发更新事件，传递空字符串作为年份参数
            emit('update', 'year', '', 'year')
            break
        case 2:
            // 当单选按钮值为2时，触发更新事件，传递星号作为年份参数
            emit('update', 'year', '*', 'year')
            break
        case 3:
            // 当单选按钮值为3时，触发更新事件，传递cycleTotal的值作为年份参数
            emit('update', 'year', cycleTotal.value, 'year')
            break
        case 4:
            // 当单选按钮值为4时，触发更新事件，传递averageTotal的值作为年份参数
            emit('update', 'year', averageTotal.value, 'year')
            break
        case 5:
            // 当单选按钮值为5时，处理checkboxList的值，并触发更新事件
            if (checkboxList.value.length === 0) {
                // 如果checkboxList为空，添加第一个checkCopy项到列表中
                checkboxList.value.push(checkCopy.value[0])
            } else {
                // 否则，将checkboxList的值复制到checkCopy中
                checkCopy.value = checkboxList.value
            }
            // 触发更新事件，传递checkboxString的值作为年份参数
            emit('update', 'year', checkboxString.value, 'year')
            break
    }
}
onMounted(() => {
    fullYear.value = Number(new Date().getFullYear())
    maxFullYear.value = fullYear.value + 10
    cycle01.value = fullYear.value
    cycle02.value = cycle01.value + 1
    average01.value = fullYear.value
    checkCopy.value = [fullYear.value]
})
</script>

<style lang="scss" scoped>
.el-input-number--small,
.el-select,
.el-select--small {
    margin: 0 0.2rem;
}

.el-select,
.el-select--small {
    width: 18.8rem;
}
</style>