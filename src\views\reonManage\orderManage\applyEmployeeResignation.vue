<!-- 申请员工离职 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" clearable />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderCode">
                <el-input class="width220" v-model="obj.queryParams.orderCode" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="客户编号:" prop="customerCode">
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入客户编号" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="接单方:" prop="orderReceiver">
                <el-select class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请选择" clearable>
                    <el-option v-for="item in receiverList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方客服:" prop="receiverService">
                <el-select class="width220" v-model="obj.queryParams.receiverService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单方:" prop="orderSender">
                <el-select class="width220" v-model="obj.queryParams.orderSender" placeholder="请选择" clearable>
                    <el-option v-for="item in senderList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单方客服:" prop="senderService">
                <el-select class="width220" v-model="obj.queryParams.senderService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="人员分布:" prop="personnelDistribution">
                <el-select class="width220" v-model="obj.queryParams.personnelDistribution" placeholder="请选择" clearable>
                    <el-option v-for="item in distributionList" :key="item.code" :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-input class="width220" v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称"
                    clearable />
            </el-form-item>
            <el-form-item label="订单状态:" prop="orderStatus">
                <el-select class="width220" v-model="obj.queryParams.orderStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in orderStatusList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleDetail">订单详情</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleApply">申报离职</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleSimilar">类似设定</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleModify">修改离职驳回信息</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleCancel">取消减员</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="小合同编号" align="center" prop="smallContractCode" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="供应商账单模板" align="center" prop="supplierBillingTemplate" />
            <el-table-column label="供应商收费模板" align="center" prop="supplierChargeTemplate" />
            <el-table-column label="城市" align="center" prop="city" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" plain @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 绑定供应商账单模版/报价单 -->
        <order-details v-model:dialogShow="obj.dialogShow" :dialogForm="obj.dialogForm" :title="obj.title"
            isDetail="true" />
    </div>
</template>

<script setup name="ApplyEmployeeResignation">

import { listScale } from "@/api/reonApi/scale";
import orderDetails from '../components/dialog/orderDetails.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客服列表
const serviceList = ref([]);
// 派单方列表
const senderList = ref([]);
// 接单方列表
const receiverList = ref([]);
// 人员分布列表
const distributionList = ref([]);
// 订单状态列表
const orderStatusList = ref([]);

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null,
        orderCode: null,
        employeeName: null,
        customerCode: null,
        customerName: null,
        idNumber: null,
        orderReceiver: null,
        receiverService: null,
        orderSender: null,
        senderService: null,
        personnelDistribution: null,
        smallContractName: null,
        orderStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {},//导入表单
    dialogShow: false,//导入弹窗
    bindSave: false,//绑定保存
    ids: [],//选中的id
    title: "",//标题
})

/** 获取客服列表 */
function getServiceList() {
    // 这里可以调用API获取客服列表
    serviceList.value = [
        { code: '1', name: '客服1' },
        { code: '2', name: '客服2' },
        { code: '3', name: '客服3' }
    ];
}

/** 获取派单方列表 */
function getSenderList() {
    // 这里可以调用API获取派单方列表
    senderList.value = [
        { code: '1', name: '派单方1' },
        { code: '2', name: '派单方2' },
        { code: '3', name: '派单方3' }
    ];
}

/** 获取接单方列表 */
function getReceiverList() {
    // 这里可以调用API获取接单方列表
    receiverList.value = [
        { code: '1', name: '接单方1' },
        { code: '2', name: '接单方2' },
        { code: '3', name: '接单方3' }
    ];
}

/** 获取人员分布列表 */
function getDistributionList() {
    // 这里可以调用API获取人员分布列表
    distributionList.value = [
        { code: '1', name: '分布1' },
        { code: '2', name: '分布2' },
        { code: '3', name: '分布3' }
    ];
}

/** 获取订单状态列表 */
function getOrderStatusList() {
    // 这里可以调用API获取订单状态列表
    orderStatusList.value = [
        { code: '1', name: '正常' },
        { code: '2', name: '待审核' },
        { code: '3', name: '已驳回' },
        { code: '4', name: '已取消' }
    ];
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                smallContractCode: 'XHT20230001',
                smallContractName: '小合同名称1',
                contractCode: 'HT20230001',
                contractName: '合同名称1',
                supplierBillingTemplate: '账单模板1',
                supplierChargeTemplate: '收费模板1',
                city: '北京'
            },
            {
                id: 2,
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                smallContractCode: 'X**********',
                smallContractName: '小合同名称2',
                contractCode: '**********',
                contractName: '合同名称2',
                supplierBillingTemplate: '账单模板2',
                supplierChargeTemplate: '收费模板2',
                city: '上海'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {

    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 订单详情
function handleDetail(row) {
    obj.dialogShow = true;
    obj.title = '订单详情';
    if (row) {
        // 如果是从表格行点击进入，可以加载该行的数据
        obj.dialogForm = { ...row };
    }
}

// 申报离职
function handleApply() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要申报离职的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 类似设定
function handleSimilar() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要设定的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 修改离职驳回信息
function handleModify() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要修改的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 取消减员
function handleCancel() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要取消减员的订单');
        return;
    }
    proxy.$modal.msgInfo('该功能正在开发中');
}

// 初始化数据
getServiceList();
getSenderList();
getReceiverList();
getDistributionList();
getOrderStatusList();
getList();
</script>
<style lang="scss" scoped></style>