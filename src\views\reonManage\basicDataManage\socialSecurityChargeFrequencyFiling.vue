<!-- 社保收费频率备案 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="城市:" prop="cityCode">
                <el-select class="width220" filterable v-model="obj.queryParams.cityCode" placeholder="请选择城市" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractNo">
                <el-input class="width220" v-model="obj.queryParams.contractNo" placeholder="请输入合同编号" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" @focus="handleCustomer"
                    placeholder="请输入客户名称" />
            </el-form-item>
            <el-form-item label="接单方:" prop="orderReceiver">
                <el-select class="width220" filterable v-model="obj.queryParams.orderReceiver" placeholder="请选择接单方"
                    clearable>
                    <el-option v-for="item in orderReceiverOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="View" :disabled="obj.single" @click="handleUpdate">查看</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" type="index" width="80" align="center" />
            <el-table-column label="城市名称" align="center" prop="cityName" />
            <el-table-column label="合同编号" align="center" prop="contractNo" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="接单方" align="center" prop="orderReceiver" />
            <el-table-column label="备案内容" align="center" prop="recordContent" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button text type="success" plain icon="View" @click="handleView(scope.row)">查看</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="25%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item label="城市名称" prop="cityCode">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.cityCode" placeholder="请选择城市" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同名称" prop="contractName">
                    <el-input readonly style="width: 100%;" v-model="obj.dialogForm.contractName" placeholder="请选择合同"
                        @click="handleCustomer" />
                </el-form-item>
                <el-form-item label="合同编号" prop="contractNo">
                    <el-input readonly style="width: 100%;" v-model="obj.dialogForm.contractNo" placeholder="自动带出" />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-input readonly style="width: 100%;" v-model="obj.dialogForm.customerName" placeholder="自动带出" />
                </el-form-item>
                <el-form-item label="接单方" prop="orderReceiver">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.orderReceiver" placeholder="请选择接单方"
                        clearable>
                        <el-option v-for="item in orderReceiverOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="文件上传" prop="fileUpload">
                    <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
                </el-form-item>
                <el-form-item label="备案内容" prop="recordContent">
                    <el-input style="width: 100%;" type="textarea" v-model="obj.dialogForm.recordContent"
                        placeholder="请输入备案内容" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <Client v-model:show="clientShow" @select="handleSelect" />
    </div>
</template>

<script setup name="SocialSecurityChargeFrequencyFiling">

import { useAreaStore } from '@/store/modules/area'
import { listImport } from "@/api/reonApi/import";
import Client from '@/views/reonManage/components/client.vue';

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据



// 接单方选项
const orderReceiverOptions = [
    { value: '1', label: '总公司' },
    { value: '2', label: '分公司' },
    { value: '3', label: '分支机构' },
    { value: '4', label: '业务部门' }
];


const clientShow = ref(false);
const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        cityCode: null,
        contractNo: null,
        customerName: null,
        orderReceiver: null,
    },//查询表单
    customerQueryParams: {
        pageNum: 1,
        pageSize: 10,
    },//客户查询表单
    rules: {
        cityCode: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        contractName: [{ required: true, message: '请选择合同', trigger: 'blur' }],
        orderReceiver: [{ required: true, message: '请选择接单方', trigger: 'blur' }],
        recordContent: [{ required: true, message: '请输入备案内容', trigger: 'blur' }],
    },
    total: 0,//总条数
    customerTotal: 0,//客户总条数
    tableData: [
        {
            id: 1,
            cityName: '北京市',
            contractNo: 'HT20230501001',
            contractType: '社保服务合同',
            customerName: '客户A',
            orderReceiver: '总公司',
            recordContent: '社保收费频率备案内容A'
        },
        {
            id: 2,
            cityName: '上海市',
            contractNo: 'HT20230502001',
            contractType: '社保服务合同',
            customerName: '客户B',
            orderReceiver: '分公司',
            recordContent: '社保收费频率备案内容B'
        },
        {
            id: 3,
            cityName: '广州市',
            contractNo: 'HT20230503001',
            contractType: '社保服务合同',
            customerName: '客户C',
            orderReceiver: '分支机构',
            recordContent: '社保收费频率备案内容C'
        }
    ],//列表
    customerTableData: [
        {
            id: 1,
            contractNo: 'HT20230501001',
            contractName: '社保服务合同A',
            customerNo: 'KH20230501001',
            customerName: '客户A',
            contractType: '社保服务合同'
        },
        {
            id: 2,
            contractNo: 'HT20230502001',
            contractName: '社保服务合同B',
            customerNo: 'KH20230502001',
            customerName: '客户B',
            contractType: '社保服务合同'
        },
        {
            id: 3,
            contractNo: 'HT20230503001',
            contractName: '社保服务合同C',
            customerNo: 'KH20230503001',
            customerName: '客户C',
            contractType: '社保服务合同'
        }
    ],//客户列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
})
/** 选择 */
function handleSelect(row) {
    if (row) {
        obj.queryParams.customerName = row.name;
    } else {
        obj.queryParams.customerName = null;
    }
}

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = {
        ...row,
        cityCode: row.cityName === '北京市' ? '110000' : row.cityName === '上海市' ? '310000' : '440100'
    };
    obj.dialogShow = true;
    obj.title = "修改";
}

/** 客户信息 */
function handleCustomer() {
    clientShow.value = true;
}

/** 查看按钮操作 */
function handleView(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = {
        ...row,
        cityCode: row.cityName === '北京市' ? '110000' : row.cityName === '上海市' ? '310000' : '440100'
    };
    obj.dialogShow = true;
    obj.title = "查看";
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}


getList();
</script>
<style lang="scss" scoped></style>