<!-- 消息中心 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="消息编号:" prop="messageId">
                <el-input class="width220" v-model="obj.queryParams.messageId" placeholder="请输入消息编号" clearable />
            </el-form-item>
            <el-form-item label="消息类型:" prop="messageType">
                <el-select class="width220" v-model="obj.queryParams.messageType" placeholder="请选择" clearable>
                    <el-option v-for="item in messageTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否已读:" prop="isRead">
                <el-select class="width220" v-model="obj.queryParams.isRead" placeholder="请选择" clearable>
                    <el-option v-for="item in readStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="创建时间:" prop="dateRange">
                <el-date-picker class="width320" v-model="obj.queryParams.dateRange" type="daterange"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD"
                    clearable />
            </el-form-item>
            <el-form-item label="消息内容:" prop="content">
                <el-input class="width220" v-model="obj.queryParams.content" placeholder="请输入消息内容关键词" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Check" @click="handleRead"
                    :disabled="obj.multiple">标记已读</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Select" @click="handleReadAll">全部已读</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出数据</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleRowClick"
            :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="55" />
            <el-table-column label="消息编号" align="center" prop="messageId" width="100" />
            <el-table-column label="消息标题" align="center" prop="title" min-width="150" />
            <el-table-column label="消息类型" align="center" prop="messageType" width="100">
                <template #default="scope">
                    <el-tag :type="getMessageTypeTag(scope.row.messageType)">
                        {{ getMessageTypeLabel(scope.row.messageType) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="消息接收人" align="center" prop="receiver" width="120" />
            <el-table-column label="是否已读" align="center" prop="isRead" width="80">
                <template #default="scope">
                    <el-tag :type="scope.row.isRead === '1' ? 'success' : 'info'">
                        {{ scope.row.isRead === '1' ? '已读' : '未读' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="消息内容" align="center" prop="content" min-width="200" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="160" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 消息详情对话框 -->
        <el-dialog :title="obj.detailTitle" v-model="obj.detailDialogShow" width="600px" append-to-body>
            <div class="message-detail">
                <div class="message-header">
                    <h3 class="message-title">{{ obj.currentMessage.title }}</h3>
                    <div class="message-meta">
                        <span class="message-type">
                            <el-tag :type="getMessageTypeTag(obj.currentMessage.messageType)" size="small">
                                {{ getMessageTypeLabel(obj.currentMessage.messageType) }}
                            </el-tag>
                        </span>
                        <span class="message-time">{{ obj.currentMessage.createTime }}</span>
                    </div>
                </div>
                <div class="message-body">
                    <p>{{ obj.currentMessage.content }}</p>
                </div>
                <div class="message-footer">
                    <span class="message-sender">发送人: {{ obj.currentMessage.sender || '系统消息' }}</span>
                    <span class="message-receiver">接收人: {{ obj.currentMessage.receiver }}</span>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="obj.detailDialogShow = false">关闭</el-button>
                    <el-button v-if="obj.currentMessage.isRead === '0'" type="primary"
                        @click="handleMarkReadInDialog">标记已读</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>



const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 消息类型选项
const messageTypeOptions = ref([
    { value: '1', label: '系统通知' },
    { value: '2', label: '业务提醒' },
    { value: '3', label: '任务通知' },
    { value: '4', label: '警告信息' },
    { value: '5', label: '其他' }
])

// 是否已读选项
const readStatusOptions = ref([
    { value: '0', label: '未读' },
    { value: '1', label: '已读' }
])

// 响应式数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        messageId: '',
        messageType: '',
        isRead: '',
        dateRange: [],
        content: ''
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    ids: [], // 选中的id

    // 消息详情对话框
    detailDialogShow: false,
    detailTitle: '消息详情',
    currentMessage: {}
})

/**
 * 获取消息类型标签类型
 * @param {string} type 消息类型值
 * @returns {string} 标签类型
 */
function getMessageTypeTag(type) {
    switch (type) {
        case '1': return '';
        case '2': return 'success';
        case '3': return 'warning';
        case '4': return 'danger';
        default: return 'info';
    }
}

/**
 * 获取消息类型标签文本
 * @param {string} type 消息类型值
 * @returns {string} 标签文本
 */
function getMessageTypeLabel(type) {
    const option = messageTypeOptions.value.find(item => item.value === type);
    return option ? option.label : '未知类型';
}

/**
 * 获取消息列表
 */
function getList() {
    obj.loading = true;

    // 模拟数据
    setTimeout(() => {
        // 生成模拟数据
        const mockData = [];
        for (let i = 0; i < 10; i++) {
            mockData.push({
                id: i + 1,
                messageId: `MSG${String(1000 + i).padStart(4, '0')}`,
                title: `消息标题 ${i + 1}`,
                messageType: String((i % 5) + 1),
                receiver: `用户${i + 1}`,
                isRead: i < 5 ? '1' : '0',
                content: `这是消息内容示例，用于测试消息中心功能。消息内容 ${i + 1}`,
                createTime: '2023-05-15 14:30:00',
                sender: i % 2 === 0 ? '系统管理员' : null
            });
        }

        // 应用查询条件过滤
        let filteredData = [...mockData];
        const params = obj.queryParams;

        if (params.messageId) {
            filteredData = filteredData.filter(item => item.messageId.includes(params.messageId));
        }

        if (params.messageType) {
            filteredData = filteredData.filter(item => item.messageType === params.messageType);
        }

        if (params.isRead) {
            filteredData = filteredData.filter(item => item.isRead === params.isRead);
        }

        if (params.content) {
            filteredData = filteredData.filter(item =>
                item.title.includes(params.content) ||
                item.content.includes(params.content)
            );
        }

        if (params.dateRange && params.dateRange.length === 2) {
            // 实际项目中需要进行日期范围过滤
            // 这里简化处理，不做实际过滤
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 500);
}

/**
 * 表格行点击事件
 * @param {Object} row 行数据
 */
function handleRowClick(row) {
    handleViewDetail(row);
}

/**
 * 查看消息详情
 * @param {Object} row 行数据
 */
function handleViewDetail(row) {
    obj.currentMessage = { ...row };
    obj.detailDialogShow = true;

    // 如果是未读消息，自动标记为已读
    if (row.isRead === '0') {
        // 在实际项目中，这里应该调用API标记消息为已读
        setTimeout(() => {
            row.isRead = '1';
            obj.currentMessage.isRead = '1';
            // 更新表格中的数据
            obj.tableData = [...obj.tableData];
        }, 500);
    }
}

/**
 * 标记单条消息为已读
 * @param {Object} row 行数据
 */
function handleMarkRead(row) {
    proxy.$modal.msgSuccess(`消息 "${row.title}" 已标记为已读`);
    row.isRead = '1';
    // 更新表格中的数据
    obj.tableData = [...obj.tableData];
}

/**
 * 在对话框中标记消息为已读
 */
function handleMarkReadInDialog() {
    proxy.$modal.msgSuccess(`消息 "${obj.currentMessage.title}" 已标记为已读`);

    // 更新当前消息状态
    obj.currentMessage.isRead = '1';

    // 更新表格中的数据
    const index = obj.tableData.findIndex(item => item.id === obj.currentMessage.id);
    if (index !== -1) {
        obj.tableData[index].isRead = '1';
        obj.tableData = [...obj.tableData];
    }

    // 关闭对话框
    obj.detailDialogShow = false;
}

/**
 * 标记选中消息为已读
 */
function handleRead() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgInfo('请选择要标记的消息');
        return;
    }

    ElMessageBox.confirm(`确定要将选中的 ${obj.ids.length} 条消息标记为已读吗？`, '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        // 更新选中消息的状态
        obj.tableData.forEach(item => {
            if (obj.ids.includes(item.id)) {
                item.isRead = '1';
            }
        });

        // 刷新表格
        obj.tableData = [...obj.tableData];

        proxy.$modal.msgSuccess(`已成功标记 ${obj.ids.length} 条消息为已读`);
    }).catch(() => { });
}

/**
 * 标记所有消息为已读
 */
function handleReadAll() {
    const unreadCount = obj.tableData.filter(item => item.isRead === '0').length;

    if (unreadCount === 0) {
        proxy.$modal.msgInfo('没有未读消息');
        return;
    }

    ElMessageBox.confirm(`确定要将所有 ${unreadCount} 条未读消息标记为已读吗？`, '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        // 更新所有消息的状态
        obj.tableData.forEach(item => {
            item.isRead = '1';
        });

        // 刷新表格
        obj.tableData = [...obj.tableData];

        proxy.$modal.msgSuccess(`已成功标记所有消息为已读`);
    }).catch(() => { });
}

/**
 * 删除单条消息
 * @param {Object} row 行数据
 */
function handleDeleteSingle(row) {
    ElMessageBox.confirm(`确定要删除消息 "${row.title}" 吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        // 从表格中移除该消息
        const index = obj.tableData.findIndex(item => item.id === row.id);
        if (index !== -1) {
            obj.tableData.splice(index, 1);
            obj.tableData = [...obj.tableData];
            obj.total = obj.tableData.length;
        }

        proxy.$modal.msgSuccess('删除成功');
    }).catch(() => { });
}

/**
 * 导出数据
 */
function handleExport() {
    // 显示加载中提示
    const loading = ElMessage({
        message: '正在导出数据，请稍候...',
        type: 'info',
        duration: 0
    });

    // 模拟导出过程
    setTimeout(() => {
        // 关闭加载中提示
        loading.close();

        // 构建导出文件名
        const fileName = `消息中心数据_${new Date().getTime()}.xlsx`;

        // 显示成功提示
        proxy.$modal.msgSuccess({
            message: `数据导出成功，文件名：${fileName}`,
            duration: 5000
        });
    }, 2000);
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/**
 * 重置按钮操作
 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    obj.queryParams = {
        pageNum: 1,
        pageSize: 10,
        messageId: '',
        messageType: '',
        isRead: '',
        dateRange: [],
        content: ''
    };
    handleQuery();
}

/**
 * 多选框选中数据
 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/**
 * 表格行类名
 * @param {Object} row 行数据
 * @returns {string} 类名
 */
function tableRowClassName({ row }) {
    return row.isRead === '0' ? 'unread-message' : '';
}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped>
// 消息详情样式
.message-detail {
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 4px;

    .message-header {
        margin-bottom: 20px;
        border-bottom: 1px solid #eee;
        padding-bottom: 15px;

        .message-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 10px 0;
            color: #333;
        }

        .message-meta {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #666;

            .message-type {
                margin-right: 10px;
            }

            .message-time {
                color: #999;
            }
        }
    }

    .message-body {
        margin-bottom: 20px;
        line-height: 1.6;
        color: #333;

        p {
            margin: 0;
            padding: 0;
        }
    }

    .message-footer {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        color: #666;
        border-top: 1px solid #eee;
        padding-top: 15px;
    }
}

// 表格行样式
:deep(.el-table__row) {
    cursor: pointer;

    &:hover {
        background-color: #f5f7fa;
    }
}

// 未读消息行样式
:deep(.el-table__row.unread-message) {
    font-weight: bold;
    background-color: #f0f9eb;

    &:hover {
        background-color: #e6f3d0;
    }
}
</style>