<!-- 供应商薪资导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateFrom" type="date"
                    placeholder="请选择日期" clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateTo" type="date" placeholder="请选择日期"
                    clearable value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item label="导入人:" prop="importUser">
                <el-select class="width220" v-model="obj.queryParams.importUser" placeholder="请选择" clearable>
                    <el-option v-for="item in importUserOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="导入编号:" prop="importCode">
                <el-input class="width220" v-model="obj.queryParams.importCode" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="warning" plain icon='Upload' @click="handleExport">导入</el-button>
                <el-button type="primary" plain icon='Download' @click="handleDownloadTemplate">下载模版</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <ExportTable2 :tableData="obj.tableData" :loading="obj.loading" :total="obj.total"
            :page="obj.queryParams.pageNum" :limit="obj.queryParams.pageSize" @row-dblclick="handleRowDblClick"
            @handlePagination="handlePagination" />
        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" title="上传导入数据" type="supplierPayrollIntroduction"
            :dialogForm="obj.dialogForm" :rules="rules" />
        <!-- 历史信息查看 -->
        <HistoricalInformation v-model:dialogShow="obj.dialogShow2" :data="obj.tableData2" />

    </div>
</template>

<script setup name="SupplierPayrollIntroduction">



const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



// 导入人选项
const importUserOptions = [
    { value: '1', label: '管理员' },
    { value: '2', label: '操作员A' },
    { value: '3', label: '操作员B' }
];

// 处理状态选项
const statusOptions = [
    { value: '0', label: '未处理' },
    { value: '1', label: '处理中' },
    { value: '2', label: '处理完成' },
    { value: '3', label: '处理失败' }
];

// 文件列表
const fileList = ref([]);

// 表单验证规则
const rules = {
    remark: [
        { required: false, message: '请输入备注', trigger: 'blur' }
    ],
    fileList: [
        { required: true, message: '请上传文件', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateFrom: null, // 导入日期从
        importDateTo: null, // 导入日期到
        importUser: null, // 导入人
        importCode: null, // 导入编号
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {
        remark: '',
        fileList: []
    }, // 表单
    dialogShow: false, // 显示对话框
    title: "", // 标题
    ids: [], // 选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                importCode: 'IMP20230501',
                importUser: '管理员',
                importTime: '2023-05-10 10:30:00',
                remark: '五月供应商薪资导入',
                successCount: 120,
                failCount: 5,
                fileName: '供应商薪资导入表_20230501.xlsx',
                processStatus: '2',
                creator: '管理员',
                createTime: '2023-05-10 10:30:00',
                updater: '管理员',
                updateTime: '2023-05-10 10:35:00'
            },
            {
                id: 2,
                importCode: 'IMP20230601',
                importUser: '操作员A',
                importTime: '2023-06-10 11:15:00',
                remark: '六月供应商薪资导入',
                successCount: 135,
                failCount: 0,
                fileName: '供应商薪资导入表_20230601.xlsx',
                processStatus: '2',
                creator: '操作员A',
                createTime: '2023-06-10 11:15:00',
                updater: '操作员A',
                updateTime: '2023-06-10 11:20:00'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}
// 双击行
function handleRowDblClick(row) {
    obj.dialogShow2 = true;
    obj.title = '历史信息查看';
}

// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}

// 数据导入
function handleExport() {
    obj.dialogShow = true;
    resetForm();
}

// 关闭导入
function handleClose() {
    obj.dialogShow = false;
}

// 下载模版
function handleDownloadTemplate() {
    proxy.$modal.msgSuccess('模版下载成功');
    // 实际项目中应该调用下载接口

    // 模拟下载文件
    const fileName = '供应商薪资导入模版.xlsx';
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 下载文件
function downloadFile(row) {
    proxy.$modal.msgSuccess('文件下载成功：' + row.fileName);
    // 实际项目中应该调用下载接口

    // 模拟下载文件
    const fileName = row.fileName;
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 查看历史信息
function handleHistoryInfo(row) {
    proxy.$modal.msgSuccess('查看历史信息：' + row.importCode);
    // 实际项目中应该跳转到详情页面
}

// 表单重置
function resetForm() {
    obj.dialogForm = {
        remark: '',
        fileList: []
    };
    fileList.value = [];
    proxy.resetForm("formRef");
}

// 获取状态类型
function getStatusType(status) {
    switch (status) {
        case '0': return 'info';
        case '1': return 'warning';
        case '2': return 'success';
        case '3': return 'danger';
        default: return '';
    }
}

// 获取状态名称
function getStatusName(status) {
    const statusMap = {
        '0': '未处理',
        '1': '处理中',
        '2': '处理完成',
        '3': '处理失败'
    };
    return statusMap[status] || '未知状态';
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.width420 {
    width: 420px;
}

.mb8 {
    margin-bottom: 8px;
}

.dialog-footer {
    text-align: center;
    padding-top: 10px;
}

.upload-demo {
    width: 360px;
}
</style>