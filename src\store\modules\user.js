import { login, logout, getInfo } from "@/api/login";
import { getToken, setToken, removeToken } from "@/utils/auth";
import defAva from "@/assets/images/profile.jpg";

const useUserStore = defineStore("user", () => {
  // state
  const token = ref(getToken());
  const id = ref("");
  const name = ref("");
  const avatar = ref("");
  const roles = ref([]);
  const permissions = ref([]);

  // actions
  // 登录
  const loginAction = (userInfo) => {
    const username = userInfo.username.trim();
    const password = userInfo.password;
    const code = userInfo.code;
    const uuid = userInfo.uuid;
    return new Promise((resolve, reject) => {
      login(username, password, code, uuid)
        .then((res) => {
          setToken(res.token);
          token.value = res.token;
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 获取用户信息
  const getInfoAction = () => {
    return new Promise((resolve, reject) => {
      getInfo()
        .then((res) => {
          const user = res.user;
          const ava =
            user.avatar == "" || user.avatar == null
              ? defAva
              : import.meta.env.VITE_APP_BASE_API + user.avatar;

          if (res.roles && res.roles.length > 0) {
            // 验证返回的roles是否是一个非空数组
            roles.value = res.roles;
            permissions.value = res.permissions;
          } else {
            roles.value = ["ROLE_DEFAULT"];
          }
          id.value = user.userId;
          name.value = user.userName;
          avatar.value = ava;
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 退出系统
  const logOut = () => {
    return new Promise((resolve, reject) => {
      logout(token.value)
        .then(() => {
          token.value = "";
          roles.value = [];
          permissions.value = [];
          removeToken();
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 为了兼容原有用法，导出与之前相同的字段与方法名
  return {
    token,
    id,
    name,
    avatar,
    roles,
    permissions,
    login: loginAction,
    getInfo: getInfoAction,
    logOut,
  };
});

export default useUserStore;

// import { login, logout, getInfo } from '@/api/login'
// import { getToken, setToken, removeToken } from '@/utils/auth'
// import defAva from '@/assets/images/profile.jpg'

// const useUserStore = defineStore(
//   'user',
//   {
//     state: () => ({
//       token: getToken(),
//       id: '',
//       name: '',
//       avatar: '',
//       roles: [],
//       permissions: []
//     }),
//     actions: {
//       // 登录
//       login(userInfo) {
//         const username = userInfo.username.trim()
//         const password = userInfo.password
//         const code = userInfo.code
//         const uuid = userInfo.uuid
//         return new Promise((resolve, reject) => {
//           login(username, password, code, uuid).then(res => {
//             setToken(res.token)
//             this.token = res.token
//             resolve()
//           }).catch(error => {
//             reject(error)
//           })
//         })
//       },
//       // 获取用户信息
//       getInfo() {
//         return new Promise((resolve, reject) => {
//           getInfo().then(res => {
//             const user = res.user
//             const avatar = (user.avatar == "" || user.avatar == null) ? defAva : import.meta.env.VITE_APP_BASE_API + user.avatar;

//             if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
//               this.roles = res.roles
//               this.permissions = res.permissions
//             } else {
//               this.roles = ['ROLE_DEFAULT']
//             }
//             this.id = user.userId
//             this.name = user.userName
//             this.avatar = avatar
//             resolve(res)
//           }).catch(error => {
//             reject(error)
//           })
//         })
//       },
//       // 退出系统
//       logOut() {
//         return new Promise((resolve, reject) => {
//           logout(this.token).then(() => {
//             this.token = ''
//             this.roles = []
//             this.permissions = []
//             removeToken()
//             resolve()
//           }).catch(error => {
//             reject(error)
//           })
//         })
//       }
//     }
//   })

// export default useUserStore
