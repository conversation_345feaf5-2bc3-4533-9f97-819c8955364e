import useDictStore from "@/store/modules/dict";
import { getDicts } from "@/api/system/dict/data";

/**
 * 自定义 hook，用于获取和缓存字典数据
 * @param {...string} args - 一个或多个字典类型的字符串参数
 * @returns {Object} - 包含字典数据的响应式对象
 */
export function useDict(...args) {
  // 创建一个响应式对象来存储字典数据
  const res = ref({});

  // 返回一个立即执行的函数，该函数会遍历传入的字典类型参数
  return (() => {
    args.forEach((dictType, index) => {
      // 初始化当前字典类型的数组
      res.value[dictType] = [];

      // 从 Vuex 状态管理库中获取当前字典类型的数据
      const dicts = useDictStore().getDict(dictType);

      // 如果字典数据已经存在于 Vuex 中，则直接使用
      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        // 如果字典数据不存在，则从 API 获取
        getDicts(dictType).then((resp) => {
          // 将 API 返回的数据转换为适合前端使用的格式
          res.value[dictType] = resp.data.map((p) => ({
            label: p.dictLabel,
            value: p.dictValue,
            elTagType: p.listClass,
            elTagClass: p.cssClass,
          }));

          // 将转换后的数据存储到 Vuex 中
          useDictStore().setDict(dictType, res.value[dictType]);
        });
      }
    });

    // 将响应式对象转换为普通对象，并返回
    return toRefs(res.value);
  })();
}
