<template>
    <el-form>
        <el-form-item>
            <el-radio v-model='radioValue' :value="1">
                周，允许的通配符[, - * ? / L #]
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="2">
                不指定
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="3">
                周期从
                <el-select clearable v-model="cycle01">
                    <el-option v-for="(item, index) of weekList" :key="index" :label="item.value" :value="item.key"
                        :disabled="item.key === 7">{{ item.value }}</el-option>
                </el-select>
                -
                <el-select clearable v-model="cycle02">
                    <el-option v-for="(item, index) of weekList" :key="index" :label="item.value" :value="item.key"
                        :disabled="item.key <= cycle01">{{ item.value }}</el-option>
                </el-select>
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="4">
                第
                <el-input-number v-model='average01' :min="1" :max="4" /> 周的
                <el-select clearable v-model="average02">
                    <el-option v-for="item in weekList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="5">
                本月最后一个
                <el-select clearable v-model="weekday">
                    <el-option v-for="item in weekList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="6">
                指定
                <el-select class="multiselect" clearable v-model="checkboxList" placeholder="可多选" multiple
                    :multiple-limit="6">
                    <el-option v-for="item in weekList" :key="item.key" :label="item.value" :value="item.key" />
                </el-select>
            </el-radio>
        </el-form-item>

    </el-form>
</template>

<script setup>
const emit = defineEmits(['update'])
const props = defineProps({
    cron: {
        type: Object,
        default: {
            second: "*",
            min: "*",
            hour: "*",
            day: "*",
            month: "*",
            week: "?",
            year: ""
        }
    },
    check: {
        type: Function,
        default: () => {
        }
    }
})
const radioValue = ref(2)
const cycle01 = ref(2)
const cycle02 = ref(3)
const average01 = ref(1)
const average02 = ref(2)
const weekday = ref(2)
const checkboxList = ref([])
const checkCopy = ref([2])
const weekList = ref([
    { key: 1, value: '星期日' },
    { key: 2, value: '星期一' },
    { key: 3, value: '星期二' },
    { key: 4, value: '星期三' },
    { key: 5, value: '星期四' },
    { key: 6, value: '星期五' },
    { key: 7, value: '星期六' }
])
const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, 1, 6)
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, 7)
    return cycle01.value + '-' + cycle02.value
})
const averageTotal = computed(() => {
    average01.value = props.check(average01.value, 1, 4)
    average02.value = props.check(average02.value, 1, 7)
    return average02.value + '#' + average01.value
})
const weekdayTotal = computed(() => {
    weekday.value = props.check(weekday.value, 1, 7)
    return weekday.value + 'L'
})
const checkboxString = computed(() => {
    return checkboxList.value.join(',')
})
watch(() => props.cron.week, value => changeRadioValue(value))
watch([radioValue, cycleTotal, averageTotal, weekdayTotal, checkboxString], () => onRadioChange())
/**
 * 根据不同的输入值类型改变radioValue的值
 * 此函数旨在解析不同格式的输入值，并根据格式设置相应的radioValue和其它相关值
 * @param {String} value - 输入值，可以是 "*", "?", "a-b" 形式, "a#b" 形式, "aL" 形式 或以逗号分隔的数字列表
 */
function changeRadioValue(value) {
    // 当输入值为"*"时，设置radioValue为1
    if (value === "*") {
        radioValue.value = 1
        // 当输入值为"?"时，设置radioValue为2
    } else if (value === "?") {
        radioValue.value = 2
        // 当输入值包含"-"时，表示一个范围，解析范围并设置cycle01和cycle02的值，同时设置radioValue为3
    } else if (value.indexOf("-") > -1) {
        const indexArr = value.split('-')
        cycle01.value = Number(indexArr[0])
        cycle02.value = Number(indexArr[1])
        radioValue.value = 3
        // 当输入值包含"#"时，表示一个特定的平均值计算方式，解析并设置average01和average02的值，同时设置radioValue为4
    } else if (value.indexOf("#") > -1) {
        const indexArr = value.split('#')
        average01.value = Number(indexArr[1])
        average02.value = Number(indexArr[0])
        radioValue.value = 4
        // 当输入值包含"L"时，表示一个特定的星期计算方式，解析并设置weekday的值，同时设置radioValue为5
    } else if (value.indexOf("L") > -1) {
        const indexArr = value.split("L")
        weekday.value = Number(indexArr[0])
        radioValue.value = 5
        // 当输入值为以逗号分隔的数字列表时，将其解析为一个数字数组并设置为checkboxList的值，同时设置radioValue为6
    } else {
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))]
        radioValue.value = 6
    }
}
/**
 * 处理单选按钮变化事件
 * 根据单选按钮的值和cron表达式的day字段值，更新day和week字段
 * 这个函数覆盖了多种情况，以确保cron表达式的正确性
 */
function onRadioChange() {
    // 当单选按钮值为2且day为'?'时，将day设置为'*'
    if (radioValue.value === 2 && props.cron.day === '?') {
        emit('update', 'day', '*', 'week')
    }
    // 当单选按钮值不为2且day不为'?'时，将day设置为'?'
    if (radioValue.value !== 2 && props.cron.day !== '?') {
        emit('update', 'day', '?', 'week')
    }
    // 根据单选按钮的值更新week字段
    switch (radioValue.value) {
        case 1:
            // 设置week为'*'
            emit('update', 'week', '*', 'week')
            break
        case 2:
            // 设置week为'?'
            emit('update', 'week', '?', 'week')
            break
        case 3:
            // 设置week为cycleTotal的值
            emit('update', 'week', cycleTotal.value, 'week')
            break
        case 4:
            // 设置week为averageTotal的值
            emit('update', 'week', averageTotal.value, 'week')
            break
        case 5:
            // 设置week为weekdayTotal的值
            emit('update', 'week', weekdayTotal.value, 'week')
            break
        case 6:
            // 处理checkbox列表，确保至少有一个默认值，并更新week字段
            if (checkboxList.value.length === 0) {
                checkboxList.value.push(checkCopy.value[0])
            } else {
                checkCopy.value = checkboxList.value
            }
            emit('update', 'week', checkboxString.value, 'week')
            break
    }
}
</script>

<style lang="scss" scoped>
.el-input-number--small,
.el-select,
.el-select--small {
    margin: 0 0.5rem;
}

.el-select,
.el-select--small {
    width: 8rem;
}

.el-select.multiselect,
.el-select--small.multiselect {
    width: 17.8rem;
}
</style>