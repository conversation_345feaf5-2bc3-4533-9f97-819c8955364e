<!-- 客户电话记录 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称或公司名称"
                    clearable />
            </el-form-item>
            <el-form-item label="通话日期:" prop="callDate">
                <el-date-picker class="width220" v-model="obj.queryParams.callDate" type="date" placeholder="请选择日期"
                    value-format="YYYY-MM-DD" clearable />
            </el-form-item>
            <el-form-item label="是否有需求:" prop="hasDemand">
                <el-select class="width220" v-model="obj.queryParams.hasDemand" placeholder="请选择" clearable>
                    <el-option v-for="item in demandOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleEdit">编辑</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Document" @click="handleDownload">下载模板</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户公司名称" align="center" prop="companyName" min-width="150" />
            <el-table-column label="联系人" align="center" prop="contactPerson" width="100" />
            <el-table-column label="联系电话" align="center" prop="contactPhone" width="120" />
            <el-table-column label="联系人邮箱" align="center" prop="contactEmail" width="150" />
            <el-table-column label="公司地址" align="center" prop="companyAddress" min-width="200" />
            <el-table-column label="通话日期" align="center" prop="callDate" width="100" />
            <el-table-column label="是否接通" align="center" width="80">
                <template #default="scope">
                    <el-tag :type="scope.row.isConnected === '1' ? 'success' : 'info'">
                        {{ scope.row.isConnected === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="是否有需求" align="center" width="100">
                <template #default="scope">
                    <el-tag :type="scope.row.hasDemand === '1' ? 'success' : 'info'">
                        {{ scope.row.hasDemand === '1' ? '是' : '否' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="产品" align="center" width="120">
                <template #default="scope">
                    {{ getProductLabels(scope.row.product) }}
                </template>
            </el-table-column>
            <el-table-column label="人数" align="center" prop="personCount" width="80" />
            <el-table-column label="报价" align="center" prop="quotation" width="100" />
            <el-table-column label="预计签约日期" align="center" prop="expectedSignDate" width="120" />
            <el-table-column label="销售" align="center" width="80">
                <template #default="scope">
                    {{ getSalesLabel(scope.row.sales) }}
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" min-width="150" />
            <el-table-column label="操作" align="center" width="120" fixed="right">
                <template #default="scope">
                    <el-button text type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 新增/编辑 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="60%" append-to-body draggable>
            <el-form :model="obj.dialogForm" ref="dialogRef" :rules="obj.rules" inline label-width="auto">
                <el-form-item label="联系人:" prop="contactPerson">
                    <el-input class="width220" v-model="obj.dialogForm.contactPerson" placeholder="请输入联系人" />
                </el-form-item>
                <el-form-item label="联系电话:" prop="contactPhone">
                    <el-input class="width220" v-model="obj.dialogForm.contactPhone" placeholder="请输入联系电话" />
                </el-form-item>
                <el-form-item label="联系人邮箱:" prop="contactEmail">
                    <el-input class="width220" v-model="obj.dialogForm.contactEmail" placeholder="请输入联系人邮箱" />
                </el-form-item>
                <el-form-item label="是否接通:" prop="isConnected">
                    <el-select class="width220" v-model="obj.dialogForm.isConnected" placeholder="请选择">
                        <el-option v-for="item in connectionOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否需要:" prop="hasDemand">
                    <el-select class="width220" v-model="obj.dialogForm.hasDemand" placeholder="请选择">
                        <el-option v-for="item in demandOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="产品类型:" prop="productType" v-if="obj.dialogForm.hasDemand === '1'">
                    <el-select class="width220" v-model="obj.dialogForm.productType" placeholder="请选择" multiple>
                        <el-option v-for="item in productOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="通话日期:" prop="callDate">
                    <el-date-picker class="width220" v-model="obj.dialogForm.callDate" type="date" placeholder="请选择通话日期"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="人数:" prop="personCount" v-if="obj.dialogForm.hasDemand === '1'">
                    <el-input class="width220" v-model="obj.dialogForm.personCount" placeholder="请输入人数" />
                </el-form-item>
                <el-form-item label="报价:" prop="quotation" v-if="obj.dialogForm.hasDemand === '1'">
                    <el-input class="width220" v-model="obj.dialogForm.quotation" placeholder="请输入报价" />
                </el-form-item>
                <el-form-item label="预计签约日期:" prop="expectedSignDate" v-if="obj.dialogForm.hasDemand === '1'">
                    <el-date-picker class="width220" v-model="obj.dialogForm.expectedSignDate" type="date"
                        placeholder="请选择预计签约日期" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="销售:" prop="sales">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择">
                        <el-option v-for="item in salesOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注:" prop="remark">
                    <el-input type="textarea" class="width420" v-model="obj.dialogForm.remark" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleSave">保存</el-button>
                <el-button @click="handleCancel">取消</el-button>
            </template>
        </el-dialog>

        <!-- 导入对话框 -->
        <UploadFile v-model:dialogShow="obj.importDialogShow" type="customerTelephoneRecord" title="上传导入数据"
            :dialogForm="obj.dialogForm" @close="handleClose" />
    </div>
</template>


<script setup name="CustomerTelephoneRecord">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 是否接通选项
const connectionOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' }
];

// 是否有需求选项
const demandOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' }
];

// 产品选项
const productOptions = [
    { value: '1', label: '商业医疗险' },
    { value: '2', label: '团体寿险' },
    { value: '3', label: '团体重疾险' },
    { value: '4', label: '团体意外险' },
    { value: '5', label: '其他保险' }
];

// 销售人员选项
const salesOptions = [
    { value: '1', label: '张三' },
    { value: '2', label: '李四' },
    { value: '3', label: '王五' },
    { value: '4', label: '赵六' },
    { value: '5', label: '孙七' }
];

// 客户电话记录数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: '', // 客户名称
        callDate: '', // 通话日期
        hasDemand: '' // 是否有需求
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            companyName: '北京科技有限公司',
            contactPerson: '王经理',
            contactPhone: '13800138000',
            contactEmail: '<EMAIL>',
            companyAddress: '北京市海淀区西二旗路100号',
            callDate: '2023-05-01',
            isConnected: '1',
            hasDemand: '1',
            product: '1',
            personCount: 50,
            quotation: '100000',
            expectedSignDate: '2023-06-01',
            sales: '1',
            remark: '客户对商业医疗险产品非常感兴趣，计划下周进一步洽谈'
        },
        {
            id: 2,
            companyName: '上海贸易有限公司',
            contactPerson: '刘总',
            contactPhone: '13900139000',
            contactEmail: '<EMAIL>',
            companyAddress: '上海市浦东新区张江路500号',
            callDate: '2023-05-02',
            isConnected: '1',
            hasDemand: '0',
            product: '',
            personCount: 0,
            quotation: '',
            expectedSignDate: '',
            sales: '2',
            remark: '客户目前没有需求，建议三个月后再跟进'
        }
    ],

    // 对话框相关数据
    dialogForm: {
        id: '',
        companyName: '',
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
        companyAddress: '',
        callDate: '',
        isConnected: '1',
        hasDemand: '0',
        product: '',
        personCount: 0,
        quotation: '',
        expectedSignDate: '',
        sales: '',
        remark: ''
    },
    dialogShow: false, // 对话框显示状态
    importDialogShow: false, // 导入对话框显示状态
    importForm: {
        file: null,
        remark: ''
    },
    ids: [], // 选中的id
    title: '', // 对话框标题
    rules: {
        companyName: [
            { required: true, message: '请输入客户公司名称', trigger: 'blur' }
        ],
        contactPerson: [
            { required: true, message: '请输入联系人', trigger: 'blur' }
        ],
        contactPhone: [
            { required: true, message: '请输入联系电话', trigger: 'blur' }
        ],
        callDate: [
            { required: true, message: '请选择通话日期', trigger: 'blur' }
        ],
        isConnected: [
            { required: true, message: '请选择是否接通', trigger: 'change' }
        ],
        hasDemand: [
            { required: true, message: '请选择是否有需求', trigger: 'change' }
        ]
    }
})

/**
* 获取产品标签
* @param {string} productIds 产品ID列表，逗号分隔
* @returns {string} 产品标签列表，逗号分隔
*/
function getProductLabels(productIds) {
    if (!productIds) return '';

    return productIds.split(',').map(id => {
        const product = productOptions.find(item => item.value === id);
        return product ? product.label : '';
    }).filter(Boolean).join(', ');
}

/**
* 获取销售人员标签
* @param {string} salesId 销售人员ID
* @returns {string} 销售人员名称
*/
function getSalesLabel(salesId) {
    if (!salesId) return '';

    const sales = salesOptions.find(item => item.value === salesId);
    return sales ? sales.label : '';
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item =>
                item.contactPerson.includes(obj.queryParams.customerName) ||
                item.companyName.includes(obj.queryParams.customerName)
            );
        }

        if (obj.queryParams.callDate) {
            filteredData = filteredData.filter(item =>
                item.callDate === obj.queryParams.callDate
            );
        }

        if (obj.queryParams.hasDemand) {
            filteredData = filteredData.filter(item =>
                item.hasDemand === obj.queryParams.hasDemand
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 编辑客户电话记录 */
function handleEdit(row) {
    const recordInfo = row || (obj.single ? null : obj.tableData.find(item => item.id ===
        obj.ids[0]));
    if (!recordInfo) {
        proxy.$modal.msgInfo('请选择要编辑的记录');
        return;
    }

    // 复制数据到表单
    obj.dialogForm = JSON.parse(JSON.stringify(recordInfo));
    obj.dialogShow = true;
    obj.title = "编辑客户电话记录";
}

/** 保存客户电话记录 */
function handleSave() {
    if (!proxy.$refs["dialogRef"]) return;

    proxy.$refs["dialogRef"].validate(valid => {
        if (valid) {
            // 模拟保存操作
            const isNew = !obj.dialogForm.id;

            if (isNew) {
                // 新增记录
                const newRecord = {
                    ...obj.dialogForm,
                    id: obj.tableData.length + 1
                };

                obj.tableData.unshift(newRecord);
                proxy.$modal.msgSuccess('新增成功');
            } else {
                // 编辑记录
                const index = obj.tableData.findIndex(item => item.id === obj.dialogForm.id);
                if (index !== -1) {
                    obj.tableData[index] = {
                        ...obj.tableData[index],
                        ...obj.dialogForm
                    };
                }
                proxy.$modal.msgSuccess('编辑成功');
            }

            obj.dialogShow = false;
            obj.total = obj.tableData.length;
        }
    });
}

/** 取消操作 */
function handleCancel() {
    obj.dialogShow = false;
}

/** 导入对话框 */
function handleImport() {
    obj.importDialogShow = true;
}

/** 关闭导入对话框 */
function handleClose() {
    obj.importDialogShow = false;
}


/** 导出数据 */
function handleExport() {
    proxy.$modal.msgSuccess('导出成功');
}

/** 下载模板 */
function handleDownload() {
    proxy.$modal.msgSuccess('模板下载成功');
}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped></style>
