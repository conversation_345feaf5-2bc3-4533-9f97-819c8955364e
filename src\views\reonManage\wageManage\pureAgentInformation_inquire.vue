<!-- 纯代发人员信息查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户账单月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择月份"
                    clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="派单客服:" prop="dispatchService">
                <el-select class="width220" v-model="obj.queryParams.dispatchService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资客服:" prop="salaryService">
                <el-select class="width220" v-model="obj.queryParams.salaryService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="6">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" @click="exportData">数据导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="合同编号" align="center" prop="contractNo" />
            <el-table-column label="签单公司" align="center" prop="signCompany" />
            <el-table-column label="派单公司" align="center" prop="dispatchCompany" />
            <el-table-column label="派单客服" align="center" prop="dispatchService" />
            <el-table-column label="薪资客服" align="center" prop="salaryService" />
            <el-table-column label="客户账单月" align="center" prop="billMonth" />
            <el-table-column label="工资计税月" align="center" prop="taxMonth" />
            <el-table-column label="发放地" align="center" prop="paymentLocation" />
            <el-table-column label="扣缴义务人编号" align="center" prop="withholdingAgentId" />
            <el-table-column label="员工姓名" align="center" prop="employeeName" />
            <el-table-column label="税率" align="center" prop="taxRate">
                <template #default="scope">
                    {{ scope.row.taxRate ? (scope.row.taxRate * 100).toFixed(2) + '%' : '' }}
                </template>
            </el-table-column>
            <el-table-column label="含税服务费" align="center" prop="serviceFeeWithTax">
                <template #default="scope">
                    {{ scope.row.serviceFeeWithTax ? '¥' + scope.row.serviceFeeWithTax.toFixed(2) : '' }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="PureAgentInformationInquire">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客户选项
const customerOptions = [
    { value: 'C001', label: '北京科技有限公司' },
    { value: 'C002', label: '上海贸易有限公司' },
    { value: 'C003', label: '广州电子有限公司' },
    { value: 'C004', label: '深圳科技有限公司' },
    { value: 'C005', label: '成都信息有限公司' }
];

// 客服选项
const serviceOptions = [
    { value: 'S001', label: '张三' },
    { value: 'S002', label: '李四' },
    { value: 'S003', label: '王五' },
    { value: 'S004', label: '赵六' },
    { value: 'S005', label: '孙七' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        billMonth: null, // 客户账单月
        customerName: null, // 客户名称
        dispatchService: null, // 派单客服
        salaryService: null, // 薪资客服
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    ids: [], // 选中的id
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        obj.tableData = [
            {
                id: 1,
                customerName: '北京科技有限公司',
                contractName: '人力资源服务合同',
                contractNo: 'HT20230001',
                signCompany: '北京签约公司',
                dispatchCompany: '北京派单公司',
                dispatchService: '张三',
                salaryService: '李四',
                billMonth: '2023-06',
                taxMonth: '2023-06',
                paymentLocation: '北京',
                withholdingAgentId: 'WH001',
                employeeName: '王五',
                taxRate: 0.1,
                serviceFeeWithTax: 1000.00
            },
            {
                id: 2,
                customerName: '上海贸易有限公司',
                contractName: '人力资源服务合同',
                contractNo: 'HT20230002',
                signCompany: '上海签约公司',
                dispatchCompany: '上海派单公司',
                dispatchService: '王五',
                salaryService: '赵六',
                billMonth: '2023-06',
                taxMonth: '2023-06',
                paymentLocation: '上海',
                withholdingAgentId: 'WH002',
                employeeName: '孙七',
                taxRate: 0.2,
                serviceFeeWithTax: 2000.00
            },
            {
                id: 3,
                customerName: '广州电子有限公司',
                contractName: '人力资源服务合同',
                contractNo: 'HT20230003',
                signCompany: '广州签约公司',
                dispatchCompany: '广州派单公司',
                dispatchService: '赵六',
                salaryService: '孙七',
                billMonth: '2023-06',
                taxMonth: '2023-06',
                paymentLocation: '广州',
                withholdingAgentId: 'WH003',
                employeeName: '周八',
                taxRate: 0.3,
                serviceFeeWithTax: 3000.00
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 数据导出 */
function exportData() {
    // 检查是否有选中记录
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请至少选择一条记录进行导出");
        return;
    }

    // 模拟导出操作
    proxy.$modal.loading("正在导出数据，请稍候...");
    setTimeout(() => {
        proxy.$modal.closeLoading();
        proxy.$modal.msgSuccess("导出成功，共导出 " + obj.ids.length + " 条记录");
    }, 1000);
}


getList();
</script>
<style lang="scss" scoped></style>