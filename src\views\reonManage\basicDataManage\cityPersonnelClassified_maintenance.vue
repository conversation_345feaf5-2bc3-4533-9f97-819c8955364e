<!-- 城市人员分类维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="所属城市:">
                <el-select class="width220" filterable v-model="obj.queryParams.selectarea" placeholder="请选择城市">
                    <el-option v-for="item in obj.cityList" :key="item.code" :label="item.name"
                        :value="item.code"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="分类名称:">
                <el-input class="width220" v-model="obj.queryParams.searchName" placeholder="请输入分类名称" clearable
                    @keyup.enter="handleQuery"></el-input>
            </el-form-item>
            <el-form-item label="分类编码:">
                <el-input class="width220" v-model="obj.queryParams.searchCode" placeholder="请输入分类编码" clearable
                    @keyup.enter="handleQuery"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple" @click="handleDelete">删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">数据导出</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="所属城市" align="center" prop="cityName"></el-table-column>
            <el-table-column label="人员分类名称" align="center" prop="classifyName"></el-table-column>
            <el-table-column label="人员分类编号" align="center" prop="categoryCode"></el-table-column>
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
                    <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <el-pagination v-if="obj.total" v-model:current-page="obj.queryParams.page"
            v-model:page-size="obj.queryParams.limit" :page-sizes="[20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="obj.total" @size-change="handleSizeChange"
            @current-change="handleCurrentChange"></el-pagination>

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="20%" append-to-body draggable>
            <el-form :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item v-if="obj.isDetails" label="分类编号">
                    <el-input disabled style="width: 100%" v-model="obj.dialogForm.categoryCode"
                        placeholder="自动生成"></el-input>
                </el-form-item>
                <el-form-item label="所属城市">
                    <el-select :disabled="obj.isDetails" style="width: 100%" filterable
                        v-model="obj.dialogForm.cityCode" placeholder="请选择城市" @change="handleCityChange">
                        <el-option v-for="item in obj.cityList" :key="item.code" :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="人员类型">
                    <el-select :disabled="!obj.dialogForm.cityCode || obj.isDetails" style="width: 100%" filterable
                        v-model="obj.dialogForm.indTypeCode" placeholder="请选择人员类型" @change="handleChange">
                        <el-option v-for="item in obj.personnelType" :key="item.code" :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="分类名称">
                    <el-input :disabled="obj.isDetails" style="width: 100%" readonly v-model="obj.dialogForm.typename"
                        placeholder="请输入分类名称"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" v-if="!obj.isDetails" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="CityPersonnelClassified_maintenance">
import { useAreaStore } from '@/store/modules/area'
import { listClassify, addClassify, updateClassify, delClassify } from '@/api/reonApi/classify'
import lodash from "lodash";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据

const obj = reactive({
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        page: 1,
        limit: 20,
        selectarea: '',
        searchName: '',
        searchCode: '',
    },//查询表单
    rules: {
        cityCode: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        personnelType: [{ required: true, message: '请选择人员类型', trigger: 'blur' }],
        className: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [],//表格数据
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
    isDetails: false,//是否详情

    cityList: window.top['area'] || [],
    personnelType: window.top['dictCachePool']['PEOPLE_IND_TYPE'] || [],
})

/** 列表 */
function getList() {
    obj.loading = true;
    let params = {
        page: obj.queryParams.page,
        limit: obj.queryParams.limit,
        paramData: {
            selectarea: obj.queryParams.selectarea,
            searchName: obj.queryParams.searchName,
            searchCode: obj.queryParams.searchCode
        }
    };
    params.paramData = JSON.stringify(params.paramData);
    axios.post(ML.contextPath + '/sys/IndividualCategory/getCategoryPage', params,
        {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
        }).then((res) => {
            if (res.data.code === 0) {
                obj.tableData = res.data.data;
                obj.total = res.data.count;
                obj.loading = false;
            }
        })
}
//分页
function handleSizeChange(size) {
    obj.queryParams.pageSize = size
    getList()
}

function handleCurrentChange(page) {
    obj.queryParams.pageNum = page
    getList()
}

/** 表单重置 */
function reset() {
    obj.dialogForm = {
        cityCode: '',
        indTypeCode: '',
        typename: '',
    };
    obj.isDetails = false;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.page = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    obj.queryParams = {
        page: 1,
        limit: 20,
        selectarea: '',
        searchName: '',
        searchCode: '',
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.categoryCode);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
    console.log(obj.ids)
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}

/** 查看按钮操作 */
function handleDetail(row) {
    reset();
    obj.dialogShow = true;
    obj.title = "详情";
    obj.isDetails = true;
    obj.dialogForm = row;
    obj.dialogForm.typename = obj.dialogForm.classifyName
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    obj.dialogForm = row.categoryCode ? JSON.parse(JSON.stringify(row)) : obj.tableData.find(item => item.categoryCode === obj.ids[0]);
    obj.dialogForm.typename = obj.dialogForm.classifyName
    obj.title = "修改";
    obj.dialogShow = true;
}

/** 城市选择 */
function handleCityChange(value) {
    const cityName = obj.cityList.find(item => item.code === value)?.name || '';
    const typeName = obj.personnelType.find(item => item.code === obj.dialogForm.indTypeCode)?.name || '';
    if (cityName && typeName) {
        obj.dialogForm.typename = cityName + '-' + typeName;
    }
}

/** 人员类型变化 */
function handleChange(value) {
    // 根据人员类型生成分类名称
    const cityName = obj.cityList.find(item => item.code === obj.dialogForm.cityCode)?.name || '';
    const typeName = obj.personnelType.find(item => item.code === value)?.name || '';
    obj.dialogForm.typename = cityName + '-' + typeName;
    if (cityName && typeName) {
        obj.dialogForm.typename = cityName + '-' + typeName;
    }
}

/** 提交按钮 */
function submitForm() {
    let params = {
        categoryCode: obj.dialogForm.categoryCode || '',
        cityCode: Number(obj.dialogForm.cityCode),
        indTypeCode: Number(obj.dialogForm.indTypeCode),
        typename: obj.dialogForm.typename,
    }
    axios.post(ML.contextPath + '/sys/IndividualCategory/saveCategory', params,
        {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }).then((res) => {
            if (res.data.code === 0) {
                ElMessage.success('新增成功')
                obj.dialogShow = false;
                getList();
            }
        })
}

/** 删除按钮操作 */
function handleDelete(row) {
    if (obj.ids && obj.ids.length === 1 && !row.categoryCode) {
        row.categoryCode = obj.ids[0]
    }
    ElMessageBox.confirm('确定删除？').then(function () {
        if (row.categoryCode) {
            axios.post(ML.contextPath + '/sys/IndividualCategory/delCategory', {
                code: row.categoryCode
            },
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                }).then((res) => {
                    ElMessage.success('删除成功')
                    getList()
                })
        } else {
            axios.post(ML.contextPath + '/sys/IndividualCategory/delCategoryBatch', {
                codes: JSON.stringify(obj.ids)
            },
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                }).then((res) => {
                    ElMessage.success('删除成功')
                    getList()
                })
        }
    })
}

/** 导出按钮操作 */
function handleExport() {
    ElMessageBox.confirm(`确定要导出数据吗？`, '导出确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        window.location.href = ML.contextPath + '/sys/IndividualCategory/export';
    })
        .catch(() => {
            ElMessage.error('导出失败!')
        });
}

getList();
</script>
<style lang="scss" scoped></style>