<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
        <span v-if="item.redirect === 'noRedirect' || index == levelList.length - 1" class="no-redirect">{{
          item.meta.title }}</span>
        <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script setup>
const route = useRoute();
const router = useRouter();
const levelList = ref([])

/**
 * 获取面包屑导航数据
 * 该函数根据当前路由生成面包屑导航所需的路由数组
 */
function getBreadcrumb() {
  // 只显示带有meta.title的路由
  let matched = route.matched.filter(item => item.meta && item.meta.title);
  const first = matched[0]
  // 判断是否为首页
  if (!isDashboard(first)) {
    matched = [{ path: '/index', meta: { title: '首页' } }].concat(matched)
  }

  // 过滤出需要显示在面包屑中的路由项
  levelList.value = matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)
}
/**
 * 判断给定的路由是否是仪表板
 * 
 * @param {Object} route - 路由对象，应包含名称属性
 * @returns {Boolean} - 如果路由是仪表板则返回true，否则返回false
 */
function isDashboard(route) {
  // 获取路由的名称
  const name = route && route.name
  // 如果没有名称，则不是仪表板
  if (!name) {
    return false
  }
  // 如果名称是'Index'，则认为是仪表板
  return name.trim() === 'Index'
}
/**
 * 处理跳转链接的函数
 * 该函数根据传入的item对象中的redirect或path属性，来决定路由跳转的目标
 * 
 * @param {Object} item - 包含跳转路径信息的对象
 *                      - redirect {string} - 如果存在，则跳转到该路径
 *                      - path {string} - 如果redirect不存在，则跳转到该路径
 */
function handleLink(item) {
  // 解构获取item对象中的redirect和path属性
  const { redirect, path } = item

  // 检查是否存在redirect属性
  if (redirect) {
    // 如果存在redirect属性，则使用router的push方法进行路由跳转
    router.push(redirect)
    // 结束函数执行
    return
  }

  // 如果不存在redirect属性，则使用router的push方法跳转到path属性指定的路径
  router.push(path)
}

// 监听路由变化，更新面包屑导航
watchEffect(() => {
  // 如果你转到重定向页面，不要更新面包屑
  if (route.path.startsWith('/redirect/')) {
    return
  }
  getBreadcrumb()
})
getBreadcrumb();
</script>

<style lang='scss' scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>