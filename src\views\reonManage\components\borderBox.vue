<template>
    <div class="border-box">
        <div class="title">{{ title }}</div>
        <div class="content">
            <slot></slot>
        </div>
    </div>
</template>
<script setup name="BorderBox" lang="ts">
defineProps({
    title: {
        type: String,
        default: ''
    }
})
</script>

<style lang="scss" scoped>
.border-box {
    margin-top: 20px;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    margin-bottom: 20px;
    padding: 10px;
    background: #fff;

    .title {
        position: absolute;
        top: -12px;
        left: 20px;
        padding: 0 10px;
        background: #fff;
        font-size: 16px;
        color: #606266;
        font-weight: 600;
    }

    .content {
        margin-top: 10px;
    }
}
</style>
