<!-- 商保增员确认 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" clearable />
            </el-form-item>
            <el-form-item label="姓名:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="请输入姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择客户名称" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractId">
                <el-input class="width220" v-model="obj.queryParams.contractId" placeholder="请输入合同编号" clearable />
            </el-form-item>
            <el-form-item label="方案编号:" prop="planId">
                <el-input class="width220" v-model="obj.queryParams.planId" placeholder="请输入方案编号" clearable />
            </el-form-item>
            <el-form-item label="收费起始月:" prop="chargeStartMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.chargeStartMonth" type="month"
                    placeholder="请选择收费起始月" format="YYYY-MM" value-format="YYYY-MM" clearable />
            </el-form-item>
            <el-form-item label="账单起始月:" prop="billStartMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billStartMonth" type="month"
                    placeholder="请选择账单起始月" format="YYYY-MM" value-format="YYYY-MM" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleBatchConfirm">批量增员确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出当前的增员数据</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Back" @click="handleReject">驳回</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" width="120" />
            <el-table-column label="姓名" align="center" prop="name" width="100" />
            <el-table-column label="证件号码" align="center" prop="idNumber" width="150" />
            <el-table-column label="处理状态" align="center" width="100">
                <template #default="scope">
                    <el-tag :type="getProcessStatusType(scope.row.processStatus)">
                        {{ getProcessStatusLabel(scope.row.processStatus) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="客户名称" align="center" prop="customerName" min-width="150" />
            <el-table-column label="合同编号" align="center" prop="contractId" width="120" />
            <el-table-column label="方案编号" align="center" prop="planId" width="120" />
            <el-table-column label="产品类型" align="center" width="100">
                <template #default="scope">
                    {{ getProductTypeLabel(scope.row.productType) }}
                </template>
            </el-table-column>
            <el-table-column label="收费起始月" align="center" prop="chargeStartMonth" width="100" />
            <el-table-column label="账单起始月" align="center" prop="billStartMonth" width="100" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 绑定账单模版 -->
        <el-dialog v-model="obj.dialogShow" title="绑定账单模版" width="40%">
            <el-button class="mb20" type="primary" @click="handleBindingConfirm">绑定</el-button>
            <el-table :data="obj.billTemplateData" border @selection-change="handleTemplateSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="账单模板" prop="templateName" />
                <el-table-column label="收费频率" prop="chargeFrequency" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="CommercialInsuranceAdditionConfirmation">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



// 处理状态选项
const processStatusOptions = [
    { value: '1', label: '待处理' },
    { value: '2', label: '处理中' },
    { value: '3', label: '已处理' },
    { value: '4', label: '处理失败' },
    { value: '5', label: '已驳回' }
];

// 产品类型选项
const productTypeOptions = [
    { value: '1', label: '医疗险' },
    { value: '2', label: '意外险' },
    { value: '3', label: '重疾险' },
    { value: '4', label: '寿险' },
    { value: '5', label: '综合险' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '北京科技有限公司' },
    { value: '2', label: '上海贸易有限公司' },
    { value: '3', label: '广州电子有限公司' },
    { value: '4', label: '深圳科技有限公司' },
    { value: '5', label: '成都信息有限公司' }
];

// 响应式数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: '', // 唯一号
        name: '', // 姓名
        idNumber: '', // 证件号码
        customerName: '', // 客户名称
        contractId: '', // 合同编号
        planId: '', // 方案编号
        chargeStartMonth: '', // 收费起始月
        billStartMonth: '', // 账单起始月
        processStatus: '' // 处理状态
    }, // 查询表单
    total: 0, // 总条数

    tableData: [
        {
            id: 1,
            uniqueId: 'UID20230501001',
            name: '张三',
            idNumber: '110101199001011234',
            processStatus: '1',
            customerName: '北京科技有限公司',
            contractId: 'CON20230501001',
            planId: 'PLAN20230501001',
            productType: '1',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            createTime: '2023-05-01 09:30:00'
        },
        {
            id: 2,
            uniqueId: 'UID20230502001',
            name: '李四',
            idNumber: '110101199002022345',
            processStatus: '2',
            customerName: '上海贸易有限公司',
            contractId: 'CON20230502001',
            planId: 'PLAN20230502001',
            productType: '2',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            createTime: '2023-05-02 10:15:00'
        },
        {
            id: 3,
            uniqueId: 'UID20230503001',
            name: '王五',
            idNumber: '110101199003033456',
            processStatus: '3',
            customerName: '广州电子有限公司',
            contractId: 'CON20230503001',
            planId: 'PLAN20230503001',
            productType: '3',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            createTime: '2023-05-03 13:45:00'
        },
        {
            id: 4,
            uniqueId: 'UID20230504001',
            name: '赵六',
            idNumber: '110101199004044567',
            processStatus: '4',
            customerName: '深圳科技有限公司',
            contractId: 'CON20230504001',
            planId: 'PLAN20230504001',
            productType: '4',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            createTime: '2023-05-04 09:10:00'
        },
        {
            id: 5,
            uniqueId: 'UID20230505001',
            name: '钱七',
            idNumber: '110101199005055678',
            processStatus: '5',
            customerName: '成都信息有限公司',
            contractId: 'CON20230505001',
            planId: 'PLAN20230505001',
            productType: '5',
            chargeStartMonth: '2023-05',
            billStartMonth: '2023-05',
            createTime: '2023-05-05 10:30:00'
        }
    ], // 列表
    dialogShow: false, // 绑定账单模版弹窗
    ids: [], // 选中的id
    title: "", // 标题

    // 账单模板数据
    billTemplateData: [
        {
            id: 1,
            templateName: '标准月度账单模板',
            chargeFrequency: '月度'
        },
        {
            id: 2,
            templateName: '标准季度账单模板',
            chargeFrequency: '季度'
        },
        {
            id: 3,
            templateName: '标准年度账单模板',
            chargeFrequency: '年度'
        }
    ]
})

/**
 * 获取处理状态标签类型
 * @param {string} status 状态值
 * @returns {string} 标签类型
 */
function getProcessStatusType(status) {
    switch (status) {
        case '1': return 'info';
        case '2': return 'warning';
        case '3': return 'success';
        case '4': return 'danger';
        case '5': return '';
        default: return '';
    }
}

/**
 * 获取处理状态标签文本
 * @param {string} status 状态值
 * @returns {string} 标签文本
 */
function getProcessStatusLabel(status) {
    const option = processStatusOptions.find(item => item.value === status);
    return option ? option.label : '未知状态';
}

/**
 * 获取产品类型标签文本
 * @param {string} type 产品类型值
 * @returns {string} 标签文本
 */
function getProductTypeLabel(type) {
    const option = productTypeOptions.find(item => item.value === type);
    return option ? option.label : '未知类型';
}

/** 列表 */
function getList() {
    obj.loading = true;

    // 模拟数据已经定义在 obj.tableData 中
    setTimeout(() => {
        obj.loading = false;
        obj.total = obj.tableData.length;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 账单模板选中数据
function handleTemplateSelectionChange(selection) {
    obj.selectedTemplates = selection;
}

// 批量确认
function handleBatchConfirm() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgInfo('请选择要确认的记录');
        return;
    }
    proxy.$modal.msgSuccess('确认成功');
    getList();
}

// 导出
function handleExport() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgInfo('请选择要导出的记录');
        return;
    }
    proxy.$modal.msgSuccess('导出成功');
}

// 驳回
function handleReject() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgInfo('请选择要驳回的记录');
        return;
    }
    proxy.$modal.msgSuccess('驳回成功');
    getList();
}
// 确认绑定
function handleBindingConfirm() {
    if (!obj.selectedTemplates || obj.selectedTemplates.length === 0) {
        proxy.$modal.msgInfo('请选择账单模板');
        return;
    }

    proxy.$modal.msgSuccess('绑定成功');
    obj.dialogShow = false;
    getList();
}
// 初始化
getList();
</script>
<style lang="scss" scoped></style>