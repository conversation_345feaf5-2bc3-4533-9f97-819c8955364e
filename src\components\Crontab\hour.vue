<template>
    <el-form>
        <el-form-item>
            <el-radio v-model='radioValue' :value="1">
                小时，允许的通配符[, - * /]
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="2">
                周期从
                <el-input-number v-model='cycle01' :min="0" :max="22" /> -
                <el-input-number v-model='cycle02' :min="cycle01 + 1" :max="23" /> 时
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="3">
                从
                <el-input-number v-model='average01' :min="0" :max="22" /> 时开始，每
                <el-input-number v-model='average02' :min="1" :max="23 - average01" /> 小时执行一次
            </el-radio>
        </el-form-item>

        <el-form-item>
            <el-radio v-model='radioValue' :value="4">
                指定
                <el-select clearable v-model="checkboxList" placeholder="可多选" multiple :multiple-limit="10">
                    <el-option v-for="item in 24" :key="item" :label="item - 1" :value="item - 1" />
                </el-select>
            </el-radio>
        </el-form-item>
    </el-form>
</template>

<script setup>
const emit = defineEmits(['update'])
const props = defineProps({
    cron: {
        type: Object,
        default: {
            second: "*",
            min: "*",
            hour: "*",
            day: "*",
            month: "*",
            week: "?",
            year: "",
        }
    },
    check: {
        type: Function,
        default: () => {
        }
    }
})
const radioValue = ref(1)
const cycle01 = ref(0)
const cycle02 = ref(1)
const average01 = ref(0)
const average02 = ref(1)
const checkboxList = ref([])
const checkCopy = ref([0])
const cycleTotal = computed(() => {
    cycle01.value = props.check(cycle01.value, 0, 22)
    cycle02.value = props.check(cycle02.value, cycle01.value + 1, 23)
    return cycle01.value + '-' + cycle02.value
})
const averageTotal = computed(() => {
    average01.value = props.check(average01.value, 0, 22)
    average02.value = props.check(average02.value, 1, 23 - average01.value)
    return average01.value + '/' + average02.value
})
const checkboxString = computed(() => {
    return checkboxList.value.join(',')
})
watch(() => props.cron.hour, value => changeRadioValue(value))
watch([radioValue, cycleTotal, averageTotal, checkboxString], () => onRadioChange())
/**
 * 更新单选按钮的值
 * 此函数根据传入的value参数更新组件的状态它首先检查cron对象中的min和second属性，
 * 如果它们被设置为'*'，则通过emit函数更新它们的值当value参数为'*'时，将radioValue设置为1
 * 如果value参数包含'-'，则表示这是一个范围值，将其分割为起始和结束值，并将radioValue设置为2
 * 如果value参数包含'/'，则表示这是一个步长值，将其分割为起始值和步长值，并将radioValue设置为3
 * 如果value参数包含','，则表示这是一个列表值，将其分割为多个值，并将radioValue设置为4
 * 
 * @param {string} value - 代表单选按钮新值的字符串它可以是'*'、范围值('start-end')、步长值('start/step')或列表值('value1,value2,...')
 */
function changeRadioValue(value) {
    // 检查分钟是否设置为'*'，如果是，则将其重置为'0'
    if (props.cron.min === '*') {
        emit('update', 'min', '0', 'hour');
    }
    // 检查秒是否设置为'*'，如果是，则将其重置为'0'
    if (props.cron.second === '*') {
        emit('update', 'second', '0', 'hour');
    }
    // 如果value为'*'，设置radioValue为1
    if (value === '*') {
        radioValue.value = 1
    } else if (value.indexOf('-') > -1) {
        // 如果value包含'-'，将其分割为起始和结束值，并设置相应的数据
        const indexArr = value.split('-')
        cycle01.value = Number(indexArr[0])
        cycle02.value = Number(indexArr[1])
        radioValue.value = 2
    } else if (value.indexOf('/') > -1) {
        // 如果value包含'/'，将其分割为起始值和步长值，并设置相应的数据
        const indexArr = value.split('/')
        average01.value = Number(indexArr[0])
        average02.value = Number(indexArr[1])
        radioValue.value = 3
    } else {
        // 如果value包含','，将其分割为多个值，并设置相应的数据
        checkboxList.value = [...new Set(value.split(',').map(item => Number(item)))]
        radioValue.value = 4
    }
}
/**
 * 处理单选按钮变化事件
 * 此函数根据单选按钮的值来决定如何更新小时字段
 * 它通过调用emit函数来触发更新事件，不同的单选按钮值会导致不同的更新参数
 */
function onRadioChange() {
    switch (radioValue.value) {
        case 1:
            // 当单选按钮值为1时，更新小时字段为通配符"*"
            emit('update', 'hour', '*', 'hour')
            break
        case 2:
            // 当单选按钮值为2时，更新小时字段为cycleTotal的值
            emit('update', 'hour', cycleTotal.value, 'hour')
            break
        case 3:
            // 当单选按钮值为3时，更新小时字段为averageTotal的值
            emit('update', 'hour', averageTotal.value, 'hour')
            break
        case 4:
            // 当单选按钮值为4时，处理逻辑更为复杂
            // 如果checkboxList为空，则向其中添加第一个checkCopy项
            // 否则，将checkboxList的值赋给checkCopy
            // 最后，更新小时字段为checkboxString的值
            if (checkboxList.value.length === 0) {
                checkboxList.value.push(checkCopy.value[0])
            } else {
                checkCopy.value = checkboxList.value
            }
            emit('update', 'hour', checkboxString.value, 'hour')
            break
    }
}
</script>

<style lang="scss" scoped>
.el-input-number--small,
.el-select,
.el-select--small {
    margin: 0 0.2rem;
}

.el-select,
.el-select--small {
    width: 18.8rem;
}
</style>