<!-- 供应商订单确认/产品变更 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-input class="width220" v-model="obj.queryParams.supplierName" placeholder="请输入供应商名称" clearable />
            </el-form-item>
            <el-form-item label="账单模板:" prop="billTemplate">
                <el-input class="width220" v-model="obj.queryParams.billTemplate" placeholder="请输入账单模板" clearable />
            </el-form-item>
            <el-form-item label="状态:" prop="status">
                <el-select class="width220" v-model="obj.queryParams.status" placeholder="请选择状态" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="参保城市:" prop="insuredCity">
                <el-select class="width220" v-model="obj.queryParams.insuredCity" placeholder="请选择参保城市" clearable>
                    <el-option v-for="item in provinces" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="身份证号码:" prop="idCard">
                <el-input class="width220" v-model="obj.queryParams.idCard" placeholder="请输入身份证号码" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleBatchConfirm">批量确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain @click="handleEnterChangeRatio">进入变更比例</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain @click="handleEnterChangeAmount">进入变更金额</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleChangeToWaitConfirm">变更为增员待确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleModify">修改</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.orderList"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail" :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" fixed prop="employeeName" />
            <el-table-column label="证件号码" align="center" fixed prop="idCard" />
            <el-table-column label="订单编号" align="center" width="140">
                <template #default="scope">
                    <el-link style="text-decoration: underline;" type="primary" @click="handleOrderDetail(scope.row)">{{
                        scope.row.orderNo }}</el-link>
                </template>
            </el-table-column>
            <el-table-column label="所属供应商" align="center" prop="supplierName" />
            <el-table-column label="账单模板名称" align="center" prop="billTemplate" />
            <el-table-column label="参保城市" align="center" prop="insuredCity" />
            <el-table-column label="小合同编号" align="center" prop="contractNo" />
            <el-table-column label="小合同名称" align="center" prop="contractName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="增员确认月" align="center" prop="confirmMonth" />
            <el-table-column label="报增时间" align="center" prop="addTime" width="180" />
            <el-table-column label="报减时间" align="center" prop="reduceTime" width="180" />
            <el-table-column label="提前几月收" align="center" prop="advanceMonths" />
            <el-table-column label="状态" align="center" prop="status" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 变更比例 -->
        <el-dialog v-model="obj.ratioDialogVisible" title="变更比例" width="50%">
            <el-form :model="obj.ratioForm" label-width="auto" v-if="obj.flag">
                <el-form-item label="参保城市" prop="insuredCity">
                    <el-select class="width220" v-model="obj.ratioForm.insuredCity" placeholder="请选择参保城市">
                        <el-option v-for="item in provinces" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
            </el-form>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-button type="danger" plain icon="Delete" @click="handleDeleteRatio">删除</el-button>
                    <el-button type="primary" plain icon="Plus" @click="handleAddRatio">新增</el-button>
                    <el-button type="primary" plain @click="handleAdjustRatio">调整比例</el-button>
                    <el-button type="danger" plain v-if="!obj.flag" icon="Delete"
                        @click="handleZeroAmount">金额清零</el-button>
                    <el-button type="warning" plain v-if="!obj.flag" icon="Refresh"
                        @click="handleRestoreAmount">金额恢复</el-button>
                </el-col>
            </el-row>
            <el-table :data="obj.feeList" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="序号" align="center" prop="id" />
                <el-table-column label="产品名称" align="center" prop="productName" />
                <el-table-column label="社保组名称" align="center" prop="securityGroupName" />
                <el-table-column label="社保比例名称" align="center" prop="ratioName" />
                <el-table-column label="个人金额" align="center" prop="personalAmount" />
                <el-table-column label="企业金额" align="center" prop="companyAmount" />
            </el-table>
            <template #footer>
                <el-button type="primary" @click="handleConfirm">确认</el-button>
            </template>
        </el-dialog>
        <!-- 修改 -->
        <el-dialog v-model="obj.modifyDialogVisible" title="修改" width="65%">
            <el-form class="formHight" :model="obj.modifyForm" inline label-width="auto">
                <border-box title="实做信息">
                    <el-form-item label="雇员姓名:" prop="employeeName">
                        <el-input class="width180" v-model="obj.modifyForm.employeeName" placeholder="请输入雇员姓名" />
                    </el-form-item>
                    <el-form-item label="证件号码:" prop="idCard">
                        <el-input class="width180" v-model="obj.modifyForm.idCard" placeholder="请输入证件号码" />
                    </el-form-item>
                    <el-form-item label="订单编号:" prop="orderNo">
                        <el-input class="width180" v-model="obj.modifyForm.orderNo" placeholder="请输入订单编号" />
                    </el-form-item>
                    <el-form-item label="客户名称:" prop="customerName">
                        <el-input class="width180" v-model="obj.modifyForm.customerName" placeholder="请输入客户名称" />
                    </el-form-item>
                    <el-form-item label="所属供应商:" prop="supplierName">
                        <el-input class="width180" v-model="obj.modifyForm.supplierName" placeholder="请输入所属供应商" />
                    </el-form-item>
                    <el-form-item label="参保城市:" prop="insuredCity">
                        <el-input class="width180" v-model="obj.modifyForm.insuredCity" placeholder="请输入参保城市" />
                    </el-form-item>
                    <el-form-item label="账单模板:" prop="billTemplate">
                        <el-input class="width180" v-model="obj.modifyForm.billTemplate" placeholder="请输入账单模板" />
                    </el-form-item>
                    <el-form-item label="收费频率:" prop="chargeFrequency">
                        <el-input class="width180" v-model="obj.modifyForm.chargeFrequency" placeholder="请输入收费频率" />
                    </el-form-item>
                    <el-form-item label="增员确认月:" prop="confirmMonth">
                        <el-input class="width180" v-model="obj.modifyForm.confirmMonth" placeholder="请输入增员确认月" />
                    </el-form-item>
                </border-box>
                <border-box title="费用信息">
                    <el-form-item label="账单起始月:" prop="billStartMonth">
                        <el-input class="width180" v-model="obj.modifyForm.billStartMonth" placeholder="请输入账单起始月" />
                    </el-form-item>
                    <el-table :data="obj.feeList" border>
                        <el-table-column label="序号" align="center" prop="id" />
                        <el-table-column label="产品名称" align="center" prop="productName" />
                        <el-table-column label="金额" align="center" prop="amount" />
                        <el-table-column label="收费起始月" align="center" prop="chargeStartMonth" />
                        <el-table-column label="收费截止月" align="center" prop="chargeEndMonth" />
                        <el-table-column label="账单起始月" align="center" prop="billStartMonth" />
                        <el-table-column label="社保比例编号" align="center" prop="ratioCode" />
                        <el-table-column label="比例名称" align="center" prop="ratioName" />
                        <el-table-column label="企业比例" align="center" prop="companyRatio" />
                        <el-table-column label="个人比例" align="center" prop="personalRatio" />
                        <el-table-column label="企业附加" align="center" prop="companyExtra" />
                        <el-table-column label="个人附加" align="center" prop="personalExtra" />
                        <el-table-column label="企业基数" align="center" prop="companyBase" />
                        <el-table-column label="个人基数" align="center" prop="personalBase" />
                        <el-table-column label="企业金额" align="center" prop="companyAmount" />
                        <el-table-column label="个人金额" align="center" prop="personalAmount" />
                        <el-table-column label="备注" align="center" prop="remark" />
                    </el-table>
                </border-box>
                <border-box title="服务费信息">
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="24">
                            <el-button type="primary" plain icon='Plus' @click="handleAddService">新增</el-button>
                            <el-button type="danger" plain icon='Delete' @click="handleDeleteService">删除</el-button>
                        </el-col>
                    </el-row>
                    <el-table :data="obj.serviceList" border @selection-change="handleSelectionChangeService">
                        <el-table-column type="selection" width="55" />
                        <el-table-column label="收费起始月" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.chargeStartMonth" type="month"
                                    placeholder="请选择收费起始月" />
                            </template>
                        </el-table-column>
                        <el-table-column label="收费截止月" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.chargeEndMonth" type="month"
                                    placeholder="请选择收费截止月" />
                            </template>
                        </el-table-column>
                        <el-table-column label="账单起始月" align="center">
                            <template #default="scope">
                                <el-date-picker style="width: 100%;" v-model="scope.row.billStartMonth" type="month"
                                    placeholder="请选择账单起始月" />
                            </template>
                        </el-table-column>
                        <el-table-column label="报价单" align="center">
                            <template #default="scope">
                                <el-select style="width: 100%;" v-model="scope.row.quoteNo" placeholder="请选择报价单">
                                    <el-option v-for="item in quoteNoOptions" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="金额" align="center" prop="amount" />
                        <el-table-column label="金额（不含税）" align="center" prop="amountWithoutTax" />
                        <el-table-column label="增值税" align="center" prop="tax" />
                        <el-table-column label="增值税率" align="center" prop="taxRate" />
                        <el-table-column label="备注" align="center" prop="remark" />
                    </el-table>
                </border-box>
                <border-box title="一次性费用">
                    <el-row :gutter="10" class="mb8">
                        <el-col :span="24">
                            <el-button type="primary" plain icon='Plus' @click="handleAddOneTime">新增</el-button>
                            <el-button type="danger" plain icon='Delete' @click="handleDeleteOneTime">删除</el-button>
                        </el-col>
                    </el-row>
                    <el-table :data="obj.oneTimeList" border @selection-change="handleSelectionChangeOneTime">
                        <el-table-column type="selection" width="55" />
                        <el-table-column label="发生月" align="center" prop="billMonth">
                            <template #default="scope">
                                <el-date-picker v-model="scope.row.billMonth" type="month" placeholder="请选择发生月" />
                            </template>
                        </el-table-column>
                        <el-table-column label="一次性类型" align="center" prop="oneTimeType">
                            <template #default="scope">
                                <el-select v-model="scope.row.oneTimeType" placeholder="请选择一次性类型">
                                    <el-option v-for="item in oneTimeTypeOptions" :key="item.value" :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="企业一次性费用" align="center">
                            <template #default="scope">
                                <el-input v-model="scope.row.companyOneTimeAmount" placeholder="请输入企业一次性费用" />
                            </template>
                        </el-table-column>
                        <el-table-column label="个人一次性费用" align="center">
                            <template #default="scope">
                                <el-input v-model="scope.row.personalOneTimeAmount" placeholder="请输入个人一次性费用" />
                            </template>
                        </el-table-column>
                        <el-table-column label="备注" align="center">
                            <template #default="scope">
                                <el-input v-model="scope.row.remark" placeholder="请输入备注" />
                            </template>
                        </el-table-column>
                    </el-table>
                </border-box>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleConfirm">确认</el-button>
                <el-button type="primary" @click="handleCancel">取消</el-button>
            </template>
        </el-dialog>


        <!-- 新增比例 -->
        <el-dialog v-model="obj.addRatioDialogVisible" title="新增比例" width="50%">
            <el-form :model="obj.addRatioForm" label-width="auto" inline>
                <el-form-item label="参保城市" prop="insuredCity">
                    <el-select class="width220" v-model="obj.addRatioForm.insuredCity" placeholder="请选择参保城市">
                        <el-option v-for="item in provinces" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="个人基数" prop="personalBase">
                    <el-input class="width220" v-model="obj.addRatioForm.personalBase" placeholder="请输入个人基数" />
                </el-form-item>
                <el-form-item label="企业基数" prop="companyBase">
                    <el-input class="width220" v-model="obj.addRatioForm.companyBase" placeholder="请输入企业基数" />
                </el-form-item>
                <el-form-item label="福利起始月" prop="welfareStartMonth">
                    <el-date-picker class="width220" v-model="obj.addRatioForm.welfareStartMonth" type="month"
                        placeholder="请选择福利起始月" />
                </el-form-item>
                <el-form-item label="福利截止月" prop="welfareEndMonth">
                    <el-date-picker class="width220" v-model="obj.addRatioForm.welfareEndMonth" type="month"
                        placeholder="请选择福利截止月" />
                </el-form-item>
                <el-form-item label="账单起始月" prop="billStartMonth">
                    <el-date-picker class="width220" v-model="obj.addRatioForm.billStartMonth" type="month"
                        placeholder="请选择账单起始月" />
                </el-form-item>
                <el-form-item label="社保公积金比例" prop="socialSecurityRatio">
                    <el-input class="width220" v-model="obj.addRatioForm.socialSecurityRatio"
                        placeholder="请输入社保公积金比例" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleConfirm">提交</el-button>
            </template>
        </el-dialog>
        <!-- 调整比例 -->
        <el-dialog v-model="obj.adjustRatioDialogVisible" title="替换比例" width="50%">
            <el-form :model="obj.adjustRatioForm" label-width="auto" inline>
                <el-form-item label="产品名称" prop="productName">
                    <el-input class="width220" v-model="obj.adjustRatioForm.productName" placeholder="请输入产品名称" />
                </el-form-item>
                <el-form-item label="社保公积金比例" prop="socialSecurityRatio">
                    <el-input class="width220" v-model="obj.adjustRatioForm.socialSecurityRatio"
                        placeholder="请输入社保公积金比例" />
                </el-form-item>
                <el-form-item label="新比例" prop="newRatio">
                    <el-input class="width220" v-model="obj.adjustRatioForm.newRatio" placeholder="请输入新比例" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleConfirm">保存</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup name="SupplierOrder_verify">

import { useAreaStore } from '@/store/modules/area'

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据

/** 表格行样式 */
function tableRowClassName({ row, rowIndex }) {
    if (row.status === '待确认') {
        return 'red-row'
    } else if (row.status === '已确认') {
        return 'blue-row'
    }
    return '';
}

// 状态选项
const statusOptions = [
    { value: '1', label: '待确认' },
    { value: '2', label: '已确认' },
    { value: '3', label: '已取消' },
    { value: '4', label: '已实做' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        billTemplate: null,
        status: null,
        insuredCity: null,
        customerName: null,
        orderNo: null,
        employeeName: null,
        idCard: null,
    },//查询表单
    total: 10,//总条数
    orderList: [],//订单列表
    ratioForm: {},//变更比例表单
    modifyForm: {
        // 实做信息
        employeeName: null,
        idCard: null,
        orderNo: null,
        customerName: null,
        supplierName: null,
        insuredCity: null,
        billTemplate: null,
        chargeFrequency: null,
        confirmMonth: null,
        // 费用信息
        billStartMonth: null
    },//修改表单
    ratioDialogVisible: false,//变更比例弹窗
    modifyDialogVisible: false,//修改弹窗
    selectedIds: [],//选中的id
    feeList: [], // 费用信息列表
    serviceList: [], // 服务费信息列表
    oneTimeList: [], // 一次性费用列表
    selectedIdsService: [],//选中的服务费
    selectedIdsOneTime: [],//选中的一性费用

    addRatioDialogVisible: false,//新增比例弹窗
    addRatioForm: {},//新增比例表单
    adjustRatioDialogVisible: false,//调整比例弹窗
    adjustRatioForm: {},//调整比例表单


    flag: false//
})


/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.orderList = [
            {
                id: 1,
                employeeName: '张三',
                idCard: '110101199001011234',
                orderNo: 'ORD20230501',
                supplierName: '供应商A',
                billTemplate: '模板1',
                insuredCity: '北京',
                contractNo: 'CN20230501',
                contractName: '合同1',
                customerName: '客户A',
                confirmMonth: '2023-05',
                addTime: '2023-05-01 10:30:00',
                reduceTime: '',
                advanceMonths: 3,
                status: '待确认'
            },
            {
                id: 2,
                employeeName: '李四',
                idCard: '310101199002022345',
                orderNo: 'ORD20230502',
                supplierName: '供应商B',
                billTemplate: '模板2',
                insuredCity: '上海',
                contractNo: '**********',
                contractName: '合同2',
                customerName: '客户B',
                confirmMonth: '2023-05',
                addTime: '2023-05-02 09:15:00',
                reduceTime: '2023-05-15 14:20:00',
                advanceMonths: 2,
                status: '已确认'
            }
        ];
        obj.total = obj.orderList.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.selectedIds = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}
// 批量确认
function handleBatchConfirm() {
    if (obj.selectedIds.length === 0) {
        proxy.$modal.msgError("请选择要确认的数据");
        return;
    }

    proxy.$modal.confirm('是否确认所选数据？').then(function () {
        proxy.$modal.msgSuccess("确认成功");
        getList();
    }).catch(() => { });
}

// 进入变更比例
function handleEnterChangeRatio() {
    if (obj.selectedIds.length === 0) {
        proxy.$modal.msgError("请选择要变更比例的数据");
        return;
    }
    obj.flag = true;
    obj.ratioDialogVisible = true;
}

// 进入变更金额
function handleEnterChangeAmount() {
    if (obj.selectedIds.length === 0) {
        proxy.$modal.msgError("请选择要变更金额的数据");
        return;
    }
    obj.flag = false;
    obj.ratioDialogVisible = true;
}

// 变更为增员待确认
function handleChangeToWaitConfirm() {
    if (obj.selectedIds.length === 0) {
        proxy.$modal.msgError("请选择要变更的数据");
        return;
    }

    proxy.$modal.confirm('是否将所选数据变更为增员待确认？').then(function () {
        proxy.$modal.msgSuccess("变更成功");
        getList();
    }).catch(() => { });
}

// 修改
function handleModify() {
    if (obj.selectedIds.length === 0) {
        proxy.$modal.msgError("请选择要修改的数据");
        return;
    }

    obj.modifyDialogVisible = true;
}
//详情
function handleDetail(row) {
    // 深拷贝选中的行数据到修改表单
    obj.modifyForm = JSON.parse(JSON.stringify(row));
    obj.modifyDialogVisible = true;
}

// 新增比例
function handleAddRatio() {
    obj.addRatioDialogVisible = true;
}

// 删除比例
function handleDeleteRatio() {
    proxy.$modal.msgSuccess("删除比例成功");
}

// 调整比例
function handleAdjustRatio() {
    obj.adjustRatioDialogVisible = true;
}

// 金额清零
function handleZeroAmount() {
    proxy.$modal.msgSuccess("金额清零成功");
}

// 金额恢复
function handleRestoreAmount() {
    proxy.$modal.msgSuccess("金额恢复成功");
}

// 确认
function handleConfirm() {
    proxy.$modal.msgSuccess("确认成功");
    obj.ratioDialogVisible = false;
    getList();
}

// 取消
function handleCancel() {
    obj.modifyDialogVisible = false;
}

// 选中服务费数据变化
function handleSelectionChangeService(selection) {
    obj.selectedIdsService = selection.map(item => item.index);
}

// 新增服务费
function handleAddService() {
    obj.serviceList.push({
        index: obj.serviceList.length
    });
}

// 删除服务费
function handleDeleteService() {
    obj.serviceList = obj.serviceList.filter(item => !obj.selectedIdsService.includes(item.index));
    obj.selectedIdsService = [];
}
// 选中一次性费用数据变化
function handleSelectionChangeOneTime(selection) {
    obj.selectedIdsOneTime = selection.map(item => item.index);
}
// 新增一次性费用
function handleAddOneTime() {
    obj.oneTimeList.push({
        index: obj.oneTimeList.length
    });
}

// 删除一次性费用
function handleDeleteOneTime() {
    obj.oneTimeList = obj.oneTimeList.filter(item => !obj.selectedIdsOneTime.includes(item.index));
    obj.selectedIdsOneTime = [];
}

getList();
</script>
<style lang="scss" scoped>
:deep(.red-row) {
    color: #f00;
}

:deep(.blue-row) {
    color: #337ecc;
}
</style>