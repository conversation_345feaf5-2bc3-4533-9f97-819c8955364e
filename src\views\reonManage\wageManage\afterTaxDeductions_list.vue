<!-- 税后扣除名单 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="产品类型:" prop="productType">
                <el-select class="width220" v-model="obj.queryParams.productType" placeholder="请选择" clearable>
                    <el-option v-for="item in productTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="withholdingName">
                <el-select class="width220" v-model="obj.queryParams.withholdingName" placeholder="请选择" clearable>
                    <el-option v-for="item in withholdingOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="除外客户:" prop="excludeCustomer">
                <el-select class="width220" v-model="obj.queryParams.excludeCustomer" placeholder="请选择" clearable>
                    <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="产品类型" align="center" width="120">
                <template #default="scope">
                    {{ getProductTypeName(scope.row.productType) }}
                </template>
            </el-table-column>
            <el-table-column label="扣缴义务人名称" align="center" min-width="150">
                <template #default="scope">
                    {{ getWithholdingName(scope.row.withholdingName) }}
                </template>
            </el-table-column>
            <el-table-column label="扣缴义务人类型" align="center" width="120">
                <template #default="scope">
                    {{ getWithholdingTypeName(scope.row.withholdingType) }}
                </template>
            </el-table-column>
            <el-table-column label="工资发放地" align="center" width="120">
                <template #default="scope">
                    {{ getSalaryLocationName(scope.row.salaryLocation) }}
                </template>
            </el-table-column>
            <el-table-column label="除外客户" align="center" width="100">
                <template #default="scope">
                    {{ getYesNoLabel(scope.row.excludeCustomer) }}
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 新增/编辑对话框 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="30%" append-to-body draggable
            @close="handleClose">
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" label-width="auto">
                <el-form-item label="产品类型:" prop="productType">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.productType" placeholder="请选择" clearable>
                        <el-option v-for="item in productTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="扣缴义务人:" prop="withholdingName">
                    <el-select style="width: 100%;" v-model="obj.dialogForm.withholdingName" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in withholdingOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户:" prop="customerName">
                    <el-input style="width: 100%;" v-model="obj.dialogForm.customerName" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item>
                    <div class="flex-between-center" style="width: 100%;">
                        <el-button type="primary" @click="handleReset">重置除外客户</el-button>
                        <div class="tag-box">
                            <span>除外客户:</span>
                            <el-tag v-for="tag in 5" :key="tag.name" closable :type="tag.type">
                                {{ tag.name }}
                            </el-tag>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="handleSubmit">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 产品类型选项
const productTypeOptions = [
    { value: '1', label: '商业医疗险' },
    { value: '2', label: '团体寿险' },
    { value: '3', label: '团体重疾险' },
    { value: '4', label: '团体意外险' },
    { value: '5', label: '其他保险' }
];

// 扣缴义务人选项
const withholdingOptions = [
    { value: '1', label: '北京科技有限公司' },
    { value: '2', label: '上海贸易有限公司' },
    { value: '3', label: '广州电子有限公司' },
    { value: '4', label: '深圳科技有限公司' },
    { value: '5', label: '成都信息有限公司' }
];

// 扣缴义务人类型选项
const withholdingTypeOptions = [
    { value: '1', label: '企业' },
    { value: '2', label: '事业单位' },
    { value: '3', label: '个体工商户' },
    { value: '4', label: '其他组织' }
];

// 工资发放地选项
const salaryLocationOptions = [
    { value: '1', label: '北京市' },
    { value: '2', label: '上海市' },
    { value: '3', label: '广州市' },
    { value: '4', label: '深圳市' },
    { value: '5', label: '成都市' }
];

// 是否选项
const yesNoOptions = [
    { value: '1', label: '是' },
    { value: '0', label: '否' }
];

// 税后扣除名单数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        productType: '', // 产品类型
        withholdingName: '', // 扣缴义务人
        excludeCustomer: '' // 除外客户
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            productType: '1',
            withholdingName: '1',
            withholdingType: '1',
            salaryLocation: '1',
            excludeCustomer: '0'
        },
        {
            id: 2,
            productType: '2',
            withholdingName: '2',
            withholdingType: '2',
            salaryLocation: '2',
            excludeCustomer: '1'
        }
    ],
    dialogShow: false, // 新增/编辑对话框
    dialogForm: {}, // 新增/编辑对话框表单
    ids: [], // 选中的id
    title: '', // 新增/编辑对话框标题
});

/**
 * 获取产品类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getProductTypeName(typeId) {
    if (!typeId) return '-';

    const type = productTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}

/**
 * 获取扣缴义务人名称
 * @param {string} withholdingId 扣缴义务人ID
 * @returns {string} 扣缴义务人名称
 */
function getWithholdingName(withholdingId) {
    if (!withholdingId) return '-';

    const withholding = withholdingOptions.find(item => item.value === withholdingId);
    return withholding ? withholding.label : '-';
}

/**
 * 获取扣缴义务人类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getWithholdingTypeName(typeId) {
    if (!typeId) return '-';

    const type = withholdingTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}

/**
 * 获取工资发放地名称
 * @param {string} locationId 地点ID
 * @returns {string} 地点名称
 */
function getSalaryLocationName(locationId) {
    if (!locationId) return '-';

    const location = salaryLocationOptions.find(item => item.value === locationId);
    return location ? location.label : '-';
}

/**
 * 获取是否标签
 * @param {string} value 值
 * @returns {string} 标签
 */
function getYesNoLabel(value) {
    return value === '1' ? '是' : '否';
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.productType) {
            filteredData = filteredData.filter(item =>
                item.productType === obj.queryParams.productType
            );
        }

        if (obj.queryParams.withholdingName) {
            filteredData = filteredData.filter(item =>
                item.withholdingName === obj.queryParams.withholdingName
            );
        }

        if (obj.queryParams.excludeCustomer) {
            filteredData = filteredData.filter(item =>
                item.excludeCustomer === obj.queryParams.excludeCustomer
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    obj.dialogShow = true;
    obj.title = '新增';
}

/** 编辑按钮操作 */
function handleEdit() {
    obj.dialogShow = true;
    obj.title = '编辑';
}

/** 删除按钮操作 */
function handleDelete() {
    console.log('删除');
}

/** 重置除外客户按钮操作 */
function handleReset() {
    console.log('重置除外客户');
}


/** 导出数据 */
function handleExport() {
    console.log('导出数据');
}

/** 关闭对话框 */
function handleClose() {
    obj.dialogForm = {};
    obj.dialogShow = false;

}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped>
.tag-box {
    display: flex;
    align-items: center;
    gap: 10px;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 5px;
    border-left: 5px solid #ccc;
}
</style>