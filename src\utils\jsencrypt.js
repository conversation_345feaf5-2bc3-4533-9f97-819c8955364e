import JSEncrypt from "jsencrypt/bin/jsencrypt.min";

// 密钥对生成 http://web.chacuo.net/netrsakeypair
// 公钥
const publicKey =
  "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH\n" +
  "nzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==";

// 私钥
const privateKey =
  "MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY\n" +
  "7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN\n" +
  "PuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gA\n" +
  "kM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWow\n" +
  "cSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99Ecv\n" +
  "DQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthh\n" +
  "YhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3\n" +
  "UP8iWi1Qw0Y=";

// 加密
/**
 * 使用公钥对给定文本进行加密
 *
 * @param {string} txt - 需要加密的文本
 * @returns {string} - 加密后的文本
 */
export function encrypt(txt) {
  // 创建一个新的JSEncrypt实例
  const encryptor = new JSEncrypt();
  // 设置公钥
  encryptor.setPublicKey(publicKey);
  // 对数据进行加密
  return encryptor.encrypt(txt);
}

// 解密
/**
 * 解密给定的文本
 * 使用私钥对加密文本进行解密
 *
 * @param {string} txt - 需要解密的加密文本
 * @returns {string} - 解密后的原始文本
 */
export function decrypt(txt) {
  // 创建一个新的JSEncrypt实例
  const encryptor = new JSEncrypt();
  // 设置私钥，用于解密操作
  encryptor.setPrivateKey(privateKey);
  // 对数据进行解密并返回结果
  return encryptor.decrypt(txt);
}
