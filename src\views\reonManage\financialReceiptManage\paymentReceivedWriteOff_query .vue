<!-- 到款核销查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户帐套:" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户帐套" clearable>
                    <el-option label="帐套1" value="1" />
                    <el-option label="帐套2" value="2" />
                    <el-option label="帐套3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付类型:" prop="paymentType">
                <el-select class="width220" v-model="obj.queryParams.paymentType" placeholder="请选择支付类型" clearable>
                    <el-option label="类型1" value="1" />
                    <el-option label="类型2" value="2" />
                    <el-option label="类型3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="到款时间>=:" prop="paymentTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentTimeStart" type="datetime"
                    placeholder="请选择开始时间" clearable />
            </el-form-item>
            <el-form-item label="到款时间<=:" prop="paymentTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentTimeEnd" type="datetime"
                    placeholder="请选择结束时间" clearable />
            </el-form-item>
            <el-form-item label="到款金额>=:" prop="paymentAmountMin">
                <el-input class="width220" v-model="obj.queryParams.paymentAmountMin" placeholder="请输入最小金额" clearable />
            </el-form-item>
            <el-form-item label="到款金额<=:" prop="paymentAmountMax">
                <el-input class="width220" v-model="obj.queryParams.paymentAmountMax" placeholder="请输入最大金额" clearable />
            </el-form-item>
            <el-form-item label="核销时间>=:" prop="writeOffTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.writeOffTimeStart" type="datetime"
                    placeholder="请选择开始时间" clearable />
            </el-form-item>
            <el-form-item label="核销时间<=:" prop="writeOffTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.writeOffTimeEnd" type="datetime"
                    placeholder="请选择结束时间" clearable />
            </el-form-item>
            <el-form-item label="核销金额>=:" prop="writeOffAmountMin">
                <el-input class="width220" v-model="obj.queryParams.writeOffAmountMin" placeholder="请输入最小金额"
                    clearable />
            </el-form-item>
            <el-form-item label="核销金额<=:" prop="writeOffAmountMax">
                <el-input class="width220" v-model="obj.queryParams.writeOffAmountMax" placeholder="请输入最大金额"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            :row-class-name="rowClassName">
            <el-table-column label="到款分公司" align="center" prop="paymentBranch" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="到款客户" align="center" prop="paymentCustomer" />
            <el-table-column label="到款金额" align="center" prop="paymentAmount" />
            <el-table-column label="到款日期" align="center" prop="paymentDate" />
            <el-table-column label="到款银行" align="center" prop="paymentBank" />
            <el-table-column label="支付类型" align="center" prop="paymentType" />
            <el-table-column label="到款备注" align="center" prop="paymentRemark" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="PaymentReceivedWriteOff_query">
import useUserStore from "@/store/modules/user";

const roles = useUserStore().roles;
console.log(roles);

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        customerAccount: null,
        paymentType: null,
        paymentTimeStart: null,
        paymentTimeEnd: null,
        paymentAmountMin: null,
        paymentAmountMax: null,
        writeOffTimeStart: null,
        writeOffTimeEnd: null,
        writeOffAmountMin: null,
        writeOffAmountMax: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
})

// 行样式
function rowClassName(row) {
    //  #D7D1C9   #BAD1D1  #D9B9E7  #DCD5E8
    return
    if (row.paymentAmount > 10000) {
        return 'D7D1C9';
    } else if (row.paymentAmount < 10000) {
        return 'BAD1D1';
    } else {
        return '';
    }
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                paymentBranch: '上海分公司',
                customerName: '客户名称1',
                customerCode: 'CUST001',
                paymentCustomer: '到款客户A',
                paymentAmount: 10000.00,
                paymentDate: '2023-01-15',
                paymentBank: '中国银行',
                paymentType: '类型1',
                paymentRemark: '正常到款'
            },
            {
                id: 2,
                paymentBranch: '北京分公司',
                customerName: '客户名称2',
                customerCode: 'CUST002',
                paymentCustomer: '到款客户B',
                paymentAmount: 20000.00,
                paymentDate: '2023-02-15',
                paymentBank: '工商银行',
                paymentType: '类型2',
                paymentRemark: '预付款'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>