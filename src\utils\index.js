import { parseTime } from "./ruoyi";

/**
 * 格式化日期时间
 * @param {string|number} cellValue - 表示日期时间的字符串或时间戳
 * @returns {string} - 格式化后的日期时间字符串，格式为 "YYYY-MM-DD HH:mm:ss"
 */
export function formatDate(cellValue) {
  // 检查 cellValue 是否为 null 或空字符串，如果是则直接返回空字符串
  if (cellValue == null || cellValue == "") return "";

  // 将 cellValue 转换为 Date 对象
  var date = new Date(cellValue);

  // 获取年份
  var year = date.getFullYear();

  // 获取月份，月份从0开始，所以需要加1，并补零
  var month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;

  // 获取日期，并补零
  var day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();

  // 获取小时，并补零
  var hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();

  // 获取分钟，并补零
  var minutes =
    date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();

  // 获取秒数，并补零
  var seconds =
    date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();

  // 返回格式化后的日期时间字符串
  return (
    year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds
  );
}

/**
 * 格式化时间
 * @param {string|number} time - 时间戳或日期字符串
 * @param {string} option - 格式化选项，可选
 * @returns {string} - 格式化后的时间字符串
 */
export function formatTime(time, option) {
  // 如果时间戳是10位的，将其转换为毫秒级时间戳
  if (("" + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    // 如果时间戳已经是毫秒级的，直接使用
    time = +time;
  }

  // 将时间戳转换为Date对象
  const d = new Date(time);
  // 获取当前时间的时间戳
  const now = Date.now();

  // 计算当前时间与给定时间的时间差（秒）
  const diff = (now - d) / 1000;

  // 如果时间差小于30秒，返回“刚刚”
  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // 如果时间差小于1小时，返回“X分钟前”
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    // 如果时间差小于1天，返回“X小时前”
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    // 如果时间差小于2天，返回“1天前”
    return "1天前";
  }

  // 如果提供了格式化选项，使用指定的格式进行格式化
  if (option) {
    return parseTime(time, option);
  } else {
    // 如果没有提供格式化选项，返回默认格式的时间字符串
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

/**
 * 从URL中解析查询参数并返回一个对象
 * @param {string} url - 要解析的URL，默认为当前页面的URL
 * @returns {Object} - 包含查询参数的对象
 */
export function getQueryObject(url) {
  // 如果没有提供URL，则使用当前页面的URL
  url = url == null ? window.location.href : url;
  // 获取URL中查询参数部分
  const search = url.substring(url.lastIndexOf("?") + 1);
  const obj = {};
  // 正则表达式用于匹配查询参数
  const reg = /([^?&=]+)=([^?&=]*)/g;
  // 使用正则表达式替换查询参数部分，并将其转换为对象
  search.replace(reg, (rs, $1, $2) => {
    // 解码查询参数的名称
    const name = decodeURIComponent($1);
    // 解码查询参数的值
    let val = decodeURIComponent($2);
    // 将值转换为字符串
    val = String(val);
    // 将名称和值添加到对象中
    obj[name] = val;
    // 返回原始的查询参数字符串
    return rs;
  });
  // 返回包含查询参数的对象
  return obj;
}

/**
 * 计算UTF-8字符串的字节长度
 * @param {string} str - 输入的UTF-8字符串
 * @returns {number} - 字符串的字节长度
 */
export function byteLength(str) {
  // 初始化字节长度为字符串的字符数
  let s = str.length;
  // 从字符串末尾开始遍历
  for (var i = str.length - 1; i >= 0; i--) {
    // 获取当前字符的Unicode编码
    const code = str.charCodeAt(i);
    // 如果字符编码在0x7f到0x7ff之间，增加一个字节
    if (code > 0x7f && code <= 0x7ff) s++;
    // 如果字符编码在0x7ff到0xffff之间，增加两个字节
    else if (code > 0x7ff && code <= 0xffff) s += 2;
    // 如果字符编码在0xDC00到0xDFFF之间，跳过下一个字符（处理代理对）
    if (code >= 0xdc00 && code <= 0xdfff) i--;
  }
  // 返回计算得到的字节长度
  return s;
}

/**
 * 清除数组中的空值
 * @param {Array} actual - 输入的数组
 * @returns {Array} - 清除空值后的新数组
 */
export function cleanArray(actual) {
  // 创建一个新数组来存储非空值
  const newArray = [];
  // 遍历输入的数组
  for (let i = 0; i < actual.length; i++) {
    // 如果当前元素存在（非空）
    if (actual[i]) {
      // 将当前元素添加到新数组中
      newArray.push(actual[i]);
    }
  }
  // 返回清除空值后的新数组
  return newArray;
}

/**
 * 将对象转换为URL参数字符串
 * @param {Object} json - 要转换的对象
 * @returns {string} - 转换后的URL参数字符串
 */
export function param(json) {
  // 如果传入的对象为空，则返回空字符串
  if (!json) return "";

  // 使用Object.keys获取对象的所有键，并使用map方法遍历每个键
  // 对于每个键，检查其对应的值是否为undefined，如果是则返回空字符串
  // 否则，使用encodeURIComponent对键和值进行编码，并拼接成键值对字符串
  const keyValuePairs = Object.keys(json).map((key) => {
    if (json[key] === undefined) return "";
    return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
  });

  // 使用cleanArray函数清除数组中的空值，并使用join方法将数组中的元素拼接成一个字符串，每个元素之间用&符号分隔
  return cleanArray(keyValuePairs).join("&");
}

/**
 * 将URL查询字符串转换为对象
 * @param {string} url - 包含查询字符串的URL
 * @returns {Object} - 包含查询字符串参数的对象
 */
export function param2Obj(url) {
  // 从URL中提取查询字符串部分，并进行URL解码
  const search = decodeURIComponent(url.split("?")[1]).replace(/\+/g, " ");

  // 如果查询字符串为空，则返回空对象
  if (!search) {
    return {};
  }

  // 创建一个空对象来存储查询字符串参数
  const obj = {};

  // 将查询字符串分割成数组，每个元素是一个键值对
  const searchArr = search.split("&");

  // 遍历查询字符串数组
  searchArr.forEach((v) => {
    // 查找键值对中的等号位置
    const index = v.indexOf("=");

    // 如果找到了等号
    if (index !== -1) {
      // 提取键
      const name = v.substring(0, index);

      // 提取值
      const val = v.substring(index + 1, v.length);

      // 将键值对添加到对象中
      obj[name] = val;
    }
  });

  // 返回包含查询字符串参数的对象
  return obj;
}

/**
 * 将HTML字符串转换为纯文本
 * @param {string} val - 输入的HTML字符串
 * @returns {string} - 转换后的纯文本
 */
export function html2Text(val) {
  // 创建一个新的div元素
  const div = document.createElement("div");
  // 将输入的HTML字符串设置为div的innerHTML
  div.innerHTML = val;
  // 返回div的textContent或innerText，即纯文本内容
  return div.textContent || div.innerText;
}

/**
 * 合并两个对象，第二个对象的属性会覆盖第一个对象的同名属性
 * @param {Object} target - 目标对象，即要被合并的对象
 * @param {Object} source - 源对象，即合并的来源对象
 * @returns {Object} - 合并后的对象
 */
export function objectMerge(target, source) {
  // 如果目标对象不是对象类型，则将其初始化为空对象
  if (typeof target !== "object") {
    target = {};
  }
  // 如果源对象是数组类型，则直接返回源对象的副本
  if (Array.isArray(source)) {
    return source.slice();
  }
  // 遍历源对象的所有属性
  Object.keys(source).forEach((property) => {
    // 获取源对象当前属性的值
    const sourceProperty = source[property];
    // 如果当前属性的值是对象类型
    if (typeof sourceProperty === "object") {
      // 递归调用 objectMerge 函数，合并目标对象和源对象的当前属性
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      // 如果当前属性的值不是对象类型，则直接将源对象的当前属性值赋给目标对象
      target[property] = sourceProperty;
    }
  });
  // 返回合并后的目标对象
  return target;
}

/**
 * 切换元素的类名
 * @param {HTMLElement} element - 要切换类名的元素
 * @param {string} className - 要切换的类名
 * @returns {void}
 */
export function toggleClass(element, className) {
  // 如果元素或类名为空，则直接返回
  if (!element || !className) {
    return;
  }

  // 获取元素当前的类名字符串
  let classString = element.className;

  // 查找类名在类名字符串中的位置
  const nameIndex = classString.indexOf(className);

  // 如果类名不存在，则添加类名
  if (nameIndex === -1) {
    classString += " " + className;
  } else {
    // 如果类名存在，则移除类名
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length);
  }

  // 更新元素的类名字符串
  element.className = classString;
}

/**
 * 获取当前时间或指定时间
 * @param {string} type - 时间类型，可选值为 'start' 或其他
 * @returns {Date} - 返回一个 Date 对象
 */
export function getTime(type) {
  // 如果 type 为 'start'，则返回当前时间减去90天的时间戳
  if (type === "start") {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    // 如果 type 不是 'start'，则返回当前日期的开始时间（即时间部分为00:00:00）
    return new Date(new Date().toDateString());
  }
}

/**
 * 防抖函数
 * @param {Function} func - 需要防抖的函数
 * @param {number} wait - 延迟时间，单位为毫秒
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} - 返回一个新的函数，该函数在 wait 时间内只会执行一次
 */
export function debounce(func, wait, immediate) {
  // 定义一个变量用于存储定时器
  let timeout, args, context, timestamp, result;

  // 定义一个内部函数，用于在延迟时间后执行传入的函数
  const later = function () {
    // 计算距离上一次触发的时间间隔
    const last = +new Date() - timestamp;

    // 如果时间间隔小于设定的延迟时间且大于0，则重新设置定时器
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      // 清除定时器
      timeout = null;
      // 如果 immediate 为 false，则执行传入的函数
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  // 返回一个新的函数，该函数在 wait 时间内只会执行一次
  return function (...args) {
    // 保存当前的上下文和参数
    context = this;
    timestamp = +new Date();
    // 判断是否需要立即执行
    const callNow = immediate && !timeout;
    // 如果定时器不存在，则设置定时器
    if (!timeout) timeout = setTimeout(later, wait);
    // 如果需要立即执行，则执行传入的函数
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    // 返回执行结果
    return result;
  };
}

/**
 * 深度克隆一个对象或数组
 * @param {Object|Array} source - 需要克隆的源对象或数组
 * @returns {Object|Array} - 克隆后的新对象或数组
 * @throws {Error} - 如果源对象为空或不是对象类型，则抛出错误
 */
export function deepClone(source) {
  // 如果源对象为空或不是对象类型，则抛出错误
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "deepClone");
  }
  // 根据源对象的构造函数创建一个新的目标对象或数组
  const targetObj = source.constructor === Array ? [] : {};
  // 遍历源对象的所有属性
  Object.keys(source).forEach((keys) => {
    // 如果当前属性的值是对象类型
    if (source[keys] && typeof source[keys] === "object") {
      // 递归调用 deepClone 函数，克隆当前属性的值
      targetObj[keys] = deepClone(source[keys]);
    } else {
      // 如果当前属性的值不是对象类型，则直接将源对象的当前属性值赋给目标对象
      targetObj[keys] = source[keys];
    }
  });
  // 返回克隆后的目标对象或数组
  return targetObj;
}

/**
 * 去除数组中的重复元素
 * @param {Array} arr - 输入的数组
 * @returns {Array} - 去除重复元素后的新数组
 */
export function uniqueArr(arr) {
  // 使用 Set 数据结构去除数组中的重复元素，并将结果转换为数组
  return Array.from(new Set(arr));
}

/**
 * 生成一个唯一的字符串
 * @returns {string} - 生成的唯一字符串
 */
export function createUniqueString() {
  // 获取当前时间的时间戳，并转换为字符串
  const timestamp = +new Date() + "";
  // 生成一个随机数，并转换为字符串
  const randomNum = parseInt((1 + Math.random()) * 65536) + "";
  // 将时间戳和随机数拼接，并转换为32进制的字符串
  return (+(randomNum + timestamp)).toString(32);
}

/**
 * 检查元素是否包含指定类名
 * @param {HTMLElement} ele - 需要检查的元素
 * @param {string} cls - 需要检查的类名
 * @returns {boolean} - 如果元素包含指定类名，则返回true，否则返回false
 */
export function hasClass(ele, cls) {
  // 使用正则表达式匹配元素的类名，判断是否包含指定类名
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}

/**
 * 给指定元素添加类名
 * @param {HTMLElement} ele - 需要添加类名的元素
 * @param {string} cls - 需要添加的类名
 * @returns {void}
 */
export function addClass(ele, cls) {
  // 检查元素是否已经包含指定类名，如果不包含，则添加类名
  if (!hasClass(ele, cls)) {
    // 在元素的类名字符串末尾添加一个空格和指定的类名
    ele.className += " " + cls;
  }
}

/**
 * 移除指定元素的类名
 * @param {HTMLElement} ele - 需要移除类名的元素
 * @param {string} cls - 需要移除的类名
 * @returns {void}
 */
export function removeClass(ele, cls) {
  // 检查元素是否包含指定类名，如果包含，则移除类名
  if (hasClass(ele, cls)) {
    // 创建一个正则表达式，用于匹配类名及其前后的空格
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    // 使用正则表达式替换类名为空格，从而移除类名
    ele.className = ele.className.replace(reg, " ");
  }
}

/**
 * 创建一个映射函数，用于检查给定的值是否在指定的字符串列表中
 * @param {string} str - 包含要检查的值的字符串，多个值用逗号分隔
 * @param {boolean} expectsLowerCase - 是否期望输入的值为小写
 * @returns {Function} - 返回一个函数，该函数接受一个值并返回一个布尔值，指示该值是否在字符串列表中
 */
export function makeMap(str, expectsLowerCase) {
  // 创建一个空的对象作为映射表
  const map = Object.create(null);
  // 将输入的字符串按逗号分隔成数组
  const list = str.split(",");
  // 遍历数组中的每个值
  for (let i = 0; i < list.length; i++) {
    // 将数组中的每个值作为键，值为 true，添加到映射表中
    map[list[i]] = true;
  }
  // 返回一个函数，该函数接受一个值并返回一个布尔值，指示该值是否在映射表中
  return expectsLowerCase
    ? // 如果 expectsLowerCase 为 true，则将输入的值转换为小写后再检查
      (val) => map[val.toLowerCase()]
    : // 如果 expectsLowerCase 为 false，则直接检查输入的值
      (val) => map[val];
}

export const exportDefault = "export default ";

export const beautifierConf = {
  html: {
    indent_size: "2",
    indent_char: " ",
    max_preserve_newlines: "-1",
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: "separate",
    brace_style: "end-expand",
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: "110",
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true,
  },
  js: {
    indent_size: "2",
    indent_char: " ",
    max_preserve_newlines: "-1",
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: "normal",
    brace_style: "end-expand",
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: "110",
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true,
  },
};

/**
 * 将字符串转换为首字母大写的格式
 * @param {string} str - 需要转换的字符串
 * @returns {string} - 转换后的字符串
 */
export function titleCase(str) {
  // 使用正则表达式匹配字符串中的每个单词的首字母，并将其转换为大写
  return str.replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
}

/**
 * 将下划线命名法的字符串转换为驼峰命名法
 * @param {string} str - 需要转换的字符串
 * @returns {string} - 转换后的字符串
 */
export function camelCase(str) {
  // 使用正则表达式匹配下划线及其后的小写字母，并将其替换为大写字母
  return str.replace(/_[a-z]/g, (str1) => str1.substr(-1).toUpperCase());
}

/**
 * 判断一个字符串是否为有效的数字字符串
 * @param {string} str - 需要判断的字符串
 * @returns {boolean} - 如果字符串是有效的数字字符串，则返回true，否则返回false
 */
export function isNumberStr(str) {
  // 使用正则表达式判断字符串是否符合数字字符串的格式
  // 正则表达式解释：
  // ^[+-]? ：可选的正负号
  // (0|([1-9]\d*)) ：匹配整数部分，可以是0或者非零开头的数字
  // (\.\d+)? ：可选的小数部分，以小数点开头，后跟至少一位数字
  // $ ：字符串结束
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str);
}
