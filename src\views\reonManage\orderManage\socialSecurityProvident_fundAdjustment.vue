<!-- 社保公积金调整 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="调整名称:" prop="adjustmentName">
                <el-input class="width220" v-model="obj.queryParams.adjustmentName" placeholder="请输入调整名称" />
            </el-form-item>
            <el-form-item label="社保公积金组:" prop="securityGroup">
                <el-input class="width220" v-model="obj.queryParams.securityGroup" placeholder="请输入社保公积金组" />
            </el-form-item>
            <el-form-item label="调整类型:" prop="adjustmentType">
                <el-select class="width220" v-model="obj.queryParams.adjustmentType" placeholder="请选择调整类型">
                    <el-option label="基数调整" value="1" />
                    <el-option label="比例调整" value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="调整范围:" prop="adjustmentScope">
                <el-select class="width220" v-model="obj.queryParams.adjustmentScope" placeholder="请选择调整范围">
                    <el-option label="全部" value="1" />
                    <el-option label="部分" value="2" />
                </el-select>
            </el-form-item>
            <el-form-item label="调整起始年月:" prop="adjustmentStartMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.adjustmentStartMonth" type="month"
                    placeholder="请选择调整起始年月" />
            </el-form-item>
            <el-form-item label="调整任务号:" prop="adjustmentTaskNo">
                <el-input class="width220" v-model="obj.queryParams.adjustmentTaskNo" placeholder="请输入调整任务号" />
            </el-form-item>
            <el-form-item label="调整状态:" prop="adjustmentStatus">
                <el-select class="width220" v-model="obj.queryParams.adjustmentStatus" placeholder="请选择调整状态">
                    <el-option label="未调整" value="0" />
                    <el-option label="调整中" value="1" />
                    <el-option label="已调整" value="2" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon='Plus' @click="handleAdd">新增调整</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" @click="handleAdd">引用调整</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" @click="handleDetail">进入调整</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="调整编号" align="center" prop="adjustmentCode" />
            <el-table-column label="调整名称" align="center" prop="adjustmentName" />
            <el-table-column label="社保公积金组" align="center" prop="securityGroup" />
            <el-table-column label="调整类型" align="center" prop="adjustmentType" />
            <el-table-column label="调整范围" align="center" prop="adjustmentScope" />
            <el-table-column label="调整起始年月" align="center" prop="adjustmentStartMonth" />
            <el-table-column label="调整任务号" align="center" prop="adjustmentTaskNo" />
            <el-table-column label="调整状态" align="center" prop="adjustmentStatus" />
            <el-table-column label="成功数" align="center" prop="successCount" />
            <el-table-column label="失败数" align="center" prop="failCount" />
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="primary" plain @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 新增转移 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="70%" append-to-body @close="handleClose">
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <el-form-item label="调整名称" prop="adjustmentName">
                    <el-input class="width220" v-model="obj.dialogForm.adjustmentName" placeholder="请输入调整名称" />
                </el-form-item>
                <el-form-item label="社保公积金组" prop="securityGroup">
                    <el-select class="width220" v-model="obj.dialogForm.securityGroup" placeholder="请选择社保公积金组">
                        <el-option label="社保组1" value="1" />
                        <el-option label="社保组2" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="调整类型" prop="adjustmentType">
                    <el-select class="width220" v-model="obj.dialogForm.adjustmentType" placeholder="请选择调整类型">
                        <el-option label="基数调整" value="1" />
                        <el-option label="比例调整" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="调整范围" prop="adjustmentScope">
                    <el-select class="width220" v-model="obj.dialogForm.adjustmentScope" placeholder="请选择调整范围">
                        <el-option label="全部" value="1" />
                        <el-option label="部分" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item label="调整起始月" prop="adjustmentStartMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.adjustmentStartMonth" type="month"
                        placeholder="请选择调整起始月" />
                </el-form-item>
                <el-form-item label="是否包含0费用员工" prop="includeZeroCost">
                    <el-select class="width220" v-model="obj.dialogForm.includeZeroCost" placeholder="请选择">
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否调整非连续的个人订单" prop="adjustNonContinuous">
                    <el-select class="width220" v-model="obj.dialogForm.adjustNonContinuous" placeholder="请选择">
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="接单方" prop="receiver">
                    <el-select class="width220" v-model="obj.dialogForm.receiver" placeholder="请选择接单方">
                        <el-option label="接单方1" value="1" />
                        <el-option label="接单方2" value="2" />
                    </el-select>
                </el-form-item>
                <el-row class="mb8" v-show="!obj.isDetail">
                    <el-col :span="24" class="ml20" style="color: red;">
                        小提示：关于是否调整非连续的个人订单 否：调整同一个接单方下面所有的连续的订单。是：将会调整接单方下证件号所有符合条件的订单。
                    </el-col>
                </el-row>
                <el-form-item label="基数低于原基数是否退费" prop="refundIfLower">
                    <el-select class="width220" v-model="obj.dialogForm.refundIfLower" placeholder="请选择">
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否单立户" prop="isSingleAccount">
                    <el-select class="width220" v-model="obj.dialogForm.isSingleAccount" placeholder="请选择">
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-row class="mb20" v-show="!obj.isDetail">
                    <el-col :span="24" class="ml20" style="color: red;">
                        小提示：关于基数低于原基数是否退费 是：不进行判断直接进行调整。否：如果新基数小于原基数不进行调整
                    </el-col>
                </el-row>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleAdd">保存保存设置</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleAdd">下载核对模版</el-button>
                    </el-col>
                </el-row>
                <el-table :data="obj.dialogForm.tableData" style="width: 100%"
                    @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="产品名称" align="center" prop="productName" />
                    <el-table-column label="原比例" align="center" prop="originalRatio" />
                    <el-table-column label="新比例" align="center" prop="newRatio" />
                    <el-table-column label="新企业比例" align="center" prop="newEnterpriseRatio" />
                    <el-table-column label="新个人比例" align="center" prop="newPersonalRatio" />
                    <el-table-column label="企业基数" align="center" prop="enterpriseBase" />
                    <el-table-column label="个人基数列" align="center" prop="personalBase" />
                </el-table>
                <el-row :gutter="10" class="mt20" v-show="!obj.isDetail">
                    <el-col :span="8">
                        <el-form-item label="上传文件" prop="uploadFile">
                            <FileUpload v-model="obj.dialogForm.uploadFile" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="primary" @click="handleAdd">调整</el-button>
                    </el-col>
                    <el-col :span="1.5" style="color: red;line-height: 32px;">
                        一次不能超过3000条数据
                    </el-col>
                </el-row>
                <div class="mt20" v-show="obj.isDetail">
                    <el-button class="mb8" type="primary" v-if="obj.tabName == 0" @click="handleAdd">导出</el-button>
                    <el-tabs type="border-card" @tab-change="handleTabChange">
                        <el-tab-pane label="订单调整明细">
                            <el-table :data="obj.dialogForm.tableData" border style="width: 100%">
                                <el-table-column label="姓名" align="center" prop="name" />
                                <el-table-column label="订单编号" align="center" prop="orderCode" />
                                <el-table-column label="客户编号" align="center" prop="customerCode" />
                                <el-table-column label="调整状态" align="center" prop="adjustmentStatus" />
                                <el-table-column label="失败原因" align="center" prop="failReason" />
                                <el-table-column label="提示信息" align="center" prop="promptInfo" />
                            </el-table>
                        </el-tab-pane>
                        <el-tab-pane label="实做调整明细"></el-tab-pane>
                    </el-tabs>
                </div>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    adjustmentName: [
        { required: true, message: '调整名称不能为空', trigger: 'blur' }
    ],
    securityGroup: [
        { required: true, message: '社保公积金组不能为空', trigger: 'change' }
    ],
    adjustmentType: [
        { required: true, message: '调整类型不能为空', trigger: 'change' }
    ],
    adjustmentStartMonth: [
        { required: true, message: '调整起始月不能为空', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        adjustmentName: null,
        securityGroup: null,
        adjustmentType: null,
        adjustmentScope: null,
        adjustmentStartMonth: null,
        adjustmentTaskNo: null,
        adjustmentStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogShow: false,//新增调整
    dialogForm: {
        tableData: []
    },//调整表单
    rules: rules,//表单验证规则
    ids: [],//选中的id
    title: "",//标题
    isDetail: false,//是否查看
    tabName: '0'//当前选中的tab
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                adjustmentCode: 'TZ20230001',
                adjustmentName: '社保基数调整',
                securityGroup: '社保组1',
                adjustmentType: '基数调整',
                adjustmentScope: '全部',
                adjustmentStartMonth: '2023-01',
                adjustmentTaskNo: 'RW20230001',
                adjustmentStatus: '已调整',
                successCount: 10,
                failCount: 0,
                createBy: '创建人1',
                createTime: '2023-01-01 10:00:00'
            },
            {
                id: 2,
                adjustmentCode: 'TZ20230002',
                adjustmentName: '公积金比例调整',
                securityGroup: '社保组2',
                adjustmentType: '比例调整',
                adjustmentScope: '部分',
                adjustmentStartMonth: '2023-02',
                adjustmentTaskNo: '**********',
                adjustmentStatus: '调整中',
                successCount: 8,
                failCount: 2,
                createBy: '创建人2',
                createTime: '2023-02-01 10:00:00'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 新增调整 */
function handleAdd() {
    obj.dialogShow = true;
    obj.isDetail = false;
    obj.title = "新增调整";
    obj.dialogForm = {
        tableData: [
            {
                id: 1,
                productName: '养老保险',
                originalRatio: '8%',
                newRatio: '8%',
                newEnterpriseRatio: '8%',
                newPersonalRatio: '8%',
                enterpriseBase: '5000',
                personalBase: '5000'
            },
            {
                id: 2,
                productName: '医疗保险',
                originalRatio: '10%',
                newRatio: '10%',
                newEnterpriseRatio: '8%',
                newPersonalRatio: '2%',
                enterpriseBase: '5000',
                personalBase: '5000'
            },
            {
                id: 3,
                productName: '失业保险',
                originalRatio: '2%',
                newRatio: '2%',
                newEnterpriseRatio: '1.5%',
                newPersonalRatio: '0.5%',
                enterpriseBase: '5000',
                personalBase: '5000'
            },
            {
                id: 4,
                productName: '公积金',
                originalRatio: '12%',
                newRatio: '12%',
                newEnterpriseRatio: '7%',
                newPersonalRatio: '5%',
                enterpriseBase: '5000',
                personalBase: '5000'
            }
        ],
        uploadFile: []
    };
}

/** 查看调整信息 */
function handleDetail(row) {
    obj.dialogShow = true;
    obj.isDetail = true;
    obj.title = "社保公积金调整详情";

    // 模拟数据，实际开发时可以从表格中获取
    if (row) {
        obj.dialogForm = {
            ...row, tableData: [
                {
                    id: 1,
                    name: '张三',
                    orderCode: '**********',
                    customerCode: '**********',
                    adjustmentStatus: '调整成功',
                    failReason: '',
                    promptInfo: '调整成功'
                },
                {
                    id: 2,
                    name: '李四',
                    orderCode: '**********',
                    customerCode: '**********',
                    adjustmentStatus: '调整失败',
                    failReason: '基数不符合要求',
                    promptInfo: '新基数低于原基数'
                }
            ]
        };
    } else if (obj.ids.length === 1) {
        const selectedRow = obj.tableData.find(item => item.id === obj.ids[0]);
        if (selectedRow) {
            obj.dialogForm = {
                ...selectedRow, tableData: [
                    {
                        id: 1,
                        name: '张三',
                        orderCode: '**********',
                        customerCode: '**********',
                        adjustmentStatus: '调整成功',
                        failReason: '',
                        promptInfo: '调整成功'
                    },
                    {
                        id: 2,
                        name: '李四',
                        orderCode: '**********',
                        customerCode: '**********',
                        adjustmentStatus: '调整失败',
                        failReason: '基数不符合要求',
                        promptInfo: '新基数低于原基数'
                    }
                ]
            };
        }
    }
}

/** 关闭 */
function handleClose() {
    obj.dialogShow = false;
    obj.isDetail = false;
    obj.dialogForm = {
        tableData: []
    };
}

/** 切换tab */
function handleTabChange(tab) {
    obj.tabName = tab;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>