import { createApp } from "vue";

import Cookies from "js-cookie";
//element-plus
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import locale from "element-plus/es/locale/lang/zh-cn";

import "@/assets/styles/index.scss"; // global css

import App from "./App";
import store from "./store";
import router from "./router";
import directive from "./directive"; // directive

// 注册指令
import plugins from "./plugins"; // plugins
import { download } from "@/utils/request";

// svg图标
import "virtual:svg-icons-register";
import SvgIcon from "@/components/SvgIcon";
import elementIcons from "@/components/SvgIcon/svgicon";

import "./permission"; // permission control

import { useDict } from "@/utils/dict";
import {
  parseTime,
  resetForm,
  addDateRange,
  handleTree,
  selectDictLabel,
  selectDictLabels,
  formatAmount,
} from "@/utils/ruoyi";


// 分页组件
import Pagination from "@/components/Pagination";
// 自定义表格工具组件
import RightToolbar from "@/components/RightToolbar";
// 富文本组件
import Editor from "@/components/Editor";
// 富文本组件
import TEditor from "@/components/TEditor";
// 文件上传组件
import FileUpload from "@/components/FileUpload";
// 图片上传组件
import ImageUpload from "@/components/ImageUpload";
// 图片预览组件
import ImagePreview from "@/components/ImagePreview";
// 自定义树选择组件
import TreeSelect from "@/components/TreeSelect";
// 字典标签组件
import DictTag from "@/components/DictTag";

// 自定义组件
import BorderBox from "@/views/reonManage/components/borderBox.vue";
//表单导入组件
import UploadFile from "@/views/reonManage/components/dialog/uploadFile";
//列筛选组件
import ColumnFilter from '@/views/reonManage/components/columnFilter.vue'
//导出表格组件
import ExportTable2 from '@/views/reonManage/components/table/exportTable2.vue'
//历史信息查看组件
import HistoricalInformation from '@/views/reonManage/components/dialog/historicalInformation.vue'
//分页打印表格组件
import PaginatedPrintTable from '@/components/PaginatedPrintTable.vue'

//打印
import print from "vue3-print-nb";
//lodash
import lodash from 'lodash';

const app = createApp(App);

// 全局方法挂载
app.config.globalProperties.useDict = useDict;//字典
app.config.globalProperties.download = download;//下载文件
app.config.globalProperties.parseTime = parseTime;//日期格式化
app.config.globalProperties.resetForm = resetForm;//表单重置
app.config.globalProperties.handleTree = handleTree;//构造树型结构数据
app.config.globalProperties.addDateRange = addDateRange;//添加日期范围
app.config.globalProperties.selectDictLabel = selectDictLabel;//回显数据字典
app.config.globalProperties.selectDictLabels = selectDictLabels;//回显数据字典（字符串数组）
app.config.globalProperties.formatAmount = formatAmount;//格式化金额
app.config.globalProperties.lodash = lodash;//lodash库

// 全局组件挂载
app.component("DictTag", DictTag);//字典标签组件
app.component("Pagination", Pagination);//分页组件
app.component("TreeSelect", TreeSelect);//自定义树选择组件
app.component("FileUpload", FileUpload);//文件上传组件
app.component("ImageUpload", ImageUpload);//图片上传组件
app.component("ImagePreview", ImagePreview);//图片预览组件
app.component("RightToolbar", RightToolbar);//自定义表格工具组件
app.component("Editor", Editor);//富文本组件
app.component("TEditor", TEditor);//富文本组件

app.component("BorderBox", BorderBox);//自定义组件
app.component("UploadFile", UploadFile);//表单导入组件
app.component("ColumnFilter", ColumnFilter);//列筛选组件
app.component("ExportTable2", ExportTable2);//导出表格组件
app.component("HistoricalInformation", HistoricalInformation);//历史信息查看组件
app.component("PaginatedPrintTable", PaginatedPrintTable);//分页打印表格组件
app.use(router);
app.use(store);
app.use(plugins);
app.use(elementIcons);
app.component("svg-icon", SvgIcon);

directive(app);

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get("size") || "default",
});

app.use(print);

app.mount("#app");
