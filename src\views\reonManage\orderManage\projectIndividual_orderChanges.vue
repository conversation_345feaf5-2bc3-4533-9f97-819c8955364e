<!-- 项目个人订单变更 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户编号:" prop="customerCode">
                <el-select class="width220" v-model="obj.queryParams.customerCode" placeholder="请选择" clearable>
                    <el-option v-for="item in customerList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderCode">
                <el-input class="width220" v-model="obj.queryParams.orderCode" placeholder="请输入订单编号" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" />
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractCode">
                <el-input class="width220" @click="obj.contractShow = true" v-model="obj.queryParams.contractCode"
                    placeholder="请输入合同编号" />
            </el-form-item>
            <el-form-item label="变更方式:" prop="changeType">
                <el-select class="width220" v-model="obj.queryParams.changeType" placeholder="请选择" clearable>
                    <el-option v-for="item in changeTypeList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" />
            </el-form-item>
            <div class="content" :class="{ 'expanded': obj.showMore }">
                <el-form-item label="证件类型:" prop="idType">
                    <el-select class="width220" v-model="obj.queryParams.idType" placeholder="请选择" clearable>
                        <el-option v-for="item in idTypeList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="小合同名称:" prop="smallContractName">
                    <el-input class="width220" readonly @click="obj.sendOrdersShow = true"
                        v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称" />
                </el-form-item>
                <el-form-item label="接单客服:" prop="orderService">
                    <el-select class="width220" v-model="obj.queryParams.orderService" placeholder="请选择" clearable>
                        <el-option v-for="item in serviceList" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="接单方:" prop="orderReceiver">
                    <el-input class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请输入接单方" />
                </el-form-item>
                <el-form-item label="派单方:" prop="orderSender">
                    <el-input class="width220" v-model="obj.queryParams.orderSender" placeholder="请输入派单方" />
                </el-form-item>
                <el-form-item label="入职时间起:" prop="entryDateStart">
                    <el-date-picker v-model="obj.queryParams.entryDateStart" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="入职时间止:" prop="entryDateEnd">
                    <el-date-picker v-model="obj.queryParams.entryDateEnd" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="离职时间起:" prop="leaveDateStart">
                    <el-date-picker v-model="obj.queryParams.leaveDateStart" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="离职时间止:" prop="leaveDateEnd">
                    <el-date-picker v-model="obj.queryParams.leaveDateEnd" type="date" placeholder="请选择日期" />
                </el-form-item>
                <el-form-item label="是否是单立户:" prop="isSingleAccount">
                    <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="入离职状态:" prop="employmentStatus">
                    <el-select class="width220" v-model="obj.queryParams.employmentStatus" placeholder="请选择" clearable>
                        <el-option v-for="item in employmentStatusList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="账单模版:" prop="billTemplate">
                    <el-select class="width220" v-model="obj.queryParams.billTemplate" placeholder="请选择" clearable>
                        <el-option v-for="item in billTemplateList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="收费模板:" prop="chargeTemplate">
                    <el-select class="width220" v-model="obj.queryParams.chargeTemplate" placeholder="请选择" clearable>
                        <el-option v-for="item in chargeTemplateList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </div>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" icon="Expand" plain @click="showMore">显示更多</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleChange">进入变更</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="订单编号" align="center" prop="orderCode" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="申请入职时间" align="center" prop="applyEntryDate" />
            <el-table-column label="数据确认状态" align="center" prop="dataConfirmStatus" />
            <el-table-column label="变更状态" align="center" prop="changeStatus" />
            <el-table-column label="离职日期" align="center" prop="leaveDate" />
            <el-table-column label="申请离职日期" align="center" prop="applyLeaveDate" />
            <el-table-column label="入离职状态" align="center" prop="employmentStatus" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />




        <!-- 合同编号 -->
        <el-dialog v-model="obj.contractShow" :title="obj.title" width="55%" append-to-body>
            <div style="display: flex; justify-content: space-between;" class="mt20">
                <el-input class="mb20" style="width: 30%" v-model="obj.contractSearchKey" placeholder="合同编号/合同名称">
                    <template #append>
                        <el-button icon="Search" @click="searchContract" />
                    </template>
                </el-input>
                <div class="clear">
                    <el-button type="primary" @click="clearContractSelection">清空</el-button>
                    <el-button type="primary" @click="selectContract">选择</el-button>
                </div>
            </div>
            <el-table :data="obj.contractList" border style="width: 100%" highlight-current-row
                @current-change="handleCurrentChange">
                <el-table-column typee="index" label="序号" width="80" />
                <el-table-column label="合同编号" prop="contractCode" />
                <el-table-column label="合同名称" prop="contractName" />
                <el-table-column label="合同单类型" prop="contractType" />
                <el-table-column label="客户名称" prop="customerName" />
                <el-table-column label="客户编号" prop="customerCode" />
            </el-table>
            <pagination :total="obj.total" v-model:page="obj.queryParams.pageNum"
                v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        </el-dialog>
    </div>
</template>


<script setup>

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 客户列表
const customerList = ref([]);
// 证件类型列表
const idTypeList = ref([]);
// 客服列表
const serviceList = ref([]);
// 入离职状态列表
const employmentStatusList = ref([]);
// 变更方式列表
const changeTypeList = ref([]);
// 账单模版列表
const billTemplateList = ref([]);
// 收费模板列表
const chargeTemplateList = ref([]);

const obj = reactive({
    showSearch: true,//显示搜索
    showMore: false,//显示更多
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerCode: null,
        customerName: null,
        uniqueId: null,
        orderCode: null,
        employeeName: null,
        contractCode: null,
        changeType: null,
        idNumber: null,
        idType: null,
        smallContractName: null,
        orderService: null,
        orderReceiver: null,
        orderSender: null,
        entryDateStart: null,
        entryDateEnd: null,
        leaveDateStart: null,
        leaveDateEnd: null,
        isSingleAccount: null,
        employmentStatus: null,
        billTemplate: null,
        chargeTemplate: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {},//导入表单
    dialogShow: false,//导入弹窗
    contractShow: false,//合同编号弹窗
    sendOrdersShow: false,//小合同名称弹窗
    bindSave: false,//绑定保存
    ids: [],//选中的id
    title: "选择合同",//标题
    contractList: [],//合同编号列表
    contractSearchKey: '',//合同搜索关键字
    currentRow: null,//当前选中行
})


/** 获取客户列表 */
function getCustomerList() {
    // 这里可以调用API获取客户列表
    customerList.value = [
        { code: '1', name: '客户姓名1' },
        { code: '2', name: '客户姓名2' },
        { code: '3', name: '客户姓名3' }
    ];
}

/** 获取证件类型列表 */
function getIdTypeList() {
    // 这里可以调用API获取证件类型列表
    idTypeList.value = [
        { code: '1', name: '身份证' },
        { code: '2', name: '护照' },
        { code: '3', name: '军官证' }
    ];
}

/** 获取客服列表 */
function getServiceList() {
    // 这里可以调用API获取客服列表
    serviceList.value = [
        { code: '1', name: '客服1' },
        { code: '2', name: '客服2' },
        { code: '3', name: '客服3' }
    ];
}

/** 获取入离职状态列表 */
function getEmploymentStatusList() {
    // 这里可以调用API获取入离职状态列表
    employmentStatusList.value = [
        { code: '1', name: '在职' },
        { code: '2', name: '离职' },
        { code: '3', name: '待入职' }
    ];
}

/** 获取变更方式列表 */
function getChangeTypeList() {
    // 这里可以调用API获取变更方式列表
    changeTypeList.value = [
        { code: '1', name: '变更方式1' },
        { code: '2', name: '变更方式2' },
        { code: '3', name: '变更方式3' }
    ];
}

/** 获取账单模版列表 */
function getBillTemplateList() {
    // 这里可以调用API获取账单模版列表
    billTemplateList.value = [
        { code: '1', name: '账单模版1' },
        { code: '2', name: '账单模版2' },
        { code: '3', name: '账单模版3' }
    ];
}

/** 获取收费模板列表 */
function getChargeTemplateList() {
    // 这里可以调用API获取收费模板列表
    chargeTemplateList.value = [
        { code: '1', name: '收费模板1' },
        { code: '2', name: '收费模板2' },
        { code: '3', name: '收费模板3' }
    ];
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeName: '雇员姓名1',
                uniqueId: 'WY20230001',
                orderCode: 'DD20230001',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                contractName: '合同名称1',
                contractCode: 'HT20230001',
                smallContractName: '小合同名称1',
                applyEntryDate: '2023-01-01',
                dataConfirmStatus: '已确认',
                changeStatus: '已变更',
                leaveDate: '2023-12-31',
                applyLeaveDate: '2023-12-15',
                employmentStatus: '在职'
            },
            {
                id: 2,
                employeeName: '雇员姓名2',
                uniqueId: 'WY20230002',
                orderCode: 'DD20230002',
                customerCode: '**********',
                customerName: '客户名称2',
                contractName: '合同名称2',
                contractCode: 'HT20230002',
                smallContractName: '小合同名称2',
                applyEntryDate: '2023-02-01',
                dataConfirmStatus: '未确认',
                changeStatus: '未变更',
                leaveDate: '',
                applyLeaveDate: '',
                employmentStatus: '在职'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 获取合同列表 */
function getContractList() {
    // 这里可以调用API获取合同列表
    const contractData = [
        {
            contractCode: 'HT20230001',
            contractName: '合同名称1',
            contractType: '合同类型1',
            customerName: '客户名称1',
            customerCode: 'KH20230001'
        },
        {
            contractCode: 'HT20230002',
            contractName: '合同名称2',
            contractType: '合同类型2',
            customerName: '客户名称2',
            customerCode: '**********'
        },
        {
            contractCode: '**********',
            contractName: '合同名称3',
            contractType: '合同类型3',
            customerName: '客户名称3',
            customerCode: '**********'
        }
    ];
    obj.contractList = contractData;
    return contractData;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 显示更多 */
function showMore() {
    obj.showMore = !obj.showMore;
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 单选表格行 */
function handleCurrentChange(row) {
    obj.currentRow = row;
    obj.queryParams.contractCode = row.contractCode;
    obj.contractShow = false;
}

/** 搜索合同 */
function searchContract() {
    // 这里可以根据搜索关键字过滤合同列表
    if (obj.contractSearchKey) {
        const filteredList = obj.contractList.filter(item =>
            item.contractCode.includes(obj.contractSearchKey) ||
            item.contractName.includes(obj.contractSearchKey)
        );
        obj.contractList = filteredList.length > 0 ? filteredList : getContractList();
    } else {
        getContractList();
    }
}

/** 清空合同选择 */
function clearContractSelection() {
    obj.currentRow = null;
    obj.queryParams.contractCode = null;
    obj.contractSearchKey = '';
    getContractList();
}

/** 选择合同 */
function selectContract() {
    if (!obj.currentRow) {
        proxy.$modal.msgError('请选择一条合同数据');
        return;
    }
    obj.queryParams.contractCode = obj.currentRow.contractCode;
    obj.contractShow = false;
}

/** 进入变更 */
function handleChange() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要变更的订单');
        return;
    }
    proxy.$modal.confirm('确认要进入变更吗？').then(() => {
        // 这里可以调用API进行变更操作
        proxy.$modal.msgSuccess('操作成功');
        getList();
    }).catch(() => { });
}

// 初始化数据
getCustomerList();
getIdTypeList();
getServiceList();
getEmploymentStatusList();
getChangeTypeList();
getBillTemplateList();
getChargeTemplateList();
getContractList();
getList();
</script>
<style lang="scss" scoped>
.content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out;
}

.content.expanded {
    max-height: 1000px;
    /* 设置一个足够大的值 */
}

:deep(.el-table__body tr.current-row>td.el-table__cell) {
    background-color: aqua;
}
</style>