<!-- 图标闪烁 -->
<template>
  <el-icon :class="['blink-icon', className]">
    <component :is="icon" />
  </el-icon>
</template>

<script setup name="BlinkIcon">

defineProps({
  icon: {
    type: String,
    required: true
  },
  className: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.blink-icon {
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0;
    transform: scale(1.2);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
