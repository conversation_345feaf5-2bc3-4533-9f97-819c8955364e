/* 宽度 */
.width80 {
    width: 80px;
}

.width120 {
    width: 120px;
}

.width170 {
    width: 170px;
}

.width180 {
    width: 180px;
}

.width220 {
    width: 220px;
}

.width320 {
    width: 320px;
}

.width420 {
    width: 420px;
}

.width620 {
    width: 620px;
}

/* 左margin */
.marl5 {
    margin-left: 5px;
}

.marl10 {
    margin-left: 10px;
}

.marl15 {
    margin-left: 15px;
}

.marl20 {
    margin-left: 20px;
}

/* 右margin */
.marr5 {
    margin-right: 5px;
}

.marr10 {
    margin-right: 10px;
}

.marr15 {
    margin-right: 15px;
}

.marr20 {
    margin-right: 20px;
}

/* 上margin */
.mart10 {
    margin-top: 10px;
}

.mart20 {
    margin-top: 20px;
}

.mart30 {
    margin-top: 30px;
}

/* 下margin */
.marb10 {
    margin-bottom: 10px;
}

.marb20 {
    margin-bottom: 20px;
}

.marb30 {
    margin-bottom: 30px;
}

/* 左margin */
.padl5 {
    padding-left: 5px;
}

.padl10 {
    padding-left: 10px;
}

.padl15 {
    padding-left: 15px;
}

.padl20 {
    padding-left: 20px;
}

/* 右padding */
.padr5 {
    padding-right: 5px;
}

.padr10 {
    padding-right: 10px;
}

.padr15 {
    padding-right: 15px;
}

.padr20 {
    padding-right: 20px;
}

/* 上padding */
.padt10 {
    padding-top: 10px;
}

.padt20 {
    padding-top: 20px;
}

.padt30 {
    padding-top: 30px;
}

/* 下padding */
.padb10 {
    padding-bottom: 10px;
}

.padb20 {
    padding-bottom: 20px;
}

.padb30 {
    padding-bottom: 30px;
}

/* 超出显示省略号 */
.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 超出2行显示省略号 */
.ellipsis2 {
    overflow: hidden;
    text-overflow: ellipsis;
    /* 将对象作为弹性伸缩盒子模型显示 */
    display: -webkit-box;
    /* 限制在一个块元素显示的文本的行数 */
    /* -webkit-line-clamp 其实是一个不规范属性，使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端；*/
    -webkit-line-clamp: 2;
    line-clamp: 2;
    /* 设置或检索伸缩盒对象的子元素的排列方式 */
    -webkit-box-orient: vertical;
}

/* 字体颜色 */
.cor66 {
    color: #666666;
}

.corred {
    color: #ff3d3d;
}

.corc8 {
    color: #c8c8c8;
}

.cor82 {
    color: #828282;
}

.cor00 {
    color: #000;
}

.corff {
    color: #fff;
}

/* 字体宽度 */
.fw300 {
    font-weight: 300;
}

.fw600 {
    font-weight: 600;
}


/* 弹性盒子 */
.flex {
    display: flex;
}

.flex-between {
    display: flex;
    justify-content: space-between;
}

.flex-around {
    display: flex;
    justify-content: space-around;
}

.flex-evenly {
    display: flex;
    justify-content: space-evenly;
}

.flex-between-center {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flex-around-center {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.flex-evenly-center {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.flex-center-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flex-center {
    display: flex;
    align-items: center;
}

.cursor-pointer {
    cursor: pointer;
}

.dialog-footer {
    text-align: center;
    padding-top: 10px;
}



// form高度
.formHight {
    max-height: 75vh;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 10px;
}

//打印区域样式
.print-area {
    display: none;
}

@media print {
    .print-area {
        display: block;
        width: 100%;
        padding: 0;
        margin: 0;
    }

    .print-table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #000;
        table-layout: fixed;
    }

    .print-table th,
    .print-table td {
        border: 1px solid #000;
        padding: 8px;
        text-align: center;
        font-size: 8px;
        word-break: break-word;
    }

    .print-table th {
        background-color: #f0f0f0 !important;
        font-weight: bold;
        color: #000;
    }

    .print-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    @page {
        size: landscape;
        margin: 1cm;
    }
}