<!-- 薪资计算 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户:" prop="customer">
                <el-input class="width220" v-model="obj.queryParams.customer" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="薪资类别名称:" prop="salaryCategory">
                <el-input class="width220" v-model="obj.queryParams.salaryCategory" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发放编号/名称:" prop="payrollNumber">
                <el-select class="width220" filterable v-model="obj.queryParams.payrollNumber" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资所属月起:" prop="salaryMonthStart">
                <el-select class="width220" filterable v-model="obj.queryParams.salaryMonthStart" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="工资计税月:" prop="salaryMonthEnd">
                <el-input class="width220" v-model="obj.queryParams.salaryMonthEnd" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发放状态:" prop="payStatus">
                <el-input class="width220" v-model="obj.queryParams.payStatus" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户账单月起:" prop="customerBillMonthStart">
                <el-input class="width220" v-model="obj.queryParams.customerBillMonthStart" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户账单月止:" prop="customerBillMonthEnd">
                <el-input class="width220" v-model="obj.queryParams.customerBillMonthEnd" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectCustomer">
                <el-select class="width220" filterable v-model="obj.queryParams.projectCustomer" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="薪资客服:" prop="salaryCustomer">
                <el-select class="width220" filterable v-model="obj.queryParams.salaryCustomer" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="创建人:" prop="createUser">
                <el-input class="width220" v-model="obj.queryParams.createUser" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增发放</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="View" @click="handleView">查看抵扣专项信息</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button color="#626aef" plain :disabled="obj.single" @click="handleCalculate">计算</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" :disabled="obj.single"
                    @click="handleAddBatch">新增发放批次</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" icon="View" plain @click="handleDetail">查看详情</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleRowDblclick">
            <el-table-column type="selection" width="55" />
            <el-table-column label="发放编号" align="center">
                <template #default="scope">
                    <dict-tag :options="provinces" :value="scope.row.city" />
                </template>
            </el-table-column>
            <el-table-column label="发放名称" align="center" prop="username">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.username" />
                </template>
            </el-table-column>
            <el-table-column label="客户名称" align="center" prop="email">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.email" />
                </template>
            </el-table-column>
            <el-table-column label="薪资类别名称" align="center" prop="salaryCategory" />
            <el-table-column label="所属月" align="center" prop="salaryMonthStart" />
            <el-table-column label="工资计税月" align="center" prop="salaryMonthEnd" />
            <el-table-column label="客户账单月" align="center" prop="customerBillMonthStart" />
            <el-table-column label="是否确认" align="center" prop="isConfirm" />
            <el-table-column label="数据确认时间" align="center" prop="confirmTime" />
            <el-table-column label="发放状态" align="center" prop="payStatus" />
            <el-table-column label="缓发人数" align="center" prop="slowCount" />
            <el-table-column label="未发放人数" align="center" prop="unpaidCount" />
            <el-table-column label="发放人数" align="center" prop="paidCount" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="创建人" align="center" prop="createUser" />
            <el-table-column label="项目客服" align="center" prop="projectCustomer" />
            <el-table-column label="薪资客服" align="center" prop="salaryCustomer" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button type="warning" plain icon="View" :disabled="obj.single"
                        @click="handleView">查看抵扣专项信息</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="55%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="薪资类别名称:" prop="salaryCategory">
                    <el-select class="width220" filterable v-model="obj.dialogForm.salaryCategory" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="发放名称:" prop="payrollNumber">
                    <el-select class="width220" filterable v-model="obj.dialogForm.payrollNumber" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户账单月:" prop="customerBillMonthStart">
                    <el-select class="width220" filterable v-model="obj.dialogForm.customerBillMonthStart"
                        placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工资所属月:" prop="salaryMonthStart">
                    <el-input class="width220" v-model="obj.dialogForm.salaryMonthStart" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="工资计税月:" prop="salaryMonthEnd">
                    <el-input class="width220" v-model="obj.dialogForm.salaryMonthEnd" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <div style="color: red;font-weight: bold; font-style: italic;">
                        工资计税月与客户账单月应一致
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 查看抵扣专项信息 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="60%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="客户名称:" prop="customer">
                    <el-input class="width220" v-model="obj.dialogForm.customer" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="姓名:" prop="name">
                    <el-input class="width220" v-model="obj.dialogForm.name" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="证件号码:" prop="idNumber">
                    <el-input class="width220" v-model="obj.dialogForm.idNumber" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="所得月起:" prop="incomeMonthStart">
                    <el-input class="width220" v-model="obj.dialogForm.incomeMonthStart" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="所得月止:" prop="incomeMonthEnd">
                    <el-input class="width220" v-model="obj.dialogForm.incomeMonthEnd" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">查询</el-button>
                    <el-button type="primary" @click="handleReset">重置</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="obj.tableData3" border>
                <el-table-column label="序号" align="center" prop="id" />
                <el-table-column label="员工编号" align="center" prop="employeeNo" />
                <el-table-column label="姓名" align="center" prop="name" />
                <el-table-column label="证件类型" align="center" prop="idType" />
                <el-table-column label="证件号码" align="center" prop="idNo" />
                <el-table-column label="所得月" align="center" prop="incomeMonthStart" />
                <el-table-column label="子女教育支出" align="center" prop="childEducationDeduction" />
                <el-table-column label="住房租金支出" align="center" prop="housingRentDeduction" />
                <el-table-column label="住房贷款利息支出" align="center" prop="housingLoanDeduction" />
                <el-table-column label="赡养老人支出" align="center" prop="oldSupportDeduction" />
                <el-table-column label="大病医疗" align="center" prop="largeDiseaseDeduction" />
                <el-table-column label="继续教育" align="center" prop="continueEducationDeduction" />
                <el-table-column label="3岁以下婴幼儿照护" align="center" prop="childcareDeduction" />
                <el-table-column label="个人养老金" align="center" prop="personalPension" />
                <el-table-column label="其它扣除" align="center" prop="otherDeduction" />
                <el-table-column label="入库时间" align="center" prop="createTime" />
                <el-table-column label="抵扣状态" align="center" prop="deductionStatus" />
            </el-table>
        </el-dialog>
        <!-- 人员明细 -->
        <PersonnelDetails v-model:dialogShow="obj.dialogShow3" title="查看发放详情" :tableData="obj.tableData2"
            menuName="payrollCalculation" />
    </div>
</template>

<script setup name="PayrollCalculation">
import { listScale } from '@/api/reonApi/scale';
import PersonnelDetails from '@/views/reonManage/components/dialog/personnelDetails.vue'
import { useAreaStore } from '@/store/modules/area'

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    rules: {
        city: [{ required: true, message: '请选择所属城市', trigger: 'blur' }],
        type: [{ required: true, message: '请选择人员类型', trigger: 'blur' }],
    },
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            salaryCategory: '工资',
            salaryMonthStart: '2023-06',
            salaryMonthEnd: '2023-06',
            customerBillMonthStart: '2023-06',
        }
    ],//列表
    tableData2: [
        {
            id: 1,
            employeeNo: '0000000',
            name: '张三',
            idType: '身份证',
            idNo: '****************78',
            salaryMonthStart: '2023-06',
            salaryMonthEnd: '2023-06',
            customerBillMonthStart: '2023-06',
            customerBillMonthEnd: '2023-06',
            bankName: '工商银行',
            bankCardNo: '****************',
            payLocation: '北京',
            payeeName: '北京科技有限公司',
            isPayLocationChange: '是',
            isPayeeChange: '是',
            isBankCardChange: '是',
            payStatus: '已发放',
            bankPayRemark: '无',
            customerConfirm: '是',
            isPayeeChange: '是',
            isBankCardChange: '是',
        }
    ],//列表
    tableData3: [
        {
            id: 1,
            employeeNo: '0000000',
            name: '张三',
            idType: '身份证',
            idNo: '****************78',
            incomeMonthStart: '2023-06',
            childEducationDeduction: '1000',
            housingRentDeduction: '1000',
            housingLoanDeduction: '1000',
            oldSupportDeduction: '1000',
            largeDiseaseDeduction: '1000',
            continueEducationDeduction: '1000',
            childcareDeduction: '1000',
            personalPension: '1000',
            otherDeduction: '1000',
            createTime: '2023-06',
            deductionStatus: '已抵扣',
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    dialogShow2: false, //弹出框
    dialogShow3: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {
        formTable: []
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 行双击
function handleRowDblclick(row) {
    handleDetail(row);
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增发放";
}
/** 查看按钮操作 */
function handleDetail(row) {
    obj.dialogShow3 = true;
    obj.title = "查看发放详情";
}

/** 人员明细 */
function closeStaffDetails() {
    obj.dialogShow3 = false;
}

/** 查看按钮操作 */
function handleView() {
    obj.dialogShow2 = true;
    obj.title = "查看抵扣专项信息";
}

/** 计算按钮操作 */
function handleCalculate() {
    const _ids = obj.ids;
}

/** 新增发放批次按钮操作 */
function handleAddBatch() {
    const _ids = obj.ids;
}

/** 删除按钮操作 */
function handleDelete(row) {

    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delClassify(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}


/** 导出按钮操作 */
function handleExport() {
    proxy.download('system/users/export', {
        ...obj.queryParams
    }, `users_${new Date().getTime()}.xlsx`)
}

getList();
</script>
<style lang="scss" scoped></style>