<template>
  <el-scrollbar ref="scrollContainer" :vertical="false" class="scroll-container" @wheel.prevent="handleScroll">
    <slot />
  </el-scrollbar>
</template>

<script setup>
import useTagsViewStore from '@/store/modules/tagsView'

const tagAndTagSpacing = ref(4);
const { proxy } = getCurrentInstance();

const scrollWrapper = computed(() => proxy.$refs.scrollContainer.$refs.wrapRef);

onMounted(() => {
  scrollWrapper.value.addEventListener('scroll', emitScroll, true)
})

onBeforeUnmount(() => {
  scrollWrapper.value.removeEventListener('scroll', emitScroll)
})

function handleScroll(e) {
  const eventDelta = e.wheelDelta || -e.deltaY * 40
  const $scrollWrapper = scrollWrapper.value;
  $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4
}

const emits = defineEmits()
const emitScroll = () => {
  emits('scroll')
}

const tagsViewStore = useTagsViewStore()
const visitedViews = computed(() => tagsViewStore.visitedViews);

/**
 * 滚动到指定的目标标签
 * 此函数用于在标签页组件中，滚动至指定的标签页，以确保目标标签在可视区域内
 * @param {Object} currentTag 当前选中的标签页对象，包含路径等信息
 */
function moveToTarget(currentTag) {
  // 获取滚动容器的DOM元素
  const $container = proxy.$refs.scrollContainer.$el
  // 计算容器的宽度
  const $containerWidth = $container.offsetWidth
  // 获取可滚动的区域的DOM元素
  const $scrollWrapper = scrollWrapper.value;

  // 初始化首尾标签页变量
  let firstTag = null
  let lastTag = null

  // 如果有已访问的视图，则设置首尾标签页
  if (visitedViews.value.length > 0) {
    firstTag = visitedViews.value[0]
    lastTag = visitedViews.value[visitedViews.value.length - 1]
  }

  // 如果当前标签是第一个标签，则滚动到最左边
  if (firstTag === currentTag) {
    $scrollWrapper.scrollLeft = 0
  }
  // 如果当前标签是最后一个标签，则滚动到最右边
  else if (lastTag === currentTag) {
    $scrollWrapper.scrollLeft = $scrollWrapper.scrollWidth - $containerWidth
  }
  else {
    // 获取所有标签页的DOM元素
    const tagListDom = document.getElementsByClassName('tags-view-item');
    // 找到当前标签在已访问视图中的索引
    const currentIndex = visitedViews.value.findIndex(item => item === currentTag)
    // 初始化前后标签页变量
    let prevTag = null
    let nextTag = null
    // 遍历所有标签页，找到当前标签的前后标签页
    for (const k in tagListDom) {
      if (k !== 'length' && Object.hasOwnProperty.call(tagListDom, k)) {
        if (tagListDom[k].dataset.path === visitedViews.value[currentIndex - 1].path) {
          prevTag = tagListDom[k];
        }
        if (tagListDom[k].dataset.path === visitedViews.value[currentIndex + 1].path) {
          nextTag = tagListDom[k];
        }
      }
    }

    // 计算滚动到下一个标签页的位置
    const afterNextTagOffsetLeft = nextTag.offsetLeft + nextTag.offsetWidth + tagAndTagSpacing.value

    // 计算滚动到前一个标签页的位置
    const beforePrevTagOffsetLeft = prevTag.offsetLeft - tagAndTagSpacing.value
    // 如果下一个标签页的位置超出当前可视区域，则滚动到该位置
    if (afterNextTagOffsetLeft > $scrollWrapper.scrollLeft + $containerWidth) {
      $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth
    }
    // 如果前一个标签页的位置不在当前可视区域内，则滚动到该位置
    else if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {
      $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft
    }
  }
}

defineExpose({
  moveToTarget,
})
</script>

<style lang='scss' scoped>
.scroll-container {
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  width: 100%;

  :deep(.el-scrollbar__bar) {
    bottom: 0px;
  }

  :deep(.el-scrollbar__wrap) {
    height: 39px;
  }
}
</style>