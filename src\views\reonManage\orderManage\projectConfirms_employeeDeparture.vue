<!-- 接单确认员工离职 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" clearable />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderCode">
                <el-input class="width220" v-model="obj.queryParams.orderCode" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="客户编号:" prop="customerCode">
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入客户编号" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="接单客服:" prop="receiverService">
                <el-input class="width220" v-model="obj.queryParams.receiverService" placeholder="请输入接单客服" clearable />
            </el-form-item>
            <el-form-item label="接单方:" prop="receiver">
                <el-input class="width220" v-model="obj.queryParams.receiver" placeholder="请输入接单方" clearable />
            </el-form-item>
            <el-form-item label="派单方:" prop="sender">
                <el-input class="width220" v-model="obj.queryParams.sender" placeholder="请输入派单方" clearable />
            </el-form-item>
            <el-form-item label="派单方客服:" prop="senderService">
                <el-input class="width220" v-model="obj.queryParams.senderService" placeholder="请输入派单方客服" clearable />
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-input class="width220" v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称"
                    clearable />
            </el-form-item>
            <el-form-item label="人员分布:" prop="personnelDistribution">
                <el-input class="width220" v-model="obj.queryParams.personnelDistribution" placeholder="请输入人员分布"
                    clearable />
            </el-form-item>
            <el-form-item label="申请离职日期(起):" prop="resignationDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.resignationDateStart" placeholder="请选择开始日期" />
            </el-form-item>
            <el-form-item label="申请离职日期(止):" prop="resignationDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.resignationDateEnd" placeholder="请选择结束日期" />
            </el-form-item>
            <el-form-item label="小合同编号:" prop="smallContractCode">
                <el-input class="width220" v-model="obj.queryParams.smallContractCode" placeholder="请输入小合同编号"
                    clearable />
            </el-form-item>
            <el-form-item label="是否单立户:" prop="isSingleAccount">
                <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleDetail">订单详情</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleConfirm">进入确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleBatchConfirm">批量确认</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleSave">保存</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail" :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员编号" align="center" prop="employeeCode" />
            <el-table-column label="订单编号" align="center" prop="orderCode" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="人员分布" align="center" prop="personnelDistribution" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="是否单立户" align="center" prop="isSingleAccount" />
            <el-table-column label="派单方" align="center" prop="sender" />
            <el-table-column label="派单方客服" align="center" prop="senderService" />
            <el-table-column label="接单方" align="center" prop="receiver" />
            <el-table-column label="接单方客服" align="center" prop="receiverService" />
            <el-table-column label="入职日期" align="center" prop="entryDate" />
            <el-table-column label="申请入职时间" align="center" prop="applyEntryTime" />
            <el-table-column label="入离职状态" align="center" prop="entryExitStatus" />
            <el-table-column label="离职确认状态" align="center" prop="resignationConfirmStatus" />
            <el-table-column label="离职日期" align="center" prop="resignationDate" />
            <el-table-column label="申请离职时间" align="center" prop="applyResignationTime" />
            <el-table-column label="离职原因" align="center" prop="resignationReason" />
            <el-table-column label="联系电话" align="center" prop="contactPhone" />
            <el-table-column label="手机号" align="center" prop="mobilePhone" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 订单详情 -->
        <order-details v-model:dialogShow="obj.dialogShow" :dialogForm="obj.dialogForm" :title="obj.title"
            isDetail="true" />
        <!-- 进入确认 -->
        <el-dialog v-model="obj.dialogShow2" title="离职确认" width="60%" append-to-body>
            <el-form :model="obj.dialogForm" inline label-width="auto">
                <el-form-item label="雇员编号" prop="employeeCode">
                    <el-input class="width220" v-model="obj.dialogForm.employeeCode" placeholder="请输入雇员编号" disabled />
                </el-form-item>
                <el-form-item label="雇员姓名" prop="employeeName">
                    <el-input class="width220" v-model="obj.dialogForm.employeeName" placeholder="请输入雇员姓名" disabled />
                </el-form-item>
                <el-form-item label="客户编号" prop="customerCode">
                    <el-input class="width220" v-model="obj.dialogForm.customerCode" placeholder="请输入客户编号" disabled />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-input class="width220" v-model="obj.dialogForm.customerName" placeholder="请输入客户名称" disabled />
                </el-form-item>
                <el-form-item label="小合同" prop="smallContractName">
                    <el-input class="width220" v-model="obj.dialogForm.smallContractName" placeholder="请输入小合同名称"
                        disabled />
                </el-form-item>
                <el-form-item label="证件编号" prop="idNumber">
                    <el-input class="width220" v-model="obj.dialogForm.idNumber" placeholder="请输入证件编号" disabled />
                </el-form-item>
                <el-form-item label="派单方" prop="sender">
                    <el-input class="width220" v-model="obj.dialogForm.sender" placeholder="请输入派单方" disabled />
                </el-form-item>
                <el-form-item label="接单方" prop="receiver">
                    <el-input class="width220" v-model="obj.dialogForm.receiver" placeholder="请输入接单方" disabled />
                </el-form-item>
                <el-form-item label="派单方客服" prop="senderService">
                    <el-input class="width220" v-model="obj.dialogForm.senderService" placeholder="请输入派单方客服" disabled />
                </el-form-item>
                <el-form-item label="接单方客服" prop="receiverService">
                    <el-input class="width220" v-model="obj.dialogForm.receiverService" placeholder="请输入接单方客服"
                        disabled />
                </el-form-item>
                <el-form-item label="订单状态" prop="orderStatus">
                    <el-input class="width220" v-model="obj.dialogForm.orderStatus" placeholder="请输入订单状态" disabled />
                </el-form-item>
                <el-form-item label="是否离职外呼" prop="isResignationCall">
                    <el-select class="width220" v-model="obj.dialogForm.isResignationCall" placeholder="请选择">
                        <el-option label="是" value="1" />
                        <el-option label="否" value="0" />
                    </el-select>
                </el-form-item>
                <el-form-item label="入职日期" prop="entryDate">
                    <el-input class="width220" v-model="obj.dialogForm.entryDate" placeholder="请输入入职日期" disabled />
                </el-form-item>
                <el-form-item label="申报入职时间" prop="applyEntryTime">
                    <el-input class="width220" v-model="obj.dialogForm.applyEntryTime" placeholder="请输入申报入职时间"
                        disabled />
                </el-form-item>
                <el-form-item label="离职日期" prop="resignationDate">
                    <el-date-picker class="width220" v-model="obj.dialogForm.resignationDate" placeholder="请选择离职日期" />
                </el-form-item>
                <el-form-item label="申报离职日期" prop="applyResignationDate">
                    <el-input class="width220" v-model="obj.dialogForm.applyResignationDate" placeholder="请输入申报离职日期"
                        disabled />
                </el-form-item>
                <el-form-item label="申报离职人" prop="applyResignationPerson">
                    <el-input class="width220" v-model="obj.dialogForm.applyResignationPerson" placeholder="请输入申报离职人"
                        disabled />
                </el-form-item>
                <el-form-item label="离职原因" prop="resignationReason">
                    <el-select class="width220" v-model="obj.dialogForm.resignationReason" placeholder="请选择">
                        <el-option label="个人原因" value="个人原因" />
                        <el-option label="公司原因" value="公司原因" />
                        <el-option label="其他" value="其他" />
                    </el-select>
                </el-form-item>
                <el-form-item label="减员原因" prop="reductionReason">
                    <el-select class="width220" v-model="obj.dialogForm.reductionReason" placeholder="请选择">
                        <el-option label="个人原因" value="个人原因" />
                        <el-option label="公司原因" value="公司原因" />
                        <el-option label="其他" value="其他" />
                    </el-select>
                </el-form-item>
                <el-form-item label="减员详细原因" prop="reductionDetailReason">
                    <el-input class="width220" v-model="obj.dialogForm.reductionDetailReason" placeholder="请输入减员详细原因" />
                </el-form-item>
                <el-form-item label="离职备注" prop="resignationRemark">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.resignationRemark"
                        placeholder="请输入离职备注" />
                </el-form-item>
                <el-form-item label="离职确认过程" prop="resignationConfirmProcess">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.resignationConfirmProcess"
                        placeholder="请输入离职确认过程" />
                </el-form-item>
                <el-form-item label="变更确认过程" prop="changeConfirmProcess">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.changeConfirmProcess"
                        placeholder="请输入变更确认过程" />
                </el-form-item>
                <el-form-item label="接单备注" prop="receiverRemark">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.receiverRemark"
                        placeholder="请输入接单备注" />
                </el-form-item>
                <el-form-item label="变更备注" prop="changeRemark">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.changeRemark"
                        placeholder="请输入变更备注" />
                </el-form-item>
                <border-box title="产品">
                    <el-table :data="obj.dialogForm.products" border>
                        <el-table-column label="序号" align="center" type="index" />
                        <el-table-column label="产品名称" align="center" prop="productName" />
                        <el-table-column label="社保公积金" align="center" prop="socialSecurity" />
                        <el-table-column label="收费开始月" align="center" prop="chargeStartMonth" />
                        <el-table-column label="收费截止月" align="center" prop="chargeEndMonth" />
                    </el-table>
                </border-box>
                <el-form-item label="备注" prop="remark">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";
import orderDetails from '../components/dialog/orderDetails.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null,
        orderCode: null,
        employeeName: null,
        idNumber: null,
        customerCode: null,
        customerName: null,
        receiverService: null,
        receiver: null,
        sender: null,
        senderService: null,
        smallContractName: null,
        personnelDistribution: null,
        resignationDateStart: null,
        resignationDateEnd: null,
        smallContractCode: null,
        isSingleAccount: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {
        products: []
    },//表单
    dialogShow: false,//订单详情弹窗
    dialogShow2: false,//离职确认弹窗
    bindSave: false,//绑定保存
    ids: [],//选中的id
    title: "",//标题
})

// 表格行类名
const tableRowClassName = ({ row }) => {
    if (row.id === 1) {
        return 'warning-row'
    } else if (row.id === 3) {
        return 'success-row'
    }
    return ''
}

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeCode: 'GY20230001',
                orderCode: 'DD20230001',
                employeeName: '张三',
                idNumber: '110101199001011234',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                personnelDistribution: '北京',
                smallContractName: '小合同名称1',
                isSingleAccount: '是',
                sender: '派单方1',
                senderService: '派单方客服1',
                receiver: '接单方1',
                receiverService: '接单方客服1',
                entryDate: '2023-01-01',
                applyEntryTime: '2023-01-01 10:00:00',
                entryExitStatus: '在职',
                resignationConfirmStatus: '未确认',
                resignationDate: '2023-06-01',
                applyResignationTime: '2023-05-20 10:00:00',
                resignationReason: '个人原因',
                contactPhone: '010-********',
                mobilePhone: '***********'
            },
            {
                id: 2,
                employeeCode: 'GY20230002',
                orderCode: 'DD20230002',
                employeeName: '李四',
                idNumber: '110101199001011235',
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                personnelDistribution: '上海',
                smallContractName: '小合同名称2',
                isSingleAccount: '否',
                sender: '派单方2',
                senderService: '派单方客服2',
                receiver: '接单方2',
                receiverService: '接单方客服2',
                entryDate: '2023-02-01',
                applyEntryTime: '2023-02-01 10:00:00',
                entryExitStatus: '在职',
                resignationConfirmStatus: '未确认',
                resignationDate: '2023-07-01',
                applyResignationTime: '2023-06-20 10:00:00',
                resignationReason: '公司原因',
                contactPhone: '010-********',
                mobilePhone: '***********'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 订单详情
function handleDetail(row) {
    obj.dialogShow = true;
    obj.title = '订单详情';
    obj.dialogForm = row || {};
}

// 关闭订单详情
function handleClose() {
    obj.dialogShow = false;
    obj.dialogForm = {
        products: []
    };
}

// 进入确认
function handleConfirm() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条数据进行确认');
        return;
    }
    obj.dialogShow2 = true;
    // 模拟数据
    const row = obj.tableData.find(item => item.id === obj.ids[0]);
    if (row) {
        obj.dialogForm = {
            ...row,
            products: [
                {
                    id: 1,
                    productName: '产品1',
                    socialSecurity: '社保',
                    chargeStartMonth: '2023-01',
                    chargeEndMonth: '2023-06'
                },
                {
                    id: 2,
                    productName: '产品2',
                    socialSecurity: '公积金',
                    chargeStartMonth: '2023-01',
                    chargeEndMonth: '2023-06'
                }
            ],
            remark: ''
        };
    }
}

// 批量确认
function handleBatchConfirm() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要确认的数据');
        return;
    }
    proxy.$modal.confirm('确认要批量确认选中的数据吗？').then(() => {
        proxy.$modal.msgSuccess('确认成功');
    }).catch(() => { });
}

// 保存
function handleSave() {
    proxy.$modal.confirm('确认要保存当前数据吗？').then(() => {
        proxy.$modal.msgSuccess('保存成功');
    }).catch(() => { });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped>
:deep(.warning-row) {
    color: red;
}
</style>
