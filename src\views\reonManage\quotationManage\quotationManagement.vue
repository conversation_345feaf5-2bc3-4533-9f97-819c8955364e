<!-- 报价管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="100px">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="区域:" prop="region">
                <el-select class="width220" v-model="obj.queryParams.region" placeholder="请选择区域" clearable>
                    <el-option v-for="item in regionOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报价单编号:" prop="offerNo">
                <el-input class="width220" v-model="obj.queryParams.offerNo" placeholder="请输入报价单编号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="报价单名称:" prop="offerName">
                <el-input class="width220" v-model="obj.queryParams.offerName" placeholder="请输入报价单名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="销售:" prop="sales">
                <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择销售" clearable>
                    <el-option v-for="item in salesOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报价单类型:" prop="offerType">
                <el-select class="width220" v-model="obj.queryParams.offerType" placeholder="请选择报价单类型" clearable>
                    <el-option v-for="item in offerTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="产品类别:" prop="productCategory">
                <el-select class="width220" v-model="obj.queryParams.productCategory" placeholder="请选择产品类别" clearable>
                    <el-option v-for="item in productCategoryOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="obj.multiple"
                    @click="handleDelete">删除</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="报价编号" align="center" prop="offerNo" />
            <el-table-column label="报价单名称" align="center" prop="offerName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="报价单类型" align="center" prop="offerType" />
            <el-table-column label="产品类型" align="center" prop="productType" />
            <el-table-column label="区域" align="center" prop="region" />
            <el-table-column label="销售" align="center" prop="sales" />
            <el-table-column label="日期" align="center" prop="date">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.date, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="报价流程" align="center" prop="offerProcess" />
            <el-table-column label="报价单状态" align="center" prop="offerStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.offerStatus === '1' ? 'success' : scope.row.offerStatus === '2' ? 'warning' : 'danger'">
                        {{ scope.row.offerStatus === '1' ? '有效' : scope.row.offerStatus === '2' ? '待审核' : '失效' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="审核状态" align="center" prop="approvalStatus">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.approvalStatus === '1' ? 'success' : scope.row.approvalStatus === '2' ? 'warning' : 'info'">
                        {{ scope.row.approvalStatus === '1' ? '已审核' : scope.row.approvalStatus === '2' ? '审核中' : '未审核'
                        }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="150">
                <template #default="scope">
                    <el-button link icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                    <el-button link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改2对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="60%" draggable append-to-body>
            <el-tabs type="border-card">
                <el-tab-pane label="基本信息">
                    <el-form class="formHight" ref="formRef" :model="obj.dialogForm" inline :rules="obj.rules"
                        label-width="auto">
                        <el-form-item label="报价名称" prop="offerName">
                            <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.offerName"
                                placeholder="请输入报价名称" />
                        </el-form-item>
                        <el-form-item label="报价单类型" prop="offerType">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.offerType"
                                placeholder="请选择报价单类型" clearable>
                                <el-option v-for="item in offerTypeOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="报价流程" prop="offerProcess">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.offerProcess"
                                placeholder="请选择报价流程" clearable>
                                <el-option label="标准流程" value="标准流程" />
                                <el-option label="定制流程" value="定制流程" />
                                <el-option label="竞价流程" value="竞价流程" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="客户名称" prop="customerName">
                            <el-input :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.customerName"
                                placeholder="请输入客户名称" />
                        </el-form-item>
                        <el-form-item label="产品类型" prop="productType">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.productType"
                                placeholder="请选择产品类型" clearable>
                                <el-option v-for="item in productCategoryOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="产品子类" prop="productSubtype">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.productSubtype"
                                placeholder="请选择产品子类" clearable>
                                <el-option label="子类型1" value="子类型1" />
                                <el-option label="子类型2" value="子类型2" />
                                <el-option label="子类型3" value="子类型3" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="区域" prop="region">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.region"
                                placeholder="请选择区域" clearable>
                                <el-option v-for="item in regionOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="是否含税" prop="includeTax">
                            <el-select :disabled="obj.isDetail" class="width220" v-model="obj.dialogForm.includeTax"
                                placeholder="请选择是否含税" clearable>
                                <el-option label="是" value="1" />
                                <el-option label="否" value="0" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="税率" prop="taxRate">
                            <el-input :disabled="obj.isDetail" class="width170" v-model="obj.dialogForm.taxRate"
                                placeholder="请输入税率" />
                            <span class="ml5">%</span><span class="ml5" style="color:#1E9FFF;cursor: pointer;"
                                @click="openHTML">示例</span>
                        </el-form-item>
                        <el-form-item label="收费频率" prop="billingFrequency">
                            <el-select :disabled="obj.isDetail" class="width220"
                                v-model="obj.dialogForm.billingFrequency" placeholder="请选择收费频率" clearable>
                                <el-option label="月度" value="月度" />
                                <el-option label="季度" value="季度" />
                                <el-option label="半年" value="半年" />
                                <el-option label="年度" value="年度" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="回款日期" prop="paymentDate">
                            <el-date-picker :disabled="obj.isDetail" class="width220" clearable
                                v-model="obj.dialogForm.paymentDate" type="date" value-format="YYYY-MM-DD"
                                placeholder="请选择回款日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-table :data="obj.dialogForm.usersList" class="mt10 mb20" style="width: 100%" border
                            @selection-change="handleSelectionChange">
                            <el-table-column type="selection" width="55" />
                            <el-table-column label="人数" prop="startValue" align="center">
                            </el-table-column>
                            <el-table-column label="首批" prop="endValue" align="center">
                            </el-table-column>
                            <el-table-column label="报价" prop="price" align="center">
                            </el-table-column>
                            <el-table-column label="赠送产品成本" prop="price" align="center">
                            </el-table-column>
                            <el-table-column label="赠送产品成本备注" prop="price" align="center">
                            </el-table-column>
                        </el-table>
                        <el-form-item label="备注" prop="remark">
                            <el-input :disabled="obj.isDetail" class="width620" v-model="obj.dialogForm.remark"
                                :rows="2" type="textarea" placeholder="请输入备注内容" />
                        </el-form-item>
                        <el-divider content-position="left">文件上传</el-divider>
                        <!-- <TEditor style="width: 1000px;" v-model="obj.dialogForm.editorContent" /> -->
                        <file-upload @update:modelValue="uploadedSuccessfully" v-model="obj.dialogForm.isActive" />
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="流程信息">
                    <ProcessInformation :tableData="obj.dialogForm.tableData" />
                </el-tab-pane>
            </el-tabs>

            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="QuotationManagement">
import { listInstance, delInstance } from '@/api/reonApi/instance';
import { ElMessage } from 'element-plus'
import ProcessInformation from '@/views/reonManage/components/processInformation.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 区域选项
const regionOptions = [
    { value: '1', label: '华北区' },
    { value: '2', label: '华东区' },
    { value: '3', label: '华南区' },
    { value: '4', label: '西南区' },
    { value: '5', label: '西北区' },
    { value: '6', label: '东北区' },
    { value: '7', label: '华中区' }
];

// 销售选项
const salesOptions = [
    { value: '1', label: '销售A' },
    { value: '2', label: '销售B' },
    { value: '3', label: '销售C' },
    { value: '4', label: '销售D' }
];

// 报价单类型选项
const offerTypeOptions = [
    { value: '1', label: '标准报价' },
    { value: '2', label: '定制报价' },
    { value: '3', label: '竞价报价' },
    { value: '4', label: '特殊报价' }
];

// 产品类别选项
const productCategoryOptions = [
    { value: '1', label: '人力资源服务' },
    { value: '2', label: '社保服务' },
    { value: '3', label: '培训服务' },
    { value: '4', label: '其他服务' }
];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        region: null,
        offerNo: null,
        offerName: null,
        sales: null,
        offerType: null,
        productCategory: null
    },//查询表单
    rules: {
        username: [
            { required: true, message: "2不能为空", trigger: "blur" }
        ],
        email: [
            { required: true, message: "3不能为空", trigger: "blur" }
        ],
    },
    total: 0,//总条数

    tableData: [
        {
            id: 1,
            offerNo: 'OFFER20230501001',
            offerName: '人力资源服务报价A',
            customerName: '客户A',
            offerType: '标准报价',
            productType: '人力资源服务',
            region: '华北区',
            sales: '销售A',
            date: '2023-05-01',
            offerProcess: '标准流程',
            offerStatus: '1',
            approvalStatus: '1'
        },
        {
            id: 2,
            offerNo: 'OFFER20230502001',
            offerName: '社保服务报价B',
            customerName: '客户B',
            offerType: '定制报价',
            productType: '社保服务',
            region: '华东区',
            sales: '销售B',
            date: '2023-05-02',
            offerProcess: '定制流程',
            offerStatus: '2',
            approvalStatus: '2'
        }
    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
    isDetail: false,//是否查看
})
const openHTML = () => {
    ElMessage({
        dangerouslyUseHTMLString: true,
        message: `
          <div style='text-align:center;'>默认:0%</div>
          <div style='text-align:center;'>派遣、代理通常:5%</div>
          <div style='text-align:center;'>外包1、外包2通常:6.83%</div>
          `,
    })
}
/** 列表 */
function getList() {
    obj.loading = true;
    listInstance(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    obj.isDetail = false;
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增报价单";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = {
        ...row
    };
    obj.dialogShow = true;
    obj.title = "修改报价单";
}

/** 查看 */
function handleDetail(row) {
    obj.isDetail = true;
    obj.dialogForm = {
        ...row
    };
    obj.dialogShow = true;
    obj.title = "查看报价单";
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const _ids = row?.id || obj.ids;
    proxy.$modal.confirm('是否确认删除城市人员分类编号为"' + _ids + '"的数据项？').then(function () {
        return delInstance(_ids);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消删除");
    });
}


// 上传成功回调
function uploadedSuccessfully(value) {
    console.log(value);
}

getList();
</script>
<style lang="scss" scoped></style>