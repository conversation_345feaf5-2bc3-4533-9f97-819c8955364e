<!-- 垫付报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="集团名称:" prop="groupName">
                <el-input class="width220" v-model="obj.queryParams.groupName" placeholder="请输入集团名称" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" readonly @click="handleClient" v-model="obj.queryParams.customerName"
                    placeholder="请选择客户" />
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-form-item label="客户帐套:" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户帐套" clearable>
                    <el-option v-for="item in customerAccountOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择账单年月"
                    clearable />
            </el-form-item>
            <el-form-item label="财务应收年月:" prop="financeReceivableMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.financeReceivableMonth" type="month"
                    placeholder="请选择财务应收年月" clearable />
            </el-form-item>
            <el-form-item label="出账单分公司:" prop="billingCompany">
                <el-select class="width220" v-model="obj.queryParams.billingCompany" placeholder="请选择分公司" clearable>
                    <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="锁定状态:" prop="lockStatus">
                <el-select class="width220" v-model="obj.queryParams.lockStatus" placeholder="请选择锁定状态" clearable>
                    <el-option v-for="item in lockStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="核销状态:" prop="verificationStatus">
                <el-select class="width220" v-model="obj.queryParams.verificationStatus" placeholder="请选择核销状态"
                    clearable>
                    <el-option v-for="item in verificationStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="责任客服:" prop="customerService">
                <el-select class="width220" v-model="obj.queryParams.customerService" placeholder="请选择责任客服" clearable>
                    <el-option v-for="item in customerServiceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb20">
                <el-col :span="24">
                    <el-button icon="Refresh" @click="handleReset">重置</el-button>
                    <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
                    <el-button type="primary" icon="Download" @click="handleExport">导出明细</el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" border style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="groupName" label="集团名称" align="center" />
            <el-table-column prop="customerName" label="客户名称" align="center" />
            <el-table-column prop="customerAccount" label="客户帐套" align="center" />
            <el-table-column prop="billMonth" label="账单年月" align="center" />
            <el-table-column prop="financeReceivableMonth" label="财务应收年月" align="center" />
            <el-table-column prop="billPersonCount" label="账单人数" align="center">
                <template #default="scope">
                    <span>{{ scope.row.billPersonCount }} 人</span>
                </template>
            </el-table-column>
            <el-table-column prop="receivableAmount" label="应收金额" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.receivableAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="verifiedAmount" label="已核销金额" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.verifiedAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="verificationStatus" label="核销状态" align="center">
                <template #default="scope">
                    <dict-tag :options="verificationStatusOptions" :value="scope.row.verificationStatus" />
                </template>
            </el-table-column>
            <el-table-column prop="advanceAmount" label="垫付金额" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.advanceAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="dispatchCompany" label="派单分公司" align="center" />
            <el-table-column prop="projectService" label="项目客服" align="center" />
        </el-table>
        <client v-model:show="clientShow" @select="handleSelect" />
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="AdvanceStatement">

import { listScale } from "@/api/reonApi/scale";
import client from '@/views/reonManage/components/client.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 客户帐套选项
const customerAccountOptions = ref([
    { value: '1', label: '帐套A' },
    { value: '2', label: '帐套B' },
    { value: '3', label: '帐套C' }
]);

// 公司选项
const companyOptions = ref([
    { value: '1', label: '分公司A' },
    { value: '2', label: '分公司B' },
    { value: '3', label: '分公司C' }
]);

// 锁定状态选项
const lockStatusOptions = ref([
    { value: '0', label: '未锁定' },
    { value: '1', label: '已锁定' }
]);

// 核销状态选项
const verificationStatusOptions = ref([
    { value: '0', label: '未核销' },
    { value: '1', label: '部分核销' },
    { value: '2', label: '全部核销' }
]);

// 客服选项
const customerServiceOptions = ref([
    { value: '1', label: '客服1' },
    { value: '2', label: '客服2' },
    { value: '3', label: '客服3' }
]);

// 客户选择对话框显示状态
const clientShow = ref(false);

const obj = reactive({
    // 加载状态
    loading: false,
    // 选中数据
    ids: [],
    // 单选
    single: true,
    // 多选
    multiple: true,
    // 总条数
    total: 0,
    // 表格数据
    tableData: [],
    // 查询参数
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null,
        customerName: null,
        contractName: null,
        customerAccount: null,
        billMonth: null,
        financeReceivableMonth: null,
        billingCompany: null,
        lockStatus: null,
        verificationStatus: null,
        customerService: null
    }
});



/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                groupName: '集团A',
                customerName: '客户A',
                customerAccount: '帐套A',
                billMonth: '2023-05',
                financeReceivableMonth: '2023-06',
                billPersonCount: 100,
                receivableAmount: 50000.00,
                verifiedAmount: 30000.00,
                verificationStatus: '1',
                advanceAmount: 20000.00,
                dispatchCompany: '分公司A',
                projectService: '客服1'
            },
            {
                id: 2,
                groupName: '集团B',
                customerName: '客户B',
                customerAccount: '帐套B',
                billMonth: '2023-06',
                financeReceivableMonth: '2023-07',
                billPersonCount: 150,
                receivableAmount: 75000.00,
                verifiedAmount: 75000.00,
                verificationStatus: '2',
                advanceAmount: 0.00,
                dispatchCompany: '分公司B',
                projectService: '客服2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    handleSearch();
}

/** 客户选择 */
function handleClient() {
    clientShow.value = true;
}

/** 导出按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 选择客户 */
function handleSelect(row) {
    if (row) {
        obj.queryParams.customerName = row.name;
        obj.queryParams.customerId = row.id;
    } else {
        obj.queryParams.customerName = null;
        obj.queryParams.customerId = null;
    }
}


// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>