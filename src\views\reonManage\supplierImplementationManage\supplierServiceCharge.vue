<!-- 供应商关联实做服务费页面 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-input class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请输入福利办理方" />
            </el-form-item>
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-input class="width220" v-model="obj.queryParams.supplierName" placeholder="请输入供应商名称" />
            </el-form-item>
            <el-form-item label="生成月:" prop="generateMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.generateMonth" type="month"
                    placeholder="请选择生成月" />
            </el-form-item>
            <el-form-item label="是否锁定:" prop="isLocked">
                <el-select class="width220" v-model="obj.queryParams.isLocked" placeholder="请选择是否锁定">
                    <el-option v-for="item in lockOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleCalculate">计算服务费</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon='Plus' @click="handleAdd">新增关联</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleUpdate">修改关联</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出详情</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="供应商" align="center" prop="supplierName" />
            <el-table-column label="供应商账单模版" align="center" prop="billTemplate" />
            <el-table-column label="供应商账单状态" align="center" prop="billStatus" />
            <el-table-column label="账单服务费总额" align="center" prop="serviceFeeTotal" />
            <el-table-column label="账单服务费人数" align="center" prop="serviceFeeCount" />
            <el-table-column label="报价单编号" align="center" prop="quoteNo" />
            <el-table-column label="报价单名称" align="center" prop="quoteName" />
            <el-table-column label="报价单金额" align="center" prop="quoteAmount" />
            <el-table-column label="最早生成月" align="center" prop="earliestMonth" />
            <el-table-column label="生成月" align="center" prop="generateMonth" />
            <el-table-column label="生成人" align="center" prop="creator" />
            <el-table-column label="生成时间" align="center" prop="createTime" width="180" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 计算服务费 -->
        <el-dialog v-model="obj.dialogShow" title="计算服务费" width="30%">
            <el-form class="mt20" :model="obj.dialogForm" ref="dialogRef" label-width="auto">
                <el-form-item label="生成月:" prop="generateMonth">
                    <el-date-picker style="width:100%" v-model="obj.dialogForm.generateMonth" type="month"
                        placeholder="请选择生成月" />
                </el-form-item>
                <el-form-item label="福利办理方:" prop="welfareHandler">
                    <el-input style="width:100%" v-model="obj.dialogForm.welfareHandler" placeholder="请输入福利办理方" />
                </el-form-item>
                <el-form-item label="供应商:" prop="supplierName">
                    <el-input style="width:100%" v-model="obj.dialogForm.supplierName" placeholder="请输入供应商" />
                </el-form-item>
                <el-form-item label="账单模版名称:" prop="billTemplate">
                    <el-select style="width:100%" v-model="obj.dialogForm.billTemplate" placeholder="请选择账单模版名称">
                        <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="报价单编号:" prop="quoteNo">
                    <el-input style="width:100%" v-model="obj.dialogForm.quoteNo" placeholder="请输入报价单编号" />
                </el-form-item>
                <el-form-item label="报价单名称:" prop="quoteName">
                    <el-input style="width:100%" v-model="obj.dialogForm.quoteName" placeholder="请输入报价单名称" />
                </el-form-item>
                <el-form-item label="服务费:" prop="serviceFee">
                    <el-input style="width:100%" v-model="obj.dialogForm.serviceFee" placeholder="请输入服务费" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleCalculateSubmit">计算</el-button>
                <el-button @click="closeDialog">取消</el-button>
            </template>
        </el-dialog>
        <!-- 新增关联 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="30%">
            <el-form class="mt20" :model="obj.relationForm" ref="relationRef" label-width="auto">
                <el-form-item label="福利办理方:" prop="welfareHandler">
                    <el-select style="width:100%" v-model="obj.relationForm.welfareHandler" placeholder="请选择福利办理方">
                        <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="供应商:" prop="supplierName">
                    <el-select style="width:100%" v-model="obj.relationForm.supplierName" placeholder="请选择供应商">
                        <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="报价单编号:" prop="quoteNo">
                    <el-select style="width:100%" v-model="obj.relationForm.quoteNo" placeholder="请选择报价单编号">
                        <el-option v-for="item in quoteNoOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="报价单名称:" prop="quoteName">
                    <el-input style="width:100%" v-model="obj.relationForm.quoteName" placeholder="请输入报价单名称" />
                </el-form-item>
                <el-form-item label="服务费:" prop="serviceFee">
                    <el-input style="width:100%" v-model="obj.relationForm.serviceFee" placeholder="请输入服务费" />
                </el-form-item>
                <el-form-item label="生成开始月:" prop="startMonth">
                    <el-date-picker style="width:100%" v-model="obj.relationForm.startMonth" type="month"
                        placeholder="请选择生成开始月" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="handleRelationSubmit">保存</el-button>
                <el-button @click="closeRelationDialog">取消</el-button>
            </template>
        </el-dialog>

    </div>
</template>


<script setup name="SupplierServiceCharge">

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 锁定状态选项
const lockOptions = [
    { value: '0', label: '未锁定' },
    { value: '1', label: '已锁定' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null,
        supplierName: null,
        generateMonth: null,
        isLocked: null,
    },//查询表单
    total: 10,//总条数
    tableData: [],//列表
    dialogForm: {
        generateMonth: null,
        welfareHandler: null,
        supplierName: null,
        billTemplate: null,
        quoteNo: null,
        quoteName: null,
        serviceFee: null
    },//计算服务费表单
    relationForm: {
        welfareHandler: null,
        supplierName: null,
        quoteNo: null,
        quoteName: null,
        serviceFee: null,
        startMonth: null
    },//关联表单
    dialogShow: false,//计算服务费弹窗
    dialogShow2: false,//关联弹窗
    ids: [],//选中的id
    title: "",//标题
})


/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                welfareHandler: '福利办理方A',
                supplierName: '供应商A',
                billTemplate: '模版A',
                billStatus: '已生成',
                serviceFeeTotal: 10000,
                serviceFeeCount: 100,
                quoteNo: '**********',
                quoteName: '报价单A',
                quoteAmount: 10000,
                earliestMonth: '2023-01',
                generateMonth: '2023-05',
                creator: '张三',
                createTime: '2023-05-15 10:30:00'
            },
            {
                id: 2,
                welfareHandler: '福利办理方B',
                supplierName: '供应商B',
                billTemplate: '模版B',
                billStatus: '未生成',
                serviceFeeTotal: 15000,
                serviceFeeCount: 150,
                quoteNo: '**********',
                quoteName: '报价单B',
                quoteAmount: 15000,
                earliestMonth: '2023-02',
                generateMonth: '2023-05',
                creator: '李四',
                createTime: '2023-05-16 14:20:00'
            },
            {
                id: 3,
                welfareHandler: '福利办理方C',
                supplierName: '供应商C',
                billTemplate: '模版C',
                billStatus: '已生成',
                serviceFeeTotal: 8000,
                serviceFeeCount: 80,
                quoteNo: '**********',
                quoteName: '报价单C',
                quoteAmount: 8000,
                earliestMonth: '2023-03',
                generateMonth: '2023-06',
                creator: '王五',
                createTime: '2023-06-10 09:15:00'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 计算服务费
function handleCalculate() {
    obj.dialogShow = true;
}

// 关闭计算服务费弹窗
function closeDialog() {
    obj.dialogShow = false;
    obj.dialogForm = {
        generateMonth: null,
        welfareHandler: null,
        supplierName: null,
        billTemplate: null,
        quoteNo: null,
        quoteName: null,
        serviceFee: null
    };
}

// 提交计算服务费
function handleCalculateSubmit() {
    if (!obj.dialogForm.generateMonth) {
        proxy.$modal.msgError("请选择生成月");
        return;
    }
    if (!obj.dialogForm.welfareHandler) {
        proxy.$modal.msgError("请输入福利办理方");
        return;
    }
    if (!obj.dialogForm.supplierName) {
        proxy.$modal.msgError("请输入供应商");
        return;
    }

    proxy.$modal.msgSuccess("计算服务费成功");
    closeDialog();
    getList();
}

// 新增关联
function handleAdd() {
    obj.dialogShow2 = true;
    obj.title = "新增关联";
}

// 修改关联
function handleUpdate() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要修改的数据");
        return;
    }

    // 获取选中的数据
    const selectedData = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedData) {
        obj.relationForm = {
            welfareHandler: selectedData.welfareHandler,
            supplierName: selectedData.supplierName,
            quoteNo: selectedData.quoteNo,
            quoteName: selectedData.quoteName,
            serviceFee: selectedData.serviceFeeTotal / selectedData.serviceFeeCount,
            startMonth: selectedData.earliestMonth
        };
        obj.dialogShow2 = true;
        obj.title = "修改关联";
    }
}

// 关闭关联弹窗
function closeRelationDialog() {
    obj.dialogShow2 = false;
    obj.relationForm = {
        welfareHandler: null,
        supplierName: null,
        quoteNo: null,
        quoteName: null,
        serviceFee: null,
        startMonth: null
    };
}

// 提交关联
function handleRelationSubmit() {
    if (!obj.relationForm.welfareHandler) {
        proxy.$modal.msgError("请输入福利办理方");
        return;
    }
    if (!obj.relationForm.supplierName) {
        proxy.$modal.msgError("请输入供应商");
        return;
    }
    if (!obj.relationForm.quoteNo) {
        proxy.$modal.msgError("请输入报价单编号");
        return;
    }
    if (!obj.relationForm.startMonth) {
        proxy.$modal.msgError("请选择生成开始月");
        return;
    }

    proxy.$modal.msgSuccess(obj.title + "成功");
    closeRelationDialog();
    getList();
}

// 导出详情
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

getList();
</script>
<style lang="scss" scoped></style>