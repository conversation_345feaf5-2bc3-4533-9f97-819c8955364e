<!-- 实发数据导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入编号:" prop="importNo">
                <el-input class="width220" v-model="obj.queryParams.importNo" placeholder="请输入导入编号" clearable />
            </el-form-item>
            <el-form-item label="姓名:" prop="name">
                <el-input class="width220" v-model="obj.queryParams.name" placeholder="请输入姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号" clearable />
            </el-form-item>
            <el-form-item label="数据年月:" prop="dataMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.dataMonth" type="month" format="YYYY-MM"
                    value-format="YYYY-MM" placeholder="请选择年月" clearable />
            </el-form-item>
            <el-form-item label="扣缴义务人:" prop="withholdingName">
                <el-select class="width220" v-model="obj.queryParams.withholdingName" placeholder="请选择" clearable>
                    <el-option v-for="item in withholdingOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>

            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24">
                <el-button type="warning" plain icon="Upload" @click="handleImport">导入工资条实发数据</el-button>
                <el-button type="primary" plain icon="View" @click="handleRecord">查看导入记录</el-button>
            </el-col>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column type="index" align="center" width="50" />
            <el-table-column label="导入编号" align="center" prop="importNo" width="120" />
            <el-table-column label="导入时间" align="center" prop="importTime" width="150" />
            <el-table-column label="账号" align="center" prop="account" width="180" />
            <el-table-column label="户名" align="center" prop="accountName" width="100" />
            <el-table-column label="扣缴义务人名称" align="center" prop="withholdingName" min-width="150" />
            <el-table-column label="金额" align="center" prop="amount" width="100">
                <template #default="scope">
                    {{ scope.row.amount.toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column label="实际代扣金额" align="center" prop="actualAmount" width="120">
                <template #default="scope">
                    {{ scope.row.actualAmount.toFixed(2) }}
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" width="80">
                <template #default="scope">
                    <el-tag :type="getStatusLabel(scope.row.status).type">
                        {{ getStatusLabel(scope.row.status).label }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="注释" align="center" prop="note" width="100" />
            <el-table-column label="参考号" align="center" prop="referenceNo" width="120" />
            <el-table-column label="提示" align="center" prop="tip" width="100" />
            <el-table-column label="数据年月" align="center" prop="dataMonth" width="100" />
            <el-table-column label="开户行" align="center" prop="bank" width="120" />
            <el-table-column label="开户地" align="center" prop="bankLocation" width="150" />
            <el-table-column label="经办日" align="center" prop="operationDate" width="100" />
            <el-table-column label="期望日" align="center" prop="expectedDate" width="100" />
            <el-table-column label="收款(付款)账户名" align="center" prop="receiverName" width="150" />
            <el-table-column label="收款(付款)账号" align="center" prop="receiverAccount" width="180" />
            <el-table-column label="用途" align="center" prop="purpose" width="100" />
            <el-table-column label="业务参考号" align="center" prop="businessRefNo" width="120" />
            <el-table-column label="类型" align="center" prop="type" width="100" />
            <el-table-column label="渠道标志" align="center" prop="channel" width="100" />
            <el-table-column label="创建人" align="center" prop="creator" width="100" />
            <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 导入工资条实发数据 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" type="actualDataImport" title="上传导入数据"
            :dialogForm="obj.dialogForm" />


        <!-- 查看导入记录 -->
        <ImportRecord v-model:dialogShow="obj.dialogShow2" menuName="actualDataImport" title="查看导入记录" />

    </div>
</template>

<script setup name="ActualData_import">
import ImportRecord from '@/views/reonManage/components/dialog/importRecord.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 扣缴义务人选项
const withholdingOptions = [
    { value: '1', label: '北京科技有限公司' },
    { value: '2', label: '上海贸易有限公司' },
    { value: '3', label: '广州电子有限公司' },
    { value: '4', label: '深圳科技有限公司' },
    { value: '5', label: '成都信息有限公司' }
];



// 实发数据导入相关数据
const obj = reactive({
    showSearch: true, // 显示搜索
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importNo: '', // 导入编号
        name: '', // 姓名
        idNumber: '', // 证件号
        dataMonth: '', // 数据年月
        withholdingName: '' // 扣缴义务人
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            importNo: 'DR********001',
            importTime: '2023-05-01 10:00:00',
            account: '6222021234567890123',
            accountName: '张三',
            withholdingName: '北京科技有限公司',
            amount: 8500.00,
            actualAmount: 8500.00,
            status: '1',
            note: '工资发放',
            referenceNo: 'REF********001',
            tip: '',
            dataMonth: '2023-05',
            bank: '中国工商银行',
            bankLocation: '北京市海淀区支行',
            operationDate: '2023-05-01',
            expectedDate: '2023-05-05',
            receiverName: '张三',
            receiverAccount: '6222021234567890123',
            purpose: '工资',
            businessRefNo: 'BIZ********001',
            type: '工资发放',
            channel: '网银',
            creator: '管理员',
            createTime: '2023-05-01 09:30:00'
        },
        {
            id: 2,
            importNo: 'DR20230502001',
            importTime: '2023-05-02 11:30:00',
            account: '6222021234567890124',
            accountName: '李四',
            withholdingName: '上海贸易有限公司',
            amount: 9200.00,
            actualAmount: 9200.00,
            status: '1',
            note: '工资发放',
            referenceNo: 'REF20230502001',
            tip: '',
            dataMonth: '2023-05',
            bank: '中国建设银行',
            bankLocation: '上海市浦东新区支行',
            operationDate: '2023-05-02',
            expectedDate: '2023-05-06',
            receiverName: '李四',
            receiverAccount: '6222021234567890124',
            purpose: '工资',
            businessRefNo: 'BIZ20230502001',
            type: '工资发放',
            channel: '网银',
            creator: '管理员',
            createTime: '2023-05-02 10:45:00'
        }

    ],

    // 导入记录数据
    importRecords: [
        {
            id: 1,
            importNo: 'DR********001',
            importer: '1',
            importTime: '2023-05-01 10:00:00',
            remark: '5月份工资数据导入',
            successCount: 100,
            failCount: 0,
            importFile: '工资数据********.xlsx',
            status: '1',
            creator: '管理员',
            createTime: '2023-05-01 09:30:00',
            updater: '',
            updateTime: ''
        },
        {
            id: 2,
            importNo: 'DR20230502001',
            importer: '2',
            importTime: '2023-05-02 11:30:00',
            remark: '补充导入数据',
            successCount: 50,
            failCount: 2,
            importFile: '工资数据20230502.xlsx',
            status: '1',
            creator: '管理员',
            createTime: '2023-05-02 10:45:00',
            updater: '管理员',
            updateTime: '2023-05-02 11:30:00'
        },
        {
            id: 3,
            importNo: 'DR20230503001',
            importer: '3',
            importTime: '2023-05-03 09:15:00',
            remark: '特殊工资数据',
            successCount: 30,
            failCount: 0,
            importFile: '工资数据20230503.xlsx',
            status: '1',
            creator: '管理员',
            createTime: '2023-05-03 08:30:00',
            updater: '',
            updateTime: ''
        }
    ],

    // 对话框相关数据
    dialogShow: false, // 导入对话框显示状态
    dialogForm: {},
    dialogShow2: false, // 导入记录对话框显示状态
});

/**
 * 获取状态标签
 * @param {string} status 状态值
 * @returns {string} 状态标签
 */
function getStatusLabel(status) {
    const statusMap = {
        '1': { label: '成功', type: 'success' },
        '2': { label: '失败', type: 'danger' },
        '3': { label: '处理中', type: 'warning' },
        '4': { label: '待处理', type: 'info' }
    };
    return statusMap[status] || { label: '未知', type: 'info' };
}




/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.importNo) {
            filteredData = filteredData.filter(item =>
                item.importNo.includes(obj.queryParams.importNo)
            );
        }

        if (obj.queryParams.name) {
            filteredData = filteredData.filter(item =>
                item.accountName.includes(obj.queryParams.name)
            );
        }

        if (obj.queryParams.idNumber) {
            // 模拟证件号筛选，实际数据中没有证件号字段
            // 这里使用账号代替
            filteredData = filteredData.filter(item =>
                item.account.includes(obj.queryParams.idNumber)
            );
        }

        if (obj.queryParams.dataMonth) {
            // 如果是日期对象，转换为年月格式
            let dataMonth = obj.queryParams.dataMonth;
            if (dataMonth instanceof Date) {
                const year = dataMonth.getFullYear();
                const month = String(dataMonth.getMonth() + 1).padStart(2, '0');
                dataMonth = `${year}-${month}`;
            }

            filteredData = filteredData.filter(item =>
                item.dataMonth === dataMonth
            );
        }

        if (obj.queryParams.withholdingName) {
            filteredData = filteredData.filter(item =>
                item.withholdingName === obj.queryParams.withholdingName ||
                item.withholdingName.includes(obj.queryParams.withholdingName)
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}



/** 导入工资条实发数据 */
function handleImport() {
    obj.dialogShow = true;
}


/** 查看导入记录 */
function handleRecord() {
    obj.dialogShow2 = true;
    console.log(11111111);

}

/** 关闭导入 */
function handleClose() {
    obj.dialogShow = false;
}

/** 关闭导入记录 */
function handleClose2() {
    obj.dialogShow2 = false;
}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped></style>