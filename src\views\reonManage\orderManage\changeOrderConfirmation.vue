<!-- 变更订单确认 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户编号:" prop="customerCode">
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入客户编号" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" />
            </el-form-item>
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入唯一号" />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderCode">
                <el-input class="width220" v-model="obj.queryParams.orderCode" placeholder="请输入订单编号" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" />
            </el-form-item>
            <el-form-item label="证件类型:" prop="idType">
                <el-select class="width220" v-model="obj.queryParams.idType" placeholder="请选择" clearable>
                    <el-option v-for="item in idTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" />
            </el-form-item>
            <el-form-item label="小合同名称:" prop="smallContractName">
                <el-input class="width220" v-model="obj.queryParams.smallContractName" placeholder="请输入小合同名称" />
            </el-form-item>
            <el-form-item label="接单方客服:" prop="receiverService">
                <el-select class="width220" v-model="obj.queryParams.receiverService" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="接单方:" prop="receiver">
                <el-input class="width220" v-model="obj.queryParams.receiver" placeholder="请输入接单方" />
            </el-form-item>
            <el-form-item label="派单方:" prop="sender">
                <el-input class="width220" v-model="obj.queryParams.sender" placeholder="请输入派单方" />
            </el-form-item>
            <el-form-item label="订单变更方式:" prop="changeType">
                <el-select class="width220" v-model="obj.queryParams.changeType" placeholder="请选择" clearable>
                    <el-option v-for="item in changeTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="创建时间起:" prop="createTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.createTimeStart" placeholder="请选择开始日期" />
            </el-form-item>
            <el-form-item label="创建时间止:" prop="createTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.createTimeEnd" placeholder="请选择结束日期" />
            </el-form-item>
            <el-form-item label="人员分布:" prop="personnelDistribution">
                <el-select class="width220" v-model="obj.queryParams.personnelDistribution" placeholder="请选择" clearable>
                    <el-option v-for="item in distributionOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleConfirm">确认订单</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleReject">驳回</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleBatchConfirm">批量确认订单</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" />
            <el-table-column label="订单编号" align="center" prop="orderCode" />
            <el-table-column label="证件类型" align="center" prop="idType" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="人员分布" align="center" prop="personnelDistribution" />
            <el-table-column label="派单类型" align="center" prop="dispatchType" />
            <el-table-column label="小合同名称" align="center" prop="smallContractName" />
            <el-table-column label="申请入职时间" align="center" prop="applyEntryTime" />
            <el-table-column label="申报入职人" align="center" prop="entryApplicant" />
            <el-table-column label="入职备注" align="center" prop="entryRemark" />
            <el-table-column label="入职过程" align="center" prop="entryProcess" />
            <el-table-column label="填写备注" align="center" prop="fillRemark" />
            <el-table-column label="订单变更方式" align="center" prop="changeType" />
            <el-table-column label="操作" align="center">
                <template #default="scope">
                    <el-button text type="primary" plain @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 订单详情 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="70%" append-to-body>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <el-divider content-position="left">个人订单</el-divider>
                <el-form-item label="小合同" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="客户" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="人员分类" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="报价单" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="小合同编号" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="客户编号" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="城市" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="报价单编号" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="派单方" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="接单方" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="合同名称" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="订单状态" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>

                <el-form-item label="雇员姓名" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="雇员编号" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="电话" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="手机" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="证件类型" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="证件编号" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="入职日期" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="总收费时间" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="实际工作地" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="入职备注" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="增员原因" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="未增反馈" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="机动分类2" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="机动分类3" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="机动分类4" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="机动分类5" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="有统筹医疗" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="有社保卡" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="公积金账号" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="派单客服" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="接单客服" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="集中地投保" prop="typeCode">
                    <el-checkbox class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>

                <el-form-item label="是否存档" prop="typeCode">
                    <el-checkbox class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="是否外呼" prop="typeCode">
                    <el-checkbox class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="备注类型" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-divider content-position="left">劳动合同</el-divider>
                <el-form-item label="是否需要签订" prop="typeCode">
                    <el-checkbox class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="合同签订地" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="劳务合同类别" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同版本" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="合同版本地" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="劳动合同起始时间" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="劳动合同结束时间" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="工作制" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="是否有试用期" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="试用期起始时间" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="试用期月数" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="试用工资" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="派遣期限起" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="派遣期限止" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="正式工资" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="合同原则" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="签署日期" prop="typeCode">
                    <el-input disabled class="width220" v-model="obj.dialogForm.sales" />
                </el-form-item>
                <el-form-item label="试用期结束时间" prop="typeCode">
                    <el-select class="width220" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-divider content-position="left">社保公积金</el-divider>
                <el-table :data="obj.tableData" border show-summary>
                    <el-table-column label="序号" type="index" width="80" />
                    <el-table-column label="产品名称" align="center" prop="productName" />
                    <el-table-column label="社保公积金" align="center" prop="socialSecurity" />
                    <el-table-column label="收费起始月" align="center" prop="chargeStartMonth" />
                    <el-table-column label="收费截止月" align="center" prop="chargeEndMonth" />
                    <el-table-column label="账单起始月" align="center" prop="billStartMonth" />
                    <el-table-column label="企业基数" align="center" prop="companyBase" />
                    <el-table-column label="个人基数" align="center" prop="personalBase" />
                    <el-table-column label="企业金额" align="center" prop="companyAmount" />
                    <el-table-column label="个人金额" align="center" prop="personalAmount" />
                    <el-table-column label="社保比例名称" align="center" prop="socialSecurityRatioName" />
                    <el-table-column label="企业比例" align="center" prop="companyRatio" />
                    <el-table-column label="个人比例" align="center" prop="personalRatio" />
                    <el-table-column label="企业附加" align="center" prop="companyAdditional" />
                    <el-table-column label="个人附加" align="center" prop="personalAdditional" />
                    <el-table-column label="账单模板" align="center" prop="billTemplate" />
                    <el-table-column label="收费模板" align="center" prop="chargeTemplate" />
                    <el-table-column label="备注" align="center" prop="remark" />
                </el-table>
                <el-divider content-position="left">一次性费用</el-divider>
                <el-table :data="obj.tableData" border>
                    <el-table-column label="一次性类型" align="center" prop="oneTimeType" />
                    <el-table-column label="发生月" align="center" prop="occurrenceMonth" />
                    <el-table-column label="企业一次性费用" align="center" prop="companyOneTimeFee" />
                    <el-table-column label="个人一次性费用" align="center" prop="personalOneTimeFee" />
                    <el-table-column label="备注" align="center" prop="remark" />
                </el-table>
                <el-divider content-position="left">变更备注</el-divider>
                <el-form-item label="变更备注" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入备注" />
                </el-form-item>
                <el-divider content-position="left">挂起</el-divider>
                <el-form-item label="挂起原因" prop="typeCode">
                    <el-select class="width420" disabled v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="备注" prop="typeCode">
                    <el-input class="width420" type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入备注" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="obj.dialogShow = false">取消</el-button>
                <el-button type="primary" @click="handleSave">确定</el-button>
            </template>
        </el-dialog>
        <!-- 驳回 -->
        <el-dialog v-model="obj.dialogShow2" title="驳回原因" width="20%" append-to-body>
            <el-input v-model="obj.dialogForm.sales" placeholder="请输入驳回原因" />
            <template #footer>
                <el-button @click="obj.dialogShow2 = false">取消</el-button>
                <el-button type="primary" @click="handleSave">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 证件类型选项
const idTypeOptions = ref([
    { value: '1', label: '身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '军官证' }
]);

// 客服选项
const serviceOptions = ref([
    { value: '1', label: '客服1' },
    { value: '2', label: '客服2' },
    { value: '3', label: '客服3' }
]);

// 变更方式选项
const changeTypeOptions = ref([
    { value: '1', label: '变更方式1' },
    { value: '2', label: '变更方式2' },
    { value: '3', label: '变更方式3' }
]);

// 人员分布选项
const distributionOptions = ref([
    { value: '1', label: '分布1' },
    { value: '2', label: '分布2' },
    { value: '3', label: '分布3' }
]);

// 表单验证规则
const rules = {
    // 可以根据需要添加验证规则
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerCode: null,
        customerName: null,
        uniqueId: null,
        orderCode: null,
        employeeName: null,
        idType: null,
        idNumber: null,
        smallContractName: null,
        receiverService: null,
        receiver: null,
        sender: null,
        changeType: null,
        createTimeStart: null,
        createTimeEnd: null,
        personnelDistribution: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    dialogForm: {
        sales: ''
    },//表单数据
    dialogShow: false,//详情弹窗
    dialogShow2: false,//驳回弹窗
    bindSave: false,//绑定保存
    ids: [],//选中的id
    title: "订单详情",//标题
    rules: {}
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeName: '张三',
                uniqueId: 'WY20230001',
                orderCode: 'DD20230001',
                idType: '身份证',
                idNumber: '110101199001011234',
                customerCode: 'KH20230001',
                customerName: '客户名称1',
                personnelDistribution: '分布1',
                dispatchType: '派单类型1',
                smallContractName: '小合同名称1',
                applyEntryTime: '2023-01-01',
                entryApplicant: '申请人1',
                entryRemark: '入职备注1',
                entryProcess: '入职过程1',
                fillRemark: '填写备注1',
                changeType: '变更方式1'
            },
            {
                id: 2,
                employeeName: '李四',
                uniqueId: 'WY20230002',
                orderCode: 'DD20230002',
                idType: '身份证',
                idNumber: '110101199001011235',
                customerCode: 'KH20230002',
                customerName: '客户名称2',
                personnelDistribution: '分布2',
                dispatchType: '派单类型2',
                smallContractName: '小合同名称2',
                applyEntryTime: '2023-02-01',
                entryApplicant: '申请人2',
                entryRemark: '入职备注2',
                entryProcess: '入职过程2',
                fillRemark: '填写备注2',
                changeType: '变更方式2'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 订单详情
function handleDetail(row) {
    obj.dialogShow = true;
    obj.title = '订单详情';
}

// 确认订单
function handleConfirm() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要确认的订单');
        return;
    }
    obj.dialogShow = true;
    obj.title = '确认订单';
}

// 批量确认订单
function handleBatchConfirm() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要确认的订单');
        return;
    }
    proxy.$modal.msgSuccess('批量确认成功');
}

// 驳回
function handleReject() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要驳回的订单');
        return;
    }
    obj.dialogShow2 = true;
}

// 保存
function handleSave() {
    if (obj.dialogShow) {
        obj.dialogShow = false;
        proxy.$modal.msgSuccess('保存成功');
    } else if (obj.dialogShow2) {
        obj.dialogShow2 = false;
        proxy.$modal.msgSuccess('驳回成功');
    }
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>