<!-- 供应商账单报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-input class="width220" v-model="obj.queryParams.supplierName" placeholder="请输入供应商名称" clearable />
            </el-form-item>
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="报表年月起:" prop="reportMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.reportMonthStart" type="month"
                    placeholder="请选择年月" clearable />
            </el-form-item>
            <el-form-item label="报表年月止:" prop="reportMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.reportMonthEnd" type="month"
                    placeholder="请选择年月" clearable />
            </el-form-item>
            <el-form-item label="锁定状态:" prop="lockStatus">
                <el-select class="width220" v-model="obj.queryParams.lockStatus" placeholder="请选择状态" clearable>
                    <el-option v-for="item in lockStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付状态:" prop="paymentStatus">
                <el-select class="width220" v-model="obj.queryParams.paymentStatus" placeholder="请选择状态" clearable>
                    <el-option v-for="item in paymentStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否单立户:" prop="isSingleAccount">
                <el-select class="width220" v-model="obj.queryParams.isSingleAccount" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商客服:" prop="supplierService">
                <el-select class="width220" v-model="obj.queryParams.supplierService" placeholder="请选择客服" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectService">
                <el-select class="width220" v-model="obj.queryParams.projectService" placeholder="请选择客服" clearable>
                    <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNumber">
                <el-input class="width220" v-model="obj.queryParams.orderNumber" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择城市" clearable>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" type="index" width="60" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="大合同编号" align="center" width="100" prop="parentContractNumber" />
            <el-table-column label="大合同名称" align="center" width="100" prop="parentContractName" />
            <el-table-column label="签约方抬头" align="center" width="100" prop="signingParty" />
            <el-table-column label="合同类别" align="center" prop="contractType" />
            <el-table-column label="小合同编号" align="center" width="100" prop="subContractNumber" />
            <el-table-column label="小合同名称" align="center" width="100" prop="subContractName" />
            <el-table-column label="城市" align="center" prop="city" />
            <el-table-column label="是否单立户" align="center" width="100" prop="isSingleAccount" />
            <el-table-column label="供应商客服" align="center" width="100" prop="supplierService" />
            <el-table-column label="派单分公司" align="center" width="100" prop="dispatchCompany" />
            <el-table-column label="派单客服" align="center" prop="dispatchService" />
            <el-table-column label="接单客服" align="center" prop="receiverService" />
            <el-table-column label="供应商名称" align="center" width="100" prop="supplierName" />
            <el-table-column label="账单模板" align="center" prop="billTemplate" />
            <el-table-column label="收费频率" align="center" prop="chargeFrequency" />
            <el-table-column label="报表年月" align="center" prop="reportMonth" />
            <el-table-column label="服务月" align="center" prop="serviceMonth" />
            <el-table-column label="账单类型" align="center" prop="billType" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="订单编号" align="center" prop="orderNumber" />
            <el-table-column label="身份证" align="center" prop="idNumber" />
            <el-table-column label="年龄" align="center" prop="age" />
            <el-table-column label="户口性质" align="center" prop="householdType" />
            <el-table-column label="公积金账号" align="center" width="100" prop="housingFundAccount" />
            <el-table-column label="服务费" align="center" prop="serviceFee" />
            <el-table-column label="养老保险" align="center">
                <el-table-column label="总额" align="center" prop="pensionTotal" />
                <el-table-column label="企业基数" align="center" prop="pensionCompanyBase" />
                <el-table-column label="企业比例" align="center" prop="pensionCompanyRatio" />
                <el-table-column label="企业金额" align="center" prop="pensionCompanyAmount" />
                <el-table-column label="个人基数" align="center" prop="pensionPersonalBase" />
                <el-table-column label="个人比例" align="center" prop="pensionPersonalRatio" />
                <el-table-column label="个人金额" align="center" prop="pensionPersonalAmount" />
            </el-table-column>

            <el-table-column label="医疗保险" align="center">
                <el-table-column label="总额" align="center" prop="medicalTotal" />
                <el-table-column label="企业基数" align="center" prop="medicalCompanyBase" />
                <el-table-column label="企业比例" align="center" prop="medicalCompanyRatio" />
                <el-table-column label="企业金额" align="center" prop="medicalCompanyAmount" />
                <el-table-column label="个人基数" align="center" prop="medicalPersonalBase" />
                <el-table-column label="个人比例" align="center" prop="medicalPersonalRatio" />
                <el-table-column label="个人金额" align="center" prop="medicalPersonalAmount" />
            </el-table-column>
            <el-table-column label="失业保险" align="center">
                <el-table-column label="总额" align="center" prop="unemploymentTotal" />
                <el-table-column label="企业基数" align="center" prop="unemploymentCompanyBase" />
                <el-table-column label="企业比例" align="center" prop="unemploymentCompanyRatio" />
                <el-table-column label="企业金额" align="center" prop="unemploymentCompanyAmount" />
                <el-table-column label="个人基数" align="center" prop="unemploymentPersonalBase" />
                <el-table-column label="个人比例" align="center" prop="unemploymentPersonalRatio" />
                <el-table-column label="个人金额" align="center" prop="unemploymentPersonalAmount" />
            </el-table-column>
            <el-table-column label="工伤保险" align="center">
                <el-table-column label="总额" align="center" prop="injuryTotal" />
                <el-table-column label="企业基数" align="center" prop="injuryCompanyBase" />
                <el-table-column label="企业比例" align="center" prop="injuryCompanyRatio" />
                <el-table-column label="企业金额" align="center" prop="injuryCompanyAmount" />
                <el-table-column label="个人基数" align="center" prop="injuryPersonalBase" />
                <el-table-column label="个人比例" align="center" prop="injuryPersonalRatio" />
                <el-table-column label="个人金额" align="center" prop="injuryPersonalAmount" />
            </el-table-column>
            <el-table-column label="大病保险" align="center">
                <el-table-column label="总额" align="center" prop="seriousIllnessTotal" />
                <el-table-column label="企业基数" align="center" prop="seriousIllnessCompanyBase" />
                <el-table-column label="企业比例" align="center" prop="seriousIllnessCompanyRatio" />
                <el-table-column label="企业金额" align="center" prop="seriousIllnessCompanyAmount" />
                <el-table-column label="个人基数" align="center" prop="seriousIllnessPersonalBase" />
                <el-table-column label="个人比例" align="center" prop="seriousIllnessPersonalRatio" />
                <el-table-column label="个人金额" align="center" prop="seriousIllnessPersonalAmount" />
            </el-table-column>
            <el-table-column label="残疾保障金" align="center">
                <el-table-column label="总额" align="center" prop="disabilityTotal" />
                <el-table-column label="企业基数" align="center" prop="disabilityCompanyBase" />
                <el-table-column label="企业比例" align="center" prop="disabilityCompanyRatio" />
                <el-table-column label="企业金额" align="center" prop="disabilityCompanyAmount" />
                <el-table-column label="个人基数" align="center" prop="disabilityPersonalBase" />
                <el-table-column label="个人比例" align="center" prop="disabilityPersonalRatio" />
                <el-table-column label="个人金额" align="center" prop="disabilityPersonalAmount" />
            </el-table-column>
            <el-table-column label="住房公积金" align="center">
                <el-table-column label="总额" align="center" prop="housingFundTotal" />
                <el-table-column label="企业基数" align="center" prop="housingFundCompanyBase" />
                <el-table-column label="企业比例" align="center" prop="housingFundCompanyRatio" />
                <el-table-column label="企业金额" align="center" prop="housingFundCompanyAmount" />
                <el-table-column label="个人基数" align="center" prop="housingFundPersonalBase" />
                <el-table-column label="个人比例" align="center" prop="housingFundPersonalRatio" />
                <el-table-column label="个人金额" align="center" prop="housingFundPersonalAmount" />
            </el-table-column>
            <el-table-column label="补充工伤保险" align="center">
                <el-table-column label="总额" align="center" prop="supplementaryInjuryTotal" />
                <el-table-column label="企业基数" align="center" prop="supplementaryInjuryCompanyBase" />
                <el-table-column label="企业比例" align="center" prop="supplementaryInjuryCompanyRatio" />
                <el-table-column label="企业金额" align="center" prop="supplementaryInjuryCompanyAmount" />
                <el-table-column label="个人基数" align="center" prop="supplementaryInjuryPersonalBase" />
                <el-table-column label="个人比例" align="center" prop="supplementaryInjuryPersonalRatio" />
                <el-table-column label="个人金额" align="center" prop="supplementaryInjuryPersonalAmount" />
            </el-table-column>
            <el-table-column label="合计" align="center" prop="total" />
            <el-table-column label="锁定状态" align="center" prop="lockStatus" />
            <el-table-column label="支付状态" align="center" prop="paymentStatus" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="SupplierBillingReport">

import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
];

// 锁定状态选项
const lockStatusOptions = [
    { value: '0', label: '未锁定' },
    { value: '1', label: '已锁定' }
];

// 支付状态选项
const paymentStatusOptions = [
    { value: '0', label: '未支付' },
    { value: '1', label: '已支付' },
    { value: '2', label: '支付中' }
];

// 客服选项
const serviceOptions = [
    { value: '1', label: '客服1' },
    { value: '2', label: '客服2' },
    { value: '3', label: '客服3' },
    { value: '4', label: '客服4' }
];


const obj = reactive({
    showSearch: true, // 显示搜索
    ids: [], // 选中id
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        customerName: null,
        reportMonthStart: null,
        reportMonthEnd: null,
        lockStatus: null,
        paymentStatus: null,
        isSingleAccount: null,
        supplierService: null,
        projectService: null,
        employeeName: null,
        idNumber: null,
        orderNumber: null,
        city: null
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerName: '客户A',
                parentContractNumber: 'HT20230001',
                parentContractName: '大合同1',
                signingParty: '公司A',
                contractType: '劳务派遣',
                subContractNumber: 'HT20230001-01',
                subContractName: '小合同1',
                city: '北京',
                isSingleAccount: 'Y',
                supplierService: '客服1',
                dispatchCompany: '北京分公司',
                dispatchService: '客服2',
                receiverService: '客服3',
                supplierName: '供应商A',
                billTemplate: '模板1',
                chargeFrequency: '月缴',
                reportMonth: '2023-05',
                serviceMonth: '2023-05',
                billType: '社保',
                employeeName: '张三',
                orderNumber: 'DD20230001',
                idNumber: '110101199001011234',
                age: 30,
                householdType: '城镇户口',
                housingFundAccount: 'GJ20230001',
                serviceFee: 100.00,
                // 养老保险
                pensionTotal: 1000.00,
                pensionCompanyBase: 5000.00,
                pensionCompanyRatio: 16,
                pensionCompanyAmount: 800.00,
                pensionPersonalBase: 5000.00,
                pensionPersonalRatio: 8,
                pensionPersonalAmount: 400.00,
                // 医疗保险
                medicalTotal: 500.00,
                medicalCompanyBase: 5000.00,
                medicalCompanyRatio: 8,
                medicalCompanyAmount: 400.00,
                medicalPersonalBase: 5000.00,
                medicalPersonalRatio: 2,
                medicalPersonalAmount: 100.00,
                // 失业保险
                unemploymentTotal: 150.00,
                unemploymentCompanyBase: 5000.00,
                unemploymentCompanyRatio: 2,
                unemploymentCompanyAmount: 100.00,
                unemploymentPersonalBase: 5000.00,
                unemploymentPersonalRatio: 1,
                unemploymentPersonalAmount: 50.00,
                // 工伤保险
                injuryTotal: 50.00,
                injuryCompanyBase: 5000.00,
                injuryCompanyRatio: 1,
                injuryCompanyAmount: 50.00,
                injuryPersonalBase: 0,
                injuryPersonalRatio: 0,
                injuryPersonalAmount: 0,
                // 大病保险
                seriousIllnessTotal: 25.00,
                seriousIllnessCompanyBase: 5000.00,
                seriousIllnessCompanyRatio: 0.5,
                seriousIllnessCompanyAmount: 25.00,
                seriousIllnessPersonalBase: 0,
                seriousIllnessPersonalRatio: 0,
                seriousIllnessPersonalAmount: 0,
                // 残疾保障金
                disabilityTotal: 75.00,
                disabilityCompanyBase: 5000.00,
                disabilityCompanyRatio: 1.5,
                disabilityCompanyAmount: 75.00,
                disabilityPersonalBase: 0,
                disabilityPersonalRatio: 0,
                disabilityPersonalAmount: 0,
                // 住房公积金
                housingFundTotal: 1000.00,
                housingFundCompanyBase: 5000.00,
                housingFundCompanyRatio: 10,
                housingFundCompanyAmount: 500.00,
                housingFundPersonalBase: 5000.00,
                housingFundPersonalRatio: 10,
                housingFundPersonalAmount: 500.00,
                // 补充工伤保险
                supplementaryInjuryTotal: 100.00,
                supplementaryInjuryCompanyBase: 5000.00,
                supplementaryInjuryCompanyRatio: 2,
                supplementaryInjuryCompanyAmount: 100.00,
                supplementaryInjuryPersonalBase: 0,
                supplementaryInjuryPersonalRatio: 0,
                supplementaryInjuryPersonalAmount: 0,
                // 合计和状态
                total: 3000.00,
                lockStatus: '1',
                paymentStatus: '1'
            }
        ];
        obj.total = 1;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}


/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}


/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

// 初始化数据
getList();
</script>

<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>