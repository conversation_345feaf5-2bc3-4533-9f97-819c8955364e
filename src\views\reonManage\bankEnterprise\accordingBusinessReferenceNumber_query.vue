<!-- 根据业务类型号查询业务模式号 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form @submit.prevent :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="业务类型号:" prop="businessTypeNo">
                <el-input class="width220" v-model="obj.queryParams.businessTypeNo" placeholder="请输入业务类型号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="业务模式编号" align="center" prop="businessModeNo" />
            <el-table-column label="业务模式名称" align="center" prop="businessModeName" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="AccordingBusinessReferenceNumber_query">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessTypeNo: null,
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                businessModeNo: 'BM001',
                businessModeName: '单笔转账'
            },
            {
                id: 2,
                businessModeNo: 'BM002',
                businessModeName: '批量转账'
            },
            {
                id: 3,
                businessModeNo: 'BM003',
                businessModeName: '定期转账'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

getList();

</script>
<style lang="scss" scoped></style>