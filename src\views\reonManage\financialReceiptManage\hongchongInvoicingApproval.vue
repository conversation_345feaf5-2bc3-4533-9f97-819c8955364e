<!-- 红冲开票审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="签单分公司:" prop="signBranch">
                <el-select class="width220" v-model="obj.queryParams.signBranch" placeholder="请选择签单分公司" clearable>
                    <el-option label="上海分公司" value="上海分公司" />
                    <el-option label="北京分公司" value="北京分公司" />
                    <el-option label="广州分公司" value="广州分公司" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户帐套" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户帐套" clearable>
                    <el-option label="帐套1" value="1" />
                    <el-option label="帐套2" value="2" />
                    <el-option label="帐套3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户付款方:" prop="customerPayer">
                <el-select class="width220" v-model="obj.queryParams.customerPayer" placeholder="请选择客户付款方" clearable>
                    <el-option label="付款方1" value="1" />
                    <el-option label="付款方2" value="2" />
                    <el-option label="付款方3" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="财务应收月<=:" prop="financialReceivableMonthEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.financialReceivableMonthEnd" type="month"
                    placeholder="请选择结束月份" clearable />
            </el-form-item>
            <el-form-item label="财务应收月>=:" prop="financialReceivableMonthStart">
                <el-date-picker class="width220" v-model="obj.queryParams.financialReceivableMonthStart" type="month"
                    placeholder="请选择开始月份" clearable />
            </el-form-item>
            <el-form-item label="发票金额<=:" prop="invoiceAmountMax">
                <el-input class="width220" v-model="obj.queryParams.invoiceAmountMax" placeholder="请输入最大金额" clearable />
            </el-form-item>
            <el-form-item label="发票金额>=:" prop="invoiceAmountMin">
                <el-input class="width220" v-model="obj.queryParams.invoiceAmountMin" placeholder="请输入最小金额" clearable />
            </el-form-item>
            <el-form-item label="开票起始日期:" prop="invoiceStartDate">
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceStartDate" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="开票截止日期:" prop="invoiceEndDate">
                <el-date-picker class="width220" v-model="obj.queryParams.invoiceEndDate" type="date"
                    placeholder="请选择结束日期" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" icon="Check" @click="handleApprove">通过</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" icon="Close" @click="handleReject">驳回</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" icon="Printer" @click="handlePrintInvoiceApplication">打印开票申请单</el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button type="primary" icon="List" @click="handleInvoiceRecord">开票记录</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" icon="Remove" @click="handleOneClickRedPunch">一键红冲</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" type="index" width="80" />
            <el-table-column label="签单抬头" align="center" prop="signTitle" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户付款方" align="center" prop="customerPayer" />
            <el-table-column label="客户帐套" align="center" prop="customerAccount" />
            <el-table-column label="开票金额" align="center" sortable prop="invoiceAmount" />
            <el-table-column label="开票日期" align="center" prop="invoiceDate" />
            <el-table-column label="发票状态" align="center" prop="invoiceStatus" />
            <el-table-column label="开票备注" align="center" prop="invoiceRemark" />
            <el-table-column label="其他税收分类" align="center" prop="taxCategory" />
            <el-table-column label="发票编号" align="center" prop="invoiceNumber" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="RedPunchMakeOutAnInvoice">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        signBranch: null,
        customerName: null,
        customerAccount: null,
        customerPayer: null,
        financialReceivableMonthEnd: null,
        financialReceivableMonthStart: null,
        invoiceAmountMax: null,
        invoiceAmountMin: null,
        invoiceStartDate: null,
        invoiceEndDate: null
    },//查询表单
    total: 0,//总条数
    ids: [],//选中id
    tableData: [],//列表
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                signTitle: '抬头1',
                customerCode: 'CUST001',
                customerName: '客户名称1',
                customerPayer: '付款方1',
                customerAccount: '帐套1',
                invoiceAmount: 10000.00,
                invoiceDate: '2023-01-15',
                invoiceStatus: '待红冲',
                invoiceRemark: '需要红冲的发票',
                taxCategory: '服务费',
                invoiceNumber: 'INV20230115001'
            },
            {
                id: 2,
                signTitle: '抬头2',
                customerCode: 'CUST002',
                customerName: '客户名称2',
                customerPayer: '付款方2',
                customerAccount: '帐套2',
                invoiceAmount: 20000.00,
                invoiceDate: '2023-02-15',
                invoiceStatus: '待红冲',
                invoiceRemark: '退款需要红冲',
                taxCategory: '咨询费',
                invoiceNumber: 'INV20230215001'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框操作 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 通过
function handleApprove() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要通过的数据');
        return;
    }
    proxy.$modal.confirm('是否确认通过选中的数据？').then(function () {
        // 这里可以调用API通过数据
        proxy.$modal.msgSuccess('通过操作成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 驳回
function handleReject() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要驳回的数据');
        return;
    }
    proxy.$modal.confirm('是否确认驳回选中的数据？').then(function () {
        // 这里可以调用API驳回数据
        proxy.$modal.msgSuccess('驳回操作成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 打印开票申请单
function handlePrintInvoiceApplication() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要打印开票申请单的数据');
        return;
    }
    proxy.$modal.msgSuccess('打印开票申请单成功');
}

// 开票记录
function handleInvoiceRecord() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要查看开票记录的数据');
        return;
    }
    proxy.$modal.msgSuccess('查看开票记录成功');
}

// 一键红冲
function handleOneClickRedPunch() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要红冲的数据');
        return;
    }
    proxy.$modal.confirm('是否确认红冲选中的数据？').then(function () {
        // 这里可以调用API红冲数据
        proxy.$modal.msgSuccess('红冲操作成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped></style>