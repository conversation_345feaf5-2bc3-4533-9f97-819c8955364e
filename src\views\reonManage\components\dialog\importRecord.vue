<template>
    <div>
        <el-dialog v-model="dialogShow" :title="props.title" width="80%" @close="handleClose">
            <el-form :model="obj.queryParams" inline label-width="auto" v-if="props.menuName != 'employeeContract'">
                <el-form-item label="导入日期从:" prop="importDateFrom">
                    <el-date-picker class="width220" v-model="obj.queryParams.importDateFrom" type="date"
                        placeholder="请选择日期" value-format="YYYY-MM-DD" clearable />
                </el-form-item>
                <el-form-item label="导入日期到:" prop="importDateTo">
                    <el-date-picker class="width220" v-model="obj.queryParams.importDateTo" type="date"
                        placeholder="请选择日期" value-format="YYYY-MM-DD" clearable />
                </el-form-item>
                <el-form-item label="导入人:" prop="importer">
                    <el-select class="width220" v-model="obj.queryParams.importer" placeholder="请选择" clearable>
                        <el-option v-for="item in importerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="导入编号:" prop="importNo">
                    <el-input class="width220" v-model="obj.queryParams.importNo" placeholder="请输入导入编号" clearable />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleRecordQuery">查询</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>
            <el-row v-if="props.menuName == 'employeeContract'">
                <file-upload />
            </el-row>
            <el-table :data="obj.tableData" border>
                <el-table-column type="index" align="center" width="50" />
                <el-table-column label="导入编号" align="center" prop="importNo" width="120" />
                <el-table-column label="导入人" align="center" width="100">
                    <template #default="scope">
                        {{ scope.row.importer }}
                    </template>
                </el-table-column>
                <el-table-column label="导入时间" align="center" prop="importTime" width="150" />
                <el-table-column v-if="props.menuName != 'employeeContract'" label="备注" align="center" prop="remark"
                    min-width="150" />
                <el-table-column label="成功记录数" align="center" prop="successCount" width="100" />
                <el-table-column label="失败记录数" align="center" prop="failCount" width="100" />
                <el-table-column label="导入文件" align="center" prop="importFile" width="180" />
                <el-table-column label="处理状态" align="center" width="100">
                    <template #default="scope">
                        <el-tag
                            :type="scope.row.status === '1' ? 'success' : scope.row.status === '2' ? 'warning' : 'info'">
                            {{ scope.row.status === '1' ? '处理成功' : scope.row.status === '2' ? '处理中' : '处理失败' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="创建人" align="center" prop="creator" width="100" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="150" />
                <el-table-column label="修改人" align="center" prop="updater" width="100" />
                <el-table-column label="修改时间" align="center" prop="updateTime" width="150" />
                <!-- 入职提醒 -->
                <el-table-column label="入职提醒" align="center" prop="remark" min-width="150" />
                <el-table-column label="历史信息查看" align="center" prop="remark" min-width="150">
                    <template #default="scope">
                        <el-button type="primary" text icon="View" @click="handleHistory(scope.row)">查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <template #footer v-if="props.menuName == 'employeeContract'">
                <el-button type="primary" @click="handleImport">开始上传</el-button>
                <el-button @click="obj.dialogShow = false">取消</el-button>
            </template>
        </el-dialog>

        <HistoricalInformation v-model:dialogShow="obj.dialogShow2" menuName="actualDataImport" title="历史信息查看" />
    </div>
</template>
<script setup>


const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    menuName: {
        type: String,
        default: ''
    }
})
// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});
const emit = defineEmits(['close', 'update:dialogShow'])
const menuName = ref(props.menuName)

watch(menuName, (newVal) => {
    menuName.value = newVal;
})

const obj = reactive({
    queryParams: {
        importDateFrom: '',
        importDateTo: '',
        importer: '',
        importNo: ''
    },
    tableData: [
        {
            importNo: '123456',
            importer: '管理员',
            importTime: '2021-01-01 12:00:00',
            remark: '备注',
            successCount: 100,
            failCount: 10,
            importFile: '123456.xlsx',
            status: '1',
            creator: '管理员',
            createTime: '2021-01-01 12:00:00',
            updater: '管理员',
            updateTime: '2021-01-01 12:00:00'
        },
        {
            importNo: '123456',
            importer: '管理员',
            importTime: '2021-01-01 12:00:00',
            remark: '备注',
            successCount: 100,
        }
    ]
})

const importerOptions = [
    { value: 'admin', label: '管理员' },
    { value: 'user1', label: '用户1' },
    { value: 'user2', label: '用户2' }
]

/** 关闭 */
function handleClose() {
    dialogShow.value = false;
    emit('close')
}

/** 查询 */
async function handleRecordQuery() {
    console.log(obj.queryParams)

}

/** 重置 */
async function resetQuery() {
    obj.queryParams = {
        importDateFrom: '',
        importDateTo: '',
        importer: '',
        importNo: ''
    }
    handleRecordQuery()
}

/** 查看历史 */
function handleHistory(row) {
    obj.dialogShow2 = true;
}
</script>