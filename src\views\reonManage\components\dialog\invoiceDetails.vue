<template>
    <div>
        <el-dialog v-model="dialogShow" :title="props.title" width="80%" append-to-body draggable @close="handleClose">
            <el-form :model="props.dialogForm" ref="formRef" class="formHight" inline label-width="auto">
                <el-form-item label="客户编号:" prop="customerCode">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.customerCode"
                        placeholder="请输入客户编号" clearable />
                </el-form-item>
                <el-form-item label="客户名称:" prop="customerName">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.customerName"
                        placeholder="请输入客户名称" clearable />
                </el-form-item>
                <el-form-item label="集团名称:" prop="groupName">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.groupName"
                        placeholder="请输入集团名称" clearable />
                </el-form-item>
                <el-form-item label="出账单分公司:" prop="customerAccount">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.customerAccount"
                        placeholder="请输入出账单分公司" clearable />
                </el-form-item>
                <el-form-item label="客户开票抬头:" prop="invoiceTitle">
                    <el-select :disabled="disabled" class="width220" v-model="props.dialogForm.invoiceTitle"
                        placeholder="请选择客户开票抬头" clearable>
                        <el-option label="开票抬头1" value="1" />
                        <el-option label="开票抬头2" value="2" />
                        <el-option label="开票抬头3" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="联系人:" prop="contactPerson">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.contactPerson"
                        placeholder="请输入联系人" clearable />
                </el-form-item>
                <el-form-item label="客户联系地址:" prop="contactAddress">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.contactAddress"
                        placeholder="请输入客户联系地址" clearable />
                </el-form-item>
                <el-form-item label="开票总额:" prop="invoiceAmount">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.invoiceAmount"
                        placeholder="请输入开票总额" clearable />
                </el-form-item>
                <el-form-item label="开票明细总额:" prop="invoiceDetailAmount">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.invoiceDetailAmount"
                        placeholder="请输入开票明细总额" clearable />
                </el-form-item>
                <el-form-item label="纳税人识别号:" prop="taxpayerNumber">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.taxpayerNumber"
                        placeholder="请输入纳税人识别号" clearable />
                </el-form-item>
                <el-form-item label="发票地址:" prop="invoiceAddress">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.invoiceAddress"
                        placeholder="请输入发票地址" clearable />
                </el-form-item>
                <el-form-item label="发票电话:" prop="invoicePhone">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.invoicePhone"
                        placeholder="请输入发票电话" clearable />
                </el-form-item>
                <el-form-item label="开户行名称:" prop="bankName">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.bankName"
                        placeholder="请输入开户行名称" clearable />
                </el-form-item>
                <el-form-item label="账号:" prop="account">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.account"
                        placeholder="请输入账号" clearable />
                </el-form-item>
                <el-form-item label="推送模式:" prop="pushMode">
                    <el-select :disabled="disabled" class="width220" v-model="props.dialogForm.pushMode"
                        placeholder="请选择推送模式" clearable>
                        <el-option label="推送模式1" value="1" />
                        <el-option label="推送模式2" value="2" />
                        <el-option label="推送模式3" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户资质:" prop="customerQualification">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.customerQualification"
                        placeholder="请输入客户资质" clearable />
                </el-form-item>
                <el-form-item label="(服务费)发票类型:" prop="invoiceType">
                    <el-select :disabled="disabled" class="width220" v-model="props.dialogForm.invoiceType"
                        placeholder="请选择发票类型" clearable>
                        <el-option label="发票类型1" value="1" />
                        <el-option label="发票类型2" value="2" />
                        <el-option label="发票类型3" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="服务费税收分类别名:" prop="invoiceBranch">
                    <el-select :disabled="disabled" class="width220" v-model="props.dialogForm.invoiceBranch"
                        placeholder="请选择税收分类别名" clearable>
                        <el-option label="发票类型1" value="1" />
                        <el-option label="发票类型2" value="2" />
                        <el-option label="发票类型3" value="3" />
                    </el-select>
                </el-form-item>
                <el-form-item label="服务费税收分类别名:" prop="taxonomyName">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.taxonomyName"
                        placeholder="请输入服务费税收分类别名" clearable />
                </el-form-item>
                <el-form-item label="代收代付税收分类别名:" prop="collectionTaxonomyName">
                    <el-input :disabled="disabled" class="width220" v-model="props.dialogForm.collectionTaxonomyName"
                        placeholder="请输入代收代付税收分类别名" clearable />
                </el-form-item>
            </el-form>
            <el-divider content-position="left">服务费</el-divider>
            <el-table border :data="serviceFeeTableData || props.serviceFeeTableData">
                <el-table-column label="产品类型" align="center" prop="productType" />
                <el-table-column label="账单年月" align="center" prop="billMonth" />
                <el-table-column label="财务应收账单年月" align="center" prop="financialReceivableMonth" />
                <el-table-column label="客户帐套" align="center" prop="customerAccount" />
                <el-table-column label="合同编号" align="center" prop="contractNo" />
                <el-table-column label="总金额" align="center" prop="totalAmount" />
                <el-table-column label="金额" align="center" prop="amount" />
                <el-table-column label="增值税金额" align="center" prop="invoiceAmount" />
                <el-table-column label="增值税税率" align="center" prop="invoiceTaxRate" />
                <el-table-column label="人数" align="center" prop="invoicePersonCount" />
                <el-table-column label="大合同类型" align="center" prop="invoiceBigContractType" />
                <el-table-column label="签约方抬头" align="center" prop="invoiceSignatory" />
            </el-table>
            <el-divider content-position="left">代收代付</el-divider>
            <el-table border :data="collectionTableData || props.collectionTableData">
                <el-table-column label="账单年月" align="center" prop="billMonth" />
                <el-table-column label="财务应收账单年月" align="center" prop="financialReceivableMonth" />
                <el-table-column label="客户帐套" align="center" prop="customerAccount" />
                <el-table-column label="合同编号" align="center" prop="contractNo" />
                <el-table-column label="金额" align="center" prop="receivableAmount" />
                <el-table-column label="个人金额" align="center" prop="personalAmount" />
                <el-table-column label="企业金额" align="center" prop="enterpriseAmount" />
                <el-table-column label="类型" align="center" prop="invoiceType" />
                <el-table-column label="大合同类型" align="center" prop="invoiceBigContractType" />
                <el-table-column label="签约方抬头" align="center" prop="invoiceSignatory" />
            </el-table>
            <el-divider content-position="left">一次性收费</el-divider>
            <el-table border :data="onceTableData || props.onceTableData">
                <el-table-column label="产品类型" align="center" prop="productType" />
                <el-table-column label="产品" align="center" prop="product" />
                <el-table-column label="总金额" align="center" prop="totalAmount" />
                <el-table-column label="金额" align="center" prop="amount" />
                <el-table-column label="增值税金额" align="center" prop="invoiceAmount" />
                <el-table-column label="增值税税率" align="center" prop="invoiceTaxRate" />
                <el-table-column label="人数" align="center" prop="invoicePersonCount" />
                <el-table-column label="备注" align="center" prop="invoiceRemark" />
            </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="handleIssueInvoice">开票</el-button>
                    <el-button @click="handleClose">取消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
const serviceFeeTableData = ref([
    {
        productType: '服务费',
        billMonth: '2023-01',
        financialReceivableMonth: '2023-01',
        customerAccount: '帐套001',
        contractNo: '合同001',
        totalAmount: 10000.00,
        amount: 10000.00,
        invoiceAmount: 10000.00,
        invoiceTaxRate: '13%',
        invoicePersonCount: 1,
        invoiceBigContractType: '大合同类型1',
        invoiceSignatory: '签约方抬头1'
    }
]);

const collectionTableData = ref([
    {
        billMonth: '2023-01',
        financialReceivableMonth: '2023-01',
        customerAccount: '帐套001',
        contractNo: '合同001',
        receivableAmount: 10000.00,
        personalAmount: 10000.00,
        enterpriseAmount: 10000.00,
        invoiceType: '发票类型1',
        invoiceBigContractType: '大合同类型1',
        invoiceSignatory: '签约方抬头1'
    }
]);

const onceTableData = ref([
    {
        productType: '服务费',
        product: '产品1',
        totalAmount: 10000.00,
        amount: 10000.00,
        invoiceAmount: 10000.00,
        invoiceTaxRate: '13%',
        invoicePersonCount: 1,
        invoiceBigContractType: '大合同类型1',
        invoiceSignatory: '签约方抬头1'
    }
]);

const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: '',
    },
    dialogForm: {
        type: Object,
        default: () => { },
    },
    serviceFeeTableData: {
        type: Array,
        default: () => [],
    },
    collectionTableData: {
        type: Array,
        default: () => [],
    },
    onceTableData: {
        type: Array,
        default: () => [],
    },
});
// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});
const emit = defineEmits([
    'issueInvoice',
    'close',
    'update:dialogShow',
]);

/** 关闭 */
const handleClose = () => {
    dialogShow.value = false;
    emit('close');
};

/** 开票 */
const handleIssueInvoice = () => {
    emit('issueInvoice');
};

const disabled = computed(() => {
    switch (props.title) {
        case '查看开票明细':
            return true;
        case '开票明细':
            return false;
        default:
            return false;
    }
});
console.log(disabled.value);

</script>
<style lang="scss" scoped></style>