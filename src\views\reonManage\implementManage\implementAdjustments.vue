<!-- 实做调整 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="调整名称:" prop="adjustName">
                <el-input class="width220" v-model="obj.queryParams.adjustName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-input class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="调整类型:" prop="adjustType">
                <el-select class="width220" v-model="obj.queryParams.adjustType" placeholder="请选择" clearable>
                    <el-option v-for="item in adjustTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="调整起始年月:" prop="adjustStartMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.adjustStartMonth" type="month"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="调整任务号:" prop="taskNo">
                <el-input class="width220" v-model="obj.queryParams.taskNo" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="调整状态:" prop="adjustStatus">
                <el-select class="width220" v-model="obj.queryParams.adjustStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in adjustStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增调整</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Link" @click="handleReference">引用调整</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Right" @click="handleEnter">进入调整</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="调整任务号" align="center" prop="taskNo" />
            <el-table-column label="调整名称" align="center" prop="adjustName" />
            <el-table-column label="福利包编号" align="center" prop="welfarePackageNo" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="调整类型" align="center" prop="adjustType" />
            <el-table-column label="调整起始年月" align="center" prop="adjustStartMonth" />
            <el-table-column label="上传原始文件" align="center">
                <template #default="scope">
                    <el-button style="text-decoration: underline; color: #01aaed;" text
                        @click="handleViewOriginalFile(scope.row)">{{ scope.row.uploadOriginalFile }}</el-button>
                </template>
            </el-table-column>
            <el-table-column label="详情" align="center">
                <template #default="scope">
                    <el-button style="text-decoration: underline; color: #01aaed;" text
                        @click="handleDetail(scope.row)">查看</el-button>
                </template>
            </el-table-column>
            <el-table-column label="调整状态" align="center" prop="adjustStatus">
                <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.adjustStatus)">
                        {{ scope.row.adjustStatus }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="生成人" align="center" prop="creator" />
            <el-table-column label="生成时间" align="center" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 新增调整 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="70%" append-to-body @close="handleClose">
            <el-form :model="obj.dialogForm" ref="dialogRef" :rules="rules" inline label-width="auto">
                <el-form-item label="调整名称:" prop="adjustName">
                    <el-input class="width220" v-model="obj.dialogForm.adjustName" placeholder="请输入调整名称" clearable />
                </el-form-item>
                <el-form-item label="福利包名称:" prop="welfarePackageName">
                    <el-select class="width220" v-model="obj.dialogForm.welfarePackageName" placeholder="请选择" clearable>
                        <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="调整类型:" prop="adjustType">
                    <el-select class="width220" v-model="obj.dialogForm.adjustType" placeholder="请选择" clearable>
                        <el-option v-for="item in adjustTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="调整范围:" prop="adjustScope">
                    <el-input class="width220" v-model="obj.dialogForm.adjustScope" placeholder="请输入调整范围" />
                </el-form-item>
                <el-form-item label="调整起始月:" prop="adjustStartMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.adjustStartMonth" type="month"
                        placeholder="请选择" clearable />
                </el-form-item>
                <el-row>
                    <el-col :span="24">
                        <el-form-item>
                            <el-button type="primary" @click="handleImport">保存导入设置</el-button>
                            <el-button type="primary" @click="handleDownload">下载核对模版</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-table :data="obj.dialogForm.productList" border @selection-change="handleProductSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="产品名称" align="center" prop="productName" />
                    <el-table-column label="原比例" align="center" prop="originalRatio" />
                    <el-table-column label="新比例" align="center" prop="newRatio" />
                    <el-table-column label="新企业比例" align="center" prop="newCompanyRatio" />
                    <el-table-column label="新个人比例" align="center" prop="newPersonalRatio" />
                    <el-table-column label="企业基数列" align="center" prop="companyBaseColumn" />
                    <el-table-column label="个人基数列" align="center" prop="personalBaseColumn" />
                </el-table>
                <el-row class="mt10" v-if="!obj.details">
                    <el-form-item label=" 上传文件:" prop="file">
                        <FileUpload v-model="obj.dialogForm.file" />
                    </el-form-item>
                </el-row>
                <el-tabs type="border-card" v-if="obj.details" class="mt20">
                    <el-tab-pane label="实做调整明细">
                        <el-table :data="obj.dialogForm.productList" border>
                            <el-table-column label="姓名" align="center" prop="name" />
                            <el-table-column label="订单编号" align="center" prop="orderNo" />
                            <el-table-column label="客户编号" align="center" prop="customerNo" />
                            <el-table-column label="调整状态" align="center" prop="adjustStatus" />
                            <el-table-column label="失败原因" align="center" prop="failReason" />
                        </el-table>
                    </el-tab-pane>
                </el-tabs>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="submitForm">保存</el-button>
                <el-button @click="cancelForm">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup name="ActualAdjustment">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 调整类型选项
const adjustTypeOptions = [
    { value: '社保基数调整', label: '社保基数调整' },
    { value: '公积金比例调整', label: '公积金比例调整' },
    { value: '医保比例调整', label: '医保比例调整' }
];

// 调整状态选项
const adjustStatusOptions = [
    { value: '已完成', label: '已完成' },
    { value: '进行中', label: '进行中' },
    { value: '未开始', label: '未开始' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: 'WP001', label: '标准福利包' },
    { value: 'WP002', label: '高级福利包' },
    { value: 'WP003', label: '基础福利包' }
];

// 表单验证规则
const rules = {
    adjustName: [
        { required: true, message: '请输入调整名称', trigger: 'blur' }
    ],
    welfarePackageName: [
        { required: true, message: '请选择福利包名称', trigger: 'change' }
    ],
    adjustType: [
        { required: true, message: '请选择调整类型', trigger: 'change' }
    ],
    adjustStartMonth: [
        { required: true, message: '请选择调整起始月', trigger: 'change' }
    ],
    file: [
        { required: false, message: '请上传文件', trigger: 'change' }
    ]
};

// 模拟数据
const mockTableData = [
    {
        id: 1,
        taskNo: 'ADJ20230001',
        adjustName: '社保基数调整',
        welfarePackageNo: 'WP001',
        welfarePackageName: '标准福利包',
        adjustType: '社保基数调整',
        adjustStartMonth: '2023-07',
        uploadOriginalFile: '2023-08-01.xlsx',
        details: '查看',
        adjustStatus: '已完成',
        creator: '张三',
        createTime: '2023-06-15 10:30:00'
    },
    {
        id: 2,
        taskNo: 'ADJ20230002',
        adjustName: '公积金比例调整',
        welfarePackageNo: 'WP002',
        welfarePackageName: '高级福利包',
        adjustType: '公积金比例调整',
        adjustStartMonth: '2023-08',
        uploadOriginalFile: '2023-08-01.xlsx',
        details: '查看',
        adjustStatus: '进行中',
        creator: '李四',
        createTime: '2023-06-20 14:15:00'
    },
    {
        id: 3,
        taskNo: 'ADJ20230003',
        adjustName: '医保比例调整',
        welfarePackageNo: 'WP003',
        welfarePackageName: '基础福利包',
        adjustType: '医保比例调整',
        adjustStartMonth: '2023-09',
        uploadOriginalFile: '2023-08-01.xlsx',
        details: '查看',
        adjustStatus: '未开始',
        creator: '王五',
        createTime: '2023-06-25 09:45:00'
    }
];

// 模拟产品数据
const mockProductData = [
    {
        id: 1,
        productName: '养老保险',
        originalRatio: '16%',
        newRatio: '18%',
        newCompanyRatio: '12%',
        newPersonalRatio: '6%',
        companyBaseColumn: 'C',
        personalBaseColumn: 'D'
    },
    {
        id: 2,
        productName: '医疗保险',
        originalRatio: '10%',
        newRatio: '12%',
        newCompanyRatio: '8%',
        newPersonalRatio: '4%',
        companyBaseColumn: 'E',
        personalBaseColumn: 'F'
    },
    {
        id: 3,
        productName: '失业保险',
        originalRatio: '2%',
        newRatio: '3%',
        newCompanyRatio: '2%',
        newPersonalRatio: '1%',
        companyBaseColumn: 'G',
        personalBaseColumn: 'H'
    }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        adjustName: null, // 调整名称
        welfarePackageName: null, // 福利包名称
        adjustType: null, // 调整类型
        adjustStartMonth: null, // 调整起始年月
        taskNo: null, // 调整任务号
        adjustStatus: null, // 调整状态
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        adjustName: '',
        welfarePackageName: '',
        adjustType: '',
        adjustScope: '',
        adjustStartMonth: '',
        file: null,
        productList: mockProductData
    }, // 对话框表单
    dialogShow: false, // 对话框显示
    ids: [], // 选中的id
    productIds: [], // 选中的产品id
    title: "", // 标题
    details: false // 详情
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 选中数据改变 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增调整 */
function handleAdd() {
    obj.dialogShow = true;
    obj.title = "新增调整";
    resetDialogForm();
}

/** 引用调整 */
function handleReference() {
    obj.dialogShow = true;
    obj.title = "引用调整";
}

/** 进入调整 */
function handleEnter() {
    obj.dialogShow = true;
    obj.title = "进入调整";
}

/** 查看详情 */
function handleDetail(row) {
    obj.details = true;
    obj.dialogShow = true;
    obj.title = "查看调整";
}

/** 关闭 */
function handleClose() {
    obj.dialogShow = false;
    obj.details = false;
}

/** 获取状态类型 */
function getStatusType(status) {
    if (status === '已完成') return 'success';
    if (status === '进行中') return 'warning';
    if (status === '未开始') return 'info';
    return 'primary';
}

/** 导入数据 */
function handleImport() {
    obj.dialogShow = true;
    obj.title = "上传导入数据";
}

/** 下载模版 */
function handleDownload() {
    console.log(111);
}





/**
 * 表单相关函数
 */

/** 选中产品数据改变 */
function handleProductSelectionChange(selection) {
    obj.productIds = selection.map(item => item.id);
}

/** 提交表单 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

/** 取消表单 */
function cancelForm() {
    obj.dialogShow = false;
    resetDialogForm();
}

/** 重置对话框表单 */
function resetDialogForm() {
    obj.dialogForm = {
        adjustName: '',
        welfarePackageName: '',
        adjustType: '',
        adjustScope: '',
        adjustStartMonth: '',
        file: null,
        productList: []
    };
    proxy.resetForm("dialogRef");
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}

.mt10 {
    margin-top: 10px;
}
</style>