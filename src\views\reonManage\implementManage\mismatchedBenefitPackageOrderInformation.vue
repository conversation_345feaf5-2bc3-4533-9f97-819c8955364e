<!-- 未匹配福利包订单信息 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" v-model="obj.queryParams.city" placeholder="请选择" clearable>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="比例名称:" prop="ratioName">
                <el-input class="width220" v-model="obj.queryParams.ratioName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="单立户名称:" prop="singleAccountName">
                <el-input class="width220" v-model="obj.queryParams.singleAccountName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态:" prop="status">
                <el-select class="width220" v-model="obj.queryParams.status" placeholder="请选择" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleSetProcessed">设置已处理</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" :disabled="obj.single"
                    @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="订单号" align="center" prop="orderNo" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="是否是大户" align="center" prop="isBigAccount" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="未匹配比例" align="center" prop="unmatchedRatio" />
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <el-tag :type="scope.row.status === '1' ? 'success' : 'warning'">
                        {{ scope.row.status === '1' ? '已处理' : '未处理' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="MismatchedBenefitPackageOrderInformation">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
];

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 状态选项
const statusOptions = [
    { value: '0', label: '未处理' },
    { value: '1', label: '已处理' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        orderNo: 'ORD20230001',
        welfareHandler: '公司A',
        isBigAccount: '是',
        customerName: '客户A',
        unmatchedRatio: '养老保险比例',
        status: '0',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 2,
        orderNo: 'ORD20230002',
        welfareHandler: '公司B',
        isBigAccount: '否',
        customerName: '客户B',
        unmatchedRatio: '医疗保险比例',
        status: '0',
        createTime: '2023-02-15 09:15:00'
    },
    {
        id: 3,
        orderNo: 'ORD20230003',
        welfareHandler: '公司C',
        isBigAccount: '是',
        customerName: '客户C',
        unmatchedRatio: '失业保险比例',
        status: '1',
        createTime: '2023-03-15 11:45:00'
    }
];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        city: null, // 城市
        welfareHandler: null, // 福利办理方
        orderNo: null, // 订单号
        ratioName: null, // 比例名称
        customerName: null, // 客户名称
        singleAccountName: null, // 单立户名称
        status: null, // 状态
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    ids: [], // 选中的id
    title: "" // 标题
})

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.city) {
            filteredData = filteredData.filter(item => item.city === obj.queryParams.city);
        }

        if (obj.queryParams.welfareHandler) {
            filteredData = filteredData.filter(item => item.welfareHandler.includes(obj.queryParams.welfareHandler));
        }

        if (obj.queryParams.orderNo) {
            filteredData = filteredData.filter(item => item.orderNo.includes(obj.queryParams.orderNo));
        }

        if (obj.queryParams.ratioName) {
            filteredData = filteredData.filter(item => item.unmatchedRatio.includes(obj.queryParams.ratioName));
        }

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item => item.customerName.includes(obj.queryParams.customerName));
        }

        if (obj.queryParams.status) {
            filteredData = filteredData.filter(item => item.status === obj.queryParams.status);
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}


/** 设置已处理按钮操作 */
function handleSetProcessed() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要设置的记录');
        return;
    }

    proxy.$modal.confirm('是否确认设置已处理？').then(() => {
        // 实际项目中应该调用API进行设置
        // 模拟设置已处理
        obj.tableData.forEach(item => {
            if (obj.ids.includes(item.id)) {
                item.status = '1';
            }
        });

        proxy.$modal.msgSuccess('设置成功');
        obj.ids = [];
    }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行导出');
        return;
    }

    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>