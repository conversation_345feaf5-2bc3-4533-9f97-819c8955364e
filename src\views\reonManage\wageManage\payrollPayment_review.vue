<!-- 工资支付-制单 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="出款公司名称:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="支付类型:" prop="paymentType">
                <el-input class="width220" v-model="obj.queryParams.paymentType" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="申请人:" prop="applicant">
                <el-input class="width220" v-model="obj.queryParams.applicant" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="支付方式:" prop="paymentMethod">
                <el-input class="width220" v-model="obj.queryParams.paymentMethod" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="申请支付时间>=:" prop="applyTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeStart" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="申请支付时间<=:" prop="applyTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.applyTimeEnd" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="工资支付日期:" prop="salaryPaymentDate">
                <el-date-picker class="width220" v-model="obj.queryParams.salaryPaymentDate" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="制单状态:" prop="documentStatus">
                <el-select class="width220" v-model="obj.queryParams.documentStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in documentStatusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否退票重发:" prop="isRefundAndResend">
                <el-select class="width220" v-model="obj.queryParams.isRefundAndResend" placeholder="请选择" clearable>
                    <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="download"
                    @click="handleDownloadPayoutDetailFile">下载出款明细文件</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handlePrintPayoutSummaryFile">补打出款汇总文件</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleSubmitPayout">出款提交</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="payroll-payment-documentation" />
            <el-button type="primary" plain @click="handleReject">驳回</el-button>
            <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            <el-button type="success" plain icon="Printer" @click="handlePrint">打印</el-button>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" align="center" />
            <el-table-column v-if="obj.selectedColumns.includes('companyName')" label="出款公司名称" align="center" sortable
                prop="companyName" />
            <el-table-column v-if="obj.selectedColumns.includes('customerName')" label="客户名称" align="center"
                prop="customerName" />
            <el-table-column v-if="obj.selectedColumns.includes('payee')" label="收款方" align="center" prop="payee" />
            <el-table-column v-if="obj.selectedColumns.includes('salaryPaymentDate')" label="工资支付日期" align="center"
                prop="salaryPaymentDate" />
            <el-table-column v-if="obj.selectedColumns.includes('total')" label="总计" align="center" prop="total" />
            <el-table-column v-if="obj.selectedColumns.includes('productPlan')" label="产品方案" align="center"
                prop="productPlan" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentType')" label="支付类型" align="center"
                prop="paymentType" />
            <el-table-column v-if="obj.selectedColumns.includes('isRefundAndResend')" label="是否退票重发" align="center"
                prop="isRefundAndResend" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentMonth')" label="支付所属年月" align="center"
                prop="paymentMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('actualSalary')" label="实付工资款" align="center"
                prop="actualSalary" />
            <el-table-column v-if="obj.selectedColumns.includes('personalTax')" label="个税款" align="center"
                prop="personalTax" />
            <el-table-column v-if="obj.selectedColumns.includes('compensation')" label="补偿金" align="center"
                prop="compensation" />
            <el-table-column v-if="obj.selectedColumns.includes('compensationTax')" label="补偿金个税" align="center"
                prop="compensationTax" />
            <el-table-column v-if="obj.selectedColumns.includes('yearEndBonus')" label="年终奖" align="center"
                prop="yearEndBonus" />
            <el-table-column v-if="obj.selectedColumns.includes('yearEndBonusTax')" label="年终奖个税" align="center"
                prop="yearEndBonusTax" />
            <el-table-column v-if="obj.selectedColumns.includes('laborSalary')" label="劳务工资" align="center"
                prop="laborSalary" />
            <el-table-column v-if="obj.selectedColumns.includes('laborSalaryTax')" label="劳务个税工资" align="center"
                prop="laborSalaryTax" />
            <el-table-column v-if="obj.selectedColumns.includes('serviceFee')" label="服务费" align="center"
                prop="serviceFee" />
            <el-table-column v-if="obj.selectedColumns.includes('disabilityFund')" label="残障金" align="center"
                prop="disabilityFund" />
            <el-table-column v-if="obj.selectedColumns.includes('interBankFee')" label="跨行手续费" align="center"
                prop="interBankFee" />
            <el-table-column v-if="obj.selectedColumns.includes('unionFee')" label="工会费" align="center"
                prop="unionFee" />
            <el-table-column v-if="obj.selectedColumns.includes('totalTax')" label="税金合计" align="center"
                prop="totalTax" />
            <el-table-column v-if="obj.selectedColumns.includes('applicant')" label="申请人" align="center"
                prop="applicant" />
            <el-table-column v-if="obj.selectedColumns.includes('documentStatus')" label="制单状态" align="center"
                prop="documentStatus" />
            <el-table-column v-if="obj.selectedColumns.includes('supplierPaymentTime')" label="供应商发薪时间" align="center"
                prop="supplierPaymentTime" />
            <el-table-column label="操作" align="center" width="120">
                <template #default="scope">
                    <el-button text icon="View" @click="handleOrderDetail(scope.row)">订单详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" title="上传导入数据" type="payrollPayment_documentation"
            :dialogForm="obj.dialogForm" :rules="rules" />
    </div>
</template>

<script setup name="PayrollPayment_documentation">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



// 制单状态选项
const documentStatusOptions = [
    { value: '1', label: '待审核' },
    { value: '2', label: '已审核' },
    { value: '3', label: '已驳回' },
    { value: '4', label: '已完成' }
];

// 是否选项
const yesNoOptions = [
    { value: 'Y', label: '是' },
    { value: 'N', label: '否' }
];


// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        companyName: '公司A',
        customerName: '客户A',
        payee: '张三',
        salaryPaymentDate: '2023-06-01',
        total: 10000,
        productPlan: '方案A',
        paymentType: '工资',
        isRefundAndResend: 'N',
        paymentMonth: '2023-06',
        actualSalary: 8000,
        personalTax: 500,
        compensation: 0,
        compensationTax: 0,
        yearEndBonus: 0,
        yearEndBonusTax: 0,
        laborSalary: 0,
        laborSalaryTax: 0,
        serviceFee: 1000,
        disabilityFund: 200,
        interBankFee: 50,
        unionFee: 100,
        totalTax: 500,
        applicant: '李四',
        documentStatus: '1',
        supplierPaymentTime: '2023-06-05'
    },
    {
        id: 2,
        companyName: '公司B',
        customerName: '客户B',
        payee: '王五',
        salaryPaymentDate: '2023-06-02',
        total: 12000,
        productPlan: '方案B',
        paymentType: '工资',
        isRefundAndResend: 'Y',
        paymentMonth: '2023-06',
        actualSalary: 9500,
        personalTax: 600,
        compensation: 0,
        compensationTax: 0,
        yearEndBonus: 0,
        yearEndBonusTax: 0,
        laborSalary: 0,
        laborSalaryTax: 0,
        serviceFee: 1200,
        disabilityFund: 240,
        interBankFee: 60,
        unionFee: 120,
        totalTax: 600,
        applicant: '赵六',
        documentStatus: '2',
        supplierPaymentTime: '2023-06-06'
    }
];

// 表单验证规则
const rules = {
    withholdingAgent: [
        { required: true, message: '请选择扣缴义务人', trigger: 'blur' }
    ]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null, // 出款公司名称
        paymentType: null, // 支付类型
        applicant: null, // 申请人
        paymentMethod: null, // 支付方式
        customerName: null, // 客户名称
        applyTimeStart: null, // 申请支付时间起
        applyTimeEnd: null, // 申请支付时间止
        salaryPaymentDate: null, // 工资支付日期
        documentStatus: null, // 制单状态
        isRefundAndResend: null, // 是否退票重发
    }, // 查询表单
    total: 0, // 总条数

    tableData: [], // 列表
    dialogForm: {
        withholdingAgent: '',
        remark: '',
        uploadFile: null
    }, // 导入表单
    dialogShow: false, // 导入弹窗
    ids: [], // 选中的id

    title: "", // 标题

    selectedColumns: ['companyName', 'customerName', 'payee', 'salaryPaymentDate', 'total', 'productPlan',
        'paymentType', 'isRefundAndResend', 'paymentMonth', 'actualSalary', 'personalTax', 'compensation',
        'compensationTax', 'yearEndBonus', 'yearEndBonusTax', 'laborSalary', 'laborSalaryTax', 'serviceFee',
        'disabilityFund', 'interBankFee', 'unionFee', 'totalTax', 'applicant', 'documentStatus', 'supplierPaymentTime'],//默认显示所有列
    columnOptions: [
        { label: '出款公司名称', prop: 'companyName' },
        { label: '客户名称', prop: 'customerName' },
        { label: '收款方', prop: 'payee' },
        { label: '工资支付日期', prop: 'salaryPaymentDate' },
        { label: '总计', prop: 'total' },
        { label: '产品方案', prop: 'productPlan' },
        { label: '支付类型', prop: 'paymentType' },
        { label: '是否退票重发', prop: 'isRefundAndResend' },
        { label: '支付所属年月', prop: 'paymentMonth' },
        { label: '实付工资款', prop: 'actualSalary' },
        { label: '个税款', prop: 'personalTax' },
        { label: '补偿金', prop: 'compensation' },
        { label: '补偿金个税', prop: 'compensationTax' },
        { label: '年终奖', prop: 'yearEndBonus' },
        { label: '年终奖个税', prop: 'yearEndBonusTax' },
        { label: '劳务工资', prop: 'laborSalary' },
        { label: '劳务个税工资', prop: 'laborSalaryTax' },
        { label: '服务费', prop: 'serviceFee' },
        { label: '残障金', prop: 'disabilityFund' },
        { label: '跨行手续费', prop: 'interBankFee' },
        { label: '工会费', prop: 'unionFee' },
        { label: '税金合计', prop: 'totalTax' },
        { label: '申请人', prop: 'applicant' },
        { label: '制单状态', prop: 'documentStatus' },
        { label: '供应商发薪时间', prop: 'supplierPaymentTime' },
    ],//列配置选项
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟异步请求
    setTimeout(() => {
        // 这里可以根据查询条件过滤数据
        const filteredData = mockTableData.filter(item => {
            const matchCompanyName = !obj.queryParams.companyName ||
                item.companyName.includes(obj.queryParams.companyName);
            const matchPaymentType = !obj.queryParams.paymentType ||
                item.paymentType.includes(obj.queryParams.paymentType);
            const matchApplicant = !obj.queryParams.applicant ||
                item.applicant.includes(obj.queryParams.applicant);
            const matchCustomerName = !obj.queryParams.customerName ||
                item.customerName.includes(obj.queryParams.customerName);
            const matchDocumentStatus = !obj.queryParams.documentStatus ||
                item.documentStatus === obj.queryParams.documentStatus;
            const matchIsRefundAndResend = !obj.queryParams.isRefundAndResend ||
                item.isRefundAndResend === obj.queryParams.isRefundAndResend;

            return matchCompanyName && matchPaymentType && matchApplicant &&
                matchCustomerName && matchDocumentStatus && matchIsRefundAndResend;
        });

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 出款提交 */
function handleSubmitPayout() {
    proxy.$modal.msgInfo('出款提交');
}

/** 下载出款明细文件 */
function handleDownloadPayoutDetailFile() {
    proxy.$modal.msgInfo('下载出款明细文件');
}

/** 打印出款汇总文件 */
function handlePrintPayoutSummaryFile() {
    proxy.$modal.msgInfo('打印出款汇总文件');
}

/** 驳回 */
function handleReject() {
    proxy.$modal.msgInfo('驳回');
}

/** 导出 */
function handleExport() {
    proxy.$modal.msgInfo('导出');
}

/** 打印 */
function handlePrint() {
    proxy.$modal.msgInfo('打印');
}

/** 订单详情 */
function handleOrderDetail(row) {
    proxy.$modal.msgInfo('查看订单详情: ' + row.companyName + ' - ' + row.customerName);
}


getList();
</script>
<style lang="scss" scoped>
//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>