<!-- 会议纪要 -->
<template>
    <el-table :data="tableData || props.tableData" border>
        <el-table-column label="序号" type="index" align="center" width="60" />
        <el-table-column label="合同编号" align="center" prop="contractNumber" />
        <el-table-column label="会议时间" align="center" prop="meetingTime" />
        <el-table-column label="会议主题" align="center" prop="meetingTheme" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="上传人" align="center" prop="uploader" />
        <el-table-column label="上传时间" align="center" prop="uploadTime" />
    </el-table>
</template>
<script setup>

const tableData = ref([
    {
        contractNumber: '123456',
        meetingTime: '2023-06-01',
        meetingTheme: '会议主题',
        remark: '备注',
        uploader: '上传人',
        uploadTime: '2023-06-01'
    },
    {
        contractNumber: '123456',
        meetingTime: '2023-06-01',
        meetingTheme: '会议主题',
        remark: '备注',
        uploader: '上传人',
        uploadTime: '2023-06-01'
    }
]);

const props = defineProps({
    tableData: {
        type: Array,
        default: () => []
    }
})
</script>
<style lang="scss" scoped></style>
