<!-- 支付管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="支付类型:" prop="paymentType">
                <el-select class="width220" filterable v-model="obj.queryParams.paymentType" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="审批状态:" prop="approvalStatus">
                <el-select class="width220" filterable v-model="obj.queryParams.approvalStatus" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="申请支付时间起:" prop="applyTimeStart">
                <el-date-picker v-model="obj.queryParams.applyTimeStart" type="date" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="申请支付时间止:" prop="applyTimeEnd">
                <el-date-picker v-model="obj.queryParams.applyTimeEnd" type="date" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="福利办理方:" prop="benefitsHandler">
                <el-select class="width220" filterable v-model="obj.queryParams.benefitsHandler" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="支付所属年月:" prop="paymentMonth">
                <el-date-picker v-model="obj.queryParams.paymentMonth" type="date" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="审批通过时间>=:" prop="approvalTimeStart">
                <el-date-picker v-model="obj.queryParams.approvalTimeStart" type="date" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="审批通过时间<=:" prop="approvalTimeEnd">
                <el-date-picker v-model="obj.queryParams.approvalTimeEnd" type="date" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="支付方式:" prop="paymentMethod">
                <el-select class="width220" filterable v-model="obj.queryParams.paymentMethod" placeholder="请选择"
                    clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.province" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增支付申请</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single"
                    @click="handleUpdate">修改支付申请</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="handleDetail">
            <el-table-column type="selection" width="55" />
            <el-table-column label="审批状态" align="center" prop="approvalStatus" />
            <el-table-column label="支付类型" align="center" prop="paymentType" />
            <el-table-column label="支付方式" align="center" prop="paymentMethod" />
            <el-table-column label="收款方" align="center" prop="payee" />
            <el-table-column label="收款银行" align="center" prop="payeeBank" />
            <el-table-column label="银行账号/支票号" align="center" width="120" prop="bankAccount" />
            <el-table-column label="开户银行" align="center" prop="bankBranch" />
            <el-table-column label="应付金额" align="center" prop="payableAmount" />
            <el-table-column label="申请金额" align="center" prop="requestAmount" />
            <el-table-column label="支付所属年月" align="center" width="120" prop="paymentMonth" />
            <el-table-column label="最晚支付时间" align="center" width="120" prop="latestPaymentDate" />
            <el-table-column label="申请人" align="center" prop="applicant" />
            <el-table-column label="申请时间" align="center" prop="applyTime" />
            <el-table-column label="支付地" align="center" prop="paymentPlace" />
            <el-table-column label="福利办理方" align="center" width="120" prop="welfareHandler" />
            <el-table-column label="审批通过时间" align="center" width="120" prop="approvalTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="60%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="福利办理方" prop="welfareAgent">
                    <el-select class="width220" filterable v-model="obj.dialogForm.welfareAgent" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in welfareAgentOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="福利包名称" prop="welfarePackageName">
                    <el-input class="width220" v-model="obj.dialogForm.name" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="报表年月" prop="reportMonth">
                    <el-date-picker v-model="obj.dialogForm.reportMonth" type="month" placeholder="请选择"
                        value-format="YYYY-MM" />
                </el-form-item>
                <el-form-item label="支付详细类型" prop="paymentDetailType">
                    <el-input class="width220" v-model="obj.dialogForm.paymentDetailType" placeholder="请输入" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" plain @click="handleAdd">选择支付费用</el-button>
                    <el-button type="primary" plain @click="handleAdd">重新选中金额</el-button>
                </el-form-item>
                <el-form-item label="支付方式" prop="paymentMethod">
                    <el-select class="width220" filterable v-model="obj.dialogForm.paymentMethod" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="详细支付方式" prop="paymentMethodDetail">
                    <el-input class="width220" v-model="obj.dialogForm.paymentMethodDetail" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="收款方" prop="payee">
                    <el-input class="width220" v-model="obj.dialogForm.payee" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="收款银行" prop="payeeBank">
                    <el-select class="width220" filterable v-model="obj.dialogForm.city" placeholder="请选择" clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="收款银行开户行" prop="bankBranch">
                    <el-select class="width220" filterable v-model="obj.dialogForm.bankBranch" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="银行账号/支票号" prop="bankAccount">
                    <el-input class="width220" v-model="obj.dialogForm.bankAccount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="单据数量" prop="documentCount">
                    <el-input class="width220" v-model="obj.dialogForm.documentCount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="申请支付总额" prop="requestAmount">
                    <el-input class="width220" v-model="obj.dialogForm.requestAmount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="应付总额" prop="payableAmount">
                    <el-input class="width220" v-model="obj.dialogForm.payableAmount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="最晚支付日期" prop="latestPaymentDate">
                    <el-date-picker v-model="obj.dialogForm.latestPaymentDate" type="date" placeholder="请选择"
                        value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="支付所属年月" prop="paymentMonth">
                    <el-input class="width220" v-model="obj.dialogForm.paymentMonth" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="支付地" prop="paymentPlace">
                    <el-select class="width220" filterable v-model="obj.dialogForm.paymentPlace" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="付款类型" prop="paymentType">
                    <el-select class="width220" filterable v-model="obj.dialogForm.paymentType" placeholder="请选择"
                        clearable>
                        <el-option v-for="item in provinces" :key="item.code" :label="item.province"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="差异金额" prop="differenceAmount">
                    <el-input class="width220" v-model="obj.dialogForm.differenceAmount" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="历史差异" prop="historyDifference">
                    <el-input class="width220" v-model="obj.dialogForm.historyDifference" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="付款用途" prop="paymentPurpose">
                    <el-input type="textarea" style="width: 600px;" :rows="3" v-model="obj.dialogForm.paymentPurpose"
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" style="width: 600px;" :rows="3" v-model="obj.dialogForm.remark"
                        placeholder="请输入" />
                </el-form-item>
                <el-divider content-position="left">文件上传</el-divider>
                <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 详情对话框 -->
        <PaymentApplication v-model:dialogShow="obj.dialogShow2" title="查看支付管理" :form="obj.detailForm"
            menuName="payment" :disabled="true" />


    </div>
</template>

<script setup name="Payment">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";
import PaymentApplication from '@/views/reonManage/components/dialog/paymentApplication.vue'

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    rules: {

    },
    total: 0,//总条数

    tableData: [
        {
            approvalStatus: '通过',
            paymentType: '银行转账',
            paymentMethod: '现金支付',
            payee: '张三',
            payeeBank: '工商银行',
            bankAccount: '*********',
            bankBranch: '北京分行',
            payableAmount: '10000',
            requestAmount: '10000',
            paymentMonth: '2023-05',
            latestPaymentDate: '2023-05-15',
            applicant: '张三',
            applyTime: '2023-05-01 10:30:00',
            paymentPlace: '北京',
            welfareHandler: '公司A',
            approvalTime: '2023-05-03 15:20:00',
        },
        {
            approvalStatus: '通过',
            paymentType: '银行转账',
            paymentMethod: '现金支付',
            payee: '张三',
            payeeBank: '工商银行',
            bankAccount: '*********',
            bankBranch: '上海分行',
            payableAmount: '10000',
            requestAmount: '10000',
            paymentMonth: '2023-05',
            latestPaymentDate: '2023-05-15',
            applicant: '张三',
            applyTime: '2023-05-01 10:30:00',
            paymentPlace: '北京',
            welfareHandler: '公司A',
            approvalTime: '2023-05-03 15:20:00',
        }

    ],//列表
    dialogForm: {}, //表单
    dialogShow: false, //弹出框
    ids: [],//选中的id
    title: "",//标题
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}
/** 查看按钮操作 */
function handleDetail(row) {
    console.log(row);
    obj.dialogShow2 = true;
    obj.title = "详情";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const _id = row.id || obj.ids
    getUsers(_id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改";
    });
}


/** 提交按钮 */
function submitForm() {
    console.log(obj.dialogForm);
    return
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

getList();
</script>
<style lang="scss" scoped></style>