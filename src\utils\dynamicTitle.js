import store from "@/store";
import defaultSettings from "@/settings";
import useSettingsStore from "@/store/modules/settings";

/**
 * 动态修改标题
 * @returns {void}
 */
export function useDynamicTitle() {
  // 获取设置存储实例
  const settingsStore = useSettingsStore();
  // 如果启用了动态标题
  if (settingsStore.dynamicTitle) {
    // 设置文档标题为设置存储中的标题加上默认标题
    document.title = settingsStore.title + " - " + defaultSettings.title;
  } else {
    // 设置文档标题为默认标题
    document.title = defaultSettings.title;
  }
}
