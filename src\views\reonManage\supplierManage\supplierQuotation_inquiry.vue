<!-- 供应商报价查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="省:" prop="province">
                <el-select class="width220" filterable v-model="obj.queryParams.province" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.value" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" filterable v-model="obj.queryParams.city" placeholder="请选择" clearable>
                    <el-option v-for="item in provinces" :key="item.code" :label="item.value" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="供应商名称" align="center" prop="supplierName" />
            <el-table-column label="报价单编号" align="center" prop="quotationNo" />
            <el-table-column label="城市名称" align="center" prop="cityName" />
            <el-table-column label="社保委托服务费成本" align="center" prop="socialSecurityCost" />
            <el-table-column label="发薪服务费成本" align="center" prop="payrollCost" />
            <el-table-column label="备注" align="center" prop="remark" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="SupplierQuotation">
import { useAreaStore } from '@/store/modules/area'
import { listScale } from "@/api/reonApi/scale";

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据
// const cities = areaStore.getCities('110000') // 获取城市数据
// const districts = areaStore.getDistricts('110000', '110100') // 获取区县数据
const { sys_yes_no, sys_job_group } = proxy.useDict('sys_yes_no', 'sys_job_group');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单
    total: 0,//总条数
    ids: [],//选中的id
    title: "",//标题
    tableData: [
        {
            id: 1,
            supplierName: '北京科技有限公司',
            quotationNo: 'BJ20230701001',
            cityName: '北京市',
            socialSecurityCost: '200元/人/月',
            payrollCost: '50元/人/月',
            remark: '北京地区社保服务费用标准'
        },
        {
            id: 2,
            supplierName: '上海信息技术有限公司',
            quotationNo: 'SH20230702001',
            cityName: '上海市',
            socialSecurityCost: '180元/人/月',
            payrollCost: '45元/人/月',
            remark: '上海地区社保服务费用标准'
        },
        {
            id: 3,
            supplierName: '广州数字科技有限公司',
            quotationNo: 'GZ20230703001',
            cityName: '广州市',
            socialSecurityCost: '160元/人/月',
            payrollCost: '40元/人/月',
            remark: '广州地区社保服务费用标准'
        },
        {
            id: 4,
            supplierName: '深圳智能科技有限公司',
            quotationNo: 'SZ20230704001',
            cityName: '深圳市',
            socialSecurityCost: '170元/人/月',
            payrollCost: '42元/人/月',
            remark: '深圳地区社保服务费用标准'
        },
        {
            id: 5,
            supplierName: '成都创新科技有限公司',
            quotationNo: 'CD20230705001',
            cityName: '成都市',
            socialSecurityCost: '150元/人/月',
            payrollCost: '38元/人/月',
            remark: '成都地区社保服务费用标准'
        }
    ]//列表
});

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据加载
    setTimeout(() => {
        // 实际项目中应该调用API
        // listScale(obj.queryParams).then(response => {
        //     obj.tableData = response.rows;
        //     obj.total = response.total;
        //     obj.loading = false;
        // });
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}


getList();
</script>
<style lang="scss" scoped></style>