<!-- 前道订单产品变更待确认 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="供应商名称:" prop="supplierName">
                <el-select class="width220" v-model="obj.queryParams.supplierName" placeholder="请选择供应商名称" clearable>
                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="参保城市:" prop="insuredCity">
                <el-select class="width220" v-model="obj.queryParams.insuredCity" placeholder="请选择参保城市" clearable>
                    <el-option v-for="item in insuredCityOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="状态:" prop="status">
                <el-select class="width220" v-model="obj.queryParams.status" placeholder="请选择状态" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="变更时间起:" prop="changeTimeStart">
                <el-date-picker class="width220" v-model="obj.queryParams.changeTimeStart" type="datetime"
                    placeholder="请选择开始时间" clearable />
            </el-form-item>
            <el-form-item label="变更时间止:" prop="changeTimeEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.changeTimeEnd" type="datetime"
                    placeholder="请选择结束时间" clearable />
            </el-form-item>
            <el-form-item label="订单号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入订单编号" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="身份证号码:" prop="idCard">
                <el-input class="width220" v-model="obj.queryParams.idCard" placeholder="请输入身份证号码" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 按钮 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleBatchSync">批量同步</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain @click="handleNoSync">不需同步</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain @click="handleBackSync">回退同步</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" fixed prop="employeeName" />
            <el-table-column label="证件号码" align="center" prop="idCard" />
            <el-table-column label="订单编号" align="center" width="140">
                <template #default="scope">
                    <el-link style="text-decoration: underline;" type="primary" @click="handleOrderDetail(scope.row)">{{
                        scope.row.orderNo }}</el-link>
                </template>
            </el-table-column>
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="报增时间" align="center" prop="addTime" width="180" />
            <el-table-column label="报减时间" align="center" prop="reduceTime" width="180" />
            <el-table-column label="实做状态" align="center" prop="implementStatus" />
            <el-table-column label="同步状态" align="center" prop="syncStatus" />
            <el-table-column label="变更类型" align="center" prop="changeType" />
            <el-table-column label="参保城市" align="center" prop="insuredCity" />
            <el-table-column label="所属供应商" align="center" prop="supplierName" />
            <el-table-column label="操作人" align="center" prop="operator" />
            <el-table-column label="创建人" align="center" prop="creator" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 订单详情 -->
        <order-details v-model:dialogShow="obj.dialogShow" :dialogForm="obj.dialogForm" :title="obj.title"
            isDetail="true" />
    </div>
</template>


<script setup name="SupplierOrderAlter">
import orderDetails from '../components/dialog/orderDetails.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

/** 表格行样式 */
function tableRowClassName({ row, rowIndex }) {
    if (row.syncStatus === '已同步') {
        return 'red-row'
    } else if (row.syncStatus === '待同步') {
        return 'blue-row'
    }
    return '';
}


// 状态选项
const statusOptions = [
    { value: '0', label: '待同步' },
    { value: '1', label: '已同步' },
    { value: '2', label: '不需同步' },
    { value: '3', label: '已回退' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierName: null,
        insuredCity: null,
        customerName: null,
        status: null,
        changeTimeStart: null,
        changeTimeEnd: null,
        orderNo: null,
        employeeName: null,
        idCard: null,
    },//查询表单
    total: 10,//总条数
    tableData: [],//列表
    ids: [],//选中的id
})


/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                employeeName: '张三',
                idCard: '110101199001011234',
                orderNo: 'ORD20230501',
                customerName: '客户A',
                addTime: '2023-05-01 10:30:00',
                reduceTime: '2023-05-15 14:20:00',
                implementStatus: '已实做',
                syncStatus: '待同步',
                changeType: '报减',
                insuredCity: '北京',
                supplierName: '供应商A',
                operator: '李四',
                creator: '王五'
            },
            {
                id: 2,
                employeeName: '李四',
                idCard: '310101199002022345',
                orderNo: 'ORD20230502',
                customerName: '客户B',
                addTime: '2023-05-02 09:15:00',
                reduceTime: '',
                implementStatus: '已实做',
                syncStatus: '已同步',
                changeType: '报增',
                insuredCity: '上海',
                supplierName: '供应商B',
                operator: '王五',
                creator: '赵六'
            },
            {
                id: 3,
                employeeName: '王五',
                idCard: '******************',
                orderNo: 'ORD20230503',
                customerName: '客户C',
                addTime: '2023-05-03 11:45:00',
                reduceTime: '2023-05-20 16:30:00',
                implementStatus: '未实做',
                syncStatus: '不需同步',
                changeType: '变更',
                insuredCity: '广州',
                supplierName: '供应商C',
                operator: '赵六',
                creator: '孙八'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 批量同步
function handleBatchSync() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要同步的数据");
        return;
    }
    proxy.$modal.confirm('是否确认批量同步所选数据？').then(function () {
        proxy.$modal.msgSuccess("同步成功");
        getList();
    }).catch(() => { });
}

// 不需同步
function handleNoSync() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要标记为不需同步的数据");
        return;
    }
    proxy.$modal.confirm('是否确认将所选数据标记为不需同步？').then(function () {
        proxy.$modal.msgSuccess("操作成功");
        getList();
    }).catch(() => { });
}

// 回退同步
function handleBackSync() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError("请选择要回退同步的数据");
        return;
    }
    proxy.$modal.confirm('是否确认回退同步所选数据？').then(function () {
        proxy.$modal.msgSuccess("回退成功");
        getList();
    }).catch(() => { });
}

// 选中数据变化
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

// 订单详情
function handleOrderDetail(row) {
    obj.dialogShow = true;
    obj.dialogForm = row;
    obj.title = '订单详情';
}

// 关闭订单详情
function handleClose() {
    obj.dialogShow = false;
}

getList();
</script>
<style lang="scss" scoped>
:deep(.red-row) {
    color: #f00;
}

:deep(.blue-row) {
    color: #337ecc;
}
</style>