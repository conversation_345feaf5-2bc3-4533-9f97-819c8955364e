import vue from "@vitejs/plugin-vue";

import createAutoImport from "./auto-import";
import createSvgIcon from "./svg-icon";
import createCompression from "./compression";
import createSetupExtend from "./setup-extend";

/**
 * 创建Vite插件
 * @param {Object} viteEnv - Vite环境变量对象
 * @param {Boolean} isBuild - 是否为构建模式，默认为false
 * @returns {Array} 返回Vite插件数组
 */
export default function createVitePlugins(viteEnv, isBuild = false) {
  // 初始化Vite插件数组，包含Vue插件
  const vitePlugins = [vue()];
  // 添加自动导入插件
  vitePlugins.push(createAutoImport());
  // 添加setup扩展插件
  vitePlugins.push(createSetupExtend());
  // 添加SVG图标插件，并传入构建模式参数
  vitePlugins.push(createSvgIcon(isBuild));
  // 如果是构建模式，添加压缩插件
  isBuild && vitePlugins.push(...createCompression(viteEnv));
  // 返回Vite插件数组
  return vitePlugins;
}
