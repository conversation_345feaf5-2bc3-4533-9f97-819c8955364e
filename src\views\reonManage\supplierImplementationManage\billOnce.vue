<!-- 账单一次性 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="供应商名称:" prop="supplier">
                <el-select class="width220" v-model="obj.queryParams.supplier" placeholder="请选择" clearable>
                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="供应商账单模板:" prop="billTemplate">
                <el-select class="width220" v-model="obj.queryParams.billTemplate" placeholder="请选择" clearable>
                    <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="报表年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择年月"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <!-- 表格 -->
        <el-table ref="singleTableRef" border :data="obj.tableData" highlight-current-row
            @current-change="handleCurrentChange">
            <el-table-column align="center" width="60">
                <template #default="scope">
                    <el-radio v-model="radio" :value="scope.row.id" @change="handleCurrentChange(scope.row)"></el-radio>
                </template>
            </el-table-column>
            <el-table-column label="雇员姓名" align="center" prop="name" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="订单编号" align="center" prop="orderNo" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="报增时间" align="center" prop="increaseTime" />
            <el-table-column label="报减时间" align="center" prop="decreaseTime" />
            <el-table-column label="实做状态" align="center" prop="implementStatus">
                <template #default="scope">
                    <el-tag :type="scope.row.implementStatus === '1' ? 'success' : 'info'">
                        {{implementStatusOptions.find(item => item.value === scope.row.implementStatus)?.label ||
                            '未知状态'}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="同步状态" align="center" prop="syncStatus">
                <template #default="scope">
                    <el-tag :type="scope.row.syncStatus === '1' ? 'success' : 'info'">
                        {{syncStatusOptions.find(item => item.value === scope.row.syncStatus)?.label || '未知状态'}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="变更类型" align="center" prop="changeType">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.changeType === '3' ? 'danger' : (scope.row.changeType === '2' ? 'warning' : 'success')">
                        {{changeTypeOptions.find(item => item.value === scope.row.changeType)?.label || '未知类型'}}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="参保城市" align="center" prop="city">
                <template #default="scope">
                    {{cityOptions.find(item => item.value === scope.row.city)?.label || '未知城市'}}
                </template>
            </el-table-column>
            <el-table-column label="所属供应商" align="center" prop="supplier">
                <template #default="scope">
                    {{supplierOptions.find(item => item.value === scope.row.supplier)?.label || '未知供应商'}}
                </template>
            </el-table-column>
            <el-table-column label="操作人" align="center" prop="operator" />
            <el-table-column label="创建人" align="center" prop="creator" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <div v-if="obj.rows?.id">
            <!-- 按钮 -->
            <el-row :gutter="10" class="mt20 mb8">
                <el-col :span="1.5">
                    <el-button type="primary" @click="handleAdd">添加</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="primary" @click="handleDelete">删除</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="primary" @click="handleSubmit">提交</el-button>
                </el-col>
            </el-row>
            <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.billData"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="ID" align="center" prop="id" />
                <el-table-column label="一级类别" align="center" prop="firstCategory">
                    <template #header>
                        <span class="corred">*</span>
                        <span>一级类别</span>
                    </template>
                    <template #default="scope">
                        <el-select style="width: 100%;" v-model="scope.row.firstCategory" placeholder="请选择" clearable>
                            <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="二级类别" align="center" prop="secondCategory">
                    <template #header>
                        <span class="corred">*</span>
                        <span>二级类别</span>
                    </template>
                    <template #default="scope">
                        <el-select style="width: 100%;" v-model="scope.row.secondCategory" placeholder="请选择" clearable>
                            <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="金额" align="center" prop="amount">
                    <template #header>
                        <span class="corred">*</span>
                        <span>金额</span>
                    </template>
                    <template #default="scope">
                        <el-input style="width: 100%;" v-model="scope.row.amount" placeholder="请输入" clearable />
                    </template>
                </el-table-column>
                <el-table-column label="金额（不含税）" align="center" prop="amountWithoutTax">
                    <template #header>
                        <span class="corred">*</span>
                        <span>金额（不含税）</span>
                    </template>
                </el-table-column>


                <el-table-column label="增值税率(%)" align="center" prop="taxRate">
                    <template #default="scope">
                        <el-input style="width: 100%;" v-model="scope.row.taxRate" placeholder="请输入" clearable />
                    </template>
                </el-table-column>
                <el-table-column label="增值税" align="center" prop="tax" />
                <el-table-column label="总人次" align="center" prop="totalPeople">
                    <template #header>
                        <span class="corred">*</span>
                        <span>总人次</span>
                    </template>
                    <template #default="scope">
                        <el-input style="width: 100%;" v-model="scope.row.totalPeople" placeholder="请输入" clearable />
                    </template>
                </el-table-column>
                <el-table-column label="一次性支持人员" align="center" prop="oneTimeSupportPeople">
                    <template #default="scope">
                        <el-select style="width: 100%;" v-model="scope.row.oneTimeSupportPeople" placeholder="请选择"
                            clearable>
                            <el-option v-for="item in billTemplateOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column label="备注" align="center" prop="remark">
                    <template #default="scope">
                        <el-input style="width: 100%;" v-model="scope.row.remark" placeholder="请输入" clearable />
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>


<script setup name="BillOnce">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const radio = ref(null);

// 供应商选项
const supplierOptions = [
    { value: '1', label: '供应商A' },
    { value: '2', label: '供应商B' },
    { value: '3', label: '供应商C' }
];

// 账单模板选项
const billTemplateOptions = [
    { value: '1', label: '模板1' },
    { value: '2', label: '模板2' },
    { value: '3', label: '模板3' }
];


// 实做状态选项
const implementStatusOptions = [
    { value: '1', label: '已实做' },
    { value: '0', label: '未实做' }
];

// 同步状态选项
const syncStatusOptions = [
    { value: '1', label: '已同步' },
    { value: '0', label: '未同步' }
];

// 变更类型选项
const changeTypeOptions = [
    { value: '1', label: '新增' },
    { value: '2', label: '变更' },
    { value: '3', label: '离职' }
];

// 参保城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' }
];

// 模拟员工表格数据
const mockEmployeeData = [
    {
        id: 1,
        name: '张三',
        idNumber: '110101199001011234',
        orderNo: 'ORD20230001',
        customerName: '客户A',
        increaseTime: '2023-01-15',
        decreaseTime: '',
        implementStatus: '1',
        syncStatus: '1',
        changeType: '1',
        city: '1',
        supplier: '1',
        operator: '李四',
        creator: '王五'
    },
    {
        id: 2,
        name: '李四',
        idNumber: '310101199002022345',
        orderNo: 'ORD20230002',
        customerName: '客户B',
        increaseTime: '2023-02-20',
        decreaseTime: '',
        implementStatus: '0',
        syncStatus: '0',
        changeType: '2',
        city: '2',
        supplier: '2',
        operator: '王五',
        creator: '张三'
    }
];

const obj = reactive({
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplier: null, // 供应商
        billTemplate: null, // 账单模板
        billMonth: null, // 报表年月
    }, // 查询表单
    total: mockEmployeeData.length, // 总条数
    tableData: mockEmployeeData, // 员工列表
    billData: [{

    }], // 账单列表
    ids: [], // 选中的id

    rows: {},
    indexArr: []
});

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockEmployeeData];

        if (obj.queryParams.supplier) {
            filteredData = filteredData.filter(item => item.supplier === obj.queryParams.supplier);
        }
        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 处理当前行变化 */
function handleCurrentChange(row) {
    if (row) {
        radio.value = row.id;
        obj.rows = row
    }
    console.log("已选产品:", row);
}


// 选中数据改变
function handleSelectionChange(selection) {
    obj.indexArr = selection.map(item => item.index);
}
// 添加账单
function handleAdd() {
    obj.billData.push({
        index: obj.billData.length
    })
}

// 删除账单
function handleDelete() {
    if (obj.indexArr.length === 0) {
        proxy.$modal.msgError('请选择要删除的记录');
        return;
    }
    console.log(obj.billData);
    console.log(obj.indexArr);

    proxy.$modal.confirm('确认要删除选中的记录吗？').then(() => {
        // 实际项目中应该调用API进行删除
        proxy.$modal.msgSuccess('删除成功');
        // 更新本地数据
        obj.billData = obj.billData.filter(item => !obj.indexArr.includes(item.index));
        // 清空选中项
        obj.indexArr = [];
    }).catch(() => { });
}

// 提交账单
function handleSubmit() {

}

getList();
</script>
<style lang="scss" scoped></style>