<!-- 订单费用报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="服务年月开始月:" prop="startMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.startMonth" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="服务年月截止月:" prop="endMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.endMonth" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idCard">
                <el-input class="width220" v-model="obj.queryParams.idCard" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="订单号:" prop="orderNumber">
                <el-input class="width220" v-model="obj.queryParams.orderNumber" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="所属城市:" prop="city">
                <el-input class="width220" v-model="obj.queryParams.city" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="分公司/供应商:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="是否单立户:" prop="single">
                <el-select class="width220" v-model="obj.queryParams.single" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同类型:" prop="contractType">
                <el-select class="width220" v-model="obj.queryParams.contractType" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="产品类型:" prop="productType">
                <el-select class="width220" v-model="obj.queryParams.productType" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="签约方抬头:" prop="signingParty">
                <el-input class="width220" v-model="obj.queryParams.signingParty" placeholder="请输入" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
            </el-row>
            <div style="color: red; font-size: 18px;">
                点击导出后请到下载中心查看文件
            </div>
        </el-form>
    </div>
</template>

<script setup name="OrderCostStatement">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    queryParams: {

    },//查询表单

})

/** 导出明细按钮操作 */
function handleExport() {
    if (!obj.queryParams.startMonth) {
        proxy.$modal.msgWarning("请选择服务年月开始月");
        return;
    }
    if (!obj.queryParams.endMonth) {
        proxy.$modal.msgWarning("请选择服务年月截止月");
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}
</script>
<style lang="scss" scoped></style>