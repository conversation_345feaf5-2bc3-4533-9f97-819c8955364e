<!-- 雇员实做账号维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-select class="width220" v-model="obj.queryParams.customer" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable>
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入姓名" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件类型:" prop="idType">
                <el-select class="width220" v-model="obj.queryParams.idType" placeholder="请选择" clearable>
                    <el-option v-for="item in idTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain @click="handleTop">置为有效</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件类型" align="center" prop="idType" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="客户姓名" align="center" prop="customerName" />
            <el-table-column label="福利包账号" align="center" prop="welfareAccount" />
            <el-table-column label="是否有效" align="center" prop="isValid">
                <template #default="scope">
                    <el-tag :type="scope.row.isValid === '1' ? 'success' : 'danger'">
                        {{ scope.row.isValid === '1' ? '有效' : '无效' }}
                    </el-tag>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 新增/修改 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="60%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" :rules="rules" inline label-width="auto">
                <el-form-item label="福利办理方" prop="welfareHandler">
                    <el-select class="width220" v-model="obj.dialogForm.welfareHandler" placeholder="请选择" clearable>
                        <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="大户/单立户" prop="accountType">
                    <el-select class="width220" v-model="obj.dialogForm.accountType" placeholder="请选择" clearable>
                        <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户" prop="customer">
                    <el-select class="width220" v-model="obj.dialogForm.customer" placeholder="请选择" clearable>
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="福利包名称" prop="welfarePackageName">
                    <el-select class="width220" v-model="obj.dialogForm.welfarePackageName" placeholder="请选择" clearable>
                        <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="姓名" prop="employeeName">
                    <el-input class="width220" v-model="obj.dialogForm.employeeName" placeholder="请输入姓名" clearable />
                </el-form-item>
                <el-form-item label="唯一号" prop="uniqueId">
                    <el-input class="width220" v-model="obj.dialogForm.uniqueId" placeholder="请输入唯一号" clearable />
                </el-form-item>
                <el-form-item label="证件类型" prop="idType">
                    <el-select class="width220" v-model="obj.dialogForm.idType" placeholder="请选择" clearable>
                        <el-option v-for="item in idTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="证件号码" prop="idNumber">
                    <el-input class="width220" v-model="obj.dialogForm.idNumber" placeholder="请输入证件号码" clearable />
                </el-form-item>
                <el-form-item label="账号" prop="account">
                    <el-input class="width220" v-model="obj.dialogForm.account" placeholder="请输入账号" clearable />
                </el-form-item>
                <el-form-item label="委托代发银行名称" prop="bankName">
                    <el-input class="width220" v-model="obj.dialogForm.bankName" placeholder="请输入银行名称" clearable />
                </el-form-item>
                <el-form-item label="委托代发银行账号" prop="bankAccount">
                    <el-input class="width220" v-model="obj.dialogForm.bankAccount" placeholder="请输入银行账号" clearable />
                </el-form-item>
                <el-form-item label="是否为有效" prop="isValid">
                    <el-select class="width220" v-model="obj.dialogForm.isValid" placeholder="请选择" clearable>
                        <el-option v-for="item in isValidOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancelForm">取消</el-button>
                <el-button type="primary" @click="submitForm">保存</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="EmployeeImplementationAccount_mainentance">
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 证件类型选项
const idTypeOptions = [
    { value: '1', label: '身份证' },
    { value: '2', label: '护照' },
    { value: '3', label: '军官证' }
];

// 是否有效选项
const isValidOptions = [
    { value: '1', label: '有效' },
    { value: '0', label: '无效' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        welfareHandler: '公司A',
        welfarePackageName: '标准福利包',
        employeeName: '张三',
        idType: '身份证',
        idNumber: '110101199001011234',
        customerName: '客户A',
        welfareAccount: 'ZS001',
        isValid: '1'
    },
    {
        id: 2,
        welfareHandler: '公司B',
        welfarePackageName: '高级福利包',
        employeeName: '李四',
        idType: '身份证',
        idNumber: '110101199001021234',
        customerName: '客户B',
        welfareAccount: 'LS002',
        isValid: '1'
    },
    {
        id: 3,
        welfareHandler: '公司C',
        welfarePackageName: '基础福利包',
        employeeName: '王五',
        idType: '护照',
        idNumber: '*********',
        customerName: '客户C',
        welfareAccount: 'WW003',
        isValid: '0'
    }
];

// 表单验证规则
const rules = {
    welfareHandler: [
        { required: true, message: '请选择福利办理方', trigger: 'change' }
    ],
    accountType: [
        { required: true, message: '请选择大户/单立户', trigger: 'change' }
    ],
    customer: [
        { required: true, message: '请选择客户', trigger: 'change' }
    ],
    welfarePackageName: [
        { required: true, message: '请选择福利包名称', trigger: 'change' }
    ],
    employeeName: [
        { required: true, message: '请输入姓名', trigger: 'blur' }
    ],
    uniqueId: [
        { required: true, message: '请输入唯一号', trigger: 'blur' }
    ],
    idType: [
        { required: true, message: '请选择证件类型', trigger: 'change' }
    ],
    idNumber: [
        { required: true, message: '请输入证件号码', trigger: 'blur' }
    ],
    account: [
        { required: true, message: '请输入账号', trigger: 'blur' }
    ],
    isValid: [
        { required: true, message: '请选择是否有效', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        employeeName: null, // 姓名
        idType: null, // 证件类型
        idNumber: null, // 证件号码
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        employeeName: '', // 姓名
        uniqueId: '', // 唯一号
        idType: null, // 证件类型
        idNumber: '', // 证件号码
        account: '', // 账号
        bankName: '', // 委托代发银行名称
        bankAccount: '', // 委托代发银行账号
        isValid: '1' // 是否有效
    }, // 表单
    dialogShow: false, // 显示对话框
    title: '', // 对话框标题
    ids: [] // 选中的id
})

/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.welfareHandler) {
            filteredData = filteredData.filter(item => item.welfareHandler.includes(obj.queryParams.welfareHandler));
        }

        if (obj.queryParams.accountType) {
            filteredData = filteredData.filter(item => item.accountType === obj.queryParams.accountType);
        }

        if (obj.queryParams.customer) {
            filteredData = filteredData.filter(item => item.customerName.includes(obj.queryParams.customer));
        }

        if (obj.queryParams.welfarePackageName) {
            filteredData = filteredData.filter(item => item.welfarePackageName.includes(obj.queryParams.welfarePackageName));
        }

        if (obj.queryParams.employeeName) {
            filteredData = filteredData.filter(item => item.employeeName.includes(obj.queryParams.employeeName));
        }

        if (obj.queryParams.idType) {
            filteredData = filteredData.filter(item => item.idType === obj.queryParams.idType);
        }

        if (obj.queryParams.idNumber) {
            filteredData = filteredData.filter(item => item.idNumber.includes(obj.queryParams.idNumber));
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

// 表单重置
function resetForm() {
    obj.dialogForm = {
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        customer: null, // 客户
        welfarePackageName: null, // 福利包名称
        employeeName: '', // 姓名
        uniqueId: '', // 唯一号
        idType: null, // 证件类型
        idNumber: '', // 证件号码
        account: '', // 账号
        bankName: '', // 委托代发银行名称
        bankAccount: '', // 委托代发银行账号
        isValid: '1' // 是否有效
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

// 置顶有效
function handleTop() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgError('请选择要置顶的记录');
        return;
    }

    proxy.$modal.confirm('是否确认置顶有效？').then(() => {
        // 实际项目中应该调用API进行置顶
        // 模拟置顶有效
        obj.tableData.forEach(item => {
            if (obj.ids.includes(item.id)) {
                item.isValid = '1';
            }
        });

        proxy.$modal.msgSuccess('置顶成功');
        obj.ids = [];
    }).catch(() => { });
}

// 新增
function handleAdd() {
    obj.dialogShow = true;
    obj.title = '添加员工账号';
    resetForm();
}

// 修改
function handleEdit() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgError('请选择一条记录进行修改');
        return;
    }

    const id = obj.ids[0];
    const row = obj.tableData.find(item => item.id === id);

    if (!row) {
        proxy.$modal.msgError('找不到该记录');
        return;
    }

    obj.dialogShow = true;
    obj.title = '修改员工账号';

    // 模拟获取详情数据
    resetForm();
    Object.assign(obj.dialogForm, {
        welfareHandler: row.welfareHandler,
        accountType: row.accountType,
        customer: row.customer,
        welfarePackageName: row.welfarePackageName,
        employeeName: row.employeeName,
        uniqueId: row.uniqueId || '',
        idType: row.idType,
        idNumber: row.idNumber,
        account: row.welfareAccount,
        bankName: row.bankName || '',
        bankAccount: row.bankAccount || '',
        isValid: row.isValid
    });
}

// 取消表单
function cancelForm() {
    obj.dialogShow = false;
    resetForm();
}

// 提交表单
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 导出
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}

.formHight {
    min-height: 300px;
}
</style>