<!-- 实做修改 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="福利办理方:" prop="welfareHandler">
                <el-select class="width220" v-model="obj.queryParams.welfareHandler" placeholder="请选择" clearable>
                    <el-option v-for="item in welfareHandlerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="大户/单立户:" prop="accountType">
                <el-select class="width220" v-model="obj.queryParams.accountType" placeholder="请选择" clearable>
                    <el-option v-for="item in accountTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利包名称:" prop="typeName">
                <el-select class="width220" v-model="obj.queryParams.typeName" placeholder="请选择" clearable>
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-select class="width220" v-model="obj.queryParams.customer" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="单立户名称:" prop="singleAccountName">
                <el-select class="width220" v-model="obj.queryParams.singleAccountName" placeholder="请选择" clearable>
                    <el-option v-for="item in singleAccountOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="订单编号:" prop="orderNo">
                <el-input class="width220" v-model="obj.queryParams.orderNo" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="雇员状态:" prop="employeeStatus">
                <el-select class="width220" v-model="obj.queryParams.employeeStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="入职日期>=:" prop="entryDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.entryDateFrom" placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="入职日期<=:" prop="entryDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.entryDateTo" placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="福利起始月>=:" prop="welfareStartMonthFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.welfareStartMonthFrom" type="month"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="福利起始月<=:" prop="welfareStartMonthTo">
                <el-date-picker class="width220" v-model="obj.queryParams.welfareStartMonthTo" type="month"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-form-item label="接单客服:" prop="orderReceiver">
                <el-input class="width220" v-model="obj.queryParams.orderReceiver" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="办理日期>=:" prop="handleDateFrom">
                <el-date-picker class="width220" v-model="obj.queryParams.handleDateFrom" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-form-item label="办理日期<=:" prop="handleDateTo">
                <el-date-picker class="width220" v-model="obj.queryParams.handleDateTo" type="date" placeholder="请选择"
                    clearable />
            </el-form-item>
            <el-row class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Search" @click="personalOrderBtn">查询个人订单</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="View" @click="viewBtn">查看</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="modifyBasicInfoBtn">修改基本信息</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="modifyCostInfoBtn">修改费用信息</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange" @row-dblclick="viewBtn">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="手机号码" align="center" prop="phoneNumber" />
            <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="订单编号" align="center" prop="orderNo" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户编号" align="center" prop="customerNo" />
            <el-table-column label="小合同" align="center" prop="smallContract" />
            <el-table-column label="入职时间" align="center" prop="entryDate" />
            <el-table-column label="办理人" align="center" prop="handler" />
            <el-table-column label="办理时间" align="center" prop="handleTime" />
            <el-table-column label="新增方式" align="center" prop="addMethod" />
            <el-table-column label="办理月" align="center" prop="handleMonth" />
            <el-table-column label="账号" align="center" prop="accountNo" />
            <el-table-column label="福利截止月" align="center" prop="welfareEndMonth" />
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.status === '在职' ? 'success' : (scope.row.status === '离职' ? 'danger' : 'warning')">
                        {{ scope.row.status }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="福利起始月" align="center" prop="welfareStartMonth" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 查看个人订单 -->
        <order-contract-reserve-fund type="ActualModification" v-model:dialogShow="obj.dialogShow" title="查看个人订单"
            :form="obj.dialogForm" :tableData="obj.tableData" :tableData_no="obj.tableData_no" :isDetail="true" />

        <!-- 停办 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="20%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" label-width="auto">
                <el-form-item label="福利截止月" prop="sales">
                    <el-select class="width220" v-model="obj.dialogForm.sales" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="停办备注" prop="sales">
                    <el-input type="textarea" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="obj.dialogShow = false">取消</el-button>
                <el-button type="primary" @click="save">停办</el-button>
            </template>
        </el-dialog>
        <!-- 查看 -->
        <management-information v-model:dialogShow="obj.dialogShow3" title="查看" :form="obj.dialogForm"
            :tableData="obj.tableData" :tableData2="obj.tableData2" :isDetail="obj.isDetail" />


        <!-- 修改基本信息 -->
        <el-dialog v-model="obj.dialogShow4" :title="obj.title" width="70%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" inline label-width="auto">
                <el-form-item label="福利办理方" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="单立户/大户" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="福利包名称" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="客户" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>

                <el-form-item label="客户编号" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="雇员姓名" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="证件号码" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="订单编号" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="雇员状态" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>

                <el-form-item label="入职时间" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="入职申请时间" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="实做申请时间" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="福利起始月" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="办理月份" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>

                <el-form-item label="新增方式" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="账号" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="退回备注" prop="sales">
                    <el-input class="width420" type="textarea" :rows='3' v-model="obj.dialogForm.sales"
                        placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="新办备注" prop="sales">
                    <el-input class="width420" type="textarea" :rows='3' v-model="obj.dialogForm.sales"
                        placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="修改备注" prop="sales">
                    <el-input class="width420" type="textarea" :rows='3' v-model="obj.dialogForm.sales"
                        placeholder="请输入" clearable />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="obj.dialogShow4 = false">取消</el-button>
                <el-button type="primary" @click="save">保存</el-button>
            </template>
        </el-dialog>
        <!-- 修改费用信息 -->
        <el-dialog v-model="obj.dialogShow5" :title="obj.title" width="70%">
            <el-form :model="obj.dialogForm" class="formHight" ref="formRef" inline label-width="auto">
                <el-form-item label="福利办理方" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="福利包名称" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="订单编号" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="客户" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="雇员姓名" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="证件号码" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="客户编号" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="入离职状态" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="接单客服" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="入职时间" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="入职申请时间" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="办理人" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="办理时间" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="状态" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="办停人" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="办停时间" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="新增方式" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="账号" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="福利截止月" prop="sales">
                    <el-input class="width220" v-model="obj.dialogForm.sales" placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="新办备注" prop="sales">
                    <el-input class="width420" type="textarea" :rows='3' v-model="obj.dialogForm.sales"
                        placeholder="请输入" clearable />
                </el-form-item>
                <el-form-item label="修改备注" prop="sales">
                    <el-input class="width420" type="textarea" :rows='3' v-model="obj.dialogForm.sales"
                        placeholder="请输入" clearable />
                </el-form-item>
                <border-box title="产品办理信息">
                    <el-table :data="obj.dialogForm.formTable" border>
                        <el-table-column label="社保/公积金产品" prop="sales" />
                        <el-table-column label="比例名称" prop="sales" />
                        <el-table-column label="福利起始月" prop="sales" />
                        <el-table-column label="福利截止月" prop="sales" />
                        <el-table-column label="办理月份" prop="sales" />
                        <el-table-column label="企业基数" prop="sales" />
                        <el-table-column label="个人基数" prop="sales" />
                        <el-table-column label="企业金额" prop="sales" />
                        <el-table-column label="个人金额" prop="sales" />
                        <el-table-column label="企业比例" prop="sales" />
                        <el-table-column label="个人比例" prop="sales" />
                        <el-table-column label="企业附加" prop="sales" />
                        <el-table-column label="个人附加" prop="sales" />
                        <el-table-column label="备注" prop="sales" />
                    </el-table>
                </border-box>
                <border-box title="补缴信息">
                    <el-row class="mb8">
                        <el-col :span="24">
                            <el-button type="primary" plain @click="addBtn">新增</el-button>
                            <el-button type="danger" plain @click="deleteBtn">删除</el-button>
                        </el-col>
                    </el-row>
                    <el-table :data="obj.dialogForm.formTable" border>
                        <el-table-column label="社保/公积金产品" prop="sales" />
                        <el-table-column label="比例名称" prop="sales" />
                        <el-table-column label="补缴起始月" prop="sales" />
                        <el-table-column label="补缴截止月" prop="sales" />
                        <el-table-column label="补缴发生月" prop="sales" />
                        <el-table-column label="企业基数" prop="sales" />
                        <el-table-column label="个人基数" prop="sales" />
                        <el-table-column label="企业金额" prop="sales" />
                        <el-table-column label="个人金额" prop="sales" />
                        <el-table-column label="企业滞纳金" prop="sales" />
                        <el-table-column label="个人滞纳金" prop="sales" />
                        <el-table-column label="企业比例" prop="sales" />
                        <el-table-column label="个人比例" prop="sales" />
                        <el-table-column label="企业附加" prop="sales" />
                        <el-table-column label="个人附加" prop="sales" />
                        <el-table-column label="备注" prop="sales" />
                    </el-table>
                </border-box>
            </el-form>
            <template #footer>
                <el-button type="primary" @click="save">修改</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ImplementModification">
import { listScale } from "@/api/reonApi/scale";
import OrderContractReserveFund from '@/views/reonManage/components/dialog/orderContractReserveFund.vue'
import ManagementInformation from '@/views/reonManage/components/dialog/managementInformation.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 大户/单立户选项
const accountTypeOptions = [
    { value: '1', label: '大户' },
    { value: '2', label: '单立户' }
];

// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 单立户选项
const singleAccountOptions = [
    { value: '1', label: '单立户A' },
    { value: '2', label: '单立户B' },
    { value: '3', label: '单立户C' }
];

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 雇员状态选项
const employeeStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '待入职' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        employeeName: '张三',
        idNumber: '110101199001011234',
        phoneNumber: '***********',
        welfareHandler: '公司A',
        welfarePackageName: '标准福利包',
        orderNo: 'ORD20230001',
        customerName: '客户A',
        customerNo: 'CUS001',
        smallContract: '是',
        entryDate: '2023-01-01',
        handler: '李四',
        handleTime: '2023-01-15 10:30:00',
        addMethod: '手动新增',
        handleMonth: '2023-01',
        accountNo: 'ACC001',
        welfareEndMonth: '2023-12',
        status: '在职',
        welfareStartMonth: '2023-01'
    },
    {
        id: 2,
        employeeName: '李四',
        idNumber: '110101199001021234',
        phoneNumber: '***********',
        welfareHandler: '公司B',
        welfarePackageName: '高级福利包',
        orderNo: 'ORD20230002',
        customerName: '客户B',
        customerNo: 'CUS002',
        smallContract: '否',
        entryDate: '2023-02-01',
        handler: '王五',
        handleTime: '2023-02-15 10:30:00',
        addMethod: '批量导入',
        handleMonth: '2023-02',
        accountNo: 'ACC002',
        welfareEndMonth: '2023-12',
        status: '在职',
        welfareStartMonth: '2023-02'
    }
];

// 模拟产品办理信息数据
const mockProductData = [
    {
        id: 1,
        productName: '养老保险',
        ratioName: '标准比例',
        welfareStartMonth: '2023-01',
        welfareEndMonth: '2023-12',
        handleMonth: '2023-01',
        companyBase: 10000,
        personalBase: 8000,
        companyAmount: 2000,
        personalAmount: 800,
        companyRatio: '20%',
        personalRatio: '10%',
        companyExtra: 0,
        personalExtra: 0,
        remark: ''
    },
    {
        id: 2,
        productName: '医疗保险',
        ratioName: '标准比例',
        welfareStartMonth: '2023-01',
        welfareEndMonth: '2023-12',
        handleMonth: '2023-01',
        companyBase: 10000,
        personalBase: 8000,
        companyAmount: 800,
        personalAmount: 200,
        companyRatio: '8%',
        personalRatio: '2.5%',
        companyExtra: 0,
        personalExtra: 0,
        remark: ''
    }
];

// 模拟补缴信息数据
const mockBackPayData = [
    {
        id: 1,
        productName: '养老保险',
        ratioName: '标准比例',
        backPayStartMonth: '2022-12',
        backPayEndMonth: '2022-12',
        backPayMonth: '2023-01',
        companyBase: 10000,
        personalBase: 8000,
        companyAmount: 2000,
        personalAmount: 800,
        companyLateFee: 100,
        personalLateFee: 40,
        companyRatio: '20%',
        personalRatio: '10%',
        companyExtra: 0,
        personalExtra: 0,
        remark: '补缴上月社保'
    }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        welfareHandler: null, // 福利办理方
        accountType: null, // 大户/单立户
        welfarePackageName: null, // 福利包名称
        customer: null, // 客户
        singleAccountName: null, // 单立户名称
        orderNo: null, // 订单编号
        employeeName: null, // 雇员姓名
        idNumber: null, // 证件号码
        employeeStatus: null, // 雇员状态
        entryDateStart: null, // 入职日期>=
        entryDateEnd: null, // 入职日期<=
        welfareStartMonthStart: null, // 福利起始月>=
        welfareStartMonthEnd: null, // 福利起始月<=
        orderReceiver: null, // 接单客服
        handleDateStart: null, // 办理日期>=
        handleDateEnd: null, // 办理日期<=
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    dialogForm: {
        formTable: [],
        productList: mockProductData,
        backPayList: mockBackPayData
    }, // 表单
    dialogShow: false, // 显示对话框
    dialogShow2: false, // 停办对话框
    dialogShow3: false, // 查看对话框
    dialogShow4: false, // 修改基本信息对话框
    dialogShow5: false, // 修改费用信息对话框
    title: '', // 对话框标题
    ids: [], // 选中的id
    isDetail: false, // 是否查看
    tableData_no: [], // 无需办理表格数据
    tableData2: [] // 管理信息表格数据
})

/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        // obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function resetForm() {
    obj.dialogForm = {
        formTable: [],
        productList: mockProductData,
        backPayList: mockBackPayData
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

// 查询个人订单
function personalOrderBtn() {
    obj.dialogShow = true;
    resetForm();
}
/** 关闭弹窗 */
function handleClose() {
    obj.dialogShow = false;
    resetForm();
    obj.isDetail = false;
}

// 查看
function viewBtn(row) {
    console.log(row)
    obj.dialogShow3 = true;
    obj.isDetail = true;
    resetForm();
}
function handleClose2() {
    obj.dialogShow3 = false;
    resetForm();
    obj.isDetail = false;
}

// 修改基本信息
function modifyBasicInfoBtn() {
    obj.dialogShow4 = true;
    obj.title = '修改基本信息';
    resetForm();
}

// 修改费用信息
function modifyCostInfoBtn() {
    obj.dialogShow5 = true;
    obj.title = '修改费用信息';
    resetForm();
}

// 保存表单
function save() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            // 实际项目中应该调用API进行提交
            proxy.$modal.msgSuccess('保存成功');
            if (obj.dialogShow2) obj.dialogShow2 = false;
            if (obj.dialogShow4) obj.dialogShow4 = false;
            if (obj.dialogShow5) obj.dialogShow5 = false;
            resetForm();
            getList(); // 刷新列表
        }
    });
}



getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>