<!-- 福利包调整 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="调整任务号:" prop="taskNo">
                <el-input class="width220" v-model="obj.queryParams.taskNo" placeholder="请输入调整任务号" clearable />
            </el-form-item>
            <el-form-item label="调整名称:" prop="adjustName">
                <el-input class="width220" v-model="obj.queryParams.adjustName" placeholder="请输入调整名称" clearable />
            </el-form-item>
            <el-form-item label="福利包名称:" prop="welfarePackageName">
                <el-select class="width220" v-model="obj.queryParams.welfarePackageName" placeholder="请选择" clearable>
                    <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="调整起始年月:" prop="adjustStartMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.adjustStartMonth" type="month"
                    placeholder="请选择" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" icon="Upload" @click="handleAdd">新增调整</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="调整任务号" align="center" prop="taskNo" />
            <el-table-column label="调整名称" align="center" prop="adjustName" />
            <el-table-column label="福利包编号" align="center" prop="welfarePackageNo" />
            <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
            <el-table-column label="调整起始年月" align="center" prop="adjustStartMonth" />
            <el-table-column label="详情" align="center" prop="details" />
            <el-table-column label="创建人" align="center" prop="creator" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />


        <!-- 新增调整 -->
        <el-dialog v-model="obj.dialogShow" :title="obj.title" width="30%" append-to-body>
            <el-form :model="obj.dialogForm" ref="dialogRef" :rules="rules" inline label-width="auto">
                <el-form-item label="调整名称:" prop="adjustName">
                    <el-input class="width220" v-model="obj.dialogForm.adjustName" placeholder="请输入调整名称" clearable />
                </el-form-item>
                <el-form-item label="福利包名称:" prop="welfarePackageName">
                    <el-select class="width220" v-model="obj.dialogForm.welfarePackageName" placeholder="请选择" clearable>
                        <el-option v-for="item in welfarePackageOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="调整起始月:" prop="adjustStartMonth">
                    <el-date-picker class="width220" v-model="obj.dialogForm.adjustStartMonth" type="month"
                        placeholder="请选择" clearable />
                </el-form-item>
            </el-form>
            <el-table :data="obj.employeeData" border @selection-change="handleEmployeeSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="雇员姓名" align="center" prop="employeeName" />
                <el-table-column label="证件号码" align="center" prop="idNumber" />
                <el-table-column label="手机号码" align="center" prop="phoneNumber" />
                <el-table-column label="订单编号" align="center" prop="orderNo" />
                <el-table-column label="福利办理方" align="center" prop="welfareHandler" />
                <el-table-column label="福利包名称" align="center" prop="welfarePackageName" />
                <el-table-column label="福利包编号" align="center" prop="welfarePackageNo" />
                <el-table-column label="客户名称" align="center" prop="customerName" />
                <el-table-column label="小合同" align="center" prop="smallContract" />
                <el-table-column label="状态" align="center" prop="status">
                    <template #default="scope">
                        <el-tag
                            :type="scope.row.status === '在职' ? 'success' : (scope.row.status === '离职' ? 'danger' : 'warning')">
                            {{ scope.row.status }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="办理时间" align="center" prop="handleTime" />
                <el-table-column label="办理人" align="center" prop="handler" />
                <el-table-column label="办理月" align="center" prop="handleMonth" />
                <el-table-column label="办停人" align="center" prop="stopHandler" />
                <el-table-column label="办停时间" align="center" prop="stopTime" />
                <el-table-column label="福利起始月" align="center" prop="welfareStartMonth" />
                <el-table-column label="福利截止月" align="center" prop="welfareEndMonth" />
                <el-table-column label="办理过程备注" align="center" prop="remark" />
            </el-table>
            <template #footer>
                <el-button @click="cancelAdjustment">取消</el-button>
                <el-button type="primary" @click="saveAdjustment">保存</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup>
import { listScale } from "@/api/reonApi/scale";

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 福利包选项
const welfarePackageOptions = [
    { value: '1', label: '标准福利包' },
    { value: '2', label: '高级福利包' },
    { value: '3', label: '基础福利包' }
];

// 福利办理方选项
const welfareHandlerOptions = [
    { value: '1', label: '公司A' },
    { value: '2', label: '公司B' },
    { value: '3', label: '公司C' }
];

// 雇员状态选项
const employeeStatusOptions = [
    { value: '1', label: '在职' },
    { value: '2', label: '离职' },
    { value: '3', label: '待入职' }
];

// 表单验证规则
const rules = {
    adjustName: [
        { required: true, message: '请输入调整名称', trigger: 'blur' }
    ],
    welfarePackageName: [
        { required: true, message: '请选择福利包名称', trigger: 'change' }
    ],
    adjustStartMonth: [
        { required: true, message: '请选择调整起始月', trigger: 'change' }
    ]
};

// 模拟调整表格数据
const mockAdjustmentData = [
    {
        id: 1,
        taskNo: 'ADJ20230001',
        adjustName: '社保基数调整',
        welfarePackageNo: 'WP001',
        welfarePackageName: '标准福利包',
        adjustStartMonth: '2023-01',
        details: '社保基数从8000调整到10000',
        creator: '张三',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 2,
        taskNo: 'ADJ20230002',
        adjustName: '公积金比例调整',
        welfarePackageNo: 'WP002',
        welfarePackageName: '高级福利包',
        adjustStartMonth: '2023-02',
        details: '公积金比例从8%调整到12%',
        creator: '李四',
        createTime: '2023-02-15 09:15:00'
    },
    {
        id: 3,
        taskNo: 'ADJ20230003',
        adjustName: '医保比例调整',
        welfarePackageNo: 'WP003',
        welfarePackageName: '基础福利包',
        adjustStartMonth: '2023-03',
        details: '医保比例从2%调整到3%',
        creator: '王五',
        createTime: '2023-03-15 11:45:00'
    }
];

// 模拟雇员表格数据
const mockEmployeeData = [
    {
        id: 1,
        employeeName: '张三',
        idNumber: '110101199001011234',
        phoneNumber: '13800138000',
        orderNo: 'ORD20230001',
        welfareHandler: '公司A',
        welfarePackageName: '标准福利包',
        welfarePackageNo: 'WP001',
        customerName: '客户A',
        smallContract: '是',
        status: '在职',
        handleTime: '2023-01-01 10:30:00',
        handler: '李四',
        handleMonth: '2023-01',
        stopHandler: '',
        stopTime: '',
        welfareStartMonth: '2023-01',
        welfareEndMonth: '2023-12',
        remark: ''
    },
    {
        id: 2,
        employeeName: '李四',
        idNumber: '110101199001021234',
        phoneNumber: '13900139000',
        orderNo: 'ORD20230002',
        welfareHandler: '公司B',
        welfarePackageName: '高级福利包',
        welfarePackageNo: 'WP002',
        customerName: '客户B',
        smallContract: '否',
        status: '在职',
        handleTime: '2023-02-01 09:15:00',
        handler: '王五',
        handleMonth: '2023-02',
        stopHandler: '',
        stopTime: '',
        welfareStartMonth: '2023-02',
        welfareEndMonth: '2023-12',
        remark: ''
    }
];

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskNo: null, // 调整任务号
        adjustName: null, // 调整名称
        welfarePackageName: null, // 福利包名称
        adjustStartMonth: null, // 调整起始年月
    }, // 查询表单
    total: mockAdjustmentData.length, // 总条数
    tableData: mockAdjustmentData, // 列表
    dialogForm: {
        adjustName: '', // 调整名称
        welfarePackageName: '', // 福利包名称
        adjustStartMonth: '', // 调整起始月
    }, // 调整表单
    employeeData: mockEmployeeData, // 雇员数据
    dialogShow: false, // 对话框显示
    ids: [], // 选中的id
    employeeIds: [], // 选中的雇员id
    title: "新增调整", // 标题


})


/** 列表 */
function getList() {
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 新增调整
function handleAdd() {
    obj.dialogShow = true;
    obj.title = "新增调整";
    resetForm();
}

// 取消调整
function cancelAdjustment() {
    obj.dialogShow = false;
    resetForm();
}

// 保存调整
function saveAdjustment() {
    proxy.$refs["dialogRef"].validate(valid => {
        if (valid) {
            if (obj.employeeIds.length === 0) {
                proxy.$modal.msgError('请选择要调整的雇员');
                return;
            }

            // 实际项目中应该调用API进行保存
            proxy.$modal.msgSuccess('保存成功');
            obj.dialogShow = false;
            resetForm();
            getList(); // 刷新列表
        }
    });
}

// 重置表单
function resetForm() {
    obj.dialogForm = {
        adjustName: '', // 调整名称
        welfarePackageName: '', // 福利包名称
        adjustStartMonth: '', // 调整起始月
    };
    obj.employeeIds = [];
    proxy.resetForm("dialogRef");
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 选中雇员数据改变
function handleEmployeeSelectionChange(selection) {
    obj.employeeIds = selection.map(item => item.id);
}

getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>