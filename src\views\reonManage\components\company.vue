<!-- 公司 -->
<template>
    <el-dialog v-model="dialogShow" width="35%" append-to-body draggable @close="close">
        <div class="mt20 mb20 flex-between">
            <div>
                <el-input class="width320" v-model="obj.queryParams.companyName" placeholder="分公司名称" clearable>
                    <template #append>
                        <el-button @click="handleCustomerQuery" icon="Search" />
                    </template>
                </el-input>
            </div>
            <div>
                <el-button @click="clear">清空</el-button>
                <el-button type="primary" @click="select">选择</el-button>
            </div>
        </div>
        <el-table :data="obj.tableData" style="width: 100%" @current-change="handleCurrentChange" border>
            <el-table-column width="55" align="center">
                <template #default="scope">
                    <el-radio v-model="radio" :value="scope.row.id" @change="handleCurrentChange(scope.row)" />
                </template>
            </el-table-column>
            <el-table-column label="序号" align="center" width="80" prop="id" />
            <el-table-column label="分公司名称" align="center" prop="companyName" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </el-dialog>
</template>

<script setup>
import { listScale } from "@/api/reonApi/scale";

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
})

// 使用本地变量来跟踪对话框状态
const dialogShow = ref(props.show);

// 监听props.show的变化，更新本地状态
watch(() => props.show, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:show', newVal);
});

const obj = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
    },//查询表单
    tableData: [
        {
            id: 1,
            companyName: '分公司1',
        },
        {
            id: 2,
            companyName: '分公司2',
        },
        {
            id: 3,
            companyName: '分公司3',
        }
    ],
    total: 3,
    currentRow: null,
})

/** 查询 */
function handleCustomerQuery() {
    obj.queryParams.pageNum = 1;
    obj.queryParams.pageSize = 10;
    getList();
}

const radio = ref(null);
/** 选择 */
function handleCurrentChange(row) {
    radio.value = row.id;
    obj.currentRow = row;
}


/** 清空 */
function clear() {
    obj.queryParams = {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
    };
    obj.currentRow = null;
    radio.value = null;
    emit('select', obj.currentRow);
    dialogShow.value = false;
}
const emit = defineEmits(['select', 'close', 'update:show']);
/** 选择 */
function select() {
    emit('select', obj.currentRow);
    dialogShow.value = false;
}
function close() {
    emit('close');
    dialogShow.value = false;
}
function getList() {
    listScale(obj.queryParams).then(res => {
        // obj.tableData = res.rows;
        // obj.total = res.total;
    });
}
getList();
</script>
<style lang="scss" scoped></style>