<!-- 实做办理反馈导入 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="导入日期从:" prop="importDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateStart" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="导入日期到:" prop="importDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.importDateEnd" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="导入编号:" prop="importNo">
                <el-input class="width220" v-model="obj.queryParams.importNo" placeholder="请输入导入编号" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" icon="Upload" @click="handleImport">导入</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="Download" @click="handleDownload">下载模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" icon="Upload" @click="handleImportFail">导入失败截图</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-row class="mb8 fw600" style="color: #ff2800;">
            存在实做办理反馈，申报结果为失败的数据，具体信息请到 实做办理反馈查询 页面查看
        </el-row>
        <el-row class="mb8 fw600" style="color: #ff2800;">
            导入失败截图时,请将订单号+产品名称代号,例如(YD-20240508000002-1)作为截图的名字,并使用压缩包上传!
        </el-row>
        <el-row class="mb8 fw600" style="color: #0078d7;">
            产品名称代号:1:养老 2:医疗 3:失业 4:工伤 6:大病 10:公积金
        </el-row>
        <!-- 表格 -->
        <ExportTable :tableData="obj.tableData" :loading="obj.loading" :total="obj.total"
            :page="obj.queryParams.pageNum" :limit="obj.queryParams.pageSize"
            :menuName="'practiceHandlingFeedback_import'" @row-dblclick="handleDetail"
            @handlePagination="handlePagination" />


        <!-- 导入 -->
        <UploadFile v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm"
            :type="'practiceHandlingFeedback_import'" :rules="importRules" />
        <!-- 导入失败截图 -->
        <el-dialog v-model="obj.dialogShow2" :title="obj.title" width="30%" append-to-body>
            <FileUpload v-model="obj.dialogForm.file" />
            <template #footer>
                <el-button @click="obj.dialogShow2 = false">取消</el-button>
                <el-button type="primary" @click="submitFailureScreenshot">上传</el-button>
            </template>
        </el-dialog>

        <!-- 详情对话框 -->
        <HistoricalInformation :dialogShow="obj.dialogShow3" :title="obj.title" :tableData="obj.importDetailData"
            @close="obj.dialogShow3 = false" />
    </div>
</template>


<script setup name="PracticeHandlingFeedback_import">
import { listScale } from "@/api/reonApi/scale";
import HistoricalInformation from '../components/dialog/historicalInformation.vue';
import ExportTable from '@/views/reonManage/components/table/exportTable.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 处理状态选项
const processStatusOptions = [
    { value: '0', label: '待处理' },
    { value: '1', label: '处理中' },
    { value: '2', label: '处理完成' },
    { value: '3', label: '处理失败' }
];

// 模拟表格数据
const mockTableData = [
    {
        id: 1,
        importNo: 'IMP20230001',
        creator: '张三',
        createTime: '2023-01-15 10:30:00',
        successCount: 120,
        failCount: 0,
        warningCount: 5,
        importFile: '实做办理反馈_202301.xlsx',
        processStatus: '2'
    },
    {
        id: 2,
        importNo: 'IMP20230002',
        creator: '李四',
        createTime: '2023-02-15 09:15:00',
        successCount: 85,
        failCount: 3,
        warningCount: 2,
        importFile: '实做办理反馈_202302.xlsx',
        processStatus: '2'
    },
    {
        id: 3,
        importNo: 'IMP20230003',
        creator: '王五',
        createTime: '2023-03-15 11:45:00',
        successCount: 0,
        failCount: 0,
        warningCount: 0,
        importFile: '实做办理反馈_202303.xlsx',
        processStatus: '1'
    }
];

// 模拟导入明细数据
const mockImportDetailData = [
    {
        id: 1,
        importNo: 'IMP20230001',
        rowNo: 1,
        errorDesc: '',
        warningDesc: '办理时间过长',
        importResult: '成功',
        importInfo: '导入成功，有警告',
        creator: '张三',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 2,
        importNo: 'IMP20230001',
        rowNo: 2,
        errorDesc: '证件号码格式错误',
        warningDesc: '',
        importResult: '失败',
        importInfo: '证件号码格式错误',
        creator: '张三',
        createTime: '2023-01-15 10:30:00'
    },
    {
        id: 3,
        importNo: 'IMP20230002',
        rowNo: 1,
        errorDesc: '',
        warningDesc: '办理时间过短',
        importResult: '成功',
        importInfo: '导入成功，有警告',
        creator: '李四',
        createTime: '2023-02-15 09:15:00'
    }
];

// 导入表单验证规则
const importRules = {
    remark: [
        { required: true, message: '请输入备注', trigger: 'blur' }
    ],
    file: [
        { required: true, message: '请选择要上传的文件', trigger: 'change' }
    ]
};

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        importDateStart: null, // 导入日期从
        importDateEnd: null, // 导入日期到
        importNo: null, // 导入编号
    }, // 查询表单
    total: mockTableData.length, // 总条数
    tableData: mockTableData, // 列表
    importDetailData: mockImportDetailData, // 导入明细数据
    dialogForm: {
        remark: '', // 备注
        file: null // 文件
    }, // 导入表单
    dialogShow: false, // 导入弹窗
    dialogShow2: false, // 导入失败截图弹窗
    dialogShow3: false, // 详情弹窗
    ids: [], // 选中的id
    title: "", // 标题
})


/** 列表 */
function getList() {
    // 实际项目中应该调用API获取数据
    obj.loading = true;
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });

    // 模拟数据处理
    obj.loading = true;
    setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredData = [...mockTableData];

        if (obj.queryParams.importDateStart) {
            const startDate = new Date(obj.queryParams.importDateStart).getTime();
            filteredData = filteredData.filter(item => new Date(item.createTime).getTime() >= startDate);
        }

        if (obj.queryParams.importDateEnd) {
            const endDate = new Date(obj.queryParams.importDateEnd).getTime();
            filteredData = filteredData.filter(item => new Date(item.createTime).getTime() <= endDate);
        }

        if (obj.queryParams.importNo) {
            filteredData = filteredData.filter(item => item.importNo.includes(obj.queryParams.importNo));
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 200);
}

// 选中数据改变
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

// 导入
function handleImport() {
    obj.dialogShow = true;
    obj.title = "上传导入数据";
    resetForm();
}

// 下载模版
function handleDownload() {
    // 实际项目中应该调用API进行下载模板
    proxy.$modal.msgSuccess('模板下载成功');

    // 模拟下载文件
    const fileName = '实做办理反馈导入模板.xlsx';
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = '#';
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 导入失败截图
function handleImportFail() {
    obj.dialogShow2 = true;
    obj.title = "上传导入失败截图";
    resetForm();
}

// 提交失败截图
function submitFailureScreenshot() {
    if (!obj.dialogForm.file) {
        proxy.$modal.msgError('请选择要上传的文件');
        return;
    }

    // 实际项目中应该调用API进行文件上传
    proxy.$modal.msgSuccess('失败截图上传成功');
    obj.dialogShow2 = false;
    resetForm();
}

// 订单详情
function handleDetail(row) {
    obj.dialogShow3 = true;
    obj.title = "导入详情";

    // 实际项目中应该调用API获取详情数据
    // 这里使用模拟数据过滤
    obj.importDetailData = mockImportDetailData.filter(item => item.importNo === row.importNo);
}

// 关闭导入对话框
function handleImportClose() {
    // 实际项目中应该在导入组件内部处理关闭逻辑
    // 这里只需要刷新列表
    obj.dialogShow = false;
    getList(); // 刷新列表
}

// 重置表单
function resetForm() {
    obj.dialogForm = {
        remark: '', // 备注
        file: null // 文件
    };
}

// 下载文件
function handleDownloadFile(row) {
    // 实际项目中应该调用API进行文件下载
    proxy.$modal.msgSuccess('文件下载成功');
}

getList();
</script>
<style lang="scss" scoped></style>