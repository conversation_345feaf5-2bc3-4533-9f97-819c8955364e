<!-- 财务应收报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="财务应收年月:" prop="financialReceivableYearMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.financialReceivableYearMonth" type="month"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="应收金额>=:" prop="financialReceivableAmountStart">
                <el-input class="width220" v-model="obj.queryParams.financialReceivableAmountStart" placeholder="请输入"
                    clearable />
            </el-form-item>
            <el-form-item label="应收金额<=:" prop="financialReceivableAmount">
                <el-input class="width220" v-model="obj.queryParams.financialReceivableAmountEnd" placeholder="请输入"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-button type="primary" icon="Download" @click="handleExport">导出明细</el-button>
                <el-button icon="Refresh" @click="handleReset">重置</el-button>
            </el-row>
        </el-form>
    </div>
</template>

<script setup name="FinancialReceivableStatement">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const obj = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        province: null,
        typeName: null,
        typeCode: null,
    },//查询表单

})

/** 重置按钮操作 */
function handleReset() {
    obj.queryParams = {};
}

/** 导出明细按钮操作 */
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

</script>
<style lang="scss" scoped></style>