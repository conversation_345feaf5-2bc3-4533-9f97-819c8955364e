<template>
    <div>
        <el-dialog v-model="dialogShow" title="分配历史日志" width="60%" @close="handleClose">
            <el-table :data="tableData || props.tableData" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="序号" align="center" prop="id" width="60" />
                <el-table-column label="客户名称" align="center" prop="customerName" min-width="150" />
                <el-table-column label="合同名称" align="center" prop="contractName" min-width="150" />
                <el-table-column label="小合同名称" align="center" prop="subContractName" width="120" />
                <el-table-column label="客服类型" align="center" width="100">
                    <template #default="scope">
                        {{ scope.row.serviceType }}
                    </template>
                </el-table-column>
                <el-table-column label="客服" align="center" width="100">
                    <template #default="scope">
                        {{ scope.row.serviceName }}
                    </template>
                </el-table-column>
                <el-table-column label="生效日期" align="center" prop="effectiveDate" width="100" />
                <el-table-column label="分公司" align="center" width="120">
                    <template #default="scope">
                        {{ scope.row.branchCompany }}
                    </template>
                </el-table-column>
                <el-table-column v-if="props.menuName !== 'subcontract_small' && props.menuName !== 'subcontract_big'"
                    label="操作时间" align="center" prop="operationTime" width="150" />
                <el-table-column v-if="props.menuName !== 'subcontract_small' && props.menuName !== 'subcontract_big'"
                    label="操作人" align="center" prop="operator" width="100" />
            </el-table>
        </el-dialog>
    </div>
</template>
<script setup>

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const tableData = ref([
    {
        id: 1,
        customerName: '北京科技有限公司',
        contractName: '商业医疗险服务合同',
        subContractName: '员工福利险',
        serviceType: '1',
        serviceName: '1',
        effectiveDate: '2023-05-01',
        branchCompany: '1',
        operationTime: '2023-05-01 10:00:00',
        operator: '管理员'
    },
    {
        id: 2,
        customerName: '上海贸易有限公司',
        contractName: '团体寿险服务合同',
        subContractName: '团体寿险A款',
        serviceType: '1',
        serviceName: '2',
        effectiveDate: '2023-05-02',
        branchCompany: '2',
        operationTime: '2023-05-02 11:30:00',
        operator: '管理员'
    },
],);

const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    },
    tableData: {
        type: Array,
        default: () => []
    },
    menuName: {
        type: String,
        default: ''
    }
});
// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});
const emit = defineEmits(['close', 'update:dialogShow']);
const handleClose = () => {
    dialogShow.value = false;
    emit('close');
};

</script>

<style scoped></style>
