<!-- 退票录入信息查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="出款公司名称:" prop="companyName">
                <el-input class="width220" v-model="obj.queryParams.companyName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申请人:" prop="applicant">
                <el-input class="width220" v-model="obj.queryParams.applicant" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="费用所属月份:" prop="expenseMonth">
                <el-input class="width220" v-model="obj.queryParams.expenseMonth" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工资支付日期:" prop="paymentDate">
                <el-input class="width220" v-model="obj.queryParams.paymentDate" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名:" prop="personName">
                <el-input class="width220" v-model="obj.queryParams.personName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目客服:" prop="projectService">
                <el-input class="width220" v-model="obj.queryParams.projectService" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="薪资客服:" prop="salaryService">
                <el-input class="width220" v-model="obj.queryParams.salaryService" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建人:" prop="creator">
                <el-input class="width220" v-model="obj.queryParams.creator" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="发放状态:" prop="paymentStatus">
                <el-select class="width220" v-model="obj.queryParams.paymentStatus" placeholder="请选择" clearable>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="出款公司名称" align="center" sortable prop="companyName" />
            <el-table-column label="客户名称" align="center" sortable prop="customerName" />
            <el-table-column label="费用所属月份" align="center" sortable prop="expenseMonth" />
            <el-table-column label="工资支付日期" align="center" sortable prop="paymentDate" />
            <el-table-column label="姓名" align="center" sortable prop="personName" />
            <el-table-column label="证件号" align="center" sortable prop="idNumber" />
            <el-table-column label="银行卡号" align="center" sortable prop="bankCardNumber" />
            <el-table-column label="实付工资款" align="center" sortable prop="actualSalary" />
            <el-table-column label="申请人" align="center" sortable prop="applicant" />
            <el-table-column label="录入" align="center" sortable prop="entryStatus" />
            <el-table-column label="支付ID" align="center" sortable prop="paymentId" />
            <el-table-column label="薪资发放状态" align="center" sortable prop="paymentStatus" />
            <el-table-column label="项目客服" align="center" sortable prop="projectService" />
            <el-table-column label="薪资客服" align="center" sortable prop="salaryService" />
            <el-table-column label="创建人" align="center" sortable prop="creator" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="800px" append-to-body>
            <el-form ref="formRef" :model="obj.dialogForm" :rules="rules" inline label-width="auto">
                <el-form-item label="出款公司名称" prop="companyName">
                    <el-input v-model="obj.dialogForm.companyName" placeholder="请输入出款公司名称" />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-select v-model="obj.dialogForm.customerName" placeholder="请选择客户名称">
                        <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="费用所属月份" prop="expenseMonth">
                    <el-input v-model="obj.dialogForm.expenseMonth" placeholder="请输入费用所属月份" />
                </el-form-item>
                <el-form-item label="工资支付日期" prop="paymentDate">
                    <el-input v-model="obj.dialogForm.paymentDate" placeholder="请输入工资支付日期" />
                </el-form-item>
                <el-form-item label="姓名" prop="personName">
                    <el-input v-model="obj.dialogForm.personName" placeholder="请输入姓名" />
                </el-form-item>
                <el-form-item label="证件号" prop="idNumber">
                    <el-input v-model="obj.dialogForm.idNumber" placeholder="请输入证件号" />
                </el-form-item>
                <el-form-item label="银行卡号" prop="bankCardNumber">
                    <el-input v-model="obj.dialogForm.bankCardNumber" placeholder="请输入银行卡号" />
                </el-form-item>
                <el-form-item label="实付工资款" prop="actualSalary">
                    <el-input v-model="obj.dialogForm.actualSalary" placeholder="请输入实付工资款" />
                </el-form-item>
                <el-form-item label="申请人" prop="applicant">
                    <el-input v-model="obj.dialogForm.applicant" placeholder="请输入申请人" />
                </el-form-item>
                <el-form-item label="项目客服" prop="projectService">
                    <el-input v-model="obj.dialogForm.projectService" placeholder="请输入项目客服" />
                </el-form-item>
                <el-form-item label="薪资客服" prop="salaryService">
                    <el-input v-model="obj.dialogForm.salaryService" placeholder="请输入薪资客服" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ReefundTicketEntryInformation">
// 导入必要的组件和函数

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 客户选项
const customerOptions = [
    { value: '1', label: '客户A' },
    { value: '2', label: '客户B' },
    { value: '3', label: '客户C' }
];

// 状态选项
const statusOptions = [
    { value: '0', label: '未发放' },
    { value: '1', label: '已发放' },
    { value: '2', label: '发放失败' }
];

// 表单验证规则
const rules = {
    companyName: [
        { required: true, message: '请输入出款公司名称', trigger: 'blur' }
    ],
    customerName: [
        { required: true, message: '请选择客户名称', trigger: 'change' }
    ],
    personName: [
        { required: true, message: '请输入姓名', trigger: 'blur' }
    ],
    idNumber: [
        { required: true, message: '请输入证件号', trigger: 'blur' }
    ],
    bankCardNumber: [
        { required: true, message: '请输入银行卡号', trigger: 'blur' }
    ],
    actualSalary: [
        { required: true, message: '请输入实付工资款', trigger: 'blur' }
    ]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null, // 出款公司名称
        applicant: null, // 申请人
        customerName: null, // 客户名称
        expenseMonth: null, // 费用所属月份
        paymentDate: null, // 工资支付日期
        idNumber: null, // 证件号
        personName: null, // 姓名
        projectService: null, // 项目客服
        salaryService: null, // 薪资客服
        creator: null, // 创建人
        paymentStatus: null, // 发放状态
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 显示对话框
    dialogShow2: false, // 显示对话框
    title: '', // 对话框标题
    ids: [], // 选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                companyName: '某某科技有限公司',
                customerName: '客户A',
                expenseMonth: '2023-05',
                paymentDate: '2023-05-15',
                personName: '张三',
                idNumber: '110101199001011234',
                bankCardNumber: '****************',
                actualSalary: 8500.00,
                applicant: '李四',
                entryStatus: '已录入',
                paymentId: 'PAY20230515001',
                paymentStatus: '已发放',
                projectService: '王五',
                salaryService: '赵六',
                creator: '管理员'
            },
            {
                id: 2,
                companyName: '某某信息技术有限公司',
                customerName: '客户B',
                expenseMonth: '2023-05',
                paymentDate: '2023-05-16',
                personName: '李四',
                idNumber: '110101199002022345',
                bankCardNumber: '****************',
                actualSalary: 9200.00,
                applicant: '王五',
                entryStatus: '已录入',
                paymentId: 'PAY20230516001',
                paymentStatus: '已发放',
                projectService: '赵六',
                salaryService: '钱七',
                creator: '管理员'
            },
            {
                id: 3,
                companyName: '某某软件有限公司',
                customerName: '客户C',
                expenseMonth: '2023-05',
                paymentDate: '2023-05-17',
                personName: '王五',
                idNumber: '110101199003033456',
                bankCardNumber: '****************',
                actualSalary: 7800.00,
                applicant: '赵六',
                entryStatus: '已录入',
                paymentId: 'PAY20230517001',
                paymentStatus: '未发放',
                projectService: '钱七',
                salaryService: '孙八',
                creator: '管理员'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}



// 表单重置
function resetForm() {
    obj.dialogForm = {
        companyName: '',
        customerName: '',
        expenseMonth: '',
        paymentDate: '',
        personName: '',
        idNumber: '',
        bankCardNumber: '',
        actualSalary: '',
        applicant: '',
        projectService: '',
        salaryService: ''
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

// 添加按钮操作区域
function handleAdd() {
    obj.title = "添加退票录入信息";
    obj.dialogShow = true;
    resetForm();
}

// 提交表单
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.dialog-footer {
    text-align: center;
    padding-top: 10px;
}

.el-form-item {
    margin-bottom: 20px;
}
</style>