<!-- 工资个税差异 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户编号:" prop="customerCode">
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractCode">
                <el-select class="width220" v-model="obj.queryParams.contractCode" placeholder="请选择" clearable>
                    <el-option v-for="item in contractOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付年月:" prop="paymentMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.paymentMonth" type="month" placeholder="请选择月份"
                    clearable value-format="YYYY-MM" />
            </el-form-item>
            <el-form-item label="工资计税月:" prop="taxMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.taxMonth" type="month" placeholder="请选择月份"
                    clearable value-format="YYYY-MM" />
            </el-form-item>
            <el-form-item label="派单方:" prop="dispatchParty">
                <el-input class="width220" v-model="obj.queryParams.dispatchParty" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="创建人:" prop="creator">
                <el-input class="width220" v-model="obj.queryParams.creator" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="差异备注:" prop="differenceRemark">
                <el-input class="width220" v-model="obj.queryParams.differenceRemark" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Download" @click="handleExport">导出</el-button>
            </el-col>
            <column-filter v-model="obj.selectedColumns" :column-options="obj.columnOptions"
                cache-key="wageAndIncomeTaxDifferences" />
            <el-button type="primary" plain icon="Printer" @click="handlePrint">打印</el-button>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column v-if="obj.selectedColumns.includes('uniqueId')" label="唯一号" align="center"
                prop="uniqueId" />
            <el-table-column v-if="obj.selectedColumns.includes('employeeName')" label="姓名" align="center"
                prop="employeeName" />
            <el-table-column v-if="obj.selectedColumns.includes('idNumber')" label="证件号码" align="center"
                prop="idNumber" />
            <el-table-column v-if="obj.selectedColumns.includes('idType')" label="证件类型" align="center" prop="idType">
                <template #default="scope">
                    <el-tag
                        :type="scope.row.idType === '1' ? 'primary' : scope.row.idType === '2' ? 'success' : 'info'">
                        {{ getIdTypeName(scope.row.idType) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('withholdingAgent')" label="扣缴义务人" align="center"
                prop="withholdingAgent" />
            <el-table-column v-if="obj.selectedColumns.includes('withholdingAgentType')" label="扣缴义务人类型" align="center"
                prop="withholdingAgentType">
                <template #default="scope">
                    <el-tag :type="scope.row.withholdingAgentType === '1' ? 'primary' : 'success'">
                        {{ scope.row.withholdingAgentType === '1' ? '企业' : '个人' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('customerCode')" label="客户编号" align="center"
                prop="customerCode" />
            <el-table-column v-if="obj.selectedColumns.includes('customerName')" label="客户名称" align="center"
                prop="customerName" />
            <el-table-column v-if="obj.selectedColumns.includes('contractCode')" label="合同编号" align="center"
                prop="contractCode" />
            <el-table-column v-if="obj.selectedColumns.includes('contractName')" label="合同名称" align="center"
                prop="contractName" />
            <el-table-column v-if="obj.selectedColumns.includes('paymentMonth')" label="支付年月" align="center"
                prop="paymentMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('taxMonth')" label="工资计税月" align="center"
                prop="taxMonth" />
            <el-table-column v-if="obj.selectedColumns.includes('differenceAmount')" label="差异金额" align="center"
                prop="differenceAmount">
                <template #default="scope">
                    <span :class="scope.row.differenceAmount > 0 ? 'text-red' : 'text-green'">
                        {{ scope.row.differenceAmount }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column v-if="obj.selectedColumns.includes('differenceRemark')" label="差异备注" align="center"
                prop="differenceRemark" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script setup name="WageAndIncomeTaxDifferences">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


// 合同选项
const contractOptions = [
    { value: 'CT001', label: '合同1' },
    { value: 'CT002', label: '合同2' },
    { value: 'CT003', label: '合同3' }
];


const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerCode: null, // 客户编号
        customerName: null, // 客户名称
        contractCode: null, // 合同编号
        contractName: null, // 合同名称
        employeeName: null, // 雇员姓名
        idNumber: null, // 证件号码
        paymentMonth: null, // 支付年月
        taxMonth: null, // 工资计税月
        dispatchParty: null, // 派单方
        creator: null, // 创建人
        differenceRemark: null, // 差异备注
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    ids: [], // 选中的id

    columnOptions: [
        { label: '唯一号', prop: 'uniqueId' },
        { label: '姓名', prop: 'employeeName' },
        { label: '证件号码', prop: 'idNumber' },
        { label: '证件类型', prop: 'idType' },
        { label: '扣缴义务人', prop: 'withholdingAgent' },
        { label: '扣缴义务人类型', prop: 'withholdingAgentType' },
        { label: '客户编号', prop: 'customerCode' },
        { label: '客户名称', prop: 'customerName' },
        { label: '合同编号', prop: 'contractCode' },
        { label: '合同名称', prop: 'contractName' },
        { label: '支付年月', prop: 'paymentMonth' },
        { label: '工资计税月', prop: 'taxMonth' },
        { label: '差异金额', prop: 'differenceAmount' },
        { label: '差异备注', prop: 'differenceRemark' }
    ],
    selectedColumns: ['uniqueId', 'employeeName', 'idNumber', 'idType', 'withholdingAgent', 'withholdingAgentType', 'customerCode',
        'customerName', 'contractCode', 'contractName', 'paymentMonth', 'taxMonth', 'differenceAmount', 'differenceRemark']
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据，实际项目中应该调用API
    setTimeout(() => {
        obj.tableData = [
            {
                id: 1,
                uniqueId: 'WD20230501001',
                employeeName: '张三',
                idNumber: '110101199001011234',
                idType: '1',
                withholdingAgent: '某某科技有限公司',
                withholdingAgentType: '1',
                customerCode: 'CUS001',
                customerName: '某某科技有限公司',
                contractCode: 'CT001',
                contractName: '合同1',
                paymentMonth: '2023-05',
                taxMonth: '2023-05',
                differenceAmount: 500.00,
                differenceRemark: '工资与个税申报金额不一致',
                dispatchParty: '北京总部',
                creator: '管理员'
            },
            {
                id: 2,
                uniqueId: 'WD20230501002',
                employeeName: '李四',
                idNumber: '110101199002022345',
                idType: '1',
                withholdingAgent: '某某信息技术有限公司',
                withholdingAgentType: '1',
                customerCode: 'CUS002',
                customerName: '某某信息技术有限公司',
                contractCode: 'CT002',
                contractName: '合同2',
                paymentMonth: '2023-05',
                taxMonth: '2023-05',
                differenceAmount: -300.00,
                differenceRemark: '个税申报金额大于工资金额',
                dispatchParty: '上海分部',
                creator: '管理员'
            },
            {
                id: 3,
                uniqueId: 'WD20230501003',
                employeeName: '王五',
                idNumber: '110101199003033456',
                idType: '2',
                withholdingAgent: '某某软件有限公司',
                withholdingAgentType: '2',
                customerCode: 'CUS003',
                customerName: '某某软件有限公司',
                contractCode: 'CT003',
                contractName: '合同3',
                paymentMonth: '2023-05',
                taxMonth: '2023-05',
                differenceAmount: 800.00,
                differenceRemark: '工资与个税申报金额不一致',
                dispatchParty: '广州分部',
                creator: '管理员'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}



// 获取证件类型名称
function getIdTypeName(idType) {
    const idTypeMap = {
        '1': '身份证',
        '2': '护照',
        '3': '其他'
    };
    return idTypeMap[idType] || '未知类型';
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

// 多选框
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id)
}

// 导出按钮操作
function handleExport() {
    if (obj.tableData.length === 0) {
        proxy.$modal.msgError('没有数据可导出');
        return;
    }

    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 打印数据 */
function handlePrint() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgInfo('请选择要打印的数据');
        return;
    }

    proxy.$modal.msgSuccess('数据打印成功');
}

getList();
</script>
<style lang="scss" scoped>
.text-red {
    color: #f56c6c;
    font-weight: bold;
}

.text-green {
    color: #67c23a;
    font-weight: bold;
}

//筛选按钮
.top-right-btn {
    margin-left: 10px !important;
}
</style>