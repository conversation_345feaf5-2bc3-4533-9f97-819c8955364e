<!-- 非纯代发银行卡信息维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="银行卡号:" prop="bankCardNo">
                <el-input class="width220" v-model="obj.queryParams.bankCardNo" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入" clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="Download" @click="handleExport">导出数据</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="唯一号" align="center" prop="uniqueId" width="120" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" width="100" />
            <el-table-column label="证件号码" align="center" prop="idNumber" width="180" />
            <el-table-column label="银行卡号" align="center" prop="bankCardNo" width="180" />
            <el-table-column label="开户人姓名" align="center" prop="accountName" width="120" />
            <el-table-column label="开户分支行" align="center" prop="branchBank" width="150" />
            <el-table-column label="开户银行" align="center" width="150">
                <template #default="scope">
                    {{ getBankName(scope.row.bankName) }}
                </template>
            </el-table-column>

            <el-table-column label="其他银行" align="center" prop="otherBank" width="120" />
            <el-table-column label="开户行省份" align="center" prop="bankProvince" width="120" />
            <el-table-column label="开户行城市" align="center" prop="bankCity" width="120" />
            <el-table-column label="是否有效" align="center" width="80">
                <template #default="scope">
                    {{ getValidLabel(scope.row.isValid) }}
                </template>
            </el-table-column>
            <el-table-column label="业务类型" align="center" width="120">
                <template #default="scope">
                    {{ getBusinessTypeName(scope.row.businessType) }}
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" min-width="120" />
            <el-table-column label="是否竞业员工" align="center" prop="customerName" min-width="150" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="65%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" inline :rules="obj.rules"
                label-width="auto">
                <el-form-item label="唯一号" prop="uniqueId">
                    <el-input class="width220" v-model="obj.dialogForm.uniqueId" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="雇员姓名" prop="employeeName">
                    <el-input class="width220" v-model="obj.dialogForm.employeeName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开户人姓名" prop="accountName">
                    <el-input class="width220" v-model="obj.dialogForm.accountName" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="银行卡号" prop="bankCardNo">
                    <el-input class="width220" v-model="obj.dialogForm.bankCardNo" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="业务类型" prop="businessType">
                    <el-select class="width220" v-model="obj.dialogForm.businessType" placeholder="请选择">
                        <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="开户银行" prop="bankName">
                    <el-select class="width220" v-model="obj.dialogForm.bankName" placeholder="请选择">
                        <el-option v-for="item in bankOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="其他银行" prop="otherBank">
                    <el-input class="width220" v-model="obj.dialogForm.otherBank" placeholder="请输入"
                        :disabled="obj.dialogForm.bankName !== '6'" />
                </el-form-item>
                <el-form-item label="开户分支行" prop="branchBank">
                    <el-input class="width220" v-model="obj.dialogForm.branchBank" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="开户行省份" prop="bankProvince">
                    <el-select class="width220" v-model="obj.dialogForm.bankProvince" placeholder="请选择">
                        <el-option label="北京市" value="北京市" />
                        <el-option label="上海市" value="上海市" />
                        <el-option label="广东省" value="广东省" />
                        <el-option label="江苏省" value="江苏省" />
                        <el-option label="四川省" value="四川省" />
                    </el-select>
                </el-form-item>
                <el-form-item label="开户行城市" prop="bankCity">
                    <el-select class="width220" v-model="obj.dialogForm.bankCity" placeholder="请选择">
                        <el-option label="北京市" value="北京市" />
                        <el-option label="上海市" value="上海市" />
                        <el-option label="广州市" value="广州市" />
                        <el-option label="深圳市" value="深圳市" />
                        <el-option label="成都市" value="成都市" />
                    </el-select>
                </el-form-item>
                <el-form-item label="是否有效" prop="isValid">
                    <el-select class="width220" v-model="obj.dialogForm.isValid" placeholder="请选择">
                        <el-option label="是" value="是" />
                        <el-option label="否" value="否" />
                    </el-select>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                    <el-input class="width220" v-model="obj.dialogForm.email" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" class="width420" v-model="obj.dialogForm.remark" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-input class="width220" v-model="obj.dialogForm.customerName" placeholder="请输入" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="BankCardInformation_nonPure">

import { useAreaStore } from '@/store/modules/area'

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 获取省份数据
const provinces = areaStore.provinces

// 业务类型选项
const businessTypeOptions = [
    { value: '1', label: '工资代发' },
    { value: '2', label: '社保代缴' },
    { value: '3', label: '公积金代缴' },
    { value: '4', label: '其他' }
];

// 银行选项
const bankOptions = [
    { value: '1', label: '中国工商银行' },
    { value: '2', label: '中国农业银行' },
    { value: '3', label: '中国建设银行' },
    { value: '4', label: '中国银行' },
    { value: '5', label: '交通银行' },
    { value: '6', label: '其他银行' }
];

// 非纯代发银行卡信息维护数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: '', // 唯一号
        bankCardNo: '', // 银行卡号
        idNumber: '', // 证件号码
        customerName: '', // 客户名称
    }, // 查询表单
    total: 0, // 总条数

    // 模拟表格数据
    tableData: [
        {
            id: 1,
            uniqueId: 'BK20230501001',
            employeeName: '张三',
            idNumber: '110101199001011234',
            bankCardNo: '6222021234567890123',
            accountName: '张三',
            branchBank: '北京市海淀支行',
            bankName: '1',
            bankProvince: '北京市',
            bankCity: '北京市',
            isValid: '1',
            businessType: '1',
            remark: '工资代发账户',
            email: '<EMAIL>',
            customerName: '北京科技有限公司'
        },
        {
            id: 2,
            uniqueId: 'BK20230501002',
            employeeName: '李四',
            idNumber: '310101199002022345',
            bankCardNo: '6227001234567890123',
            accountName: '李四',
            branchBank: '上海市浦东支行',
            bankName: '2',
            bankProvince: '上海市',
            bankCity: '上海市',
            isValid: '1',
            businessType: '2',
            remark: '社保代缴账户',
            email: '<EMAIL>',
            customerName: '上海贸易有限公司'
        },
        {
            id: 3,
            uniqueId: 'BK20230501003',
            employeeName: '王五',
            idNumber: '******************',
            bankCardNo: '6228481234567890123',
            accountName: '王五',
            branchBank: '广州市天河支行',
            bankName: '3',
            bankProvince: '广东省',
            bankCity: '广州市',
            isValid: '1',
            businessType: '3',
            remark: '公积金代缴账户',
            email: '<EMAIL>',
            customerName: '广州电子有限公司'
        },
        {
            id: 4,
            uniqueId: 'BK20230501004',
            employeeName: '赵六',
            idNumber: '******************',
            bankCardNo: '6212261234567890123',
            accountName: '赵六',
            branchBank: '深圳市南山支行',
            bankName: '4',
            bankProvince: '广东省',
            bankCity: '深圳市',
            isValid: '0',
            businessType: '1',
            remark: '已作废账户',
            email: '<EMAIL>',
            customerName: '深圳科技有限公司'
        },
        {
            id: 5,
            uniqueId: 'BK20230501005',
            employeeName: '孙七',
            idNumber: '510101199005055678',
            bankCardNo: '6217001234567890123',
            accountName: '孙七',
            branchBank: '成都市锦江支行',
            bankName: '5',
            bankProvince: '四川省',
            bankCity: '成都市',
            isValid: '1',
            businessType: '4',
            remark: '其他业务账户',
            email: '<EMAIL>',
            customerName: '成都信息有限公司'
        }
    ],

    // 表单相关数据
    dialogForm: {}, // 表单数据
    dialogShow: false, // 弹出框显示状态
    title: '', // 弹出框标题
    ids: [], // 选中的id
    rules: {
        uniqueId: [{ required: true, message: '请输入唯一号', trigger: 'blur' }],
        employeeName: [{ required: true, message: '请输入雇员姓名', trigger: 'blur' }],
        accountName: [{ required: true, message: '请输入开户人姓名', trigger: 'blur' }],
        bankCardNo: [{ required: true, message: '请输入银行卡号', trigger: 'blur' }],
        businessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
        bankName: [{ required: true, message: '请选择开户银行', trigger: 'change' }],
        branchBank: [{ required: true, message: '请输入开户分支行', trigger: 'blur' }],
        bankProvince: [{ required: true, message: '请选择开户行省份', trigger: 'change' }],
        bankCity: [{ required: true, message: '请选择开户行城市', trigger: 'change' }],
        isValid: [{ required: true, message: '请选择是否有效', trigger: 'change' }]
    }
});

/**
 * 获取业务类型名称
 * @param {string} typeId 类型ID
 * @returns {string} 类型名称
 */
function getBusinessTypeName(typeId) {
    if (!typeId) return '-';

    const type = businessTypeOptions.find(item => item.value === typeId);
    return type ? type.label : '-';
}

/**
 * 获取银行名称
 * @param {string} bankId 银行ID
 * @returns {string} 银行名称
 */
function getBankName(bankId) {
    if (!bankId) return '-';

    const bank = bankOptions.find(item => item.value === bankId);
    return bank ? bank.label : '-';
}

/**
 * 获取是否有效标签
 * @param {string} value 值
 * @returns {string} 标签
 */
function getValidLabel(value) {
    return value === '1' ? '是' : '否';
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;

    // 模拟数据加载
    setTimeout(() => {
        // 如果有搜索条件，进行筛选
        let filteredData = [...obj.tableData];

        if (obj.queryParams.uniqueId) {
            filteredData = filteredData.filter(item =>
                item.uniqueId.includes(obj.queryParams.uniqueId)
            );
        }

        if (obj.queryParams.bankCardNo) {
            filteredData = filteredData.filter(item =>
                item.bankCardNo.includes(obj.queryParams.bankCardNo)
            );
        }

        if (obj.queryParams.idNumber) {
            filteredData = filteredData.filter(item =>
                item.idNumber.includes(obj.queryParams.idNumber)
            );
        }

        if (obj.queryParams.customerName) {
            filteredData = filteredData.filter(item =>
                item.customerName.includes(obj.queryParams.customerName)
            );
        }

        obj.total = filteredData.length;
        obj.loading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    // 重置表单
    obj.dialogForm = {
        isValid: '1' // 默认设置为有效
    };
    if (proxy.$refs["formRef"]) {
        proxy.$refs["formRef"].resetFields();
    }

    obj.dialogShow = true;
    obj.title = "新增";
}

/** 修改按钮操作 */
function handleUpdate() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgInfo('请选择一条记录进行修改');
        return;
    }

    // 模拟获取数据
    const selectedRecord = obj.tableData.find(item => item.id === obj.ids[0]);
    if (selectedRecord) {
        obj.dialogForm = JSON.parse(JSON.stringify(selectedRecord));
        obj.dialogShow = true;
        obj.title = "修改";
    }
}

/** 导出数据 */
function handleExport() {
    proxy.$modal.msgSuccess('导出数据成功');
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 初始化加载数据
onMounted(() => {
    getList();
});
</script>
<style lang="scss" scoped></style>