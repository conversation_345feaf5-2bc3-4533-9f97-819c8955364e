import request from '@/utils/request'

// 查询流程实例列表
export function listInstance(query) {
  return request({
    url: '/system/instance/list',
    method: 'get',
    params: query
  })
}

// 查询流程实例详细
export function getInstance(id) {
  return request({
    url: '/system/instance/' + id,
    method: 'get'
  })
}

// 新增流程实例
export function addInstance(data) {
  return request({
    url: '/system/instance',
    method: 'post',
    data: data
  })
}

// 修改流程实例
export function updateInstance(data) {
  return request({
    url: '/system/instance',
    method: 'put',
    data: data
  })
}

// 删除流程实例
export function delInstance(id) {
  return request({
    url: '/system/instance/' + id,
    method: 'delete'
  })
}
