<!-- 纯代发证件号码维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="唯一号:" prop="uniqueId">
                <el-input class="width220" v-model="obj.queryParams.uniqueId" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="雇员姓名：" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="证件类型:" prop="idType">
                <el-select class="width220" v-model="obj.queryParams.idType" placeholder="请选择" clearable>
                    <el-option v-for="item in id_type" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="证件编号:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-select class="width220" v-model="obj.queryParams.customerName" placeholder="请选择" clearable>
                    <el-option v-for="item in customerOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleEdit">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="View" :disabled="obj.single"
                    @click="handleHistory">查看更改历史</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <PureConsignmentTable :tableData="obj.tableData" :loading="obj.loading" :page="obj.queryParams.pageNum"
            :limit="obj.queryParams.pageSize" @handlePagination="handlePagination"
            @selection-change="handleSelectionChange" />
        <!-- 修改 -->
        <PureConsignmentForm v-model:dialogShow="obj.dialogShow" :dialogForm="obj.dialogForm" :title="obj.title"
            :rules="rules" @submit="submit" />

        <!-- 查看更改历史 -->
        <PureConsignmentHistory v-model:dialogShow="obj.historydialogShow" :tableData="obj.tableData" :title="obj.title"
            @handlePagination="handlePagination" />
    </div>
</template>

<script setup name="PureIssue_IDNumber">
import PureConsignmentTable from '@/views/reonManage/components/table/pureConsignmentTable.vue';
import PureConsignmentForm from '@/views/reonManage/components/dialog/pureConsignmentForm.vue';
import PureConsignmentHistory from '@/views/reonManage/components/dialog/pureConsignmentHistory.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()
const { id_type } = proxy.useDict("id_type");

// 客户选项
const customerOptions = [
    { value: 'C001', label: '北京科技有限公司' },
    { value: 'C002', label: '上海贸易有限公司' },
    { value: 'C003', label: '广州电子有限公司' },
    { value: 'C004', label: '深圳科技有限公司' },
    { value: 'C005', label: '成都信息有限公司' }
];


// 表单验证规则
const rules = {
    employeeName: [{ required: true, message: '请输入雇员姓名', trigger: 'blur' }],
    idType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
    idNumber: [{ required: true, message: '请输入证件号码', trigger: 'blur' }],
    phoneNumber: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }],
    salaryOtherIdType: [{ required: true, message: '请输入工资其他证件类型', trigger: 'blur' }]
};

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    historyLoading: false, // 历史记录加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null, // 唯一号
        employeeName: null, // 雇员姓名
        idType: null, // 证件类型
        idNumber: null, // 证件编号
        customerName: null, // 客户名称
    }, // 查询表单
    historyParams: {
        pageNum: 1,
        pageSize: 10,
        uniqueId: null // 唯一号
    }, // 历史记录查询参数
    total: 0, // 总条数
    historyTotal: 0, // 历史记录总条数
    tableData: [], // 列表
    historyData: [], // 历史记录列表
    dialogForm: {}, // 表单
    dialogShow: false, // 弹出框
    historydialogShow: false, // 历史记录弹出框
    ids: [], // 选中的id
    title: "", // 标题
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        obj.tableData = [
            {
                id: 1,
                uniqueId: 'UID001',
                employeeName: '张三',
                idType: '1',
                idNumber: '110101199001011234',
                customerId: 'C001',
                customerName: '北京科技有限公司',
                dispatchService: '王五',
                salaryService: '李四',
                receiveService: '赵六',
                phoneNumber: '13800138001',
                email: '<EMAIL>',
                salaryIdType: '1',
                salaryOtherIdType: '',
                nationality: 'CN',
                gender: '1',
                birthDate: '1990-01-01',
                taxReason: '1',
                birthCountry: 'CN',
                firstEntryTime: null,
                expectedDepartureTime: null,
                otherIdNumber: '',
                otherName: '',
                employmentType: '1'
            },
            {
                id: 2,
                uniqueId: 'UID002',
                employeeName: '李四',
                idType: '1',
                idNumber: '310101199002022345',
                customerId: 'C002',
                customerName: '上海贸易有限公司',
                dispatchService: '张三',
                salaryService: '王五',
                receiveService: '赵六',
                phoneNumber: '13900139001',
                email: '<EMAIL>',
                salaryIdType: '1',
                salaryOtherIdType: '',
                nationality: 'CN',
                gender: '1',
                birthDate: '1990-02-02',
                taxReason: '1',
                birthCountry: 'CN',
                firstEntryTime: null,
                expectedDepartureTime: null,
                otherIdNumber: '',
                otherName: '',
                employmentType: '1'
            }
        ];
        obj.total = obj.tableData.length;
        obj.loading = false;
    }, 300);
}

/** 获取历史记录数据 */
function getHistoryList() {
    obj.historyLoading = true;
    // 模拟API调用
    setTimeout(() => {
        // 模拟数据
        obj.historyData = [
            {
                id: 1,
                uniqueId: obj.historyParams.uniqueId || 'UID001',
                employeeName: '张三',
                idType: '1',
                idNumber: '110101199001011234',
                phoneNumber: '13800138001',
                backupTime: '2023-01-01 10:00:00',
                operator: 'admin'
            },
            {
                id: 2,
                uniqueId: obj.historyParams.uniqueId || 'UID001',
                employeeName: '张三',
                idType: '1',
                idNumber: '110101199001011234',
                phoneNumber: '13800138002',
                backupTime: '2023-02-01 11:00:00',
                operator: 'admin'
            },
            {
                id: 3,
                uniqueId: obj.historyParams.uniqueId || 'UID001',
                employeeName: '张三',
                idType: '1',
                idNumber: '110101199001011234',
                phoneNumber: '13800138003',
                backupTime: '2023-03-01 12:00:00',
                operator: 'admin'
            }
        ];
        obj.historyTotal = obj.historyData.length;
        obj.historyLoading = false;
    }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
// 分页
function handlePagination(pagination) {
    obj.queryParams.pageNum = pagination.page;
    obj.queryParams.pageSize = pagination.limit;
    getList();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 修改按钮操作 */
function handleEdit() {
    if (obj.single) {
        proxy.$modal.msgError("请选择一条记录进行修改");
        return;
    }

    // 获取选中记录
    const id = obj.ids[0];
    const record = obj.tableData.find(item => item.id === id);

    if (!record) {
        proxy.$modal.msgError("找不到选中的记录");
        return;
    }

    // 复制记录到表单
    obj.dialogForm = JSON.parse(JSON.stringify(record));

    obj.dialogShow = true;
    obj.title = '修改';
}

/** 查看更改历史按钮操作 */
function handleHistory() {
    if (obj.single) {
        proxy.$modal.msgError("请选择一条记录查看历史");
        return;
    }

    // 获取选中记录
    const id = obj.ids[0];
    const record = obj.tableData.find(item => item.id === id);

    if (!record) {
        proxy.$modal.msgError("找不到选中的记录");
        return;
    }

    // 设置历史查询参数
    obj.historyParams.uniqueId = record.uniqueId;
    obj.historyParams.pageNum = 1;

    obj.historydialogShow = true;
    obj.title = '查看更改历史';

    // 获取历史记录
    getHistoryList();
}

/** 提交表单 */
function submit() {
    proxy.$refs.formRef.validate(valid => {
        if (valid) {
            // 如果选择了其他证件类型，验证是否填写了其他证件类型
            if (obj.dialogForm.salaryIdType === 'other' && !obj.dialogForm.salaryOtherIdType) {
                proxy.$modal.msgError("请填写工资其他证件类型");
                return;
            }

            // 模拟保存操作
            proxy.$modal.loading("正在保存数据，请稍候...");
            setTimeout(() => {
                proxy.$modal.closeLoading();
                proxy.$modal.msgSuccess("保存成功");
                obj.dialogShow = false;
                getList();
            }, 500);
        }
    });
}


getList();
</script>
<style lang="scss" scoped></style>