<!-- 合同 -->
<template>
    <el-dialog v-model="show" :width="props.width" append-to-body draggable @close="close">
        <div class="mt20 mb20 flex-between">
            <div>
                <el-input class="width320" v-model="obj.queryParams.clientName" placeholder="客户名称/编号/合同名称/编号" clearable>
                    <template #append>
                        <el-button @click="handleCustomerQuery" icon="Search" />
                    </template>
                </el-input>
            </div>
            <div>
                <el-button @click="clear">清空</el-button>
                <el-button type="primary" @click="select">选择</el-button>
            </div>
        </div>
        <el-table :data="obj.tableData" style="width: 100%" border @current-change="handleCurrentChange">
            <el-table-column align="center" width="80">
                <template #default="scope">
                    <el-radio v-model="radio" :value="scope.row.id" @change="handleCurrentChange(scope.row)" />
                </template>
            </el-table-column>
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column v-if="props.isShow" label="客户名称" align="center" prop="customerName" />
            <el-table-column v-if="props.isShow" label="合同类型" align="center" prop="contractType" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
    </el-dialog>
</template>

<script setup>
// import { listScale } from "@/api/reonApi/scale";

const props = defineProps({
    width: {
        type: String,
        default: '45%'
    },
    show: {
        type: Boolean,
        default: false
    },
    isShow: {
        type: Boolean,
        default: true
    }
})
// 使用本地变量跟踪对话框状态
const show = ref(props.show);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.show, (newVal) => {
    show.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(show, (newVal) => {
    emit('update:show', newVal);
});


const radio = ref(null)
const obj = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        clientName: null,
    },//查询表单
    tableData: [
        {
            id: 1,
            contractCode: 'HT20230001',
            contractName: '合同1',
            customerCode: 'KH20230001',
            customerName: '客户名称1',
            contractType: '合同类型1'
        },
        {
            id: 2,
            contractCode: 'HT20230002',
            contractName: '合同2',
            customerCode: 'KH20230002',
            customerName: '客户名称2',
            contractType: '合同类型2'
        },
        {
            id: 3,
            contractCode: 'HT20230003',
            contractName: '合同3',
            customerCode: '**********',
            customerName: '客户名称3',
            contractType: '合同类型3'
        }
    ],
    total: 0,
    currentRow: null,
})

/** 获取列表数据 */
function getList() {
    // 这里可以调用API获取合同列表
    // listScale(obj.queryParams).then(response => {
    //     obj.tableData = response.rows;
    //     obj.total = response.total;
    // });
    obj.total = obj.tableData.length;
}

/** 选择 */
function handleCurrentChange(row) {
    radio.value = row.id;
    obj.currentRow = row;
}
/** 清空 */
function clear() {
    obj.queryParams = {
        pageNum: 1,
        pageSize: 10,
        clientName: null,
    };
    obj.currentRow = null
    radio.value = null;
    show.value = false;
    emit('select', obj.currentRow);
}
const emit = defineEmits(['select', 'close', 'update:show']);
/** 选择 */
function select() {
    show.value = false;
    emit('select', obj.currentRow);
}
/** 关闭 */
function close() {
    show.value = false;
    emit('close');
}

/** 客户查询 */
function handleCustomerQuery() {
    getList();
}
</script>
<style lang="scss" scoped>
:deep(.el-table__body tr.current-row>td.el-table__cell) {
    background-color: rgb(134, 231, 231);
}
</style>