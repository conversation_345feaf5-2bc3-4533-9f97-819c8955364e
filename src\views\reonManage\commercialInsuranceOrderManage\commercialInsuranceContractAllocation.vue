<!-- 商保合同分配 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="派单分公司:" prop="branchCompany">
                <el-select class="width220" v-model="obj.queryParams.branchCompany" placeholder="请选择" clearable>
                    <el-option v-for="item in branchCompanyOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customer">
                <el-input class="width220" v-model="obj.queryParams.customer" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客服:" prop="customerService">
                <el-select class="width220" v-model="obj.queryParams.customerService" placeholder="请选择" clearable>
                    <el-option v-for="item in customerServiceOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入合同名称" clearable />
            </el-form-item>
            <el-row>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="User" @click="handleDistribution"
                    :disabled="obj.single">分配</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="List" @click="handleDistributionHistory"
                    :disabled="obj.single">分配历史日志</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" align="center" type="index" width="60" />
            <el-table-column label="客户编号" align="center" prop="customerId" width="100" />
            <el-table-column label="客户名称" align="center" prop="customerName" min-width="120" />
            <el-table-column label="合同编号" align="center" prop="contractId" width="120" />
            <el-table-column label="合同名称" align="center" prop="contractName" min-width="150" />
            <el-table-column label="派单分公司" align="center" prop="branchCompany" width="120" />
            <el-table-column label="负责客服" align="center" prop="customerService" width="100">
                <template #default="scope">
                    <span v-if="scope.row.customerService">
                        {{ scope.row.customerService }}
                    </span>
                    <el-tag v-else type="danger">未分配</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="生效日期" align="center" prop="effectiveDate" width="100" />
            <el-table-column label="合同起始日" align="center" prop="startDate" width="100" />
            <el-table-column label="合同终止日" align="center" prop="endDate" width="100" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 分配历史日志对话框 -->
        <el-dialog v-model="obj.historyDialogShow" title="分配历史日志" width="60%" append-to-body>
            <div class="history-header" v-if="obj.currentContract">
                <div class="contract-info">
                    <span class="label">合同名称：</span>
                    <span class="value">{{ obj.currentContract.contractName }}</span>
                </div>
                <div class="customer-info">
                    <span class="label">客户名称：</span>
                    <span class="value">{{ obj.currentContract.customerName }}</span>
                </div>
            </div>
            <el-table style="width: 100%;" :data="obj.historyData" border>
                <el-table-column label="序号" align="center" type="index" width="60" />
                <el-table-column label="客户名称" align="center" prop="customerName" min-width="120" />
                <el-table-column label="合同名称" align="center" prop="contractName" min-width="150" />
                <el-table-column label="分公司" align="center" prop="branchCompany" width="120" />
                <el-table-column label="客服" align="center" prop="customerService" width="100" />
                <el-table-column label="分配时间" align="center" prop="assignTime" width="160" />
                <el-table-column label="分配人" align="center" prop="assignUser" width="100" />
                <el-table-column label="备注" align="center" prop="remark" min-width="150" />
            </el-table>
            <pagination v-show="obj.historyTotal > 0" :total="obj.historyTotal"
                v-model:page="obj.historyQueryParams.pageNum" v-model:limit="obj.historyQueryParams.pageSize"
                @pagination="getHistoryList" />
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="obj.historyDialogShow = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 分配客服对话框 -->
        <el-dialog v-model="obj.distributionDialogShow" :title="obj.distributionTitle" width="25%" append-to-body>
            <el-form :model="obj.distributionForm" ref="distributionFormRef" label-width="auto"
                :rules="obj.distributionRules">
                <el-form-item label="商保客服" prop="branchCompany">
                    <el-select style="width: 100%;" v-model="obj.distributionForm.branchCompany" placeholder="请选择分公司"
                        class="width-full" @change="handleBranchCompanyChange">
                        <el-option v-for="item in branchCompanyOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="生效日期" prop="customerService">
                    <el-date-picker style="width: 100%;" v-model="obj.distributionForm.effectiveDate" type="date"
                        placeholder="请选择生效日期" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="obj.distributionDialogShow = false">取消</el-button>
                    <el-button type="primary" @click="submitDistribution">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="CommercialInsuranceContractAllocation">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 分公司选项
const branchCompanyOptions = ref([
    { value: '1', label: '北京分公司' },
    { value: '2', label: '上海分公司' },
    { value: '3', label: '广州分公司' },
    { value: '4', label: '深圳分公司' },
    { value: '5', label: '成都分公司' }
])

// 客服选项
const customerServiceOptions = ref([
    { value: '1', label: '张三', branchCompany: '1' },
    { value: '2', label: '李四', branchCompany: '1' },
    { value: '3', label: '王五', branchCompany: '2' },
    { value: '4', label: '赵六', branchCompany: '2' },
    { value: '5', label: '刘七', branchCompany: '3' },
    { value: '6', label: '陈八', branchCompany: '3' },
    { value: '7', label: '孙九', branchCompany: '4' },
    { value: '8', label: '郭十', branchCompany: '4' },
    { value: '9', label: '吴一一', branchCompany: '5' },
    { value: '10', label: '郑一二', branchCompany: '5' }
])

// 根据选择的分公司过滤客服选项
const filteredCustomerServiceOptions = computed(() => {
    if (!obj.distributionForm.branchCompany) {
        return [];
    }
    return customerServiceOptions.value.filter(item => item.branchCompany === obj.distributionForm.branchCompany);
})

// 响应式数据
const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        branchCompany: '',
        customer: '',
        customerService: '',
        contractName: '',
        contractDateRange: []
    }, // 查询表单
    total: 0, // 总条数
    tableData: [], // 列表
    ids: [], // 选中的id

    // 分配历史日志对话框
    historyDialogShow: false,
    historyData: [],
    historyTotal: 0,
    historyQueryParams: {
        pageNum: 1,
        pageSize: 10,
        contractId: ''
    },
    currentContract: null,

    // 分配客服对话框
    distributionDialogShow: false,
    distributionTitle: '分配客服',
    distributionForm: {
        branchCompany: '',
        customerService: '',
        remark: '',
        contractIds: []
    },
    distributionRules: {
        branchCompany: [
            { required: true, message: '请选择分公司', trigger: 'change' }
        ],
        customerService: [
            { required: true, message: '请选择客服', trigger: 'change' }
        ]
    }
})

/**
 * 获取合同列表
 */
function getList() {
    obj.loading = true;

    // 模拟数据
    setTimeout(() => {
        // 生成模拟数据
        const mockData = [];
        for (let i = 0; i < 10; i++) {
            mockData.push({
                id: i + 1,
                customerId: `C${String(1000 + i).padStart(4, '0')}`,
                customerName: `测试客户${i + 1}`,
                contractId: `HT${String(2000 + i).padStart(4, '0')}`,
                contractName: `商业保险合同${i + 1}`,
                branchCompany: branchCompanyOptions.value[i % branchCompanyOptions.value.length].label,
                customerService: i % 3 === 0 ? null : customerServiceOptions.value[i % customerServiceOptions.value.length].label,
                effectiveDate: '2023-05-15',
                startDate: '2023-05-15',
                endDate: '2024-05-14'
            });
        }

        // 应用查询条件过滤
        let filteredData = [...mockData];
        const params = obj.queryParams;

        if (params.branchCompany) {
            const branchCompany = branchCompanyOptions.value.find(item => item.value === params.branchCompany)?.label;
            if (branchCompany) {
                filteredData = filteredData.filter(item => item.branchCompany === branchCompany);
            }
        }

        if (params.customer) {
            filteredData = filteredData.filter(item =>
                item.customerName.includes(params.customer) ||
                item.customerId.includes(params.customer)
            );
        }

        if (params.customerService) {
            const customerService = customerServiceOptions.value.find(item => item.value === params.customerService)?.label;
            if (customerService) {
                filteredData = filteredData.filter(item => item.customerService === customerService);
            }
        }

        if (params.contractName) {
            filteredData = filteredData.filter(item =>
                item.contractName.includes(params.contractName) ||
                item.contractId.includes(params.contractName)
            );
        }

        if (params.contractDateRange && params.contractDateRange.length === 2) {
            // 实际项目中需要进行日期范围过滤
            // 这里简化处理，不做实际过滤
        }

        obj.tableData = filteredData;
        obj.total = filteredData.length;
        obj.loading = false;
    }, 500);
}

/**
 * 获取分配历史日志
 */
function getHistoryList() {
    // 模拟数据
    setTimeout(() => {
        const mockData = [];
        for (let i = 0; i < 5; i++) {
            mockData.push({
                id: i + 1,
                customerName: obj.currentContract ? obj.currentContract.customerName : `测试客户${i + 1}`,
                contractName: obj.currentContract ? obj.currentContract.contractName : `商业保险合同${i + 1}`,
                branchCompany: branchCompanyOptions.value[i % branchCompanyOptions.value.length].label,
                customerService: customerServiceOptions.value[i % customerServiceOptions.value.length].label,
                assignTime: `2023-05-${String(10 + i).padStart(2, '0')} 14:30:00`,
                assignUser: `管理员${i + 1}`,
                remark: i % 2 === 0 ? `分配备注${i + 1}` : ''
            });
        }

        obj.historyData = mockData;
        obj.historyTotal = mockData.length;
    }, 300);
}

/**
 * 分公司变化处理
 */
function handleBranchCompanyChange() {
    obj.distributionForm.customerService = '';
}

/**
 * 搜索按钮操作
 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/**
 * 重置按钮操作
 */
function resetQuery() {
    if (proxy.$refs["queryRef"]) {
        proxy.$refs["queryRef"].resetFields();
    }
    obj.queryParams = {
        pageNum: 1,
        pageSize: 10,
        branchCompany: '',
        customer: '',
        customerService: '',
        contractName: '',
        contractDateRange: []
    };
    handleQuery();
}

/**
 * 多选框选中数据
 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/**
 * 分配客服（批量）
 */
function handleDistribution() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgInfo('请选择要分配的合同');
        return;
    }

    // 获取选中的合同
    const selectedContracts = obj.tableData.filter(item => obj.ids.includes(item.id));

    // 如果选中的合同已经有客服，提示确认
    const hasAssigned = selectedContracts.some(item => item.customerService);
    if (hasAssigned) {
        ElMessageBox.confirm('选中的合同中有已分配客服的合同，是否继续操作？', '提示', {
            confirmButtonText: '继续',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            showDistributionDialog(selectedContracts);
        }).catch(() => { });
    } else {
        showDistributionDialog(selectedContracts);
    }
}

/**
 * 显示分配对话框
 */
function showDistributionDialog(contracts) {
    obj.distributionForm = {
        branchCompany: '',
        customerService: '',
        remark: '',
        contractIds: contracts.map(item => item.id)
    };

    // 如果所有合同都属于同一分公司，默认选中该分公司
    const branchCompanies = new Set(contracts.map(item => {
        const option = branchCompanyOptions.value.find(opt => opt.label === item.branchCompany);
        return option ? option.value : null;
    }).filter(Boolean));

    if (branchCompanies.size === 1) {
        obj.distributionForm.branchCompany = Array.from(branchCompanies)[0];
    }

    obj.distributionTitle = contracts.length > 1 ?
        `批量分配客服 (${contracts.length}个合同)` :
        `分配客服 - ${contracts[0].contractName}`;

    obj.distributionDialogShow = true;
}

/**
 * 提交分配
 */
function submitDistribution() {
    if (!proxy.$refs["distributionFormRef"]) return;

    proxy.$refs["distributionFormRef"].validate(valid => {
        if (valid) {
            // 显示加载中提示
            const loading = ElMessage({
                message: '正在处理，请稍候...',
                type: 'info',
                duration: 0
            });

            // 模拟处理过程
            setTimeout(() => {
                // 关闭加载中提示
                loading.close();

                // 更新表格数据
                const selectedCustomerService = customerServiceOptions.value.find(
                    item => item.value === obj.distributionForm.customerService
                )?.label;

                obj.tableData.forEach(item => {
                    if (obj.distributionForm.contractIds.includes(item.id)) {
                        item.customerService = selectedCustomerService;
                    }
                });

                // 关闭对话框
                obj.distributionDialogShow = false;

                // 显示成功提示
                proxy.$modal.msgSuccess(`分配成功！已将 ${obj.distributionForm.contractIds.length} 个合同分配给 ${selectedCustomerService}`);
            }, 1000);
        }
    });
}

/**
 * 查看分配历史
 */
function handleViewHistory(row) {
    obj.currentContract = row;
    obj.historyQueryParams.contractId = row.contractId;
    obj.historyDialogShow = true;
    getHistoryList();
}

/**
 * 分配历史日志
 */
function handleDistributionHistory() {
    obj.currentContract = null;
    obj.historyQueryParams.contractId = '';
    obj.historyDialogShow = true;
    getHistoryList();
}

// 初始化加载数据
getList();
</script>
<style lang="scss" scoped>
// 分配历史日志头部样式
.history-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .contract-info,
    .customer-info {
        .label {
            font-weight: bold;
            color: #606266;
            margin-right: 5px;
        }

        .value {
            color: #303133;
        }
    }
}

// 对话框底部样式
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

// 表格行样式
:deep(.el-table__row) {
    &:hover {
        background-color: #f5f7fa;
    }
}
</style>