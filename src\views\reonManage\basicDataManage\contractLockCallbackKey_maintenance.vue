<!-- 契约锁回调秘钥维护 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="业务分类:" prop="businessType">
                <el-select class="width220" filterable v-model="obj.queryParams.businessType" placeholder="请选择业务分类"
                    clearable>
                    <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称"
                    @focus="handleCustomer" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>

            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" @click="handleTemplate">查看模版字段</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" :disabled="obj.multiple" @click="handleSync">同步契约锁信息</el-button>
            </el-col>

            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" type="index" width="60" align="center" />
            <el-table-column label="业务分类Id" align="center" prop="businessTypeId" />
            <el-table-column label="业务分类全称" align="center" prop="businessTypeName" />
            <el-table-column label="密钥" align="center" prop="secretKey" />
            <el-table-column label="替换名称" align="center" prop="replaceName" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="集团名称" align="center" prop="groupName" />
            <el-table-column label="创建人" align="center" prop="createBy" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="35%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" label-width="auto">
                <el-form-item label="业务分类ID" prop="businessTypeId">
                    <el-select class="width420" v-model="obj.dialogForm.businessTypeId" placeholder="请选择业务分类" clearable>
                        <el-option v-for="item in businessTypeOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="回调密钥" prop="secretKey">
                    <el-input class="width420" v-model="obj.dialogForm.secretKey" placeholder="请输入回调密钥" />
                </el-form-item>
                <el-form-item label="客户名称" prop="customerName">
                    <el-input readonly class="width420" v-model="obj.dialogForm.customerName" placeholder="请选择客户"
                        @click="handleCustomer" />
                </el-form-item>
                <el-form-item label="集团名称" prop="groupName">
                    <el-input readonly class="width420" v-model="obj.dialogForm.groupName" placeholder="自动带出" />
                </el-form-item>
                <el-form-item label="替换名称" prop="replaceName">
                    <el-input class="width420" v-model="obj.dialogForm.replaceName" placeholder="请输入替换名称" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button type="info" @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 客户信息 -->
        <Client :width="'35%'" v-model:show="obj.customerDialogShow" :isShow="false" @select="handleSelect" />
        <!-- 模版字段 -->
        <el-dialog title="模版字段" v-model="obj.templateDialogShow" width="35%" append-to-body draggable>
            <el-table :data="obj.templateForm.fields" border>
                <el-table-column label="字段名称" align="center" prop="name" />
                <el-table-column label="字段标签" align="center" prop="label" />
                <el-table-column label="字段类型" align="center" prop="type" />
            </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="obj.templateDialogShow = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ContractLockCallbackKey_maintenance">

import { useAreaStore } from '@/store/modules/area'
import { listImport } from "@/api/reonApi/import";
import Client from '@/views/reonManage/components/client.vue'

const areaStore = useAreaStore()
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const provinces = areaStore.provinces // 获取省份数据

// 业务分类选项
const businessTypeOptions = [
    { value: '1', label: '合同签署' },
    { value: '2', label: '合同存证' },
    { value: '3', label: '合同审批' },
    { value: '4', label: '其他业务' }
];



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        businessType: null,
        customerName: null,
    },//查询表单
    customerQueryParams: {
        pageNum: 1,
        pageSize: 10,
    },//客户信息查询表单
    rules: {
        businessTypeId: [{ required: true, message: '请选择业务分类', trigger: 'blur' }],
        secretKey: [{ required: true, message: '请输入回调密钥', trigger: 'blur' }],
        customerName: [{ required: true, message: '请选择客户', trigger: 'blur' }],
        replaceName: [{ required: true, message: '请输入替换名称', trigger: 'blur' }],
    },
    total: 0,//总条数
    customerTotal: 0,//客户信息总条数

    tableData: [
        {
            id: 1,
            businessTypeId: '1',
            businessTypeName: '合同签署',
            secretKey: 'KEY123456789',
            replaceName: '合同签署回调',
            customerName: '客户A',
            customerId: 'C001',
            groupName: '集团A',
            createBy: 'admin'
        },
        {
            id: 2,
            businessTypeId: '2',
            businessTypeName: '合同存证',
            secretKey: 'KEY987654321',
            replaceName: '合同存证回调',
            customerName: '客户B',
            customerId: 'C002',
            groupName: '集团B',
            createBy: 'admin'
        }
    ],//列表
    customerTableData: [
        {
            id: 1,
            customerId: 'C001',
            customerNo: 'CN001',
            customerName: '客户A',
            groupName: '集团A'
        },
        {
            id: 2,
            customerId: 'C002',
            customerNo: 'CN002',
            customerName: '客户B',
            groupName: '集团B'
        },
        {
            id: 3,
            customerId: 'C003',
            customerNo: 'CN003',
            customerName: '客户C',
            groupName: '集团C'
        }
    ],//客户信息列表
    dialogForm: {}, //表单
    templateForm: {},//模版字段
    dialogShow: false, //弹出框
    customerDialogShow: false, //客户信息弹出框
    templateDialogShow: false,//模版字段弹出框
    ids: [],//选中的id
    title: "",//标题
})
/** 选择 */
function handleSelect(row) {
    if (row) {
        obj.queryParams.customerName = row.name;
        if (obj.dialogShow) {
            obj.dialogForm.customerName = row.name;
            obj.dialogForm.groupName = row.groupName || '';
        }
    } else {
        obj.queryParams.customerName = null;
        if (obj.dialogShow) {
            obj.dialogForm.customerName = null;
            obj.dialogForm.groupName = '';
        }
    }
}

/** 列表 */
function getList() {
    obj.loading = true;
    listImport(obj.queryParams).then(response => {
        obj.total = response.total;
        obj.loading = false;
    });
}



// 表单重置
function reset() {
    obj.dialogForm = {};
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();

}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增";
}
/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    // 模拟获取数据
    obj.dialogForm = JSON.parse(JSON.stringify(row || obj.tableData.find(item => obj.ids.includes(item.id))));
    obj.dialogShow = true;
    obj.title = "修改";
}

/** 查看模版字段 */
function handleTemplate() {
    obj.templateDialogShow = true;
    obj.templateForm = {
        fields: [
            { name: 'contractNo', label: '合同编号', type: 'string' },
            { name: 'contractName', label: '合同名称', type: 'string' },
            { name: 'customerName', label: '客户名称', type: 'string' },
            { name: 'signDate', label: '签署日期', type: 'date' },
            { name: 'amount', label: '合同金额', type: 'number' },
            { name: 'status', label: '状态', type: 'string' }
        ]
    };
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addClassify(obj.dialogForm).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 客户信息
function handleCustomer() {
    obj.customerDialogShow = true;
    obj.title = "选择客户";
}

/** 客户查询 */
function handleCustomerQuery() {
    // 模拟查询客户信息
    obj.customerTotal = obj.customerTableData.length;
}

/** 重置客户查询 */
function resetCustomerQuery() {
    obj.customerQueryParams = {
        pageNum: 1,
        pageSize: 10,
    };
    handleCustomerQuery();
}

/** 客户选择 */
function handleCustomerSelectionChange(selection) {
    if (selection.length > 0) {
        const selectedCustomer = selection[0];
        obj.dialogForm.customerName = selectedCustomer.customerName;
        obj.dialogForm.groupName = selectedCustomer.groupName;
    }
}


/** 同步契约锁信息 */
function handleSync() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要同步的数据');
        return;
    }
    proxy.$modal.confirm('是否确认同步选中的契约锁信息？').then(function () {
        proxy.$modal.msgSuccess("同步成功!");
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning("用户取消同步");
    });
}

getList();
</script>
<style lang="scss" scoped></style>