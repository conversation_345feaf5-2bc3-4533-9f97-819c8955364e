<!-- 劳务合同 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input readonly class="width220" @click="obj.showCustomerName = true"
                    v-model="obj.queryParams.customerName" placeholder="请选择客户名称" clearable />
            </el-form-item>
            <el-form-item label="雇员姓名:" prop="employeeName">
                <el-input class="width220" v-model="obj.queryParams.employeeName" placeholder="请输入雇员姓名" clearable />
            </el-form-item>
            <el-form-item label="证件号码:" prop="idNumber">
                <el-input class="width220" v-model="obj.queryParams.idNumber" placeholder="请输入证件号码" clearable />
            </el-form-item>
            <el-form-item label="签署日期>:" prop="signDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.signDateStart" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="签署日期<:" prop="signDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.signDateEnd" type="date" placeholder="请选择结束日期"
                    clearable />
            </el-form-item>
            <el-form-item label="合同编号:" prop="contractCode">
                <el-input class="width220" v-model="obj.queryParams.contractCode" placeholder="请输入合同编号" clearable />
            </el-form-item>
            <el-form-item label="合同类型:" prop="contractType">
                <el-select class="width220" v-model="obj.queryParams.contractType" placeholder="请选择合同类型" clearable>
                    <el-option label="劳务合同" value="1" />
                    <el-option label="服务合同" value="2" />
                    <el-option label="其他合同" value="3" />
                </el-select>
            </el-form-item>
            <el-form-item label="合同开始日期>:" prop="contractStartDate">
                <el-date-picker class="width220" v-model="obj.queryParams.contractStartDate" type="date"
                    placeholder="请选择开始日期" clearable />
            </el-form-item>
            <el-form-item label="合同结束日期<:" prop="contractEndDate">
                <el-date-picker class="width220" v-model="obj.queryParams.contractEndDate" type="date"
                    placeholder="请选择结束日期" clearable />
            </el-form-item>
            <el-form-item label="签署状态:" prop="signStatus">
                <el-select class="width220" v-model="obj.queryParams.signStatus" placeholder="请选择签署状态" clearable>
                    <el-option label="待签署" value="0" />
                    <el-option label="已签署" value="1" />
                    <el-option label="已撤回" value="2" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="24" class="button-group">
                <el-button type="primary" plain icon="Check" @click="handleSubmit">提交</el-button>
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增劳务合同</el-button>
                <el-button type="primary" plain icon="Edit" @click="handleUpdate">修改劳务合同</el-button>
                <el-button type="info" plain icon="View" @click="handleView">查看劳务合同</el-button>
                <el-button type="success" plain icon="Connection" @click="handleRenew">续约劳务合同</el-button>
                <el-button type="warning" plain icon="Close" @click="handleTerminate">终止劳务合同</el-button>
                <el-button type="success" plain icon="Download" @click="handleExport">导出数据</el-button>
                <el-button type="info" plain icon="Document" @click="handleDownloadTemplate">下载导入模版</el-button>
                <el-button type="primary" plain icon="Upload" @click="handleBatchImport">批量导入</el-button>
                <el-button type="primary" plain icon="Upload" @click="handleUploadContract">上传劳务合同</el-button>
                <el-button type="primary" plain icon="Upload" @click="handleBatchUploadContract">批量上传劳务合同</el-button>
                <el-button type="primary" plain icon="Upload" @click="handleUploadIdentityInfo">上传身份信息文件</el-button>
                <el-button type="primary" plain icon="Upload"
                    @click="handleBatchUploadIdentityInfo">批量上传身份信息文件</el-button>
                <el-button type="warning" plain icon="EditPen" @click="handleModifyStatus">修改签署状态</el-button>
            </el-col>
        </el-row>
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="雇员姓名" align="center" prop="employeeName" />
            <el-table-column label="证件号码" align="center" prop="idNumber" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="合同编号" align="center" prop="contractCode" />
            <el-table-column label="合同名称" align="center" prop="contractName" />
            <el-table-column label="合同类型" align="center" prop="contractType" />
            <el-table-column label="签署日期" align="center" prop="signDate" />
            <el-table-column label="合同开始日期" align="center" prop="contractStartDate" />
            <el-table-column label="合同结束日期" align="center" prop="contractEndDate" />
            <el-table-column label="签署状态" align="center" prop="signStatus" />
            <el-table-column label="合同金额" align="center" prop="contractAmount" />
            <el-table-column label="录入人" align="center" prop="creator" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        <!-- 客户名称 -->
        <el-dialog v-model="obj.showCustomerName" width="30%">
            <div style="display: flex; justify-content: space-between;" class="mt20">
                <el-input class="mb20" style="width: 30%" v-model="input3" placeholder="合同编号/合同名称">
                    <template #append>
                        <el-button icon="Search" />
                    </template>
                </el-input>
                <div class="clear">
                    <el-button type="primary">清空</el-button>
                    <el-button type="primary">选择</el-button>
                </div>
            </div>
            <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="客户Id" align="center" prop="customerId" />
                <el-table-column label="客户编号" align="center" prop="customerCode" />
                <el-table-column label="客户名称" align="center" prop="customerName" />
            </el-table>
            <!-- 分页 -->
            <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        </el-dialog>
        <!-- 新增员工合同 -->
        <Contract v-model:dialogShow="obj.dialogShow" :title="obj.title" :dialogForm="obj.dialogForm" :isAdd="obj.isAdd"
            :isUpdate="obj.isUpdate" :isLook="obj.isLook" :isRenewal="obj.isRenewal" menuName="serviceContract" />

        <!-- 批量上传劳动合同 -->
        <el-dialog v-model="obj.dialogShow3" :title="obj.title" width="25%" append-to-body draggable
            @close="handleClose">
            <div style="text-align: center;margin-bottom: 10px;color:red;" v-if="obj.title == '批量上传劳务合同'">
                <div>批量上传劳务合同请用证件号+合同号作为文件名称</div>
                <div>例:135848,HT-20250848545</div>
            </div>
            <file-upload />
            <template #footer>
                <el-button type="primary" @click="handleUploadContract">上 传</el-button>
                <el-button @click="obj.dialogShow3 = false">取 消</el-button>
            </template>
        </el-dialog>

        <!-- 批量导入 -->
        <ImportRecord v-model:dialogShow="obj.dialogShow4" :title="obj.title" menuName="serviceContract" />
    </div>
</template>

<script setup name="LaborServiceContract">
import Contract from '@/views/reonManage/components/dialog/contract.vue';
import ImportRecord from '@/views/reonManage/components/dialog/importRecord.vue';

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 表单验证规则
const rules = {
    employeeName: [
        { required: true, message: '雇员姓名不能为空', trigger: 'blur' }
    ],
    idNumber: [
        { required: true, message: '证件号码不能为空', trigger: 'blur' }
    ],
    customerName: [
        { required: true, message: '客户名称不能为空', trigger: 'blur' }
    ],
    contractName: [
        { required: true, message: '合同名称不能为空', trigger: 'blur' }
    ],
    contractType: [
        { required: true, message: '合同类型不能为空', trigger: 'change' }
    ],
    contractStartDate: [
        { required: true, message: '合同开始日期不能为空', trigger: 'change' }
    ],
    contractEndDate: [
        { required: true, message: '合同结束日期不能为空', trigger: 'change' }
    ]
};

// 客户名称搜索输入
const input3 = ref('');



const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        employeeName: null,
        idNumber: null,
        signDateStart: null,
        signDateEnd: null,
        contractCode: null,
        contractType: null,
        contractStartDate: null,
        contractEndDate: null,
        signStatus: null
    },//查询表单
    total: 0,//总条数
    tableData: [],//列表
    showCustomerName: false,//显示客户名称
    dialogShow: false,//显示新增劳务合同
    dialogForm: {},
    ids: [],//选中的id
    title: "",//标题

    isAdd: false,//新增
    isUpdate: false,//修改
    isLook: false,//查看
    isRenewal: false,//续约
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                employeeName: '张三',
                idNumber: '110101199001011234',
                customerName: '客户名称1',
                contractCode: 'CONTRACT001',
                contractName: '劳务合同1',
                contractType: '劳务合同',
                signDate: '2023-01-01',
                contractStartDate: '2023-01-01',
                contractEndDate: '2024-01-01',
                signStatus: '已签署',
                contractAmount: '50000',
                creator: '李四'
            },
            {
                id: 2,
                employeeName: '李四',
                idNumber: '110101199001021234',
                customerName: '客户名称2',
                contractCode: 'CONTRACT002',
                contractName: '服务合同1',
                contractType: '服务合同',
                signDate: '2023-02-01',
                contractStartDate: '2023-02-01',
                contractEndDate: '2024-02-01',
                signStatus: '待签署',
                contractAmount: '80000',
                creator: '王五'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 关闭对话框
function handleClose() {
    obj.isAdd = false;
    obj.isUpdate = false;
    obj.isLook = false;
    obj.isRenewal = false;
    obj.dialogShow = false;
    obj.dialogForm = {};
}

// 新增劳务合同
function handleAdd() {
    obj.dialogShow = true;
    obj.title = "新增";
    obj.isAdd = true;
}

// 修改劳务合同
function handleUpdate() {
    obj.dialogShow = true;
    obj.title = "修改";
    obj.isUpdate = true;
}

// 查看劳务合同
function handleView() {
    obj.dialogShow = true;
    obj.title = "查看";
    obj.isLook = true;
}

// 续约劳务合同
function handleRenew() {
    obj.dialogShow = true;
    obj.title = "续约";
    obj.isRenewal = true;
}

// 终止劳务合同
function handleTerminate() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要终止的劳务合同数据');
        return;
    }
    proxy.$modal.confirm('是否确认终止选中的劳务合同？').then(function () {
        // 这里可以调用API终止劳务合同
        proxy.$modal.msgSuccess('终止劳务合同成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

// 导出数据
function handleExport() {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

// 下载导入模版
function handleDownloadTemplate() {
    proxy.$modal.msgSuccess('下载导入模版成功');
}

// 批量导入
function handleBatchImport() {
    obj.dialogShow4 = true;
    obj.title = "批量导入";
}

// 上传劳务合同
function handleUploadContract() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要上传劳务合同的数据');
        return;
    }
    proxy.$modal.msgSuccess('上传劳务合同成功');
}

// 批量上传劳务合同
function handleBatchUploadContract() {
    obj.dialogShow3 = true;
    obj.title = "批量上传劳务合同";
}

// 上传身份信息文件
function handleUploadIdentityInfo() {
    obj.dialogShow3 = true;
    obj.title = "上传身份信息文件";
}

// 批量上传身份信息文件
function handleBatchUploadIdentityInfo() {
    obj.dialogShow3 = true;
    obj.title = "批量上传身份信息文件";
}

// 修改签署状态
function handleModifyStatus() {
    if (obj.ids.length !== 1) {
        proxy.$modal.msgWarning('请选择一条要修改签署状态的数据');
        return;
    }
    proxy.$modal.msgSuccess('修改签署状态成功');
}

// 提交
function handleSubmit() {
    proxy.$modal.msgSuccess('提交成功');
    obj.dialogShow = false;
}

// 上传成功回调
function uploadedSuccessfully(value) {
    console.log(value);
    proxy.$modal.msgSuccess('文件上传成功');
}

getList();
</script>
<style lang="scss" scoped>
.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    /* 这里控制按钮之间的间距 */
}

.button-group .el-button {
    margin: 5px 0;
    /* 这里控制按钮上下的间距 */
    margin-right: 0;
    /* 覆盖Element UI的默认右边距 */
}
</style>