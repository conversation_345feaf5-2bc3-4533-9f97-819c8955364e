<template>
    <div>
        <el-dialog v-model="dialogShow" title="下载中心" width="60%" @close="handleClose">
            <el-form :model="obj.queryParams" ref="queryRef" label-width="auto" inline>
                <el-form-item label="任务编号">
                    <el-input class="width220" v-model="obj.queryParams.taskCode" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="任务类型">
                    <el-select class="width220" v-model="obj.queryParams.taskType" placeholder="请选择" clearable>
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="任务状态">
                    <el-select class="width220" v-model="obj.queryParams.taskStatus" placeholder="请选择" clearable>
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="创建时间起">
                    <el-date-picker class="width220" v-model="obj.queryParams.startTime" placeholder="请输入" />
                </el-form-item>
                <el-form-item label="创建时间止">
                    <el-date-picker class="width220" v-model="obj.queryParams.endTime" placeholder="请输入" />
                </el-form-item>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table :data="obj.tableData" border>
                <el-table-column label="消息编号" align="center" prop="messageCode" />
                <el-table-column label="任务类型" align="center" prop="taskType" />
                <el-table-column label="任务状态" align="center" prop="taskStatus" />
                <el-table-column label="文件名称" align="center" prop="fileName" />
                <el-table-column label="创建人" align="center" prop="createBy" />
                <el-table-column label="任务开始时间" align="center" prop="startTime" />
                <el-table-column label="操作" align="center">
                    <template #default="scope">
                        <el-button type="primary" text @click="handleDownload(scope.row)">下载文件</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
                v-model:limit="obj.queryParams.pageSize" @pagination="getList" />
        </el-dialog>
    </div>
</template>

<script setup name="DownloadCenter">
const options = ref([
    {
        value: '1',
        label: '任务类型1'
    },
    {
        value: '2',
        label: '任务类型2'
    }
]);

const props = defineProps({
    dialogShow: {
        type: Boolean,
        default: false
    }
});


// 使用本地变量跟踪对话框状态
const dialogShow = ref(props.dialogShow);

// 监听props.dialogShow的变化，更新本地状态
watch(() => props.dialogShow, (newVal) => {
    dialogShow.value = newVal;
});

// 监听本地状态的变化，通过emit更新父组件的状态
watch(dialogShow, (newVal) => {
    emit('update:dialogShow', newVal);
});

const obj = ref({
    total: 0,
    queryParams: {
        pageNum: 1,
        pageSize: 10
    },
    tableData: []
});
// 搜索
function handleQuery() {
    proxy.resetForm("queryRef");
    getList();
}
// 重置
function resetQuery() {
    obj.queryParams = {
        pageNum: 1,
        pageSize: 10
    };
    handleQuery();
}
// 下载 
function handleDownload(row) {
    console.log(row);
}
// 先定义emit
const emit = defineEmits(['update:dialogShow', 'close']);
// 关闭
function handleClose() {
    dialogShow.value = false;
    emit('close');
}
</script>
