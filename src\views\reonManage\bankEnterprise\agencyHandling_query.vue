<!-- 代发经办查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form @submit.prevent :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="业务参考号:" prop="referenceNo" required>
                <el-input class="width220" v-model="obj.queryParams.referenceNo" placeholder="请输入业务参考号" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="参考号" align="center" prop="referenceNo" />
            <el-table-column label="业务编码" align="center" prop="businessCode" />
            <el-table-column label="续传批次号" align="center" prop="continueBatchNo" />
            <el-table-column label="续传明细序号" align="center" prop="continueDetailNo" />
            <el-table-column label="续传历史查询标志" align="center" prop="continueHistoryQueryFlag" />
            <el-table-column label="保留字" align="center" prop="reservedField" />
        </el-table>
        <el-table style="margin-top: 50px;" v-loading="obj.loading" border :data="obj.tableData2"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="流程实例号" align="center" width="100" prop="flowInstanceNo" />
            <el-table-column label="业务类型" align="center" prop="businessType" />
            <el-table-column label="业务模式" align="center" prop="businessMode" />
            <el-table-column label="经办日" align="center" prop="operationDate" />
            <el-table-column label="期望日" align="center" prop="expectedDate" />
            <el-table-column label="期望时间" align="center" prop="expectedTime" />
            <el-table-column label="业务请求状态" align="center" width="120" prop="businessRequestStatus" />
            <el-table-column label="业务请求结果" align="center" width="120" prop="businessRequestResult" />
            <el-table-column label="待处理操作步骤" align="center" width="120" prop="pendingOperationStep" />
            <el-table-column label="操作别名" align="center" prop="operationAlias" />
            <el-table-column label="待处理操作序列" align="center" width="120" prop="pendingOperationSequence" />
            <el-table-column label="业务受理分行号" align="center" width="120" prop="businessAcceptBranchNo" />
            <el-table-column label="账号" align="center" prop="accountNo" />
            <el-table-column label="户名" align="center" prop="accountName" />
            <el-table-column label="交易笔数" align="center" prop="transactionCount" />
            <el-table-column label="总金额" align="center" prop="totalAmount" />
            <el-table-column label="成功笔数" align="center" prop="successCount" />
            <el-table-column label="成功金额" align="center" prop="successAmount" />
            <el-table-column label="币种" align="center" prop="currency" />
            <el-table-column label="货币市场" align="center" prop="currencyMarket" />
            <el-table-column label="代发类型" align="center" prop="agencyType" />
            <el-table-column label="用途" align="center" prop="purpose" />
            <el-table-column label="业务参考号" align="center" width="100" prop="businessReferenceNo" />
            <el-table-column label="记录状态" align="center" prop="recordStatus" />
            <el-table-column label="错误码" align="center" prop="errorCode" />
            <el-table-column label="错误描述" align="center" prop="errorDescription" />
            <el-table-column label="处理批次号" align="center" width="100" prop="processBatchNo" />
            <el-table-column label="附件标志" align="center" prop="attachmentFlag" />
            <el-table-column label="渠道标志" align="center" prop="channelFlag" />
            <el-table-column label="交易流水" align="center" prop="transactionFlow" />
            <el-table-column label="交易套号" align="center" prop="transactionSetNo" />
            <el-table-column label="保留字" align="center" prop="reservedField" />
        </el-table>
        <el-table style="margin-top: 50px;" v-loading="obj.loading" border :data="obj.tableData3"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="批次号码" align="center" prop="batchNo" />
            <el-table-column label="交易序号" align="center" prop="transactionNo" />
            <el-table-column label="账号" align="center" prop="accountNo" />
            <el-table-column label="户名" align="center" prop="accountName" />
            <el-table-column label="交易金额" align="center" prop="transactionAmount" />
            <el-table-column label="实际代扣金额" align="center" width="120" prop="actualDeductionAmount" />
            <el-table-column label="交易日期" align="center" prop="transactionDate" />
            <el-table-column label="注释" align="center" prop="remark" />
            <el-table-column label="交易状态" align="center" prop="transactionStatus" />
            <el-table-column label="失败代码" align="center" prop="failureCode" />
            <el-table-column label="失败原因" align="center" prop="failureReason" />
            <el-table-column label="系统内标志" align="center" width="100" prop="systemInternalFlag" />
            <el-table-column label="他行户口开户行" align="center" width="140" prop="otherBankOpeningBank" />
            <el-table-column label="他行户口开户地" align="center" width="140" prop="otherBankOpeningPlace" />
            <el-table-column label="他行快速标志" align="center" width="120" prop="otherBankFastFlag" />
            <el-table-column label="他行户口联行号" align="center" width="140" prop="otherBankJointBankNo" />
            <el-table-column label="客户代码" align="center" prop="customerCode" />
            <el-table-column label="合作方流水号" align="center" width="120" prop="partnerFlowNo" />
            <el-table-column label="交易流水号" align="center" width="100" prop="transactionFlowNo" />
            <el-table-column label="交易套号" align="center" prop="transactionSetNo" />
            <el-table-column label="对接代理行状态" align="center" width="140" prop="agentBankStatus" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

    </div>
</template>

<script setup name="AgencyHandling_query">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()



const obj = reactive({
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        referenceNo: null,
    },//查询表单
    total: 0,//总条数
    tableData1: [],//基本信息列表
    tableData2: [],//业务信息列表
    tableData3: [],//交易明细列表
    ids: [],//选中的id
})

/** 列表 */
function getList() {
    obj.loading = true;
    // 模拟数据
    setTimeout(() => {
        // 基本信息数据
        obj.tableData1 = [
            {
                id: 1,
                referenceNo: 'REF20230501001',
                businessCode: 'BC001',
                continueBatchNo: 'CB001',
                continueDetailNo: 'CD001',
                continueHistoryQueryFlag: '否',
                reservedField: ''
            }
        ];

        // 业务信息数据
        obj.tableData2 = [
            {
                id: 1,
                flowInstanceNo: 'FI001',
                businessType: '工资代发',
                businessMode: '批量代发',
                operationDate: '2023-05-15',
                expectedDate: '2023-05-15',
                expectedTime: '10:30:00',
                businessRequestStatus: '已完成',
                businessRequestResult: '成功',
                pendingOperationStep: '',
                operationAlias: '代发',
                pendingOperationSequence: '',
                businessAcceptBranchNo: '0001',
                accountNo: '****************',
                accountName: '张三',
                transactionCount: 100,
                totalAmount: 500000.00,
                successCount: 98,
                successAmount: 490000.00,
                currency: 'CNY',
                currencyMarket: '人民币',
                agencyType: '工资',
                purpose: '工资发放',
                businessReferenceNo: 'REF20230501001',
                recordStatus: '正常',
                errorCode: '',
                errorDescription: '',
                processBatchNo: 'PB001',
                attachmentFlag: '否',
                channelFlag: '网银',
                transactionFlow: 'TF001',
                transactionSetNo: 'TS001',
                reservedField: ''
            }
        ];

        // 交易明细数据
        obj.tableData3 = [
            {
                id: 1,
                batchNo: 'BN001',
                transactionNo: 'TN001',
                accountNo: '****************',
                accountName: '李四',
                transactionAmount: 5000.00,
                actualDeductionAmount: 5000.00,
                transactionDate: '2023-05-15',
                remark: '工资',
                transactionStatus: '成功',
                failureCode: '',
                failureReason: '',
                systemInternalFlag: '内部',
                otherBankOpeningBank: '',
                otherBankOpeningPlace: '',
                otherBankFastFlag: '',
                otherBankJointBankNo: '',
                customerCode: 'CC001',
                partnerFlowNo: 'PF001',
                transactionFlowNo: 'TFN001',
                transactionSetNo: 'TSN001',
                agentBankStatus: ''
            },
            {
                id: 2,
                batchNo: 'BN001',
                transactionNo: 'TN002',
                accountNo: '****************',
                accountName: '王五',
                transactionAmount: 5000.00,
                actualDeductionAmount: 5000.00,
                transactionDate: '2023-05-15',
                remark: '工资',
                transactionStatus: '成功',
                failureCode: '',
                failureReason: '',
                systemInternalFlag: '内部',
                otherBankOpeningBank: '',
                otherBankOpeningPlace: '',
                otherBankFastFlag: '',
                otherBankJointBankNo: '',
                customerCode: 'CC002',
                partnerFlowNo: 'PF002',
                transactionFlowNo: 'TFN002',
                transactionSetNo: 'TSN002',
                agentBankStatus: ''
            }
        ];

        obj.total = obj.tableData3.length;
        obj.loading = false;
    }, 300);
}


/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}
getList();
</script>
<style lang="scss" scoped></style>