<!-- 销售报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="当前销售起始年月:" prop="salesStartMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.salesStartMonth" type="month"
                    placeholder="请选择年月" clearable />
            </el-form-item>
            <el-form-item label="当前销售结束年月:" prop="salesEndMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.salesEndMonth" type="month"
                    placeholder="请选择年月" clearable />
            </el-form-item>
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" readonly @click="handleClient" v-model="obj.queryParams.customerName"
                    placeholder="请选择客户" />
            </el-form-item>
            <el-form-item label="账单年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择年月"
                    clearable />
            </el-form-item>
            <el-form-item label="服务类型:" prop="serviceType">
                <el-select class="width220" v-model="obj.queryParams.serviceType" placeholder="请选择" clearable>
                    <el-option v-for="item in serviceTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="销售:" prop="sales">
                <el-select class="width220" v-model="obj.queryParams.sales" placeholder="请选择" clearable>
                    <el-option v-for="item in salesOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="销售所属城市:" prop="salesCity">
                <el-select class="width220" v-model="obj.queryParams.salesCity" placeholder="请选择" clearable>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="新增/存量标识:" prop="newOrStock">
                <el-select class="width220" v-model="obj.queryParams.newOrStock" placeholder="请选择" clearable>
                    <el-option v-for="item in newOrStockOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="集团名称:" prop="groupName">
                <el-input class="width220" v-model="obj.queryParams.groupName" placeholder="请输入集团名称" clearable />
            </el-form-item>
            <el-form-item label="合同初始日期(起):" prop="contractInitDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.contractInitDateStart" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="合同开始日期(起):" prop="contractStartDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.contractStartDateStart" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="合同开始日期(止):" prop="contractStartDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.contractStartDateEnd" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="合同结束日期(起):" prop="contractEndDateStart">
                <el-date-picker class="width220" v-model="obj.queryParams.contractEndDateStart" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>
            <el-form-item label="合同结束日期(止):" prop="contractEndDateEnd">
                <el-date-picker class="width220" v-model="obj.queryParams.contractEndDateEnd" type="date"
                    placeholder="请选择日期" clearable />
            </el-form-item>

            <el-row>
                <el-form-item>
                    <el-button icon="Refresh" @click="handleReset">重置</el-button>
                    <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
                    <el-button type="primary" icon="DataAnalysis" @click="handleSummary">汇总</el-button>
                    <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
                </el-form-item>
            </el-row>
        </el-form>

        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip :data="obj.tableData" border style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column typee="index" label="序号" width="80" align="center" />
            <el-table-column prop="contractNumber" label="合同编号" align="center" />
            <el-table-column prop="contractName" label="合同名称" align="center" />
            <el-table-column prop="contractType" label="合同类型" align="center" />
            <el-table-column prop="sales" label="销售" align="center" />
            <el-table-column prop="signingCompany" label="签单分公司" align="center" />
            <el-table-column prop="customerName" label="客户名称" align="center" />
            <el-table-column prop="groupName" label="集团名称" align="center" />
            <el-table-column prop="customerAccount" label="客户帐套" align="center" />
            <el-table-column prop="salesCity" label="销售所属城市" align="center" />
            <el-table-column prop="billMonth" label="账单年月" align="center" />
            <el-table-column prop="financialReceivableMonth" label="财务应收年月" align="center" />
            <el-table-column prop="billPersonCount" label="账单人数" align="center">
                <template #default="scope">
                    <span>{{ scope.row.billPersonCount }} 人</span>
                </template>
            </el-table-column>
            <el-table-column prop="receivableAmount" label="应收金额" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.receivableAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="disabilityAmount" label="残保金" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.disabilityAmount) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="generationStatus" label="生成状态" align="center">
                <template #default="scope">
                    <dict-tag :options="statusOptions" :value="scope.row.generationStatus" />
                </template>
            </el-table-column>
            <el-table-column prop="generator" label="生成人" align="center" />
            <el-table-column prop="newOrStock" label="新增/存量标识" align="center">
                <template #default="scope">
                    <el-tag :type="scope.row.newOrStock === 'new' ? 'success' : 'info'">
                        {{ scope.row.newOrStock === 'new' ? '新增' : '存量' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="receivableServiceFee" label="应收服务费" align="center">
                <template #default="scope">
                    <span>{{ formatAmount(scope.row.receivableServiceFee) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="billingTime" label="出账时间" align="center" />
            <el-table-column label="操作" width="120" align="center">
                <template #default="scope">
                    <el-button type="primary" link icon="Download" @click="handleExportRow(scope.row)">导出</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 客户选择对话框 -->
        <client v-model:show="clientShow" @select="handleSelect" />
    </div>
</template>

<script setup>

import { listScale } from "@/api/reonApi/scale";
import client from '@/views/reonManage/components/client.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 服务类型选项
const serviceTypeOptions = [
    { value: '1', label: '社保' },
    { value: '2', label: '商保' },
    { value: '3', label: '公积金' },
    { value: '4', label: '工资代发' }
];

// 销售选项
const salesOptions = [
    { value: '1', label: '销售1' },
    { value: '2', label: '销售2' },
    { value: '3', label: '销售3' }
];

// 城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
];

// 新增/存量标识选项
const newOrStockOptions = [
    { value: 'new', label: '新增' },
    { value: 'stock', label: '存量' }
];

// 生成状态选项
const statusOptions = [
    { value: '0', label: '未生成', elTagType: 'info' },
    { value: '1', label: '已生成', elTagType: 'success' },
    { value: '2', label: '生成失败', elTagType: 'danger' }
];

const obj = reactive({
    loading: false, // 加载状态
    total: 0, // 总条数
    ids: [], // 选中id
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        salesStartMonth: null,
        salesEndMonth: null,
        customerName: null,
        customerId: null, // 客户ID，用于接口传参
        billMonth: null,
        serviceType: null,
        sales: null,
        salesCity: null,
        newOrStock: null,
        groupName: null,
        contractInitDateStart: null,
        contractStartDateStart: null,
        contractStartDateEnd: null,
        contractEndDateStart: null,
        contractEndDateEnd: null
    }, // 查询表单
    tableData: [] // 表格数据
});

const clientShow = ref(false);



/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                contractNumber: 'HT20230001',
                contractName: '合同1',
                contractType: '劳务派遣',
                sales: '销售1',
                signingCompany: '北京分公司',
                customerName: '客户A',
                groupName: '集团A',
                customerAccount: '帐套A',
                salesCity: '北京',
                billMonth: '2023-05',
                financialReceivableMonth: '2023-06',
                billPersonCount: 100,
                receivableAmount: 50000.00,
                disabilityAmount: 1000.00,
                generationStatus: '1',
                generator: '管理员',
                newOrStock: 'new',
                receivableServiceFee: 5000.00,
                billingTime: '2023-05-15 10:00:00'
            },
            {
                id: 2,
                contractNumber: 'HT20230002',
                contractName: '合同2',
                contractType: '劳务外包',
                sales: '销售2',
                signingCompany: '上海分公司',
                customerName: '客户B',
                groupName: '集团B',
                customerAccount: '帐套B',
                salesCity: '上海',
                billMonth: '2023-06',
                financialReceivableMonth: '2023-07',
                billPersonCount: 150,
                receivableAmount: 75000.00,
                disabilityAmount: 1500.00,
                generationStatus: '1',
                generator: '管理员',
                newOrStock: 'stock',
                receivableServiceFee: 7500.00,
                billingTime: '2023-06-15 11:00:00'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {

    });
}

/** 查询按钮操作 */
function handleSearch() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function handleReset() {
    proxy.resetForm("queryRef");
    obj.queryParams.customerName = null;
    obj.queryParams.customerId = null;
    handleSearch();
}

/** 客户选择 */
function handleClient() {
    clientShow.value = true;
}

/** 导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 导出行数据按钮操作 */
function handleExportRow(row) {
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 汇总按钮操作 */
function handleSummary() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可汇总');
        return;
    }
    proxy.$modal.confirm('确认汇总所有数据吗？').then(() => {
        // 调用汇总接口
        proxy.$modal.msgSuccess('汇总成功');
    }).catch(() => { });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
}

/** 选择客户 */
function handleSelect(row) {
    if (row) {
        obj.queryParams.customerName = row.name;
        obj.queryParams.customerId = row.id;
    } else {
        obj.queryParams.customerName = null;
        obj.queryParams.customerId = null;
    }
}

// 初始化数据
getList();
</script>

<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}
</style>