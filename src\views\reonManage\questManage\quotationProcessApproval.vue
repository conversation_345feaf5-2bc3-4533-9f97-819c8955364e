<!-- 报价单流程审批 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline label-width="auto">
            <el-form-item label="提交人:" prop="submitter">
                <el-select class="width220" v-model="obj.queryParams.submitter" placeholder="请选择" clearable>
                    <el-option v-for="item in submitterOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="客户:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入"
                    @focus="handleCustomer" />
            </el-form-item>
            <el-form-item label="报价单名称:" prop="contractName">
                <el-input class="width220" v-model="obj.queryParams.contractName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="创建时间起始:" prop="startTime">
                <el-date-picker class="width220" v-model="obj.queryParams.startTime" type="date" placeholder="请选择" />
            </el-form-item>
            <el-form-item label="创建时间截止:" prop="endTime">
                <el-date-picker class="width220" v-model="obj.queryParams.endTime" type="date" placeholder="请选择" />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData">
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="报价单名称" align="center" prop="contractName" />
            <el-table-column label="节点名称" align="center" prop="nodeName" />
            <el-table-column label="提交人" align="center" prop="submitter" />
            <el-table-column label="待处理人" align="center" prop="processor" />
            <el-table-column label="创建时间" align="center" prop="createTime" />
            <el-table-column label="操作" align="center" width="180">
                <template #default="scope">
                    <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <client :width="'35%'" v-model:show="clientShow" :isShow="false" @select="handleSelect" />
    </div>
</template>

<script setup name="QuotationProcessApproval">
import { listSupplement } from '@/api/reonApi/supplement';
import client from '@/views/reonManage/components/client.vue'

const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

// 提交人选项
const submitterOptions = [
    { value: '1', label: '张三' },
    { value: '2', label: '李四' },
    { value: '3', label: '王五' },
    { value: '4', label: '赵六' },
    { value: '5', label: '钱七' }
];

const obj = reactive({
    loading: false,// 加载状态
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        submitter: null,
        customerName: null,
        contractName: null,
        supplementType: null,
        startTime: null,
        endTime: null
    },
    tableData: [{
        id: 1,
        customerName: '客户A',
        contractName: '合同A补充协议',
        supplementType: '金额变更',
        nodeName: '部门经理审批',
        submitter: '张三',
        processor: '李四',
        createTime: '2023-07-01 10:00:00'
    },
    {
        id: 2,
        customerName: '客户B',
        contractName: '合同B补充协议',
        supplementType: '时间变更',
        nodeName: '财务审批',
        submitter: '王五',
        processor: '赵六',
        createTime: '2023-07-02 11:30:00'
    }],
    total: 0,

})
// 客户选择显示
const clientShow = ref(false);
/** 客户信息选择 */
function handleCustomer() {
    clientShow.value = true;
}

/** 选择客户 */
function handleSelect(row) {
    if (row) {
        obj.queryParams.customerName = row.name;
    } else {
        obj.queryParams.customerName = null;
    }
}

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    listSupplement(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 查看详情 */
function handleDetail(row) {
    proxy.$modal.msgInfo(`查看ID为${row.id}的详细信息`);
}


getList();
</script>
<style lang="scss" scoped></style>