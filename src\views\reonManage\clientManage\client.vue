<!-- 客户管理 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName" required>
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户编号:" prop="customerCode" required>
                <el-input class="width220" v-model="obj.queryParams.customerCode" placeholder="请输入客户编号" clearable />
            </el-form-item>
            <el-form-item label="城市:" prop="city">
                <el-select class="width220" filterable v-model="obj.queryParams.city" placeholder="请选择城市" clearable>
                    <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single" @click="handleUpdate">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" :disabled="obj.single"
                    @click="handleRelevance">关联</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="View" @click="handleViewRelevance">查看关联</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Download" @click="handleDownload">下载模版</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain @click="handleExport">关联公司导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="Edit" @click="handleUpdateName">修改客户名称</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="View" @click="handleViewUpdateRecord">查看修改记录</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="obj.showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <!-- 表格 -->
        <el-table v-loading="obj.loading" show-overflow-tooltip border :data="obj.tableData"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="客户编号" align="center" prop="customerCode" />
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="集团名称" align="center" prop="groupName" />
            <el-table-column label="所属行业" align="center" prop="industry" />
            <el-table-column label="城市" align="center" prop="city" />
            <el-table-column label="创建人" align="center" prop="createBy" />
            <el-table-column label="是否重点客户" align="center" prop="isKeyCustomer">
                <template #default="scope">
                    <dict-tag :options="sys_yes_no" :value="scope.row.isKeyCustomer" />
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180">
                <template #default="scope">
                    <el-button type="primary" link icon="Edit" @click="handleUpdate(scope.row)">编辑</el-button>
                    <el-button type="primary" link icon="View" @click="handleDetail(scope.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow" width="60%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="客户名称" prop="customerName"
                    :rules="[{ required: true, message: '请输入客户名称', trigger: 'blur' }]">
                    <el-input class="width220" v-model="obj.dialogForm.customerName" placeholder="请输入客户名称" clearable />
                </el-form-item>
                <el-form-item label="所属行业" prop="industry"
                    :rules="[{ required: true, message: '请选择所属行业', trigger: 'change' }]">
                    <el-select class="width220" v-model="obj.dialogForm.industry" placeholder="请选择行业" clearable>
                        <el-option v-for="item in industryOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户具体业务" prop="businessDetail">
                    <el-input class="width220" v-model="obj.dialogForm.businessDetail" placeholder="请输入客户具体业务"
                        clearable />
                </el-form-item>
                <el-form-item label="所在城市" prop="city"
                    :rules="[{ required: true, message: '请选择所在城市', trigger: 'change' }]">
                    <el-select class="width220" filterable v-model="obj.dialogForm.city" placeholder="请选择城市" clearable>
                        <el-option v-for="item in cityOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="客户联系人" prop="contactPerson">
                    <el-input class="width220" v-model="obj.dialogForm.contactPerson" placeholder="请输入客户联系人"
                        clearable />
                </el-form-item>
                <el-form-item label="客户联系电话" prop="contactPhone"
                    :rules="[{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }]">
                    <el-input class="width220" v-model="obj.dialogForm.contactPhone" placeholder="请输入客户联系电话"
                        clearable />
                </el-form-item>
                <el-form-item label="客户邮箱" prop="email"
                    :rules="[{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }]">
                    <el-input class="width220" v-model="obj.dialogForm.email" placeholder="请输入客户邮箱" clearable />
                </el-form-item>
                <el-form-item label="客户联系地址" prop="address">
                    <el-input class="width220" v-model="obj.dialogForm.address" placeholder="请输入客户联系地址" clearable />
                </el-form-item>
                <el-form-item label="客户职位" prop="position">
                    <el-input class="width220" v-model="obj.dialogForm.position" placeholder="请输入客户职位" clearable />
                </el-form-item>
                <el-form-item label="是否重点客户" prop="isKeyCustomer">
                    <el-select class="width220" v-model="obj.dialogForm.isKeyCustomer" placeholder="请选择" clearable>
                        <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="obj.dialogShow = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 查看关联 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow2" width="65%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-row class="mb8">
                    <el-form-item label="客户名称" prop="customerName">
                        <el-input class="width220" v-model="obj.dialogForm.customerName" placeholder="请输入客户名称" />
                    </el-form-item>
                    <el-form-item label="城市" prop="city">
                        <el-select class="width220" v-model="obj.dialogForm.city" placeholder="请选择城市" clearable>
                            <el-option v-for="item in cityOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="审批状态" prop="approvalStatus">
                        <el-select class="width220" v-model="obj.dialogForm.approvalStatus" placeholder="请选择审批状态"
                            clearable>
                            <el-option v-for="item in approvalStatusOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select class="width220" v-model="obj.dialogForm.status" placeholder="请选择状态" clearable>
                            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </el-row>
                <el-row class="mb8">
                    <el-col :span="24">
                        <el-form-item>
                            <el-button type="warning" plain icon="View" @click="handleViewRelevance">查看关联</el-button>
                            <el-button type="warning" plain icon="View" @click="handleViewRelevance">查看被关联</el-button>
                            <el-button type="primary" plain icon="Refresh" @click="handleReset">重置</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="mb8">
                    <el-col :span="24">
                        <el-form-item>
                            <el-button type="primary" plain @click="">接触关联</el-button>
                            <el-button type="primary" plain @click="">文件修改</el-button>
                            <el-button type="primary" plain @click="">停用关联</el-button>
                            <el-button type="primary" plain @click="">启用关联</el-button>
                            <el-button type="primary" plain @click="">重新提交</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-table :data="obj.tableData" border @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="客户编号" align="center" prop="customerCode" />
                    <el-table-column label="客户名称" align="center" prop="customerName" />
                    <el-table-column label="合同编号" align="center" prop="contractCode" />
                    <el-table-column label="合同名称" align="center" prop="contractName" />
                    <el-table-column label="所属行业" align="center" prop="industry" />
                    <el-table-column label="城市" align="center" prop="city" />
                    <el-table-column label="创建人" align="center" prop="createBy" />
                    <el-table-column label="审批状态" align="center" prop="approvalStatus">
                        <template #default="scope">
                            <el-tag :type="getApprovalStatusType(scope.row.approvalStatus)">
                                {{ getApprovalStatusLabel(scope.row.approvalStatus) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="审批人" align="center" prop="approver" />
                    <el-table-column label="审批时间" align="center" prop="approvalTime" />
                    <el-table-column label="状态" align="center" prop="status">
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                {{ getStatusLabel(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
        </el-dialog>
        <!-- 修改客户名称 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow3" width="55%" append-to-body draggable>
            <el-form class="formHight" ref="formRef" :model="obj.dialogForm" :rules="obj.rules" inline
                label-width="auto">
                <el-form-item label="修改原因" prop="changeReason"
                    :rules="[{ required: true, message: '请输入修改原因', trigger: 'blur' }]">
                    <el-input class="width220" v-model="obj.dialogForm.changeReason" placeholder="请输入修改原因" />
                </el-form-item>
                <el-form-item label="原客户名称" prop="oldCustomerName"
                    :rules="[{ required: true, message: '请输入原客户名称', trigger: 'blur' }]">
                    <el-input class="width220" v-model="obj.dialogForm.oldCustomerName" placeholder="请输入原客户名称"
                        readonly />
                </el-form-item>
                <el-form-item label="新客户名称" prop="newCustomerName"
                    :rules="[{ required: true, message: '请输入新客户名称', trigger: 'blur' }]">
                    <el-input class="width220" v-model="obj.dialogForm.newCustomerName" placeholder="请输入新客户名称" />
                </el-form-item>
                <el-divider content-position="left">文件上传</el-divider>
                <el-row class="mb8">
                    <el-col :span="24">
                        <FileUpload :limit="1" :fileSize="10" :fileType="['pdf', 'doc', 'docx', 'xls', 'xlsx']" />
                    </el-col>
                </el-row>
                <el-divider content-position="left">备注</el-divider>
                <el-input type="textarea" v-model="obj.dialogForm.remark" placeholder="请输入" />
            </el-form>
        </el-dialog>
        <!-- 查看修改记录 -->
        <el-dialog :title="obj.title" v-model="obj.dialogShow4" width="55%" append-to-body draggable>
            <el-table :data="obj.tableData" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column label="客户编号" align="center" prop="customerCode" />
                <el-table-column label="原客户名称" align="center" prop="oldCustomerName" />
                <el-table-column label="新客户名称" align="center" prop="newCustomerName" />
                <el-table-column label="修改人" align="center" prop="updateBy" />
                <el-table-column label="修改时间" align="center" prop="updateTime" />
                <el-table-column label="修改原因" align="center" prop="changeReason" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="Client">

import { listScale } from "@/api/reonApi/scale";
import FileUpload from '@/components/FileUpload'

const { proxy } = getCurrentInstance();

// 字典数据
const { sys_yes_no } = proxy.useDict('sys_yes_no');

// 城市选项
const cityOptions = [
    { value: '1', label: '北京' },
    { value: '2', label: '上海' },
    { value: '3', label: '广州' },
    { value: '4', label: '深圳' },
    { value: '5', label: '杭州' }
];

// 审批状态选项
const approvalStatusOptions = [
    { value: '0', label: '待审批' },
    { value: '1', label: '已审批' },
    { value: '2', label: '已拒绝' }
];

// 状态选项
const statusOptions = [
    { value: '0', label: '停用' },
    { value: '1', label: '启用' },
    { value: '2', label: '已删除' }
];

// 行业选项
const industryOptions = [
    { value: '1', label: '互联网' },
    { value: '2', label: '金融' },
    { value: '3', label: '教育' },
    { value: '4', label: '医疗' },
    { value: '5', label: '制造业' },
    { value: '6', label: '零售' },
    { value: '7', label: '其他' }
];

const obj = reactive({
    showSearch: true, // 显示搜索
    multiple: true, // 是否多选
    single: true, // 是否单选
    loading: false, // 加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        customerCode: null,
        city: null
    }, // 查询表单
    rules: {
        customerName: [{ required: true, message: '请输入客户名称', trigger: 'blur' }],
        industry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
        city: [{ required: true, message: '请选择所在城市', trigger: 'change' }]
    },
    total: 0, // 总条数
    tableData: [], // 列表
    dialogForm: {}, // 表单
    dialogShow: false, // 新增/修改弹出框
    dialogShow2: false, // 查看关联弹出框
    dialogShow3: false, // 修改客户名称弹出框
    dialogShow4: false, // 查看修改记录弹出框
    ids: [], // 选中的id
    title: "", // 标题
})

/** 获取列表数据 */
function getList() {
    obj.loading = true;
    // 调用API获取数据
    listScale(obj.queryParams).then(response => {
        //obj.tableData = response.rows;
        obj.total = response.total;
        obj.loading = false;

        // 模拟数据，实际开发时可以删除
        obj.tableData = [
            {
                id: 1,
                customerCode: 'KH20230001',
                customerName: '客户A',
                groupName: '集团A',
                industry: '1',
                city: '1',
                createBy: '管理员',
                isKeyCustomer: 'Y'
            },
            {
                id: 2,
                customerCode: 'KH20230002',
                customerName: '客户B',
                groupName: '集团B',
                industry: '2',
                city: '2',
                createBy: '管理员',
                isKeyCustomer: 'N'
            }
        ];
        obj.total = 2;
        obj.loading = false;
    }).catch(() => {
    });
}

/** 获取客户详情 */
function getUsers(id) {
    return new Promise((resolve) => {
        // 模拟数据，实际开发时可以调用接口
        setTimeout(() => {
            const data = obj.tableData.find(item => item.id === id) || {
                id: id,
                customerName: '客户' + id,
                industry: '1',
                businessDetail: '具体业务描述',
                city: '1',
                contactPerson: '联系人',
                contactPhone: '***********',
                email: '<EMAIL>',
                address: '北京市海淀区',
                position: '经理',
                isKeyCustomer: 'Y'
            };
            resolve({ data });
        }, 300);
    });
}

/** 添加客户 */
function addUsers(userData) {
    return new Promise((resolve) => {
        // 模拟数据，实际开发时可以调用接口
        setTimeout(() => {
            resolve({ code: 200, msg: '新增成功' });
        }, 300);
    });
}

/** 更新客户 */
function updateUsers(userData) {
    return new Promise((resolve) => {
        // 模拟数据，实际开发时可以调用接口
        setTimeout(() => {
            resolve({ code: 200, msg: '修改成功' });
        }, 300);
    });
}

/** 获取审批状态标签类型 */
function getApprovalStatusType(status) {
    switch (status) {
        case '0':
            return 'info';
        case '1':
            return 'success';
        case '2':
            return 'danger';
        default:
            return '';
    }
}

/** 获取审批状态标签文本 */
function getApprovalStatusLabel(status) {
    switch (status) {
        case '0':
            return '待审批';
        case '1':
            return '已审批';
        case '2':
            return '已拒绝';
        default:
            return status;
    }
}

/** 获取状态标签类型 */
function getStatusType(status) {
    switch (status) {
        case '0':
            return 'info';
        case '1':
            return 'success';
        case '2':
            return 'danger';
        default:
            return '';
    }
}

/** 获取状态标签文本 */
function getStatusLabel(status) {
    switch (status) {
        case '0':
            return '停用';
        case '1':
            return '启用';
        case '2':
            return '已删除';
        default:
            return status;
    }
}

/** 表单重置 */
function reset() {
    obj.dialogForm = {
        customerName: '',
        industry: '',
        businessDetail: '',
        city: '',
        contactPerson: '',
        contactPhone: '',
        email: '',
        address: '',
        position: '',
        isKeyCustomer: ''
    };
    proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 重置按钮操作（关联对话框） */
function handleReset() {
    // 重置关联对话框中的表单
    proxy.resetForm("formRef");
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length !== 1;
    obj.multiple = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    obj.dialogShow = true;
    obj.title = "新增客户";
}

/** 查看按钮操作 */
function handleDetail(row) {
    reset();
    getUsers(row.id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "客户详情";
    });
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    const id = row.id || obj.ids[0];
    getUsers(id).then(response => {
        obj.dialogForm = response.data;
        obj.dialogShow = true;
        obj.title = "修改客户";
    });
}

/** 关联按钮操作 */
function handleRelevance() {
    if (obj.single) {
        proxy.$modal.msgError('请选择要关联的客户');
        return;
    }
    obj.title = "关联客户";
    // 实现关联功能的逻辑
    proxy.$modal.msgSuccess('关联成功');
}

/** 查看关联按钮操作 */
function handleViewRelevance() {
    obj.dialogShow2 = true;
    obj.title = "查看关联";
}

/** 关联公司导出按钮操作 */
function handleExport() {
    if (obj.total === 0) {
        proxy.$modal.msgError('当前没有数据可导出');
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

/** 修改客户名称按钮操作 */
function handleUpdateName() {
    obj.dialogShow3 = true;
    obj.title = "修改客户名称";
}

/** 查看修改记录按钮操作 */
function handleViewUpdateRecord() {
    obj.dialogShow4 = true;
    obj.title = "查看修改记录";
}

/** 下载模版按钮操作 */
function handleDownload() {
    // 调用下载模版接口
    proxy.$modal.msgSuccess('模版下载成功');
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
        if (valid) {
            if (obj.dialogForm.id != null) {
                updateUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("修改成功");
                    obj.dialogShow = false;
                    getList();
                });
            } else {
                addUsers(obj.dialogForm).then(() => {
                    proxy.$modal.msgSuccess("新增成功");
                    obj.dialogShow = false;
                    getList();
                });
            }
        }
    });
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped>
.width220 {
    width: 220px;
}

.mb8 {
    margin-bottom: 8px;
}

.mb20 {
    margin-bottom: 20px;
}

.formHight {
    max-height: 500px;
    overflow-y: auto;
}
</style>