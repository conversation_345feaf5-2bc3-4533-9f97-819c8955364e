<!-- 客户一次性项目合并查询 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" ref="queryRef" inline v-show="obj.showSearch" label-width="auto">
            <el-form-item label="客户名称:" prop="customerName">
                <el-input class="width220" v-model="obj.queryParams.customerName" placeholder="请输入客户名称" clearable />
            </el-form-item>
            <el-form-item label="客户账套:" prop="customerAccount">
                <el-select class="width220" v-model="obj.queryParams.customerAccount" placeholder="请选择客户账套" clearable>
                    <el-option label="帐套A" value="帐套A" />
                    <el-option label="帐套B" value="帐套B" />
                    <el-option label="帐套C" value="帐套C" />
                </el-select>
            </el-form-item>
            <el-form-item label="账单模板编号:" prop="billTemplateCode">
                <el-input class="width220" v-model="obj.queryParams.billTemplateCode" placeholder="请输入模板编号" clearable />
            </el-form-item>
            <el-form-item label="账单年月:" prop="billMonth">
                <el-date-picker class="width220" v-model="obj.queryParams.billMonth" type="month" placeholder="请选择账单年月"
                    clearable />
            </el-form-item>
            <el-form-item label="是否锁定:" prop="isLocked">
                <el-select class="width220" v-model="obj.queryParams.isLocked" placeholder="请选择是否锁定" clearable>
                    <el-option label="是" value="1" />
                    <el-option label="否" value="0" />
                </el-select>
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-col :span="24">
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
                        <el-button type="info" icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <!-- 表格 -->
        <el-table ref="singleTableRef" border :data="obj.tableData" highlight-current-row
            @current-change="handleCurrentChange">
            <el-table-column align="center" width="60">
                <template #default="scope">
                    <el-radio v-model="radio" :value="scope.row.id" @change="handleCurrentChange(scope.row)"></el-radio>
                </template>
            </el-table-column>
            <el-table-column label="客户名称" align="center" prop="customerName" />
            <el-table-column label="客户帐套" align="center" prop="customerAccount" />
            <el-table-column label="账单模板编号" align="center" prop="billTemplateCode" />
            <el-table-column label="账单年月" align="center" prop="billMonth" />
            <el-table-column label="财务应收年月" align="center" prop="financialReceivableMonth" />
            <el-table-column label="应收金额" align="center" prop="receivableAmount" />
            <el-table-column label="账单状态" align="center" prop="billStatus" />
            <el-table-column label="驳回原因" align="center" prop="rejectReason" />
        </el-table>

        <!-- 分页 -->
        <pagination v-show="obj.total > 0" :total="obj.total" v-model:page="obj.queryParams.pageNum"
            v-model:limit="obj.queryParams.pageSize" @pagination="getList" />

        <el-row :gutter="10" class="mt20 mb10">
            <el-button type="primary" plain icon="Plus" @click="handleAdd">添加</el-button>
            <el-button type="danger" plain icon="Delete" @click="handleDelete">删除</el-button>
            <el-button type="success" plain icon="Check" @click="handleSubmit">提交审核</el-button>
        </el-row>
        <el-table :data="obj.tableData" border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" />
            <el-table-column label="一级类别" align="center">
                <template #header>
                    <span style="color: red;">*</span>
                    一级类别
                </template>
                <template #default="scope">
                    <el-select style="width: 100%;" v-model="scope.row.primaryCategory" placeholder="请选择一级类别" clearable>
                        <el-option label="工资" value="工资" />
                        <el-option label="福利" value="福利" />
                        <el-option label="其他" value="其他" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="二级类别" align="center">
                <template #default="scope">
                    <el-select style="width: 100%;" v-model="scope.row.secondaryCategory" placeholder="请选择二级类别"
                        clearable>
                        <el-option label="基本工资" value="基本工资" />
                        <el-option label="年终奖" value="年终奖" />
                        <el-option label="其他补贴" value="其他补贴" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="金额" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.amount" placeholder="请输入金额" clearable />
                </template>
            </el-table-column>
            <el-table-column label="金额(不含税)" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.amountExcludingTax" placeholder="请输入不含税金额"
                        clearable />
                </template>
            </el-table-column>
            <el-table-column label="增值税率(%)" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.vatRate" placeholder="请输入税率" clearable />
                </template>
            </el-table-column>
            <el-table-column label="增值税" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.vat" placeholder="请输入增值税" clearable />
                </template>
            </el-table-column>
            <el-table-column label="供应商成本" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.supplierCost" placeholder="请输入供应商成本" clearable />
                </template>
            </el-table-column>
            <el-table-column label="一次性支持人员" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.oneTimeStaff" placeholder="请输入人员数" clearable />
                </template>
            </el-table-column>
            <el-table-column label="供应商" align="center">
                <template #default="scope">
                    <el-select style="width: 100%;" v-model="scope.row.supplier" placeholder="请选择供应商" clearable>
                        <el-option label="供应商A" value="供应商A" />
                        <el-option label="供应商B" value="供应商B" />
                        <el-option label="供应商C" value="供应商C" />
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="大合同名称" align="center" prop="contractName" />
            <el-table-column label="备注" align="center">
                <template #default="scope">
                    <el-input style="width: 100%;" v-model="scope.row.remark" placeholder="请输入备注" clearable />
                </template>
            </el-table-column>
            <el-table-column label="上传文件" align="center" prop="uploadFile" />
        </el-table>
    </div>
</template>

<script setup name="CustomerOneTimeProjectConsolidation_query">


const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()

const radio = ref(null);

const obj = reactive({
    showSearch: true,//显示搜索
    multiple: true,//是否多选
    single: true,//是否单选
    loading: false,//加载中
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerName: null,
        customerAccount: null,
        billTemplateCode: null,
        billMonth: null,
        isLocked: null
    },//查询表单
    total: 0,//总条数
    tableData: [
        {
            id: 1,
            customerName: '客户名称1',
            customerAccount: '帐套A',
            billTemplateCode: 'TEMP001',
            billMonth: '2023-01',
            financialReceivableMonth: '2023-02',
            receivableAmount: '10000',
            billStatus: '已生成',
            rejectReason: '',
            primaryCategory: '工资',
            secondaryCategory: '基本工资',
            amount: '10000',
            amountExcludingTax: '9433.96',
            vatRate: '6',
            vat: '566.04',
            supplierCost: '5000',
            oneTimeStaff: '5',
            supplier: '供应商A',
            contractName: '大合同名称1',
            remark: '备注信息1',
            uploadFile: '文件1.pdf'
        },
        {
            id: 2,
            customerName: '客户名称2',
            customerAccount: '帐套B',
            billTemplateCode: 'TEMP002',
            billMonth: '2023-02',
            financialReceivableMonth: '2023-03',
            receivableAmount: '20000',
            billStatus: '已生成',
            rejectReason: '',
            primaryCategory: '福利',
            secondaryCategory: '年终奖',
            amount: '20000',
            amountExcludingTax: '18867.92',
            vatRate: '6',
            vat: '1132.08',
            supplierCost: '10000',
            oneTimeStaff: '10',
            supplier: '供应商B',
            contractName: '大合同名称2',
            remark: '备注信息2',
            uploadFile: '文件2.pdf'
        },
        {
            id: 3,
            customerName: '客户名称3',
            customerAccount: '帐套C',
            billTemplateCode: 'TEMP003',
            billMonth: '2023-03',
            financialReceivableMonth: '2023-04',
            receivableAmount: '15000',
            billStatus: '已生成',
            rejectReason: '',
            primaryCategory: '其他',
            secondaryCategory: '其他补贴',
            amount: '15000',
            amountExcludingTax: '14151.41',
            vatRate: '6',
            vat: '848.59',
            supplierCost: '7500',
            oneTimeStaff: '7',
            supplier: '供应商C',
            contractName: '大合同名称3',
            remark: '备注信息3',
            uploadFile: '文件3.pdf'
        },
        {
            id: 4,
            customerName: '客户名称4',
            customerAccount: '帐套A',
            billTemplateCode: 'TEMP004',
            billMonth: '2023-04',
            financialReceivableMonth: '2023-05',
            receivableAmount: '12000',
            billStatus: '已生成',
            rejectReason: '',
            primaryCategory: '工资',
            secondaryCategory: '基本工资',
            amount: '12000',
            amountExcludingTax: '11320.75',
            vatRate: '6',
            vat: '679.25',
            supplierCost: '6000',
            oneTimeStaff: '6',
            supplier: '供应商A',
            contractName: '大合同名称4',
            remark: '备注信息4',
            uploadFile: '文件4.pdf'
        }
    ],//列表
    ids: [],//选中的id
    title: "",//标题
})

/** 列表数据 */
function getList() {
    obj.loading = true;
    // 这里可以调用API获取列表数据
    setTimeout(() => {
        obj.loading = false;
        obj.total = obj.tableData.length; // 假设总条数等于当前数据长度
    }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
    obj.queryParams.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
/** 处理当前行变化 */
function handleCurrentChange(row) {
    if (row) {
        radio.value = row.id;
        console.log("已选产品:", row);
    }
}


/** 添加按钮操作 */
function handleAdd() {
    proxy.$modal.msgSuccess('添加成功');
}

/** 删除按钮操作 */
function handleDelete() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要删除的数据');
        return;
    }
    proxy.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        // 这里可以调用API删除数据
        proxy.$modal.msgSuccess('删除成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}

/** 提交审核按钮操作 */
function handleSubmit() {
    if (obj.ids.length === 0) {
        proxy.$modal.msgWarning('请选择要提交的数据');
        return;
    }
    proxy.$modal.confirm('是否确认提交选中的数据项？').then(function () {
        // 这里可以调用API提交数据
        proxy.$modal.msgSuccess('提交成功');
        getList();
    }).catch(() => {
        proxy.$modal.msgWarning('用户取消操作');
    });
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
    obj.ids = selection.map(item => item.id);
    obj.single = selection.length != 1;
    obj.multiple = !selection.length;
}

// 初始化数据
getList();
</script>
<style lang="scss" scoped>
:deep(.el-table__body tr.current-row>td.el-table__cell) {
    background-color: rgb(134, 231, 231);
}
</style>