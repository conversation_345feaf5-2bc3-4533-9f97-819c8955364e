<!-- 订单与实做差异报表 -->
<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :model="obj.queryParams" inline label-width="auto">
            <el-form-item label="大区:" prop="region" required>
                <el-select class="width220" v-model="obj.queryParams.region" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="福利办理方:" prop="practice">
                <el-input class="width220" v-model="obj.queryParams.practice" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="是否单立户:" prop="single">
                <el-select class="width220" v-model="obj.queryParams.single" placeholder="请选择" clearable>
                    <el-option v-for="item in sys_yes_no" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="服务年月起:" prop="startMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.startMonth" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-form-item label="服务年月止:" prop="endMonth" required>
                <el-date-picker class="width220" v-model="obj.queryParams.endMonth" type="date" placeholder="请选择日期"
                    clearable />
            </el-form-item>
            <el-row :gutter="10" class="mb8">
                <el-button type="primary" icon="Download" @click="handleExport">导出</el-button>
            </el-row>
        </el-form>
        <div style="color: red;margin-top: 20px;">
            点击导出后请到下载中心查看文件
        </div>
    </div>
</template>

<script setup name="OrderAndPracticeDiscrepancy">
const { proxy } = getCurrentInstance();
// const { } = proxy.useDict()


const obj = reactive({
    queryParams: {

    },//查询表单

})

/** 导出明细按钮操作 */
function handleExport() {
    if (!obj.queryParams.region) {
        proxy.$modal.msgWarning("请选择大区");
        return;
    }
    if (!obj.queryParams.startMonth) {
        proxy.$modal.msgWarning("请选择服务开始月");
        return;
    }
    if (!obj.queryParams.endMonth) {
        proxy.$modal.msgWarning("请选择服务结束月");
        return;
    }
    proxy.$modal.confirm('确认导出所有数据吗？').then(() => {
        proxy.download('system/import/export', {
            ...obj.queryParams
        }, `import_${new Date().getTime()}.xlsx`)
    }).catch(() => { });
}

</script>
<style lang="scss" scoped></style>